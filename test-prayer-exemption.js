/**
 * Test script to verify prayer exemption system
 */

const { PrayerExemptionRepository } = require('./lib/data/repositories/prayer-exemption.js')
const { PrayerExemptionUseCases } = require('./lib/domain/usecases/prayer-exemption.js')
const { StudentRepository } = require('./lib/data/repositories/student.js')
const { ClassRepository } = require('./lib/data/repositories/class.js')
const { AbsenceUseCases } = require('./lib/domain/usecases/absence.js')
const { AbsenceRepository } = require('./lib/data/repositories/absence.js')
const { getRedisCache } = require('./lib/data/cache/redis.js')
const { AttendanceType } = require('./lib/domain/entities/absence.js')
const { PrayerType, PrayerExemptionType, RecurrencePattern } = require('./lib/domain/entities/prayer-exemption.js')
const { getCurrentWITATime } = require('./lib/utils/date.js')

async function testPrayerExemptionSystem() {
  console.log('🧪 Testing Prayer Exemption System...\n')

  try {
    // Initialize repositories and use cases
    const cache = getRedisCache()
    const prayerExemptionRepo = new PrayerExemptionRepository()
    const studentRepo = new StudentRepository(cache)
    const classRepo = new ClassRepository()
    const absenceRepo = new AbsenceRepository()

    const prayerExemptionUseCases = new PrayerExemptionUseCases(
      prayerExemptionRepo,
      studentRepo,
      classRepo
    )

    const absenceUseCases = new AbsenceUseCases(
      absenceRepo,
      studentRepo,
      cache,
      prayerExemptionUseCases
    )

    // Test 1: Check existing exemptions
    console.log('📋 Test 1: Checking existing exemptions...')
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)
    
    const existingExemptions = await prayerExemptionRepo.findActiveExemptionsForDate(today)
    console.log(`Found ${existingExemptions.length} active exemptions for today`)
    
    if (existingExemptions.length > 0) {
      console.log('Existing exemptions:')
      existingExemptions.forEach(exemption => {
        console.log(`- ID: ${exemption.id}, Type: ${exemption.prayerType}, Pattern: ${exemption.recurrencePattern}`)
      })
    }

    // Test 2: Create a test exemption for today
    console.log('\n📝 Test 2: Creating test exemption for today...')
    try {
      const testExemption = await prayerExemptionUseCases.createExemption({
        exemptionType: PrayerExemptionType.GLOBAL,
        recurrencePattern: RecurrencePattern.ONCE,
        exemptionDate: today,
        prayerType: PrayerType.BOTH,
        reason: 'Test exemption for debugging prayer system',
        createdBy: 31 // Assuming super admin ID
      }, 'super_admin')
      
      console.log(`✅ Created test exemption with ID: ${testExemption.id}`)
    } catch (error) {
      console.log(`⚠️ Could not create test exemption: ${error.message}`)
    }

    // Test 3: Check exemptions again
    console.log('\n🔍 Test 3: Checking exemptions after creation...')
    const updatedExemptions = await prayerExemptionRepo.findActiveExemptionsForDate(today)
    console.log(`Found ${updatedExemptions.length} active exemptions for today`)

    // Test 4: Test student exemption check
    console.log('\n👤 Test 4: Testing student exemption check...')
    
    // Get a test student
    const testStudentCode = 'test-student-uuid' // You'll need to replace with actual student UUID
    
    const isZuhrExempted = await prayerExemptionUseCases.isStudentExempted(
      testStudentCode,
      today,
      AttendanceType.ZUHR
    )
    
    const isAsrExempted = await prayerExemptionUseCases.isStudentExempted(
      testStudentCode,
      today,
      AttendanceType.ASR
    )

    console.log(`Student ${testStudentCode.substring(0, 8)}... exemption status:`)
    console.log(`- Zuhr exempted: ${isZuhrExempted}`)
    console.log(`- Asr exempted: ${isAsrExempted}`)

    // Test 5: Test departure validation
    console.log('\n🚪 Test 5: Testing departure validation...')
    try {
      await absenceUseCases.recordAbsence(testStudentCode, AttendanceType.DISMISSAL, false)
      console.log('✅ Departure allowed (exemption working)')
    } catch (error) {
      console.log(`❌ Departure blocked: ${error.message}`)
    }

    console.log('\n✅ Prayer exemption system test completed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testPrayerExemptionSystem().catch(console.error)
