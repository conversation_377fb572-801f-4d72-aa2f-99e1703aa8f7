// Migration script for production database
const { loadEnvConfig } = require('@next/env');
const { drizzle } = require('drizzle-orm/postgres-js');
const { migrate } = require('drizzle-orm/postgres-js/migrator');
const postgres = require('postgres');

// Load environment variables
loadEnvConfig(process.cwd());

async function runMigrations() {
  console.log('🚀 Starting Database Migration');
  console.log('==============================');
  
  const connectionString = process.env.DATABASE_URL;
  
  if (!connectionString) {
    console.error('❌ DATABASE_URL not found in environment variables');
    console.error('Please ensure SSH tunnel is running: bash scripts/dev-tunnel.sh status');
    process.exit(1);
  }
  
  console.log('🔗 Connecting to database...');
  console.log('Database URL:', connectionString.replace(/:[^:@]*@/, ':****@'));
  
  // Create postgres client
  const client = postgres(connectionString, {
    max: 1, // Use single connection for migration
    ssl: false // SSH tunnel doesn't need SSL
  });
  
  // Create drizzle instance
  const db = drizzle(client);
  
  try {
    console.log('\n🔄 Running migrations...');
    
    // Run migrations from the migrations folder
    await migrate(db, { 
      migrationsFolder: './drizzle/migrations',
      migrationsTable: '__drizzle_migrations',
      migrationsSchema: 'drizzle'
    });
    
    console.log('✅ Migrations completed successfully!');
    
    // Verify the reason column was added
    console.log('\n🔍 Verifying reason column...');
    const result = await client`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'absences' 
      AND column_name = 'reason'
      AND table_schema = 'public'
    `;
    
    if (result.length > 0) {
      const column = result[0];
      console.log('✅ Reason column successfully added:');
      console.log(`   - Column: ${column.column_name}`);
      console.log(`   - Type: ${column.data_type}`);
      console.log(`   - Nullable: ${column.is_nullable}`);
    } else {
      console.log('❌ Reason column not found after migration');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    // Close the connection
    await client.end();
    console.log('\n🔌 Database connection closed');
  }
}

// Run migrations
runMigrations().catch((error) => {
  console.error('❌ Migration script failed:', error);
  process.exit(1);
});
