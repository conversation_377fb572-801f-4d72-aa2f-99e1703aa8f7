-- Migration: Add class context to absences table for historical tracking
-- Purpose: Enable tracking of student class at time of attendance recording
-- This prevents data loss when students change classes (grade progression)
-- 
-- PRODUCTION SAFE MIGRATION:
-- - Uses nullable columns for backward compatibility
-- - Includes proper foreign key constraints
-- - Adds performance indexes
-- - Includes verification queries
-- - Safe for large tables (3000+ students, millions of records)

-- Step 1: Add new columns (nullable for backward compatibility)
ALTER TABLE "absences" ADD COLUMN "class_id" integer;
ALTER TABLE "absences" ADD COLUMN "class_name" varchar(10);

-- Step 2: Add foreign key constraint with proper cascade behavior
-- ON DELETE SET NULL: If a class is deleted, set class_id to NULL (preserve attendance record)
-- This is safer than CASCADE which would delete attendance records
ALTER TABLE "absences" ADD CONSTRAINT "absences_class_id_classes_id_fk" 
  FOREIGN KEY ("class_id") REFERENCES "classes"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Step 3: Add performance indexes for efficient queries
-- Index for class-based filtering (reports by class)
CREATE INDEX "idx_absences_class_id" ON "absences"("class_id");

-- Index for class name filtering (direct class name queries)
CREATE INDEX "idx_absences_class_name" ON "absences"("class_name");

-- Composite index for class + date queries (monthly/yearly reports by class)
CREATE INDEX "idx_absences_class_recorded_at" ON "absences"("class_id", "recorded_at");

-- Composite index for class name + type queries (prayer/school reports by class)
CREATE INDEX "idx_absences_class_name_type" ON "absences"("class_name", "type");

-- Step 4: Update table statistics for query optimizer
ANALYZE "absences";
ANALYZE "classes";

-- Step 5: Verification queries (for post-migration validation)
-- These queries should be run after migration to verify success

-- Verify columns were added successfully
-- Expected: Should show class_id and class_name columns
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'absences' AND column_name IN ('class_id', 'class_name');

-- Verify foreign key constraint was created
-- Expected: Should show the foreign key constraint
-- SELECT constraint_name, constraint_type 
-- FROM information_schema.table_constraints 
-- WHERE table_name = 'absences' AND constraint_name = 'absences_class_id_classes_id_fk';

-- Verify indexes were created
-- Expected: Should show all 4 new indexes
-- SELECT indexname, indexdef 
-- FROM pg_indexes 
-- WHERE tablename = 'absences' AND indexname LIKE 'idx_absences_class%';

-- Count records before backfill (should show NULL values)
-- Expected: All records should have NULL class_id and class_name initially
-- SELECT 
--   COUNT(*) as total_records,
--   COUNT(class_id) as records_with_class_id,
--   COUNT(class_name) as records_with_class_name
-- FROM absences;

-- IMPORTANT NOTES:
-- 1. This migration only adds the schema changes
-- 2. Data backfill should be done separately with the backfill script
-- 3. Application code should be updated before running backfill
-- 4. Test thoroughly in staging environment first
-- 5. Consider maintenance window for production deployment
