-- Remove class-specific prayer exemption support
-- This migration removes the 'class' value from prayer_exemption_type enum
-- and updates existing class exemptions to be global exemptions

-- First, update any existing class exemptions to be global exemptions
UPDATE prayer_exemptions 
SET exemption_type = 'global', target_id = NULL 
WHERE exemption_type = 'class';

-- Create a new enum without the 'class' value
CREATE TYPE prayer_exemption_type_new AS ENUM ('global');

-- Update the table to use the new enum
ALTER TABLE prayer_exemptions 
ALTER COLUMN exemption_type TYPE prayer_exemption_type_new 
USING exemption_type::text::prayer_exemption_type_new;

-- Drop the old enum and rename the new one
DROP TYPE prayer_exemption_type;
ALTER TYPE prayer_exemption_type_new RENAME TO prayer_exemption_type;

-- Add comment for documentation
COMMENT ON TYPE prayer_exemption_type IS 'Prayer exemption types - only global exemptions are supported';
COMMENT ON COLUMN prayer_exemptions.exemption_type IS 'Type of exemption - only global exemptions are supported';
COMMENT ON COLUMN prayer_exemptions.target_id IS 'Target ID - always NULL for global exemptions (kept for backward compatibility)';
