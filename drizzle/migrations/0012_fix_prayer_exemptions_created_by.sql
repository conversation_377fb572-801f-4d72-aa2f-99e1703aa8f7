-- Fix prayer_exemptions.created_by to reference users.id instead of users.unique_code
-- This is necessary because admin users don't have unique_code (only students do)

-- Drop the existing foreign key constraint
ALTER TABLE "prayer_exemptions" DROP CONSTRAINT IF EXISTS "prayer_exemptions_created_by_users_unique_code_fk";

-- Change the column type from VARCHAR(36) to INTEGER to match users.id
ALTER TABLE "prayer_exemptions" ALTER COLUMN "created_by" TYPE INTEGER USING "created_by"::INTEGER;

-- Add the new foreign key constraint referencing users.id
ALTER TABLE "prayer_exemptions" ADD CONSTRAINT "prayer_exemptions_created_by_users_id_fk" 
  FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- Add comment for documentation
COMMENT ON COLUMN "prayer_exemptions"."created_by" IS 'User ID (from users.id) who created this exemption - supports both admin and student users';
