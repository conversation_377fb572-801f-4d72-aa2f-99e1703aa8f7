{"id": "bbab149d-95d8-439c-9f00-bfbd709ee529", "prevId": "f5fe360d-1135-4648-8339-812d56f2209d", "version": "6", "dialect": "postgresql", "tables": {"public.absences": {"name": "absences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "unique_code": {"name": "unique_code", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "attendance_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "recorded_at": {"name": "recorded_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "reason": {"name": "reason", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}}, "indexes": {"idx_absences_unique_code_recorded_at": {"name": "idx_absences_unique_code_recorded_at", "columns": ["unique_code", "recorded_at"], "isUnique": false}, "idx_absences_type_recorded_at": {"name": "idx_absences_type_recorded_at", "columns": ["type", "recorded_at"], "isUnique": false}, "idx_absences_unique_code_type_recorded_at": {"name": "idx_absences_unique_code_type_recorded_at", "columns": ["unique_code", "type", "recorded_at"], "isUnique": false}, "idx_absences_recorded_at_type": {"name": "idx_absences_recorded_at_type", "columns": ["recorded_at", "type"], "isUnique": false}}, "foreignKeys": {"absences_unique_code_users_unique_code_fk": {"name": "absences_unique_code_users_unique_code_fk", "tableFrom": "absences", "tableTo": "users", "columnsFrom": ["unique_code"], "columnsTo": ["unique_code"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"classes_name_unique": {"name": "classes_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}}, "public.prayer_exemptions": {"name": "prayer_exemptions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "exemption_type": {"name": "exemption_type", "type": "prayer_exemption_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "target_id": {"name": "target_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "exemption_date": {"name": "exemption_date", "type": "date", "primaryKey": false, "notNull": false}, "recurrence_pattern": {"name": "recurrence_pattern", "type": "recurrence_pattern", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'once'"}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "days_of_week": {"name": "days_of_week", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "prayer_type": {"name": "prayer_type", "type": "prayer_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {"idx_exemption_date_type": {"name": "idx_exemption_date_type", "columns": ["exemption_date", "exemption_type"], "isUnique": false}, "idx_exemption_target": {"name": "idx_exemption_target", "columns": ["target_id"], "isUnique": false}, "idx_recurring_exemptions": {"name": "idx_recurring_exemptions", "columns": ["recurrence_pattern", "start_date", "end_date"], "isUnique": false}, "idx_days_of_week": {"name": "idx_days_of_week", "columns": ["days_of_week"], "isUnique": false}}, "foreignKeys": {"prayer_exemptions_created_by_users_id_fk": {"name": "prayer_exemptions_created_by_users_id_fk", "tableFrom": "prayer_exemptions", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_one_time_exemption": {"name": "unique_one_time_exemption", "nullsNotDistinct": false, "columns": ["exemption_type", "target_id", "exemption_date", "prayer_type"]}}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "unique_code": {"name": "unique_code", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": false}, "google_email": {"name": "google_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "nis": {"name": "nis", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "whatsapp": {"name": "whatsapp", "type": "<PERSON><PERSON><PERSON>(15)", "primaryKey": false, "notNull": false}, "class_id": {"name": "class_id", "type": "serial", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "gender_type", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {"idx_users_role": {"name": "idx_users_role", "columns": ["role"], "isUnique": false}, "idx_users_class_id": {"name": "idx_users_class_id", "columns": ["class_id"], "isUnique": false}, "idx_users_unique_code": {"name": "idx_users_unique_code", "columns": ["unique_code"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_unique_code_unique": {"name": "users_unique_code_unique", "nullsNotDistinct": false, "columns": ["unique_code"]}, "users_google_email_unique": {"name": "users_google_email_unique", "nullsNotDistinct": false, "columns": ["google_email"]}, "users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}}}, "enums": {"public.attendance_type": {"name": "attendance_type", "schema": "public", "values": ["Zuhr", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ijin", "Entry", "Late Entry", "Sick", "Excused Absence", "Temporary Leave", "Return from Leave"]}, "public.gender_type": {"name": "gender_type", "schema": "public", "values": ["male", "female"]}, "public.prayer_exemption_type": {"name": "prayer_exemption_type", "schema": "public", "values": ["global", "class"]}, "public.prayer_type": {"name": "prayer_type", "schema": "public", "values": ["zuhr", "asr", "both"]}, "public.recurrence_pattern": {"name": "recurrence_pattern", "schema": "public", "values": ["once", "weekly", "monthly", "custom"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["student", "admin", "super_admin", "teacher", "receptionist"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}