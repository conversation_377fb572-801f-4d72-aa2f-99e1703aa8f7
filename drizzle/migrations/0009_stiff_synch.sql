DO $$ BEGIN
 CREATE TYPE "public"."recurrence_pattern" AS ENUM('once', 'weekly', 'monthly', 'custom');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "prayer_exemptions" DROP CONSTRAINT "unique_exemption";--> statement-breakpoint
ALTER TABLE "prayer_exemptions" DROP CONSTRAINT "prayer_exemptions_created_by_users_unique_code_fk";
--> statement-breakpoint
ALTER TABLE "prayer_exemptions" ALTER COLUMN "exemption_date" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "prayer_exemptions" ALTER COLUMN "created_by" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "prayer_exemptions" ADD COLUMN "recurrence_pattern" "recurrence_pattern" DEFAULT 'once' NOT NULL;--> statement-breakpoint
ALTER TABLE "prayer_exemptions" ADD COLUMN "start_date" date;--> statement-breakpoint
ALTER TABLE "prayer_exemptions" ADD COLUMN "end_date" date;--> statement-breakpoint
ALTER TABLE "prayer_exemptions" ADD COLUMN "days_of_week" varchar(20);--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "prayer_exemptions" ADD CONSTRAINT "prayer_exemptions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_recurring_exemptions" ON "prayer_exemptions" ("recurrence_pattern","start_date","end_date");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_days_of_week" ON "prayer_exemptions" ("days_of_week");--> statement-breakpoint
ALTER TABLE "prayer_exemptions" ADD CONSTRAINT "unique_one_time_exemption" UNIQUE("exemption_type","target_id","exemption_date","prayer_type");