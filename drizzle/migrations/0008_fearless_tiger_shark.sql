DO $$ BEGIN
 CREATE TYPE "public"."prayer_exemption_type" AS ENUM('global', 'class');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."prayer_type" AS ENUM('zuhr', 'asr', 'both');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "prayer_exemptions" (
	"id" serial PRIMARY KEY NOT NULL,
	"exemption_type" "prayer_exemption_type" NOT NULL,
	"target_id" varchar(50),
	"exemption_date" date NOT NULL,
	"prayer_type" "prayer_type" NOT NULL,
	"reason" varchar(500) NOT NULL,
	"created_by" varchar(36) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	CONSTRAINT "unique_exemption" UNIQUE("exemption_type","target_id","exemption_date","prayer_type")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "prayer_exemptions" ADD CONSTRAINT "prayer_exemptions_created_by_users_unique_code_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("unique_code") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_exemption_date_type" ON "prayer_exemptions" ("exemption_date","exemption_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_exemption_target" ON "prayer_exemptions" ("target_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_absences_type_recorded_at" ON "absences" ("type","recorded_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_absences_unique_code_type_recorded_at" ON "absences" ("unique_code","type","recorded_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_absences_recorded_at_type" ON "absences" ("recorded_at","type");