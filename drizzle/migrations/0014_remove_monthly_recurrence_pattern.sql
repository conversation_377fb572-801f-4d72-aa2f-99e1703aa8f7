-- Remove 'monthly' from recurrence_pattern enum
-- This migration removes the 'monthly' value from recurrence_pattern enum
-- and updates any existing monthly exemptions to be one-time exemptions

-- First, update any existing monthly exemptions to be one-time exemptions
-- Convert monthly to one-time with the start_date as exemption_date
UPDATE prayer_exemptions 
SET 
  recurrence_pattern = 'once',
  exemption_date = start_date,
  start_date = NULL,
  end_date = NULL,
  days_of_week = NULL
WHERE recurrence_pattern = 'monthly';

-- Create a new enum without the 'monthly' value
CREATE TYPE recurrence_pattern_new AS ENUM ('once', 'weekly', 'custom');

-- Update the table to use the new enum
ALTER TABLE prayer_exemptions 
ALTER COLUMN recurrence_pattern TYPE recurrence_pattern_new 
USING recurrence_pattern::text::recurrence_pattern_new;

-- Drop the old enum
DROP TYPE recurrence_pattern;

-- Rename the new enum to the original name
ALTER TYPE recurrence_pattern_new RENAME TO recurrence_pattern;
