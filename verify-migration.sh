#!/bin/bash

# 🔍 Migration Verification Script
# This script verifies that the reason column migration was successful

set -e

echo "🔍 Verifying Migration - Reason Column"
echo "====================================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL environment variable is not set!"
    exit 1
fi

print_status "Checking database connection..."
if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
    print_success "Database connection successful"
else
    print_error "Failed to connect to database!"
    exit 1
fi

print_status "Checking if reason column exists..."
REASON_EXISTS=$(psql "$DATABASE_URL" -t -c "
SELECT COUNT(*) FROM information_schema.columns 
WHERE table_name = 'absences' AND column_name = 'reason';
" | tr -d ' ')

if [ "$REASON_EXISTS" -gt 0 ]; then
    print_success "Reason column exists"
else
    print_error "Reason column not found!"
    exit 1
fi

print_status "Verifying column specifications..."
COLUMN_INFO=$(psql "$DATABASE_URL" -t -c "
SELECT data_type, character_maximum_length, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'absences' AND column_name = 'reason';
")

echo "Column details: $COLUMN_INFO"

if echo "$COLUMN_INFO" | grep -q "character varying.*500.*YES"; then
    print_success "Column specifications correct (varchar(500), nullable)"
else
    print_error "Column specifications incorrect!"
    exit 1
fi

print_status "Testing reason column functionality..."
TEST_CODE="verify-test-$(date +%s)"

# Test insert with reason
psql "$DATABASE_URL" -c "
INSERT INTO absences (unique_code, type, recorded_at, reason) 
VALUES ('$TEST_CODE', 'Sick', NOW(), 'Verification test');
" > /dev/null

# Test insert without reason
psql "$DATABASE_URL" -c "
INSERT INTO absences (unique_code, type, recorded_at) 
VALUES ('${TEST_CODE}-no-reason', 'Entry', NOW());
" > /dev/null

# Verify both records
WITH_REASON=$(psql "$DATABASE_URL" -t -c "
SELECT reason FROM absences WHERE unique_code = '$TEST_CODE';
" | tr -d ' ')

WITHOUT_REASON=$(psql "$DATABASE_URL" -t -c "
SELECT reason FROM absences WHERE unique_code = '${TEST_CODE}-no-reason';
" | tr -d ' ')

if [ "$WITH_REASON" = "Verification test" ]; then
    print_success "Insert with reason works correctly"
else
    print_error "Insert with reason failed!"
    exit 1
fi

if [ -z "$WITHOUT_REASON" ] || [ "$WITHOUT_REASON" = "" ]; then
    print_success "Insert without reason works correctly (NULL value)"
else
    print_error "Insert without reason failed!"
    exit 1
fi

# Clean up test data
psql "$DATABASE_URL" -c "
DELETE FROM absences WHERE unique_code IN ('$TEST_CODE', '${TEST_CODE}-no-reason');
" > /dev/null

print_success "Test data cleaned up"

print_status "Checking migration history..."
MIGRATION_COUNT=$(psql "$DATABASE_URL" -t -c "
SELECT COUNT(*) FROM __drizzle_migrations WHERE hash LIKE '%reason%' OR created_at::date = CURRENT_DATE;
" 2>/dev/null | tr -d ' ' || echo "0")

if [ "$MIGRATION_COUNT" -gt 0 ]; then
    print_success "Migration recorded in history"
else
    print_status "Migration history check skipped (table may not exist)"
fi

echo ""
echo "🎉 Verification Summary"
echo "======================"
print_success "✅ Database connection working"
print_success "✅ Reason column exists with correct specifications"
print_success "✅ Insert with reason works"
print_success "✅ Insert without reason works (NULL handling)"
print_success "✅ Test data cleanup successful"

echo ""
print_success "🔍 Migration verification completed successfully!"
echo "The reason column is ready for production use."
