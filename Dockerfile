# Base image for building and running the app
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package.json dan package-lock.json
COPY package.json package-lock.json ./

# Install dependencies using npm
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Disable Next.js telemetry
ENV NEXT_TELEMETRY_DISABLED=1

# Create minimal .env file for build process only
# Next.js build doesn't need actual secrets, just placeholder values
RUN if [ ! -f .env ]; then \
    echo "NODE_ENV=production" > .env && \
    echo "NEXT_TELEMETRY_DISABLED=1" >> .env && \
    echo "DATABASE_URL=postgresql://placeholder" >> .env && \
    echo "REDIS_URL=redis://placeholder" >> .env && \
    echo "JWT_SECRET=build-time-placeholder-secret" >> .env; \
    fi

# Build the Next.js app (uses placeholder values, real secrets provided at runtime)
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
# Disable telemetry in the production container as well
ENV NEXT_TELEMETRY_DISABLED=1

# Create a system user and group to run the app
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the standalone output and static files from the builder
COPY --from=builder /app/.next/standalone /app
COPY --from=builder /app/.next/static /app/.next/static
COPY --from=builder /app/public /app/public

# Create a startup script that validates required environment variables
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'echo "🚀 Starting ShalatYuk application..."' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Validate required environment variables' >> /app/start.sh && \
    echo 'if [ -z "$DATABASE_URL" ]; then' >> /app/start.sh && \
    echo '  echo "❌ ERROR: DATABASE_URL encovironment variable is required"' >> /app/start.sh && \
    echo '  exit 1' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo 'if [ -z "$REDIS_URL" ]; then' >> /app/start.sh && \
    echo '  echo "❌ ERROR: REDIS_URL environment variable is required"' >> /app/start.sh && \
    echo '  exit 1' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo 'if [ -z "$JWT_SECRET" ]; then' >> /app/start.sh && \
    echo '  echo "⚠️  WARNING: JWT_SECRET not provided, generating random secret..."' >> /app/start.sh && \
    echo '  echo "🔑 For production, please provide a secure JWT_SECRET"' >> /app/start.sh && \
    echo '  export JWT_SECRET=$(openssl rand -hex 32)' >> /app/start.sh && \
    echo 'else' >> /app/start.sh && \
    echo '  echo "✅ JWT_SECRET provided"' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo 'echo "✅ All environment variables validated"' >> /app/start.sh && \
    echo 'echo "🌐 Starting server on port $PORT"' >> /app/start.sh && \
    echo 'exec node server.js' >> /app/start.sh && \
    chmod +x /app/start.sh

# Set proper permissions for the nextjs user
RUN chown -R nextjs:nodejs /app

# Use the non-root user to run the app
USER nextjs

EXPOSE 3000

# Set the environment variable for the app's port
ENV PORT=3000

# Start the app using the secure startup script
CMD ["/app/start.sh"]