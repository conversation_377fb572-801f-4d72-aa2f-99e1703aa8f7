# 🚀 Quick Start - PostgreSQL Development ShalatYuk

## ✅ Status: SETUP BERHASIL!

Berdasarkan output Anda, setup PostgreSQL development sudah berhasil! 🎉

## 🌐 Akses Sekarang

Aplikasi Anda sudah berjalan dan dapat diakses di:

- **🌐 Aplikasi ShalatYuk**: http://localhost:3000
- **🗄️ Database Management (Adminer)**: http://localhost:8080

## ��️ Database Access

### Untuk Adminer (http://localhost:8080):

```
System: PostgreSQL
Server: postgres-dev (PENTING: bukan localhost!)
Username: shalatdev
Password: shalatdev123
Database: shalat_yuk_dev
```

### Untuk External Tools (TablePlus, DBeaver, etc.):

```
Host: localhost
Port: 5433
Database: shalat_yuk_dev
Username: shalatdev
Password: shalatdev123
```

## ⚡ Commands Cepat

### Harian Development

```bash
# Start development (jika belum jalan)
npm run dev

# Akses database via command line
docker exec -it shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev

# Stop containers (opsional, untuk save resource)
docker compose -f docker-compose.dev.yml down
```

### Jika Ada Masalah

```bash
# Restart containers
docker compose -f docker-compose.dev.yml restart postgres-dev

# Restart Adminer jika connection refused
docker compose -f docker-compose.dev.yml restart adminer

# Lihat logs jika ada error
docker compose -f docker-compose.dev.yml logs -f

# Reset complete jika perlu
docker compose -f docker-compose.dev.yml down -v
./scripts/dev-setup.sh
```

## 🚨 Masalah Umum Adminer

Jika Adminer menampilkan error "connection refused":

**❌ SALAH:**

- Server: localhost

**✅ BENAR:**

- Server: postgres-dev

Adminer berjalan di dalam container Docker, jadi tidak bisa menggunakan `localhost`.

## 🎯 Next Steps

1. **Buka aplikasi**: http://localhost:3000
2. **Explore database**: http://localhost:8080 (gunakan server: `postgres-dev`)
3. **Mulai coding**: Edit files di `/app` atau `/components`
4. **Baca dokumentasi lengkap**: `CARA-PENGGUNAAN-DEV.md`
5. **Panduan Adminer**: `ADMINER-SETUP-GUIDE.md`

---

**🎉 Selamat! Environment development PostgreSQL Anda sudah siap digunakan!**

Untuk panduan lengkap, baca: `CARA-PENGGUNAAN-DEV.md`
