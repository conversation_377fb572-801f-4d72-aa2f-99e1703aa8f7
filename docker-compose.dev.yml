version: '3.8'

services:
  postgres-dev:
    image: postgres:15-alpine
    restart: unless-stopped
    container_name: shalatYuk-postgres-dev
    environment:
      POSTGRES_USER: shalatdev
      POSTGRES_PASSWORD: shalatdev123
      POSTGRES_DB: shalat_yuk_dev
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-dev-db.sql:/docker-entrypoint-initdb.d/01-init.sql
    ports:
      - '5433:5432' # Using port 5433 to avoid conflicts with local PostgreSQL
    networks:
      - shalatYuk-dev-network

  redis-dev:
    image: redis:7.2-alpine
    restart: unless-stopped
    container_name: shalatYuk-redis-dev
    command: redis-server --requirepass redisdev123
    ports:
      - '6380:6379' # Using port 6380 to avoid conflicts with local Redis
    networks:
      - shalatYuk-dev-network

  adminer:
    image: adminer:latest
    restart: unless-stopped
    container_name: shalatYuk-adminer
    ports:
      - '8080:8080'
    networks:
      - shalatYuk-dev-network
    depends_on:
      - postgres-dev

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  shalatYuk-dev-network:
    driver: bridge
