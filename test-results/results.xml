<testsuites id="" name="" tests="1" failures="1" skipped="0" errors="0" time="8.215995999999999">
<testsuite name="07-admin-login.spec.ts" timestamp="2025-06-25T00:24:20.959Z" hostname="chromium" tests="1" failures="1" skipped="0" time="5.883" errors="0">
<testcase name="Admin Login Page › Super Admin Authentication › should login successfully as Super Admin" classname="07-admin-login.spec.ts" time="5.883">
<failure message="07-admin-login.spec.ts:42:9 should login successfully as Super Admin" type="FAILURE">
<![CDATA[  [chromium] › 07-admin-login.spec.ts:42:9 › Admin Login Page › Super Admin Authentication › should login successfully as Super Admin 

    Error: Admin login failed - form appears to not have submitted. Current URL: http://localhost:3000/admin

       at page-objects/admin/admin-login-page.ts:127

      125 |       // If we're still on login page and no error message, something might be preventing submission
      126 |       if (usernameValue && passwordValue) {
    > 127 |         throw new Error(
          |               ^
      128 |           `Admin login failed - form appears to not have submitted. Current URL: ${currentUrl}`
      129 |         )
      130 |       }
        at AdminLoginPage.login (/Users/<USER>/development/ShalatYuk/e2e-tests/page-objects/admin/admin-login-page.ts:127:15)
        at /Users/<USER>/development/ShalatYuk/e2e-tests/07-admin-login.spec.ts:44:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[Attempting admin login with username: superadmin, role: super_admin
Clicking login button...
Admin login - Current URL after submission: http://localhost:3000/admin
Admin login - Error message visible: false
Admin login - Loading spinner visible: false
Username field value: superadmin
Password field value: SuperAdmin123!

[[ATTACHMENT|07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/test-failed-1.png]]

[[ATTACHMENT|07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/video.webm]]

[[ATTACHMENT|07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>