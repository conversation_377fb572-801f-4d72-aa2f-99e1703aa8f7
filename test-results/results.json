{"config": {"configFile": "/Users/<USER>/development/ShalatYuk/playwright.config.ts", "rootDir": "/Users/<USER>/development/ShalatYuk/e2e-tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/development/ShalatYuk/e2e-tests/fixtures/global-setup.ts", "globalTeardown": "/Users/<USER>/development/ShalatYuk/e2e-tests/fixtures/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/development/ShalatYuk/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/development/ShalatYuk/e2e-tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/development/ShalatYuk/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/development/ShalatYuk/e2e-tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/development/ShalatYuk/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/development/ShalatYuk/e2e-tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/development/ShalatYuk/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/development/ShalatYuk/e2e-tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/development/ShalatYuk/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/development/ShalatYuk/e2e-tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 8, "webServer": {"command": "npm run dev", "port": 3000, "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "07-admin-login.spec.ts", "file": "07-admin-login.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "<PERSON><PERSON>", "file": "07-admin-login.spec.ts", "line": 5, "column": 6, "specs": [], "suites": [{"title": "Super Admin Authentication", "file": "07-admin-login.spec.ts", "line": 41, "column": 8, "specs": [{"title": "should login successfully as Super Admin", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 5883, "error": {"message": "Error: Admin login failed - form appears to not have submitted. Current URL: http://localhost:3000/admin", "stack": "Error: Admin login failed - form appears to not have submitted. Current URL: http://localhost:3000/admin\n    at AdminLoginPage.login (/Users/<USER>/development/ShalatYuk/e2e-tests/page-objects/admin/admin-login-page.ts:127:15)\n    at /Users/<USER>/development/ShalatYuk/e2e-tests/07-admin-login.spec.ts:44:7", "location": {"file": "/Users/<USER>/development/ShalatYuk/e2e-tests/page-objects/admin/admin-login-page.ts", "column": 15, "line": 127}, "snippet": "\u001b[90m   at \u001b[39mpage-objects/admin/admin-login-page.ts:127\n\n\u001b[0m \u001b[90m 125 |\u001b[39m       \u001b[90m// If we're still on login page and no error message, something might be preventing submission\u001b[39m\n \u001b[90m 126 |\u001b[39m       \u001b[36mif\u001b[39m (usernameValue \u001b[33m&&\u001b[39m passwordValue) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 127 |\u001b[39m         \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\n \u001b[90m     |\u001b[39m               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 128 |\u001b[39m           \u001b[32m`Admin login failed - form appears to not have submitted. Current URL: ${currentUrl}`\u001b[39m\n \u001b[90m 129 |\u001b[39m         )\n \u001b[90m 130 |\u001b[39m       }\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/development/ShalatYuk/e2e-tests/page-objects/admin/admin-login-page.ts", "column": 15, "line": 127}, "message": "Error: Admin login failed - form appears to not have submitted. Current URL: http://localhost:3000/admin\n\n\u001b[90m   at \u001b[39mpage-objects/admin/admin-login-page.ts:127\n\n\u001b[0m \u001b[90m 125 |\u001b[39m       \u001b[90m// If we're still on login page and no error message, something might be preventing submission\u001b[39m\n \u001b[90m 126 |\u001b[39m       \u001b[36mif\u001b[39m (usernameValue \u001b[33m&&\u001b[39m passwordValue) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 127 |\u001b[39m         \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\n \u001b[90m     |\u001b[39m               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 128 |\u001b[39m           \u001b[32m`Admin login failed - form appears to not have submitted. Current URL: ${currentUrl}`\u001b[39m\n \u001b[90m 129 |\u001b[39m         )\n \u001b[90m 130 |\u001b[39m       }\u001b[0m\n\u001b[2m    at AdminLoginPage.login (/Users/<USER>/development/ShalatYuk/e2e-tests/page-objects/admin/admin-login-page.ts:127:15)\u001b[22m\n\u001b[2m    at /Users/<USER>/development/ShalatYuk/e2e-tests/07-admin-login.spec.ts:44:7\u001b[22m"}], "stdout": [{"text": "Attempting admin login with username: superadmin, role: super_admin\n"}, {"text": "Clicking login button...\n"}, {"text": "Admin login - Current URL after submission: http://localhost:3000/admin\n"}, {"text": "Admin login - Error message visible: false\n"}, {"text": "Admin login - Loading spinner visible: false\n"}, {"text": "Username field value: superadmin\n"}, {"text": "Password field value: SuperAdmin123!\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T00:24:21.741Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/development/ShalatYuk/test-results/07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/development/ShalatYuk/test-results/07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/development/ShalatYuk/test-results/07-admin-login-Admin-Login-27eb7-successfully-as-Super-Admin-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/development/ShalatYuk/e2e-tests/page-objects/admin/admin-login-page.ts", "column": 15, "line": 127}}], "status": "unexpected"}], "id": "beecca175f61c7c62568-14cb3237efbf97164dbe", "file": "07-admin-login.spec.ts", "line": 42, "column": 9}]}]}]}], "errors": [], "stats": {"startTime": "2025-06-25T00:24:19.729Z", "duration": 8215.996, "expected": 0, "skipped": 0, "unexpected": 1, "flaky": 0}}