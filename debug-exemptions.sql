-- Debug script to check prayer exemptions
SELECT 
    id,
    exemption_type,
    exemption_date,
    recurrence_pattern,
    start_date,
    end_date,
    days_of_week,
    prayer_type,
    reason,
    is_active,
    created_at
FROM prayer_exemptions 
ORDER BY created_at DESC;

-- Check what today's date looks like in different formats
SELECT 
    NOW() as current_timestamp,
    CURRENT_DATE as current_date,
    DATE('2025-07-21') as target_date,
    EXTRACT(DOW FROM DATE('2025-07-21')) as day_of_week_number,
    TO_CHAR(DATE('2025-07-21'), 'Day') as day_name;

-- Check if there are any exemptions for Monday (day 1)
SELECT *
FROM prayer_exemptions 
WHERE 
    is_active = true 
    AND (
        (recurrence_pattern = 'once' AND exemption_date = '2025-07-21')
        OR 
        (recurrence_pattern = 'weekly' AND days_of_week LIKE '%monday%')
    );
