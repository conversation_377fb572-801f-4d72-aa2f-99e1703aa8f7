'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'

interface AdminSession {
  id: number
  username: string
  name: string
  role?: 'admin' | 'super_admin' | 'teacher' | 'receptionist'
}

export function useAdminSession() {
  const [admin, setAdmin] = useState<AdminSession | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Performance optimization: Prevent concurrent session checks
  const sessionCheckRef = useRef<Promise<void> | null>(null)
  const lastCheckTimeRef = useRef<number>(0)

  // Check session with throttling to prevent excessive API calls
  useEffect(() => {
    const checkSession = async (): Promise<void> => {
      // Prevent multiple concurrent session checks
      if (sessionCheckRef.current) {
        return sessionCheckRef.current
      }

      // Rate limiting - minimum 10 seconds between checks
      const now = Date.now()
      if (now - lastCheckTimeRef.current < 10000) {
        return
      }

      // Create and store the promise to prevent concurrent calls
      sessionCheckRef.current = (async () => {
        try {
          lastCheckTimeRef.current = now

          // Validate admin session
          const adminResponse = await fetch('/api/auth/admin/session', {
            method: 'GET',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
            },
            credentials: 'include',
          })

          if (adminResponse.ok) {
            const adminData = await adminResponse.json()
            if (adminData.admin) {
              setAdmin(adminData.admin)
            } else {
              setAdmin(null)
            }
          } else {
            // Session invalid or expired
            setAdmin(null)
          }
        } catch (error) {
          console.error('Error checking admin session:', error)
          setAdmin(null)
        } finally {
          setLoading(false)
          // Clear the promise reference
          sessionCheckRef.current = null
        }
      })()

      return sessionCheckRef.current
    }

    checkSession()
  }, [])

  // Fungsi untuk logout
  const logout = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/auth/logout?role=admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        setAdmin(null)
        router.push('/admin')
      }
    } catch (error) {
      console.error('Error logging out:', error)
    } finally {
      setLoading(false)
    }
  }

  return {
    admin,
    loading,
    isAuthenticated: !!admin,
    logout,
  }
}
