/**
 * 🎯 SIMPLIFIED SESSION MONITORING HOOK
 *
 * Root Cause Fix: Drastically reduced monitoring frequency
 * - Much longer intervals (60+ minutes)
 * - Simplified logic, minimal overhead
 * - Focus on real session issues only
 * - No complex performance tracking
 */

import { useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { SessionPerformance } from '@/lib/utils/session-performance-monitor'
import { toast } from 'sonner'

// Global session monitor state to prevent multiple instances
class SessionMonitorManager {
  private static instance: SessionMonitorManager
  private activeMonitors = new Map<
    string,
    {
      intervalRef: NodeJS.Timeout | null
      isChecking: boolean
      lastCheck: number
      callbacks: Set<() => void>
    }
  >()

  static getInstance(): SessionMonitorManager {
    if (!SessionMonitorManager.instance) {
      SessionMonitorManager.instance = new SessionMonitorManager()
    }
    return SessionMonitorManager.instance
  }

  addMonitor(role: string, intervalMs: number, callback: () => void): () => void {
    const key = `${role}-${intervalMs}`

    if (!this.activeMonitors.has(key)) {
      this.activeMonitors.set(key, {
        intervalRef: null,
        isChecking: false,
        lastCheck: 0,
        callbacks: new Set(),
      })
    }

    const monitor = this.activeMonitors.get(key)!
    monitor.callbacks.add(callback)

    // Start monitoring if not already started
    if (!monitor.intervalRef) {
      this.startMonitoring(key, role, intervalMs)
    }

    // Return cleanup function
    return () => {
      monitor.callbacks.delete(callback)
      if (monitor.callbacks.size === 0) {
        this.stopMonitoring(key)
      }
    }
  }

  private startMonitoring(key: string, role: string, intervalMs: number) {
    const monitor = this.activeMonitors.get(key)!

    const checkSession = async () => {
      if (monitor.isChecking) {
        console.log(`Session monitor ${key}: Check already in progress, skipping...`)
        return
      }

      const now = Date.now()
      if (now - monitor.lastCheck < 5000) {
        console.log(`Session monitor ${key}: Rate limited, skipping...`)
        return
      }

      try {
        monitor.isChecking = true
        monitor.lastCheck = now
        console.log(`Session monitor ${key}: Starting check for role: ${role}`)

        // ✅ FIXED: Remove non-existent session-events endpoint
        // Only do ONE API call per session check for better performance

        // ✅ PROPER ARCHITECTURE: Use correct session endpoint with performance monitoring
        const endpoint = role === 'admin' ? '/api/auth/admin/session' : '/api/auth/student/session'

        // ✅ BEST PRACTICE: Wrap session monitoring call with proper performance tracking
        const response = await SessionPerformance.monitorSessionCheck(async () => {
          return await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
            },
            credentials: 'include',
          })
        })

        if (response.status === 401) {
          console.log(`Session monitor ${key}: Session invalid`)
          this.notifyCallbacks(key, 'session_expired')
          return
        }

        if (response.ok) {
          // ✅ FIXED: Check actual response data
          const data = await response.json()
          if (data.success === true) {
            console.log(`Session monitor ${key}: Session valid`)
          } else {
            console.log(`Session monitor ${key}: Session invalid (success=false)`)
            this.notifyCallbacks(key, 'session_expired')
          }
        } else {
          console.warn(`Session monitor ${key}: Unexpected status:`, response.status)
          this.notifyCallbacks(key, 'session_expired')
        }
      } catch (error) {
        console.error(`Session monitor ${key}: Check failed:`, error)
      } finally {
        monitor.isChecking = false
      }
    }

    monitor.intervalRef = setInterval(checkSession, intervalMs)
    console.log(`Session monitor ${key}: Started with interval ${intervalMs}ms`)
  }

  private stopMonitoring(key: string) {
    const monitor = this.activeMonitors.get(key)
    if (monitor?.intervalRef) {
      clearInterval(monitor.intervalRef)
      monitor.intervalRef = null
      console.log(`Session monitor ${key}: Stopped`)
    }
    this.activeMonitors.delete(key)
  }

  private notifyCallbacks(key: string, reason: 'force_logout' | 'session_expired') {
    const monitor = this.activeMonitors.get(key)
    if (monitor) {
      console.log(`Session monitor ${key}: Notifying callbacks for reason: ${reason}`)
      monitor.callbacks.forEach(callback => {
        try {
          callback()
        } catch (error) {
          console.error(`Session monitor ${key}: Callback error:`, error)
        }
      })
    }
  }
}

interface SessionMonitorOptions {
  role: 'admin' | 'student'
  intervalMs?: number // ✅ ROOT CAUSE FIX: Much longer default interval
  onSessionInvalid?: () => void
  enabled?: boolean
}

export function useSessionMonitor({
  role,
  intervalMs = 3600000, // ✅ ROOT CAUSE FIX: Check every 60 minutes (extremely conservative)
  onSessionInvalid,
  enabled = true,
}: SessionMonitorOptions) {
  const router = useRouter()
  const cleanupRef = useRef<(() => void) | null>(null)

  // PERFORMANCE: Use singleton manager to prevent multiple monitors
  const handleSessionInvalidation = useCallback(
    async (reason: 'force_logout' | 'session_expired') => {
      console.log(`Session invalidation triggered: ${reason}`)

      // Show appropriate notification
      const message =
        reason === 'force_logout'
          ? 'Your session has been terminated by an administrator. Please login again.'
          : 'Your session has expired or been terminated. Please login again.'

      toast.error(message)

      // Call custom handler if provided
      if (onSessionInvalid) {
        onSessionInvalid()
      }

      // Redirect to login with appropriate error flag
      const loginPath = role === 'admin' ? '/admin' : '/student'
      router.push(`${loginPath}?error=${reason}`)
    },
    [role, router, onSessionInvalid]
  )

  useEffect(() => {
    if (!enabled) return

    // ✅ FIXED: Use singleton manager for proper session monitoring
    console.log(`🔄 OPTIMIZED SESSION MONITOR for role: ${role} - interval: ${intervalMs}ms`)

    const manager = SessionMonitorManager.getInstance()
    const cleanup = manager.addMonitor(role, intervalMs, () => {
      handleSessionInvalidation('session_expired')
    })

    cleanupRef.current = cleanup

    // Cleanup on unmount
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current()
        cleanupRef.current = null
        console.log(`✅ SESSION MONITOR CLEANUP for role: ${role}`)
      }
    }
  }, [role, intervalMs, enabled, handleSessionInvalidation])

  // PERFORMANCE: Simplified focus/visibility handling - no immediate checks
  useEffect(() => {
    if (!enabled) return

    const handleFocus = () => {
      console.log(`Session monitor: Tab focused for role ${role}`)
      // No immediate check - let the singleton manager handle timing
    }

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log(`Session monitor: Tab visible for role ${role}`)
        // No immediate check - let the singleton manager handle timing
      }
    }

    window.addEventListener('focus', handleFocus)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('focus', handleFocus)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [role, enabled])

  // Manual session check function (for backward compatibility)
  const manualCheck = useCallback(() => {
    console.log(`Manual session check requested for role: ${role}`)
    // In the new architecture, manual checks are handled by the singleton
    return Promise.resolve()
  }, [role])

  return {
    checkSession: manualCheck,
  }
}

/**
 * Hook for admin session monitoring
 */
export function useAdminSessionMonitor(options?: Omit<SessionMonitorOptions, 'role'>) {
  return useSessionMonitor({
    ...options,
    role: 'admin',
  })
}

/**
 * Hook for student session monitoring
 */
export function useStudentSessionMonitor(options?: Omit<SessionMonitorOptions, 'role'>) {
  return useSessionMonitor({
    ...options,
    role: 'student',
  })
}
