'use client'

import { useState, useCallback } from 'react'

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any, formData?: any) => string | null
}

export interface ValidationRules {
  [key: string]: ValidationRule
}

export interface ValidationErrors {
  [key: string]: string | null
}

export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  rules: ValidationRules
) {
  const [values, setValues] = useState<T>(initialValues)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const validateField = useCallback(
    (name: string, value: any): string | null => {
      const rule = rules[name]
      if (!rule) return null

      if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
        return 'Field ini wajib diisi'
      }

      if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
        return `Minimal ${rule.minLength} karakter`
      }

      if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
        return `Maksimal ${rule.maxLength} karakter`
      }

      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        return 'Format tidak valid'
      }

      if (rule.custom) {
        return rule.custom(value, values)
      }

      return null
    },
    [rules]
  )

  const validateAll = useCallback((): boolean => {
    const newErrors: ValidationErrors = {}
    let isValid = true

    Object.keys(rules).forEach(name => {
      const error = validateField(name, values[name])
      newErrors[name] = error
      if (error) isValid = false
    })

    setErrors(newErrors)
    return isValid
  }, [values, rules, validateField])

  const setValue = useCallback(
    (name: string, value: any) => {
      setValues(prev => ({ ...prev, [name]: value }))

      // Validate field if it has been touched
      if (touched[name]) {
        const error = validateField(name, value)
        setErrors(prev => ({ ...prev, [name]: error }))
      }
    },
    [touched, validateField]
  )

  const setFieldTouched = useCallback(
    (name: string) => {
      setTouched(prev => ({ ...prev, [name]: true }))

      // Validate field when touched
      const error = validateField(name, values[name])
      setErrors(prev => ({ ...prev, [name]: error }))
    },
    [values, validateField]
  )

  const reset = useCallback(() => {
    setValues(initialValues)
    setErrors({})
    setTouched({})
  }, [initialValues])

  const setFormValues = useCallback((newValues: Partial<T>) => {
    setValues(prev => ({ ...prev, ...newValues }))
  }, [])

  return {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validateAll,
    reset,
    setFormValues,
    isValid: Object.values(errors).every(error => !error),
  }
}
