import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuth } from './use-hybrid-auth'

interface AdminPermission {
  isSuperAdmin: boolean
  role: 'admin' | 'super_admin' | 'teacher' | 'receptionist'
}

export function useAdminPermission(
  requiredRole: 'admin' | 'super_admin' | 'teacher' | 'receptionist' = 'admin'
) {
  const router = useRouter()
  const { user: admin, isLoading: sessionLoading } = useAdminAuth()
  const [permission, setPermission] = useState<AdminPermission | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // If session is still loading, wait for it
    if (sessionLoading) return

    // If no admin session, don't check permissions
    if (!admin) {
      setLoading(false)
      return
    }

    const checkPermission = async () => {
      try {
        // First check if we already know the role from the session
        if (admin.role) {
          const isSuperAdmin = admin.role === 'super_admin'
          setPermission({ isSuperAdmin, role: admin.role as any })

          // If the required role is super_admin and the user is not a super_admin, redirect
          if (requiredRole === 'super_admin' && !isSuperAdmin) {
            console.warn('Access denied: Super admin role required')
            router.push('/admin/home')
          }

          setLoading(false)
          return
        }

        // If we don't know the role, fetch it from the API
        const response = await fetch('/api/admin/check-permission', {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
        })

        if (!response.ok) {
          throw new Error('Failed to check admin permission')
        }

        const data = await response.json()
        setPermission(data)

        // If the required role is super_admin and the user is not a super_admin, redirect
        if (requiredRole === 'super_admin' && !data.isSuperAdmin) {
          console.warn('Access denied: Super admin role required')
          router.push('/admin/home')
        }
      } catch (error) {
        console.error('Error checking admin permission:', error)
        setError(error instanceof Error ? error.message : 'Unknown error')

        // If we can't check permissions, assume the user doesn't have them
        if (requiredRole === 'super_admin') {
          router.push('/admin/home')
        }
      } finally {
        setLoading(false)
      }
    }

    checkPermission()
  }, [admin, sessionLoading, requiredRole, router])

  return { permission, loading, error }
}
