/**
 * 🎯 SIMPLIFIED HYBRID AUTH HOOKS
 *
 * Root Cause Fix:
 * - Single auth hook (no more multiple session systems)
 * - No client-side session monitoring
 * - Proper 401 handling
 * - Simple token refresh
 * - Clean logout
 *
 * Performance Benefits:
 * - No periodic session checks
 * - No complex monitoring
 * - Simple state management
 * - Efficient API calls
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { useRouter } from 'next/navigation'

interface User {
  id: number
  username?: string
  role: string
  name?: string
}

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  sessionId: string | null
}

interface LoginCredentials {
  username: string
  password: string
  userType: 'admin' | 'student'
}

// ✅ MAIN HYBRID AUTH HOOK
export function useHybridAuth() {
  const router = useRouter()
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    sessionId: null,
  })

  // ✅ LOGIN: Simple hybrid login
  const login = useCallback(
    async (credentials: LoginCredentials) => {
      try {
        setAuthState(prev => ({ ...prev, isLoading: true }))

        const response = await fetch('/api/auth/hybrid/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(credentials),
          credentials: 'include',
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Login failed')
        }

        const data = await response.json()

        setAuthState({
          user: data.user,
          isLoading: false,
          isAuthenticated: true,
          sessionId: data.sessionId,
        })

        // Redirect based on user type
        const redirectPath = credentials.userType === 'admin' ? '/admin/home' : '/student/home'
        router.push(redirectPath)

        return { success: true }
      } catch (error) {
        setAuthState(prev => ({ ...prev, isLoading: false }))
        console.error('Login error:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Login failed',
        }
      }
    },
    [router]
  )

  // ✅ LOGOUT: Simple session invalidation with proper redirect
  const logout = useCallback(async () => {
    // Store user type before clearing state
    const userType = authState.user?.role === 'student' ? 'student' : 'admin'

    try {
      await fetch('/api/auth/hybrid/logout', {
        method: 'POST',
        credentials: 'include',
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Always clear local state and redirect to appropriate login page
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        sessionId: null,
      })

      // Redirect to appropriate page based on user type
      const redirectPath = userType === 'student' ? '/student' : '/admin'
      router.push(redirectPath)
    }
  }, [router, authState.user?.role])

  // ✅ CHECK SESSION: Simple session validation
  const checkSession = useCallback(async () => {
    try {
      const response = await fetch('/api/auth/hybrid/session', {
        credentials: 'include',
      })

      if (!response.ok) {
        throw new Error('Session invalid')
      }

      const data = await response.json()

      if (data.isValid) {
        setAuthState({
          user: data.user,
          isLoading: false,
          isAuthenticated: true,
          sessionId: data.sessionId,
        })
        return true
      } else {
        throw new Error('Session invalid')
      }
    } catch (error) {
      console.error('Session check error:', error)
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        sessionId: null,
      })
      return false
    }
  }, [])

  // ✅ INITIAL SESSION CHECK: Only on mount
  useEffect(() => {
    checkSession()
  }, [checkSession])

  return {
    ...authState,
    login,
    logout,
    checkSession,
  }
}

// ✅ ADMIN AUTH HOOK: Enhanced admin authentication with profile data
export function useAdminAuth() {
  const auth = useHybridAuth()
  const [profileData, setProfileData] = useState<any>(null)
  const [profileLoading, setProfileLoading] = useState(false)
  const fetchingRef = useRef(false)

  // Memoize isAdmin to prevent unnecessary re-renders
  const isAdmin = useMemo(() => {
    return (
      auth.user?.role &&
      ['admin', 'super_admin', 'teacher', 'receptionist'].includes(auth.user.role)
    )
  }, [auth.user?.role])

  // Fetch admin profile data when authenticated
  useEffect(() => {
    if (auth.isAuthenticated && isAdmin && !profileData && !fetchingRef.current) {
      fetchingRef.current = true
      setProfileLoading(true)

      fetch('/api/admin/profile', {
        credentials: 'include',
      })
        .then(res => res.json())
        .then(data => {
          if (data.id) {
            setProfileData(data)
          }
        })
        .catch(error => {
          console.error('Failed to fetch admin profile:', error)
        })
        .finally(() => {
          setProfileLoading(false)
          fetchingRef.current = false
        })
    }
  }, [auth.isAuthenticated, isAdmin, profileData])

  // Merge session data with profile data
  const user = useMemo(() => {
    return profileData
      ? {
          ...auth.user,
          ...profileData,
        }
      : auth.user
  }, [auth.user, profileData])

  return {
    ...auth,
    user,
    isAdmin,
    isSuperAdmin: auth.user?.role === 'super_admin',
    profileLoading,
  }
}

// ✅ STUDENT AUTH HOOK: Simplified student authentication
export function useStudentAuth() {
  const auth = useHybridAuth()

  const isStudent = auth.user?.role === 'student'

  return {
    ...auth,
    isStudent,
  }
}

// ✅ API CALL HOOK: Automatic 401 handling
export function useAuthenticatedFetch() {
  const { logout } = useHybridAuth()

  const authenticatedFetch = useCallback(
    async (url: string, options: RequestInit = {}) => {
      try {
        const response = await fetch(url, {
          ...options,
          credentials: 'include',
        })

        // ✅ AUTOMATIC 401 HANDLING: No client-side monitoring needed
        if (response.status === 401) {
          console.log('🔓 401 DETECTED: Automatic logout')
          await logout()
          throw new Error('Session expired')
        }

        return response
      } catch (error) {
        if (error instanceof Error && error.message === 'Session expired') {
          throw error
        }

        console.error('Authenticated fetch error:', error)
        throw error
      }
    },
    [logout]
  )

  return authenticatedFetch
}

// ✅ ROUTE PROTECTION HOOK: Simple route guard
export function useRouteProtection(requiredRole?: string) {
  const auth = useHybridAuth()
  const router = useRouter()

  useEffect(() => {
    if (!auth.isLoading && !auth.isAuthenticated) {
      // Redirect to appropriate page based on required role
      const redirectPath = requiredRole === 'student' ? '/student' : '/admin'
      router.push(redirectPath)
      return
    }

    if (requiredRole && auth.user && !hasRequiredRole(auth.user.role, requiredRole)) {
      // Redirect to appropriate page based on required role
      const redirectPath = requiredRole === 'student' ? '/student' : '/admin'
      router.push(redirectPath)
      return
    }
  }, [auth.isLoading, auth.isAuthenticated, auth.user, requiredRole, router])

  return {
    isAuthorized:
      auth.isAuthenticated &&
      (!requiredRole || (auth.user && hasRequiredRole(auth.user.role, requiredRole))),
    isLoading: auth.isLoading,
  }
}

// ✅ HELPER FUNCTIONS

function hasRequiredRole(userRole: string, requiredRole: string): boolean {
  if (requiredRole === 'admin') {
    return ['admin', 'super_admin', 'teacher', 'receptionist'].includes(userRole)
  }

  if (requiredRole === 'student') {
    return userRole === 'student'
  }

  return userRole === requiredRole
}

// ✅ LEGACY COMPATIBILITY: For gradual migration
export function useLegacyAuthCompat() {
  const hybridAuth = useHybridAuth()

  // Map to old interface for backward compatibility
  return {
    admin: hybridAuth.user?.role !== 'student' ? hybridAuth.user : null,
    student: hybridAuth.user?.role === 'student' ? hybridAuth.user : null,
    loading: hybridAuth.isLoading,
    logout: hybridAuth.logout,
  }
}
