'use client'

import { useState } from 'react'
import { useToast } from '@/components/ui/use-toast'
import ExcelJS from 'exceljs'
import { startOfMonth, endOfMonth } from 'date-fns'
import { ExcelExportConfig } from '@/components/reports/ExcelExportDialog'

// Clean architecture Excel export imports - Matrix format only
import {
  generateMatrixExcel,
  generateMatrixExcelFilename,
} from '@/lib/utils/excel-matrix-coordinator'
import { ClientMatrixExportRepository } from '@/lib/data/repositories/client-matrix-export-repository'

interface UseExcelExportDialogOptions {
  onSuccess?: (filename: string) => void
  onError?: (error: string) => void
}

export function useExcelExportDialog(options: UseExcelExportDialogOptions = {}) {
  const { toast } = useToast()
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)

  const exportToExcel = async (config: ExcelExportConfig) => {
    try {
      setIsExporting(true)
      setExportProgress(0)

      // Step 1: Initialize (10%)
      setExportProgress(10)
      await new Promise(resolve => setTimeout(resolve, 100))

      // Create workbook
      const workbook = new ExcelJS.Workbook()

      // Set workbook properties
      workbook.creator = 'ShalatYuk System'
      workbook.title = `Laporan ${config.reportType === 'prayer' ? 'Shalat' : 'Sekolah'}`

      // Matrix format only - summary export functionality removed
      await createMatrixDetailReportWorkbook(
        workbook,
        config,
        setExportProgress,
        config.availableClasses
      )

      // Step 5: Auto-fit columns (90%) - Matrix format only
      setExportProgress(90)
      // Auto-fit columns for all worksheets in matrix format
      workbook.eachSheet(worksheet => {
        worksheet.columns.forEach(column => {
          let maxLength = 0
          column.eachCell?.({ includeEmpty: true }, cell => {
            const columnLength = cell.value ? cell.value.toString().length : 10
            if (columnLength > maxLength) {
              maxLength = columnLength
            }
          })
          column.width = Math.min(maxLength + 2, 50)
        })
      })

      // Step 6: Generate and download (100%) - Matrix format only
      setExportProgress(95)
      const filename = generateMatrixExcelFilename({
        reportType: config.reportType,
        exportFormat: config.exportFormat,
        startDate: getDateRange(config).startDate,
        endDate: getDateRange(config).endDate,
        attendanceData: [],
        enableFormatting: config.enableFormatting,
        selectedClasses: config.selectedClasses,
        availableClasses: config.availableClasses,
      })
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })

      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)

      link.setAttribute('href', url)
      link.setAttribute('download', filename)
      link.style.visibility = 'hidden'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      URL.revokeObjectURL(url)

      setExportProgress(100)

      toast({
        title: 'Export berhasil',
        description: `File ${filename} berhasil diunduh`,
      })

      options.onSuccess?.(filename)
    } catch (error) {
      console.error('Excel export error:', error)
      const errorMessage =
        error instanceof Error ? error.message : 'Terjadi kesalahan saat mengekspor'

      toast({
        title: 'Export gagal',
        description: errorMessage,
        variant: 'destructive',
      })

      options.onError?.(errorMessage)
    } finally {
      setIsExporting(false)
      setExportProgress(0)
    }
  }

  return {
    exportToExcel,
    isExporting,
    exportProgress,
  }
}

// Helper functions - Matrix format only (unused functions removed for clean code)

// Function to create matrix detail report workbook with tabs per class
async function createMatrixDetailReportWorkbook(
  workbook: ExcelJS.Workbook,
  config: ExcelExportConfig,
  setExportProgress: (progress: number) => void,
  availableClasses?: Array<{ id: string; name: string }>
) {
  setExportProgress(30)

  // Determine date range
  const { startDate, endDate } = getDateRange(config)

  console.log('🚀 HIGH PERFORMANCE MATRIX EXPORT: Starting for', config.rawData.length, 'records')

  // Initialize client matrix repository (fetches via API)
  const matrixRepo = new ClientMatrixExportRepository()

  setExportProgress(40)

  // Convert class IDs to class names for the API
  let classNames: string[] | undefined = undefined
  if (config.selectedClasses && config.selectedClasses.length > 0 && availableClasses) {
    // Convert class IDs to class names using availableClasses
    classNames = config.selectedClasses
      .map(classId => {
        const classObj = availableClasses.find(cls => cls.id === classId)
        return classObj?.name
      })
      .filter((name): name is string => name !== undefined)

    console.log('🔍 MATRIX EXPORT: Converting class IDs to names', {
      selectedClassIds: config.selectedClasses,
      resolvedClassNames: classNames,
      availableClasses: availableClasses.map(cls => ({ id: cls.id, name: cls.name })),
    })
  }

  // Get detailed attendance data with single optimized query
  const detailedData = await matrixRepo.getDetailedAttendanceForMatrix(
    startDate,
    endDate,
    classNames
  )

  setExportProgress(60)

  // Use new clean architecture Excel generator
  await generateMatrixExcel(
    workbook,
    {
      reportType: config.reportType,
      exportFormat: 'matrix',
      startDate,
      endDate,
      attendanceData: detailedData,
      enableFormatting: config.enableFormatting,
      selectedClasses: config.selectedClasses,
      availableClasses: config.availableClasses,
    },
    (progress: number) => {
      // Map progress from 60-100
      const mappedProgress = 60 + progress * 0.4
      setExportProgress(Math.round(mappedProgress))
    }
  )

  console.log('🚀 HIGH PERFORMANCE MATRIX EXPORT: Completed successfully')
}

/**
 * Get date range from config
 */
function getDateRange(config: ExcelExportConfig): { startDate: Date; endDate: Date } {
  const now = new Date()

  switch (config.dateRange) {
    case 'today':
      return {
        startDate: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
      }

    case 'yesterday':
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)
      return {
        startDate: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate()),
        endDate: new Date(
          yesterday.getFullYear(),
          yesterday.getMonth(),
          yesterday.getDate(),
          23,
          59,
          59
        ),
      }

    case 'last7days':
      const sevenDaysAgo = new Date(now)
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6)
      return {
        startDate: new Date(
          sevenDaysAgo.getFullYear(),
          sevenDaysAgo.getMonth(),
          sevenDaysAgo.getDate()
        ),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
      }

    case 'last30days':
      const thirtyDaysAgo = new Date(now)
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 29)
      return {
        startDate: new Date(
          thirtyDaysAgo.getFullYear(),
          thirtyDaysAgo.getMonth(),
          thirtyDaysAgo.getDate()
        ),
        endDate: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
      }

    case 'monthly':
      if (config.selectedMonth && config.selectedYear) {
        const monthStart = new Date(config.selectedYear, config.selectedMonth - 1, 1)
        const monthEnd = new Date(config.selectedYear, config.selectedMonth, 0, 23, 59, 59)
        return {
          startDate: monthStart,
          endDate: monthEnd,
        }
      }
      // Fallback to current month
      const currentMonthStart = startOfMonth(now)
      const currentMonthEnd = endOfMonth(now)
      return {
        startDate: currentMonthStart,
        endDate: currentMonthEnd,
      }

    case 'yearly':
      if (config.selectedYear) {
        const yearStart = new Date(config.selectedYear, 0, 1)
        const yearEnd = new Date(config.selectedYear, 11, 31, 23, 59, 59)
        return {
          startDate: yearStart,
          endDate: yearEnd,
        }
      }
      // Fallback to current year
      const currentYearStart = new Date(now.getFullYear(), 0, 1)
      const currentYearEnd = new Date(now.getFullYear(), 11, 31, 23, 59, 59)
      return {
        startDate: currentYearStart,
        endDate: currentYearEnd,
      }

    default:
      // Fallback to current month
      const fallbackMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const fallbackMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
      return {
        startDate: fallbackMonthStart,
        endDate: fallbackMonthEnd,
      }
  }
}

// End of file - all unused functions removed for clean code
