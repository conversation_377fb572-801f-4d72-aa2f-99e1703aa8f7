# 🚀 Panduan Setup Development ShalatYuk

Panduan lengkap untuk mengatur environment development lokal dengan PostgreSQL untuk project ShalatYuk.

## 📋 Prerequisites

Sebelum memulai, pastikan Anda telah menginstall:

- **Docker & Docker Compose** (Recommended)
  - [Download Docker Desktop](https://www.docker.com/products/docker-desktop/)
- **Node.js** (v18 atau lebih baru)
  - [Download Node.js](https://nodejs.org/)
- **Git**

## 🐳 Setup Otomatis dengan Docker (Recommended)

### Langkah 1: Clone & Install Dependencies

```bash
git clone <repository-url>
cd ShalatYuk
npm install
```

### Langkah 2: Setup Development Environment

```bash
# Jalankan script setup otomatis
./scripts/dev-setup.sh
```

Script ini akan:

- ✅ Mengecek instalasi Docker
- ✅ Membuat container PostgreSQL & Redis untuk development
- ✅ Setup environment variables
- ✅ Menjalankan initialization scripts
- ✅ Menginstall dummy data

### Langkah 3: Setup Database Schema

```bash
# Jalankan migrasi database
npm run db:migrate

# (Opsional) Load dummy data jika belum otomatis
npm run db:seed
```

### Langkah 4: Jalankan Development Server

```bash
npm run dev
```

## 📊 Akses Development Environment

Setelah setup berhasil, Anda dapat mengakses:

- **🌐 Aplikasi**: http://localhost:3000
- **🗄️ Adminer (DB GUI)**: http://localhost:8080
- **📊 PostgreSQL**: localhost:5433
- **🔄 Redis**: localhost:6380

### Kredensial Database

```
Host: localhost
Port: 5433
Database: shalat_yuk_dev
Username: shalatdev
Password: shalatdev123
```

## 🔧 Command Development

### Docker Commands

```bash
# Start development containers
docker-compose -f docker-compose.dev.yml up -d

# Stop development containers
docker-compose -f docker-compose.dev.yml down

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Access PostgreSQL CLI
docker exec -it shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev

# Access Redis CLI
docker exec -it shalatYuk-redis-dev redis-cli
```

### Database Commands

```bash
# Run migrations
npm run db:migrate

# Generate new migration
npm run db:generate

# Drop and recreate database
npm run db:reset

# Seed database with dummy data
npm run db:seed
```

## 🛠️ Manual Setup (Tanpa Docker)

Jika Anda tidak ingin menggunakan Docker, berikut setup manual:

### 1. Install PostgreSQL Lokal

#### macOS (dengan Homebrew)

```bash
brew install postgresql@15
brew services start postgresql@15
```

#### Ubuntu/Debian

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
```

#### Windows

Download dan install dari [PostgreSQL Official](https://www.postgresql.org/download/windows/)

### 2. Setup Database

```bash
# Login sebagai postgres user
sudo -u postgres psql

# Dalam PostgreSQL CLI:
CREATE USER shalatdev WITH PASSWORD 'shalatdev123';
CREATE DATABASE shalat_yuk_dev OWNER shalatdev;
GRANT ALL PRIVILEGES ON DATABASE shalat_yuk_dev TO shalatdev;
\q
```

### 3. Setup Environment

Copy dan edit file environment:

```bash
cp .env.local.dev .env.local
```

Edit `DATABASE_URL` di `.env.local`:

```env
DATABASE_URL=postgres://shalatdev:shalatdev123@localhost:5432/shalat_yuk_dev
```

### 4. Install Redis (Opsional)

#### macOS

```bash
brew install redis
brew services start redis
```

#### Ubuntu/Debian

```bash
sudo apt install redis-server
sudo systemctl start redis
```

## 🗂️ Struktur Database Development

```
shalat_yuk_dev/
├── Extensions
│   ├── uuid-ossp          # UUID generation
│   └── pgcrypto          # Encryption functions
├── Tables
│   ├── users             # User management
│   ├── classes           # Class management
│   ├── prayers           # Prayer data
│   ├── attendance        # Attendance records
│   └── reports           # Reporting data
└── Dummy Data            # Pre-loaded test data
```

## 🐛 Troubleshooting

### Docker Issues

**Error: Docker tidak berjalan**

```bash
# macOS/Linux
sudo systemctl start docker

# macOS dengan Docker Desktop
# Buka Docker Desktop application
```

**Error: Port sudah digunakan**

```bash
# Cek proses yang menggunakan port
lsof -i :5433
lsof -i :6380

# Hentikan proses atau ubah port di docker-compose.dev.yml
```

### Database Issues

**Error: Connection refused**

```bash
# Cek status container
docker ps

# Restart container jika perlu
docker-compose -f docker-compose.dev.yml restart postgres-dev
```

**Error: Permission denied**

```bash
# Reset container dan data
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d
```

### Environment Issues

**Error: Environment variables tidak terbaca**

```bash
# Pastikan file .env.local ada dan format benar
cat .env.local

# Copy ulang dari template
cp .env.local.dev .env.local
```

## 💡 Tips Development

1. **Gunakan Adminer** untuk management database GUI
2. **Monitor logs** dengan `docker-compose logs -f`
3. **Backup data** sebelum testing migrasi besar
4. **Reset environment** jika ada masalah dengan `dev-setup.sh`

## 🔄 Reset Development Environment

Jika Anda perlu reset ulang environment:

```bash
# Hentikan dan hapus semua container + data
docker-compose -f docker-compose.dev.yml down -v

# Hapus images (opsional)
docker rmi postgres:15-alpine redis:7-alpine adminer:latest

# Setup ulang
./scripts/dev-setup.sh
```

## 📞 Support

Jika mengalami masalah:

1. Cek log container: `docker-compose -f docker-compose.dev.yml logs`
2. Baca troubleshooting section di atas
3. Buka issue di repository project

---

**Happy coding! 🎉**
