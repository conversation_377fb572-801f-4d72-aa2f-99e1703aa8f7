# 🗄️ Panduan Menggunakan Adminer untuk PostgreSQL ShalatYuk

## 🚨 Solusi Masalah Koneksi Adminer

Jika Anda mengalami error seperti ini:

```
SQLSTATE[08006] [7] connection to server at "localhost" (::1), port 5433 failed: Connection refused
```

**Masalah**: Adminer tidak bisa menggunakan `localhost` karena ber<PERSON>lan di dalam container Docker.

## ✅ Cara Benar Mengakses Database via Adminer

### 1. Buka Adminer

Akses http://localhost:8080 di browser Anda.

### 2. <PERSON>gin dengan Kredensial yang Benar

**❌ SALAH - <PERSON><PERSON> gunakan:**

```
System: PostgreSQL
Server: localhost
Port: 5433
```

**✅ BENAR - Gunakan ini:**

```
System: PostgreSQL
Server: postgres-dev
Username: shalatdev
Password: shalatdev123
Database: shalat_yuk_dev
```

### 3. <PERSON><PERSON><PERSON>an

- **Server**: Gunakan `postgres-dev` (nama container), bukan `localhost`
- **Port**: Kosongkan field port, karena menggunakan default port 5432 internal container
- **Database**: `shalat_yuk_dev`

## 🔧 Alternatif Jika Masih Bermasalah

### Opsi 1: Restart Adminer Container

```bash
docker compose -f docker-compose.dev.yml restart adminer
```

### Opsi 2: Cek Network Connectivity

```bash
# Test koneksi dari dalam container Adminer
docker exec shalatYuk-adminer ping postgres-dev
```

### Opsi 3: Manual IP Address

Jika nama container tidak bekerja, gunakan IP internal:

```bash
# Dapatkan IP PostgreSQL container
docker inspect shalatYuk-postgres-dev | grep '"IPAddress"'
```

Kemudian gunakan IP tersebut sebagai Server di Adminer.

## 🎯 Step-by-Step Login Adminer

1. **Buka browser**: http://localhost:8080
2. **Pilih System**: PostgreSQL (dropdown pertama)
3. **Server**: `postgres-dev` (PENTING: bukan localhost!)
4. **Username**: `shalatdev`
5. **Password**: `shalatdev123`
6. **Database**: `shalat_yuk_dev` (opsional, bisa dikosongkan)
7. **Klik Login**

## 🔍 Troubleshooting

### Error: "connection refused"

- ✅ Pastikan menggunakan `postgres-dev` sebagai server
- ✅ Jangan gunakan port number di field Server
- ✅ Pastikan semua containers berjalan: `docker ps`

### Error: "Authentication failed"

- ✅ Username: `shalatdev` (bukan `postgres`)
- ✅ Password: `shalatdev123`
- ✅ Database: `shalat_yuk_dev`

### Error: "Unknown database"

- ✅ Database field bisa dikosongkan
- ✅ Setelah login, pilih database dari dropdown

## 💡 Tips Adminer

### Setelah Berhasil Login:

1. **Explore Tables**: Klik "select" di menu kiri
2. **Run Queries**: Klik "SQL command"
3. **View Data**: Klik nama table untuk melihat data
4. **Export Data**: Klik "Export" untuk backup

### Queries Berguna:

```sql
-- Lihat semua tables
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public';

-- Lihat data users
SELECT * FROM users LIMIT 10;

-- Lihat struktur table
\d users;
```

## 🛠️ Alternative Database Tools

Jika Adminer masih bermasalah, gunakan alternatif ini:

### 1. Command Line (Sudah Terbukti Berfungsi)

```bash
docker exec -it shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev
```

### 2. External Tools

Gunakan koneksi external dengan:

```
Host: localhost
Port: 5433
Database: shalat_yuk_dev
Username: shalatdev
Password: shalatdev123
```

**Tools yang direkomendasikan:**

- **TablePlus** (macOS - berbayar tapi excellent)
- **DBeaver** (Cross-platform - gratis)
- **pgAdmin** (PostgreSQL specialist - gratis)
- **DataGrip** (JetBrains - berbayar)

## 🔄 Reset Adminer (Jika Perlu)

Jika ada masalah persistent dengan Adminer:

```bash
# Stop dan remove container
docker compose -f docker-compose.dev.yml stop adminer
docker compose -f docker-compose.dev.yml rm adminer

# Start ulang
docker compose -f docker-compose.dev.yml up -d adminer
```

---

## 📋 Summary Kredensial yang Benar

**Untuk Adminer (http://localhost:8080):**

```
System: PostgreSQL
Server: postgres-dev
Username: shalatdev
Password: shalatdev123
Database: shalat_yuk_dev
```

**Untuk External Tools:**

```
Host: localhost
Port: 5433
Username: shalatdev
Password: shalatdev123
Database: shalat_yuk_dev
```

**Selamat menggunakan Adminer! 🎉**
