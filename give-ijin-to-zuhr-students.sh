#!/bin/bash

# Script untuk memberikan ijin kepada siswa yang sudah melakukan Zuhur hari ini
# Author: ShalatYuk Development Team
# Date: $(date +%Y-%m-%d)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo ""
    echo "=================================================="
    echo "🕌 MEMBERIKAN IJIN KEPADA SISWA YANG SUDAH ZUHUR"
    echo "=================================================="
    echo "Tanggal: $(date '+%Y-%m-%d %H:%M:%S WITA')"
    echo ""
}

# Load environment variables
load_env() {
    if [ -f ".env.local" ]; then
        # Load only DATABASE_URL which is what we need
        DATABASE_URL=$(grep "^DATABASE_URL=" .env.local | head -1 | cut -d'=' -f2-)
        export DATABASE_URL

        if [ -n "$DATABASE_URL" ]; then
            print_success "DATABASE_URL loaded from .env.local"
        else
            print_error "DATABASE_URL not found in .env.local"
            exit 1
        fi
    else
        print_error ".env.local file not found!"
        print_warning "Please make sure .env.local exists with DATABASE_URL"
        exit 1
    fi
}

# Check database connection
check_database_connection() {
    print_status "Checking database connection..."
    
    if [ -z "$DATABASE_URL" ]; then
        print_error "DATABASE_URL not found in environment variables"
        print_warning "Please run: npm run tunnel"
        exit 1
    fi
    
    # Hide password in output
    SAFE_URL=$(echo "$DATABASE_URL" | sed 's/:\/\/[^:]*:[^@]*@/:\/\/****:****@/')
    print_status "Using DATABASE_URL: $SAFE_URL"
    
    if psql "$DATABASE_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "Database connection successful"
    else
        print_error "Failed to connect to database!"
        print_warning "Please check your database connection and try again"
        exit 1
    fi
}

# Preview students who will get ijin
preview_students() {
    print_status "Mencari siswa yang sudah melakukan Zuhur hari ini..."
    
    # Query untuk melihat siswa yang sudah Zuhur tapi belum ada record Ijin
    PREVIEW_QUERY="
    SELECT 
        u.name,
        u.nis,
        c.name as class_name,
        a.recorded_at AT TIME ZONE 'Asia/Makassar' as zuhr_time
    FROM absences a
    JOIN users u ON a.unique_code = u.unique_code
    JOIN classes c ON u.class_id = c.id
    WHERE a.type = 'Zuhr' 
        AND DATE(a.recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
        AND a.unique_code NOT IN (
            SELECT unique_code 
            FROM absences 
            WHERE type = 'Ijin' 
                AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
        )
    ORDER BY c.name, u.name;
    "
    
    echo ""
    echo "📋 PREVIEW: Siswa yang akan mendapat ijin:"
    echo "============================================"
    
    PREVIEW_RESULT=$(psql "$DATABASE_URL" -t -c "$PREVIEW_QUERY" 2>/dev/null || echo "")
    
    if [ -z "$PREVIEW_RESULT" ] || [ "$PREVIEW_RESULT" = " " ]; then
        print_warning "Tidak ada siswa yang memenuhi kriteria"
        print_status "Kriteria: Sudah Zuhur hari ini, belum ada record Ijin"
        exit 0
    fi
    
    # Format output
    echo "$PREVIEW_RESULT" | while IFS='|' read -r name nis class_name zuhr_time; do
        name=$(echo "$name" | xargs)
        nis=$(echo "$nis" | xargs)
        class_name=$(echo "$class_name" | xargs)
        zuhr_time=$(echo "$zuhr_time" | xargs)
        echo "• $name ($nis) - $class_name - Zuhur: $zuhr_time"
    done
    
    # Count students
    STUDENT_COUNT=$(echo "$PREVIEW_RESULT" | wc -l | xargs)
    echo ""
    print_status "Total siswa yang akan mendapat ijin: $STUDENT_COUNT"
}

# Get user confirmation
get_confirmation() {
    echo ""
    print_warning "KONFIRMASI DIPERLUKAN"
    echo "Apakah Anda yakin ingin memberikan ijin kepada siswa-siswa di atas?"
    echo "Operasi ini akan menambahkan record 'Ijin' untuk setiap siswa."
    echo ""
    read -p "Ketik 'YA' untuk melanjutkan, atau tekan Enter untuk membatalkan: " confirmation
    
    if [ "$confirmation" != "YA" ]; then
        print_warning "Operasi dibatalkan oleh user"
        exit 0
    fi
}

# Execute the main operation
execute_ijin_operation() {
    print_status "Menjalankan operasi pemberian ijin..."
    
    # Create backup timestamp
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    
    # Main query to insert ijin records with correct WITA timezone
    MAIN_QUERY="
    INSERT INTO absences (unique_code, type, recorded_at, reason)
    SELECT
        unique_code,
        'Ijin' as type,
        (CURRENT_DATE + INTERVAL '15 hours')::timestamptz as recorded_at,
        'Ijin otomatis - sudah melakukan Zuhur hari ini' as reason
    FROM absences
    WHERE type = 'Zuhr'
        AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
        AND unique_code NOT IN (
            SELECT unique_code
            FROM absences
            WHERE type = 'Ijin'
                AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
        );
    "
    
    # Execute the query and capture result
    RESULT=$(psql "$DATABASE_URL" -c "$MAIN_QUERY" 2>&1)
    
    if echo "$RESULT" | grep -q "INSERT"; then
        # Extract number of inserted records
        INSERTED_COUNT=$(echo "$RESULT" | grep "INSERT" | awk '{print $3}')
        print_success "Berhasil memberikan ijin kepada $INSERTED_COUNT siswa"
        
        # Log the operation
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Ijin diberikan kepada $INSERTED_COUNT siswa" >> ijin_operations.log
        
    else
        print_error "Gagal menjalankan operasi!"
        echo "Error details: $RESULT"
        exit 1
    fi
}

# Show final results
show_results() {
    print_status "Menampilkan hasil akhir..."
    
    # Query to show students who got ijin today
    RESULT_QUERY="
    SELECT 
        u.name,
        u.nis,
        c.name as class_name,
        a.recorded_at AT TIME ZONE 'Asia/Makassar' as ijin_time,
        a.reason
    FROM absences a
    JOIN users u ON a.unique_code = u.unique_code
    JOIN classes c ON u.class_id = c.id
    WHERE a.type = 'Ijin' 
        AND DATE(a.recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
        AND a.reason LIKE '%sudah melakukan Zuhur%'
    ORDER BY a.recorded_at DESC;
    "
    
    echo ""
    echo "📊 HASIL OPERASI:"
    echo "=================="
    
    RESULT_DATA=$(psql "$DATABASE_URL" -t -c "$RESULT_QUERY" 2>/dev/null)
    
    if [ -n "$RESULT_DATA" ]; then
        echo "$RESULT_DATA" | while IFS='|' read -r name nis class_name ijin_time reason; do
            name=$(echo "$name" | xargs)
            nis=$(echo "$nis" | xargs)
            class_name=$(echo "$class_name" | xargs)
            ijin_time=$(echo "$ijin_time" | xargs)
            echo "✓ $name ($nis) - $class_name - Ijin: $ijin_time"
        done
    fi
    
    # Show summary statistics
    echo ""
    print_status "STATISTIK HARI INI:"
    
    STATS_QUERY="
    SELECT 
        'Total Zuhur' as category,
        COUNT(*) as count
    FROM absences 
    WHERE type = 'Zuhr' 
        AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
    UNION ALL
    SELECT 
        'Total Ijin' as category,
        COUNT(*) as count
    FROM absences 
    WHERE type = 'Ijin' 
        AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE;
    "
    
    psql "$DATABASE_URL" -c "$STATS_QUERY"
}

# Provide rollback information
show_rollback_info() {
    echo ""
    print_warning "INFORMASI ROLLBACK:"
    echo "Jika Anda perlu membatalkan operasi ini, gunakan command berikut:"
    echo ""
    echo "psql \"\$DATABASE_URL\" -c \""
    echo "DELETE FROM absences "
    echo "WHERE type = 'Ijin' "
    echo "    AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE"
    echo "    AND reason LIKE '%sudah melakukan Zuhur%';"
    echo "\""
    echo ""
}

# Main execution
main() {
    print_header
    load_env
    check_database_connection
    preview_students
    get_confirmation
    execute_ijin_operation
    show_results
    show_rollback_info
    
    print_success "Operasi selesai! Log tersimpan di: ijin_operations.log"
}

# Run main function
main "$@"
