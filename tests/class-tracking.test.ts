/**
 * COMPREHENSIVE TEST SUITE: Class Context Tracking Feature
 * 
 * This test suite verifies that the class context tracking feature works correctly
 * across all layers of the application (Domain, Repository, Use Cases, API)
 * 
 * Run with: npm test tests/class-tracking.test.ts
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { db } from '@/lib/data/drizzle/connection'
import { absences, users, classes } from '@/lib/data/drizzle/schema'
import { eq } from 'drizzle-orm'

describe('Class Context Tracking Feature', () => {
  let absenceRepo: AbsenceRepository
  let studentRepo: StudentRepository
  let absenceUseCases: AbsenceUseCases
  
  // Test data
  const testStudent = {
    uniqueCode: 'TEST-CLASS-TRACKING-001',
    name: 'Test Student Class Tracking',
    email: '<EMAIL>',
    role: 'student' as const,
    classId: 1, // Assuming class ID 1 exists
  }
  
  const testClass = {
    id: 1,
    name: 'X IPA 1',
  }

  beforeAll(async () => {
    // Initialize repositories and use cases
    absenceRepo = new AbsenceRepository()
    studentRepo = new StudentRepository()
    absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo)
    
    // Clean up any existing test data
    await db.delete(absences).where(eq(absences.uniqueCode, testStudent.uniqueCode))
    await db.delete(users).where(eq(users.uniqueCode, testStudent.uniqueCode))
    
    // Create test student
    await db.insert(users).values({
      uniqueCode: testStudent.uniqueCode,
      name: testStudent.name,
      email: testStudent.email,
      role: testStudent.role,
      classId: testStudent.classId,
    })
  })

  afterAll(async () => {
    // Clean up test data
    await db.delete(absences).where(eq(absences.uniqueCode, testStudent.uniqueCode))
    await db.delete(users).where(eq(users.uniqueCode, testStudent.uniqueCode))
  })

  beforeEach(async () => {
    // Clean up attendance records before each test
    await db.delete(absences).where(eq(absences.uniqueCode, testStudent.uniqueCode))
  })

  describe('Repository Layer Tests', () => {
    it('should create absence record with class context', async () => {
      // Arrange
      const createData = {
        uniqueCode: testStudent.uniqueCode,
        type: AttendanceType.ZUHR,
        recordedAt: new Date(),
        classId: testClass.id,
        className: testClass.name,
      }

      // Act
      const absence = await absenceRepo.create(createData)

      // Assert
      expect(absence).toBeDefined()
      expect(absence.uniqueCode).toBe(testStudent.uniqueCode)
      expect(absence.type).toBe(AttendanceType.ZUHR)
      expect(absence.classId).toBe(testClass.id)
      expect(absence.className).toBe(testClass.name)
    })

    it('should update absence record with class context', async () => {
      // Arrange - Create initial record
      const initialData = {
        uniqueCode: testStudent.uniqueCode,
        type: AttendanceType.ZUHR,
        recordedAt: new Date(),
      }
      const initialAbsence = await absenceRepo.create(initialData)

      // Act - Update with class context
      const updatedAbsence = await absenceRepo.update(initialAbsence.id, {
        recordedAt: new Date(),
        classId: testClass.id,
        className: testClass.name,
      })

      // Assert
      expect(updatedAbsence.classId).toBe(testClass.id)
      expect(updatedAbsence.className).toBe(testClass.name)
    })

    it('should handle null class context gracefully', async () => {
      // Arrange
      const createData = {
        uniqueCode: testStudent.uniqueCode,
        type: AttendanceType.ZUHR,
        recordedAt: new Date(),
        // No class context provided
      }

      // Act
      const absence = await absenceRepo.create(createData)

      // Assert
      expect(absence).toBeDefined()
      expect(absence.classId).toBeUndefined()
      expect(absence.className).toBeUndefined()
    })
  })

  describe('Use Cases Layer Tests', () => {
    it('should automatically include class context when recording attendance', async () => {
      // Act
      const absence = await absenceUseCases.recordAbsence(
        testStudent.uniqueCode,
        AttendanceType.ZUHR
      )

      // Assert
      expect(absence).toBeDefined()
      expect(absence.classId).toBe(testStudent.classId)
      expect(absence.className).toBe(testClass.name)
    })

    it('should preserve class context in auto-generated prayer ijin', async () => {
      // Act - Record school absence (should auto-create prayer ijin)
      await absenceUseCases.recordAbsence(
        testStudent.uniqueCode,
        AttendanceType.EXCUSED_ABSENCE
      )

      // Assert - Check that prayer ijin was created with class context
      const ijinRecords = await absenceRepo.findByUniqueCodeAndDateRange(
        testStudent.uniqueCode,
        new Date(),
        new Date(),
        [AttendanceType.IJIN]
      )

      expect(ijinRecords).toHaveLength(1)
      expect(ijinRecords[0].classId).toBe(testStudent.classId)
      expect(ijinRecords[0].className).toBe(testClass.name)
    })

    it('should handle bulk attendance recording with class context', async () => {
      // Arrange
      const startDate = new Date()
      const endDate = new Date(startDate.getTime() + 2 * 24 * 60 * 60 * 1000) // 2 days later

      // Act
      const result = await absenceUseCases.recordAbsenceRange(
        testStudent.uniqueCode,
        AttendanceType.SICK,
        startDate,
        endDate
      )

      // Assert
      expect(result.successes).toHaveLength(3) // 3 days including start and end
      result.successes.forEach(absence => {
        expect(absence.classId).toBe(testStudent.classId)
        expect(absence.className).toBe(testClass.name)
      })
    })
  })

  describe('Historical Accuracy Tests', () => {
    it('should preserve class context even if student class changes', async () => {
      // Arrange - Record attendance with current class
      const absence1 = await absenceUseCases.recordAbsence(
        testStudent.uniqueCode,
        AttendanceType.ZUHR
      )

      // Simulate student class change (in real scenario, this would happen at year-end)
      await db.update(users)
        .set({ classId: 2 }) // Change to different class
        .where(eq(users.uniqueCode, testStudent.uniqueCode))

      // Act - Record new attendance after class change
      const absence2 = await absenceUseCases.recordAbsence(
        testStudent.uniqueCode,
        AttendanceType.ASR
      )

      // Assert - First attendance should still show original class
      expect(absence1.className).toBe(testClass.name)
      // Second attendance should show new class (in real scenario)
      // For now, since we don't have class ID 2 set up, we'll just verify it's different
      expect(absence2.classId).toBeDefined()

      // Restore original class for cleanup
      await db.update(users)
        .set({ classId: testStudent.classId })
        .where(eq(users.uniqueCode, testStudent.uniqueCode))
    })

    it('should maintain data integrity with foreign key constraints', async () => {
      // This test verifies that foreign key constraints work correctly
      const createData = {
        uniqueCode: testStudent.uniqueCode,
        type: AttendanceType.ZUHR,
        recordedAt: new Date(),
        classId: 99999, // Non-existent class ID
        className: 'Non-existent Class',
      }

      // Act & Assert - Should handle foreign key constraint gracefully
      await expect(absenceRepo.create(createData)).rejects.toThrow()
    })
  })

  describe('Performance Tests', () => {
    it('should efficiently query attendance by class name', async () => {
      // Arrange - Create multiple attendance records
      const attendancePromises = []
      for (let i = 0; i < 10; i++) {
        attendancePromises.push(
          absenceUseCases.recordAbsence(
            testStudent.uniqueCode,
            i % 2 === 0 ? AttendanceType.ZUHR : AttendanceType.ASR
          )
        )
      }
      await Promise.all(attendancePromises)

      // Act - Query by class name (should use index)
      const startTime = Date.now()
      const records = await absenceRepo.findByUniqueCodeAndDateRange(
        testStudent.uniqueCode,
        new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
        new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        [AttendanceType.ZUHR, AttendanceType.ASR]
      )
      const endTime = Date.now()

      // Assert
      expect(records.length).toBeGreaterThan(0)
      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
      
      // Verify all records have class context
      records.forEach(record => {
        expect(record.classId).toBeDefined()
        expect(record.className).toBeDefined()
      })
    })
  })

  describe('Backward Compatibility Tests', () => {
    it('should handle existing records without class context', async () => {
      // Arrange - Create record without class context (simulating old data)
      const [oldRecord] = await db.insert(absences).values({
        uniqueCode: testStudent.uniqueCode,
        type: AttendanceType.ZUHR,
        recordedAt: new Date(),
        // No class context
      }).returning()

      // Act - Retrieve record
      const retrievedRecord = await absenceRepo.findById(oldRecord.id)

      // Assert - Should handle gracefully
      expect(retrievedRecord).toBeDefined()
      expect(retrievedRecord?.classId).toBeUndefined()
      expect(retrievedRecord?.className).toBeUndefined()
    })

    it('should work with mixed data (some with class context, some without)', async () => {
      // Arrange - Create mixed records
      await db.insert(absences).values([
        {
          uniqueCode: testStudent.uniqueCode,
          type: AttendanceType.ZUHR,
          recordedAt: new Date(),
          // No class context (old data)
        },
        {
          uniqueCode: testStudent.uniqueCode,
          type: AttendanceType.ASR,
          recordedAt: new Date(),
          classId: testClass.id,
          className: testClass.name,
          // With class context (new data)
        }
      ])

      // Act - Query all records
      const records = await absenceRepo.findByUniqueCodeAndDateRange(
        testStudent.uniqueCode,
        new Date(Date.now() - 24 * 60 * 60 * 1000),
        new Date(Date.now() + 24 * 60 * 60 * 1000),
        [AttendanceType.ZUHR, AttendanceType.ASR]
      )

      // Assert
      expect(records).toHaveLength(2)
      
      const zuhrRecord = records.find(r => r.type === AttendanceType.ZUHR)
      const asrRecord = records.find(r => r.type === AttendanceType.ASR)
      
      expect(zuhrRecord?.classId).toBeUndefined() // Old record
      expect(asrRecord?.classId).toBe(testClass.id) // New record
    })
  })

  describe('Edge Cases', () => {
    it('should handle student without class assignment', async () => {
      // Arrange - Create student without class
      const studentWithoutClass = {
        uniqueCode: 'TEST-NO-CLASS-001',
        name: 'Student Without Class',
        email: '<EMAIL>',
        role: 'student' as const,
        classId: null,
      }

      await db.insert(users).values(studentWithoutClass)

      try {
        // Act
        const absence = await absenceUseCases.recordAbsence(
          studentWithoutClass.uniqueCode,
          AttendanceType.ZUHR
        )

        // Assert - Should handle gracefully
        expect(absence).toBeDefined()
        expect(absence.classId).toBeUndefined()
        expect(absence.className).toBeUndefined()
      } finally {
        // Cleanup
        await db.delete(absences).where(eq(absences.uniqueCode, studentWithoutClass.uniqueCode))
        await db.delete(users).where(eq(users.uniqueCode, studentWithoutClass.uniqueCode))
      }
    })
  })
})
