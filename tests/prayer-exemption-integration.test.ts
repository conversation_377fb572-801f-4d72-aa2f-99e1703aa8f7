/**
 * Prayer Exemption Integration Tests
 * Tests the complete prayer exemption system functionality
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { PrayerExemptionUseCases } from '@/lib/domain/usecases/prayer-exemption'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { PrayerExemptionRepository } from '@/lib/data/repositories/prayer-exemption'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { ClassRepository } from '@/lib/data/repositories/class'
import {
  PrayerExemptionType,
  PrayerType,
  RecurrencePattern,
  CreatePrayerExemptionDTO,
} from '@/lib/domain/entities/prayer-exemption'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { getCurrentWITATime } from '@/lib/utils/date'

// Mock cache service
const mockCache = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
}

describe('Prayer Exemption Integration Tests', () => {
  let prayerExemptionUseCases: PrayerExemptionUseCases
  let absenceUseCases: AbsenceUseCases
  let prayerExemptionRepo: PrayerExemptionRepository
  let absenceRepo: AbsenceRepository
  let studentRepo: StudentRepository
  let classRepo: ClassRepository

  const mockSuperAdminCode = 'super-admin-uuid'
  const mockStudentCode = 'student-uuid-123'
  const mockClassId = '1'

  beforeEach(() => {
    // Initialize repositories
    prayerExemptionRepo = new PrayerExemptionRepository()
    absenceRepo = new AbsenceRepository()
    studentRepo = new StudentRepository() // ✅ FIXED: StudentRepository doesn't take cache parameter
    classRepo = new ClassRepository()

    // Initialize use cases
    prayerExemptionUseCases = new PrayerExemptionUseCases(
      prayerExemptionRepo,
      studentRepo,
      classRepo
    )

    absenceUseCases = new AbsenceUseCases(
      absenceRepo,
      studentRepo,
      mockCache as any,
      prayerExemptionUseCases
    )

    // Reset mocks
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Prayer Exemption Creation', () => {
    it('should create global exemption for both prayers', async () => {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)

      const exemptionData: CreatePrayerExemptionDTO = {
        exemptionType: PrayerExemptionType.GLOBAL,
        exemptionDate: tomorrow,
        recurrencePattern: RecurrencePattern.ONCE,
        prayerType: PrayerType.BOTH,
        reason: 'Ujian nasional - semua siswa pulang lebih awal',
        createdBy: 1, // Use number instead of string
      }

      // Mock repository create method
      const mockExemption = {
        id: 1,
        ...exemptionData,
        targetId: null,
        createdAt: new Date(),
        isActive: true,
      }

      jest.spyOn(prayerExemptionRepo, 'create').mockResolvedValue(mockExemption)

      const result = await prayerExemptionUseCases.createExemption(exemptionData, 'super_admin')

      expect(result).toEqual(mockExemption)
      expect(prayerExemptionRepo.create).toHaveBeenCalledWith(exemptionData)
    })

    // Removed class exemption test since we only support global exemptions

    it('should reject exemption creation by non-super_admin', async () => {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)

      const exemptionData: CreatePrayerExemptionDTO = {
        exemptionType: PrayerExemptionType.GLOBAL,
        exemptionDate: tomorrow,
        prayerType: PrayerType.BOTH,
        reason: 'Test exemption',
        createdBy: 'admin-uuid',
      }

      await expect(prayerExemptionUseCases.createExemption(exemptionData, 'admin')).rejects.toThrow(
        'Only super admin can create prayer exemptions'
      )
    })

    it('should reject exemption for past dates', async () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      const exemptionData: CreatePrayerExemptionDTO = {
        exemptionType: PrayerExemptionType.GLOBAL,
        exemptionDate: yesterday,
        prayerType: PrayerType.BOTH,
        reason: 'Test exemption for past date',
        createdBy: mockSuperAdminCode,
      }

      await expect(
        prayerExemptionUseCases.createExemption(exemptionData, 'super_admin')
      ).rejects.toThrow('Cannot create exemption for past dates')
    })
  })

  describe('Prayer Validation with Exemptions', () => {
    it('should allow dismissal when student is exempted from both prayers', async () => {
      const today = getCurrentWITATime()
      today.setHours(0, 0, 0, 0)

      // Mock student data
      jest.spyOn(studentRepo, 'findByUniqueCode').mockResolvedValue({
        id: 1,
        uniqueCode: mockStudentCode,
        name: 'Test Student',
        classId: 1,
        nis: '12345',
        whatsapp: '081234567890',
        gender: 'male',
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      // Mock no attendance records (student hasn't prayed)
      jest.spyOn(absenceRepo, 'findByUniqueCodeAndDate').mockResolvedValue([])

      // Mock exemption check - student is exempted from both prayers
      jest
        .spyOn(prayerExemptionUseCases, 'isStudentExempted')
        .mockResolvedValueOnce(true) // Zuhr exempted
        .mockResolvedValueOnce(true) // Asr exempted

      // Should not throw error when recording dismissal
      const mockAbsence = {
        id: 1,
        uniqueCode: mockStudentCode,
        type: AttendanceType.DISMISSAL,
        recordedAt: new Date(),
        createdAt: new Date(),
        reason: null,
      }

      jest.spyOn(absenceRepo, 'create').mockResolvedValue(mockAbsence)

      const result = await absenceUseCases.recordAbsence(mockStudentCode, AttendanceType.DISMISSAL)

      expect(result).toEqual(mockAbsence)
      expect(prayerExemptionUseCases.isStudentExempted).toHaveBeenCalledTimes(2)
    })

    it('should require Zuhr prayer when only Asr is exempted', async () => {
      const today = getCurrentWITATime()
      today.setHours(0, 0, 0, 0)

      // Mock student data
      jest.spyOn(studentRepo, 'findByUniqueCode').mockResolvedValue({
        id: 1,
        uniqueCode: mockStudentCode,
        name: 'Test Student',
        classId: 1,
        nis: '12345',
        whatsapp: '081234567890',
        gender: 'male',
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      // Mock no attendance records (student hasn't prayed)
      jest.spyOn(absenceRepo, 'findByUniqueCodeAndDate').mockResolvedValue([])

      // Mock exemption check - only Asr is exempted
      jest
        .spyOn(prayerExemptionUseCases, 'isStudentExempted')
        .mockResolvedValueOnce(false) // Zuhr not exempted
        .mockResolvedValueOnce(true) // Asr exempted

      // Should throw error because Zuhr is required but not completed
      await expect(
        absenceUseCases.recordAbsence(mockStudentCode, AttendanceType.DISMISSAL)
      ).rejects.toThrow('Absensi pulang tidak diizinkan. Shalat Zuhur belum dilakukan.')
    })

    it('should work with existing Ijin exemption (backward compatibility)', async () => {
      const today = getCurrentWITATime()
      today.setHours(0, 0, 0, 0)

      // Mock student data
      jest.spyOn(studentRepo, 'findByUniqueCode').mockResolvedValue({
        id: 1,
        uniqueCode: mockStudentCode,
        name: 'Test Student',
        classId: 1,
        nis: '12345',
        whatsapp: '081234567890',
        gender: 'male',
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      // Mock existing Ijin record
      jest.spyOn(absenceRepo, 'findByUniqueCodeAndDate').mockResolvedValue([
        {
          id: 1,
          uniqueCode: mockStudentCode,
          type: AttendanceType.IJIN,
          recordedAt: new Date(),
          createdAt: new Date(),
          reason: 'Sakit',
        },
      ])

      const mockAbsence = {
        id: 2,
        uniqueCode: mockStudentCode,
        type: AttendanceType.DISMISSAL,
        recordedAt: new Date(),
        createdAt: new Date(),
        reason: null,
      }

      jest.spyOn(absenceRepo, 'create').mockResolvedValue(mockAbsence)

      // Should allow dismissal due to existing Ijin record
      const result = await absenceUseCases.recordAbsence(mockStudentCode, AttendanceType.DISMISSAL)

      expect(result).toEqual(mockAbsence)
      // Prayer exemption check should not be called due to Ijin exemption
      expect(prayerExemptionUseCases.isStudentExempted).not.toHaveBeenCalled()
    })
  })
})
