'use client'

import { useRouter } from 'next/navigation'
import { Camera, FileText, User, Users, GraduationCap, Shield, Clock } from 'lucide-react'
// SECURE: Import the role configuration
import { getNavigationItems } from '@/lib/config/role-permissions'
import { useAdminAuth } from '@/hooks/use-hybrid-auth'

interface AdminBottomNavProps {
  activeTab:
    | 'home'
    | 'prayer-reports'
    | 'school-reports'
    | 'users'
    | 'admins'
    | 'classes'
    | 'sessions'
    | 'profile'
}

export function AdminBottomNav({ activeTab }: AdminBottomNavProps) {
  const router = useRouter()
  const { user: admin } = useAdminAuth()

  // SECURE: Check if admin session exists and has role
  if (!admin?.role) return null

  // SECURE: Get navigation items based on role
  const navItems = getNavigationItems(admin.role as any)

  // Icon mapping for existing icons
  const iconMap = {
    camera: Camera,
    'file-text': FileText,
    users: Users,
    user: User,
    'graduation-cap': GraduationCap,
    shield: Shield,
    clock: Clock,
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 flex h-16 items-center justify-around border-t border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800 md:hidden">
      {navItems.map(item => {
        const IconComponent = iconMap[item.icon as keyof typeof iconMap] || User
        const isActive = activeTab === getActiveTabFromPath(item.path)

        return (
          <button
            key={item.path}
            onClick={() => router.push(item.path)}
            className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
              isActive
                ? 'text-indigo-600 dark:text-indigo-400'
                : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
            }`}
          >
            <IconComponent className="h-5 w-5" />
            <span className="mt-1 text-xs">{item.label}</span>
          </button>
        )
      })}
    </div>
  )
}

// Helper function to determine active tab from path
function getActiveTabFromPath(path: string): string {
  if (path === '/admin/home') return 'home'
  if (path === '/admin/prayer-reports') return 'prayer-reports'
  if (path === '/admin/school-reports') return 'school-reports'
  if (path === '/admin/users') return 'users'
  if (path === '/admin/admins') return 'admins'
  if (path === '/admin/classes') return 'classes'
  if (path === '/admin/sessions') return 'sessions'
  if (path === '/admin/profile') return 'profile'
  return 'home'
}
