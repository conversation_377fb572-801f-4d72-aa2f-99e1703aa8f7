'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { formatTimeWITA } from '@/lib/utils/date'
import { TIMEZONE_CONFIG } from '@/lib/config'

interface AttendanceTimeTooltipProps {
  /** The display value (✓, ✗, Ijin, etc.) */
  displayValue: string
  /** The timestamp string from the database */
  timeValue: string | null | undefined
  /** The badge variant for styling */
  variant: 'success' | 'danger' | 'warning' | 'info' | 'secondary' | 'neutral'
  /** The type of attendance for context */
  attendanceType: string
  /** Whether to show tooltip (only for today/yesterday filters) */
  showTooltip?: boolean
  /** Additional reason text (for ijin, sick, etc.) */
  reason?: string | null
}

/**
 * Formats time string to WITA format for tooltip display
 * @param timeStr - Time string from database
 * @returns Formatted time string or null if invalid
 */
function formatTimeForTooltip(timeStr: string | null | undefined): string | null {
  if (!timeStr || timeStr === '-' || timeStr === 'null' || timeStr === null) {
    return null
  }

  // Skip count format (like "3x") - this indicates aggregated data
  if (typeof timeStr === 'string' && timeStr.includes('x')) {
    return null
  }

  // Skip boolean-like values
  if (timeStr === '✓' || timeStr === 'Ya' || timeStr === 'true' || timeStr === 'false') {
    return null
  }

  try {
    let date: Date

    // Handle different time formats
    if (timeStr.includes('T')) {
      // ISO format: "2024-01-15T13:30:00.000Z"
      date = new Date(timeStr)
      if (isNaN(date.getTime())) {
        return null
      }
    } else if (timeStr.match(/^\d{1,2}:\d{2}(:\d{2})?$/)) {
      // Already in HH:MM or HH:MM:SS format - assume it's already in WITA
      return timeStr.substring(0, 5) // Get HH:MM part
    } else if (timeStr.match(/^\d{1,2}\.\d{2}\.\d{2}$/)) {
      // Dot format: "14.15.49" -> convert to "14:15"
      const parts = timeStr.split('.')
      return `${parts[0].padStart(2, '0')}:${parts[1]}`
    } else if (timeStr.includes(',') && timeStr.includes('WITA')) {
      // Format: "29 Apr 2025, 12:15 WITA"
      const timePart = timeStr.split(',')[1]?.trim()
      if (timePart) {
        const timeOnly = timePart.replace(' WITA', '').trim()
        if (timeOnly.match(/^\d{1,2}:\d{2}$/)) {
          return timeOnly
        }
      }
      return null
    } else {
      // Try to parse as a date string
      date = new Date(timeStr)
      if (isNaN(date.getTime())) {
        return null
      }
    }

    // Format to WITA time
    return date.toLocaleTimeString(TIMEZONE_CONFIG.LOCALE, {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: TIMEZONE_CONFIG.TIMEZONE,
      hour12: false,
    })
  } catch (error) {
    console.warn('Error formatting time for tooltip:', error)
    return null
  }
}

/**
 * AttendanceTimeTooltip Component
 * 
 * Displays attendance status with optional time tooltip for "Hari ini" and "Kemaren" filters.
 * Follows best practices for accessibility and UX:
 * - Only shows tooltips when time information is available and relevant
 * - Uses consistent WITA timezone formatting
 * - Maintains existing color scheme and badge styling
 * - Provides clear, contextual information
 */
export function AttendanceTimeTooltip({
  displayValue,
  timeValue,
  variant,
  attendanceType,
  showTooltip = false,
  reason,
}: AttendanceTimeTooltipProps) {
  const formattedTime = showTooltip ? formatTimeForTooltip(timeValue) : null
  const hasTimeInfo = formattedTime !== null

  // If no tooltip should be shown or no time info available, render plain badge
  if (!showTooltip || !hasTimeInfo) {
    return <Badge variant={variant}>{displayValue}</Badge>
  }

  // Create tooltip content with time and optional reason
  const tooltipContent = (
    <div className="space-y-1">
      <div className="font-medium text-xs">
        {attendanceType}
      </div>
      <div className="text-sm">
        <span className="font-mono">{formattedTime}</span>
        <span className="text-xs text-muted-foreground ml-1">WITA</span>
      </div>
      {reason && (
        <div className="text-xs text-muted-foreground border-t pt-1 mt-1">
          <span className="font-medium">Alasan:</span> {reason}
        </div>
      )}
    </div>
  )

  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="cursor-help">
            <Badge variant={variant}>{displayValue}</Badge>
          </div>
        </TooltipTrigger>
        <TooltipContent 
          side="top" 
          className="max-w-xs bg-gray-900 text-white border-gray-700"
          sideOffset={8}
        >
          {tooltipContent}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

/**
 * Helper function to determine if tooltips should be shown based on date filter
 * @param dateFilter - Current date filter value
 * @returns Whether tooltips should be displayed
 */
export function shouldShowTimeTooltips(dateFilter: string): boolean {
  return ['today', 'yesterday'].includes(dateFilter)
}
