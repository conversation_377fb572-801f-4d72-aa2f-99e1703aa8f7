'use client'

import React, { useState } from 'react'
import { Download } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { ExcelExportDialog } from './ExcelExportDialog'
import { useExcelExportDialog } from '@/hooks/use-excel-export-dialog'

type ReportType = 'prayer' | 'school'

interface ReportExportButtonProps {
  reportType: ReportType
  rawData: any[]
  buttonText?: string
  buttonVariant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  disabled?: boolean
  className?: string
  availableClasses?: Array<{ id: string; name: string }>
}

/**
 * Reusable button component that opens the Excel export dialog
 */
export function ReportExportButton({
  reportType,
  rawData,
  buttonText,
  buttonVariant = 'outline',
  disabled = false,
  className,
  availableClasses = [],
}: ReportExportButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false)

  const { exportToExcel, isExporting, exportProgress } = useExcelExportDialog({
    onSuccess: (filename: string) => {
      console.log('Excel export successful:', filename)
      setDialogOpen(false)
    },
    onError: (error: string) => {
      console.error('Excel export failed:', error)
    },
  })

  const getDefaultButtonText = () => {
    if (reportType === 'prayer') {
      return 'Export Excel Shalat'
    } else {
      return 'Export Excel Sekolah'
    }
  }

  return (
    <>
      <Button
        variant={buttonVariant}
        onClick={() => setDialogOpen(true)}
        disabled={disabled || isExporting}
        className={className}
      >
        <Download className="mr-2 h-4 w-4" />
        {buttonText || getDefaultButtonText()}
      </Button>

      <ExcelExportDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        reportType={reportType}
        rawData={rawData}
        availableClasses={availableClasses}
        onExport={exportToExcel}
        isExporting={isExporting}
        exportProgress={exportProgress}
      />
    </>
  )
}

/**
 * Specialized button for prayer reports
 */
export function PrayerReportExportButton({
  rawData,
  availableClasses,
  ...props
}: Omit<ReportExportButtonProps, 'reportType'>) {
  return (
    <ReportExportButton
      {...props}
      reportType="prayer"
      rawData={rawData}
      availableClasses={availableClasses}
    />
  )
}

/**
 * Specialized button for school reports
 */
export function SchoolReportExportButton({
  rawData,
  availableClasses,
  ...props
}: Omit<ReportExportButtonProps, 'reportType'>) {
  return (
    <ReportExportButton
      {...props}
      reportType="school"
      rawData={rawData}
      availableClasses={availableClasses}
    />
  )
}
