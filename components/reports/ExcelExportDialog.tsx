'use client'

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'

import {
  Download,
  FileSpreadsheet,
  Calendar,
  Users,
  AlertCircle,
  CheckCircle,
  ChevronDown,
  FileText,
  Filter,
} from 'lucide-react'

// Form validation schema - Matrix format only (summary export removed)
const excelExportSchema = z.object({
  // Matrix format only - summary export functionality removed
  exportFormat: z.literal('matrix', {
    required_error: 'Format export harus matrix',
  }),
  // Matrix supports monthly and yearly only (custom removed with summary)
  dateRange: z.enum(['monthly', 'yearly'], {
    required_error: 'Pilih rentang tanggal',
  }),
  selectedMonth: z.number().min(1).max(12).optional(),
  selectedYear: z.number().min(2020).max(2030).optional(),
  customStartDate: z.date().optional(),
  customEndDate: z.date().optional(),
  selectedClasses: z.array(z.string()).default([]),
  includeMetadata: z.boolean().default(true),
  includeCharts: z.boolean().default(false),
  enableFormatting: z.boolean().default(true),
})

type FormData = z.infer<typeof excelExportSchema>

// Date range configurations - Matrix format only supports monthly and yearly
const dateRangeOptions = [
  {
    value: 'monthly',
    label: 'Bulanan',
    description: 'Data dalam 1 bulan tertentu (pilih bulan & tahun)',
    maxDays: 31,
    available: true,
  },
  {
    value: 'yearly',
    label: 'Tahunan',
    description: 'Data dalam 1 tahun tertentu (pilih tahun)',
    maxDays: 365,
    available: true,
  },
] as const

// Month options
const MONTH_OPTIONS = [
  { value: 1, label: 'Januari' },
  { value: 2, label: 'Februari' },
  { value: 3, label: 'Maret' },
  { value: 4, label: 'April' },
  { value: 5, label: 'Mei' },
  { value: 6, label: 'Juni' },
  { value: 7, label: 'Juli' },
  { value: 8, label: 'Agustus' },
  { value: 9, label: 'September' },
  { value: 10, label: 'Oktober' },
  { value: 11, label: 'November' },
  { value: 12, label: 'Desember' },
]

interface ExcelExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  reportType: 'prayer' | 'school' // Fixed based on context, not optional
  rawData: any[]
  availableClasses?: Array<{ id: string; name: string }>
  onExport: (config: ExcelExportConfig) => Promise<void>
  isExporting?: boolean
  exportProgress?: number
}

export interface ExcelExportConfig {
  reportType: 'prayer' | 'school'
  exportFormat: 'matrix' // Matrix format only - summary export removed
  dateRange: string
  selectedMonth?: number
  selectedYear?: number
  selectedClasses: string[]
  includeMetadata: boolean
  includeCharts: boolean
  enableFormatting: boolean
  rawData: any[]
  availableClasses?: Array<{ id: string; name: string }>
}

// Multi-select class component
const ClassMultiSelect = ({
  availableClasses,
  selectedClasses,
  onSelectionChange,
  isYearlyMatrix = false,
}: {
  availableClasses: Array<{ id: string; name: string }>
  selectedClasses: string[]
  onSelectionChange: (selected: string[]) => void
  isYearlyMatrix?: boolean
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleSelectAll = () => {
    if (selectedClasses.length === availableClasses.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(availableClasses.map(cls => cls.id))
    }
  }

  const handleClassToggle = (classId: string) => {
    if (selectedClasses.includes(classId)) {
      onSelectionChange(selectedClasses.filter(id => id !== classId))
    } else {
      onSelectionChange([...selectedClasses, classId])
    }
  }

  const getDisplayText = () => {
    if (selectedClasses.length === 0) {
      return isYearlyMatrix ? 'Pilih 1 kelas untuk Matrix tahunan...' : 'Pilih kelas...'
    }
    if (selectedClasses.length === availableClasses.length) return 'Semua kelas dipilih'
    if (selectedClasses.length === 1) {
      const selectedClass = availableClasses.find(cls => cls.id === selectedClasses[0])
      const className = selectedClass?.name || 'Kelas dipilih'
      return isYearlyMatrix ? `✅ ${className} (siap untuk Matrix tahunan)` : className
    }
    if (isYearlyMatrix && selectedClasses.length > 1) {
      return `⚠️ ${selectedClasses.length} kelas (Matrix tahunan perlu 1 kelas)`
    }
    return `${selectedClasses.length} kelas dipilih`
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={`h-10 w-full justify-between ${
            isYearlyMatrix
              ? selectedClasses.length === 1
                ? 'border-green-300 bg-green-50/50 text-green-700 dark:border-green-700 dark:bg-green-900/20 dark:text-green-300'
                : selectedClasses.length > 1
                  ? 'border-amber-300 bg-amber-50/50 text-amber-700 dark:border-amber-700 dark:bg-amber-900/20 dark:text-amber-300'
                  : 'border-blue-300 bg-blue-50/50 text-blue-700 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
              : ''
          }`}
        >
          <span className="truncate">{getDisplayText()}</span>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="max-h-60 overflow-auto">
          <div className="border-b p-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="select-all"
                checked={selectedClasses.length === availableClasses.length}
                onCheckedChange={handleSelectAll}
                disabled={isYearlyMatrix}
              />
              <label
                htmlFor="select-all"
                className={`text-sm font-medium ${isYearlyMatrix ? 'text-muted-foreground' : ''}`}
              >
                {isYearlyMatrix
                  ? `Pilih 1 kelas (Matrix tahunan)`
                  : `Pilih Semua (${availableClasses.length} kelas)`}
              </label>
            </div>
            {isYearlyMatrix && (
              <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                💡 Matrix tahunan hanya mendukung 1 kelas untuk performa optimal
              </p>
            )}
          </div>
          <div className="p-1">
            {availableClasses.map(cls => (
              <div
                key={cls.id}
                className="flex items-center space-x-2 rounded-sm px-2 py-1.5 hover:bg-accent"
              >
                <Checkbox
                  id={`class-${cls.id}`}
                  checked={selectedClasses.includes(cls.id)}
                  onCheckedChange={() => handleClassToggle(cls.id)}
                />
                <label htmlFor={`class-${cls.id}`} className="flex-1 cursor-pointer text-sm">
                  {cls.name}
                </label>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

export function ExcelExportDialog({
  open,
  onOpenChange,
  reportType, // Now required, not defaultReportType
  rawData,
  availableClasses = [],
  onExport,
  isExporting = false,
  exportProgress = 0,
}: ExcelExportDialogProps) {
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [dateRangeWarning, setDateRangeWarning] = useState<string>('')

  const form = useForm<FormData>({
    resolver: zodResolver(excelExportSchema),
    defaultValues: {
      // Matrix format only - summary export functionality removed
      exportFormat: 'matrix',
      dateRange: 'monthly', // Default to monthly as per specifications
      selectedMonth: new Date().getMonth() + 1,
      selectedYear: new Date().getFullYear(),
      selectedClasses: [],
      includeMetadata: true,
      includeCharts: false,
      enableFormatting: true,
    },
  })

  const watchedValues = form.watch()

  // Helper function to get yearly matrix requirement message
  const getYearlyMatrixRequirement = () => {
    if (watchedValues.exportFormat === 'matrix' && watchedValues.dateRange === 'yearly') {
      if (watchedValues.selectedClasses.length === 0) {
        return {
          type: 'info' as const,
          message:
            'Untuk laporan tahunan Matrix, silakan pilih tepat 1 kelas dari filter di bawah.',
        }
      } else if (watchedValues.selectedClasses.length > 1) {
        return {
          type: 'warning' as const,
          message: `Laporan tahunan Matrix hanya mendukung 1 kelas. Saat ini ${watchedValues.selectedClasses.length} kelas dipilih.`,
        }
      } else {
        return {
          type: 'success' as const,
          message: `✅ Siap! Laporan tahunan Matrix untuk kelas ${watchedValues.selectedClasses[0]}.`,
        }
      }
    }
    return null
  }

  // Helper function to check if export should be disabled and get tooltip message
  const getExportButtonState = () => {
    // Check basic validation first
    if (isExporting || validationErrors.length > 0 || !!dateRangeWarning) {
      return {
        disabled: true,
        tooltip: isExporting
          ? 'Export sedang berlangsung...'
          : validationErrors.length > 0
            ? validationErrors[0]
            : dateRangeWarning,
      }
    }

    // Check yearly matrix specific requirements
    if (watchedValues.exportFormat === 'matrix' && watchedValues.dateRange === 'yearly') {
      if (watchedValues.selectedClasses.length === 0) {
        return {
          disabled: true,
          tooltip: 'Pilih tepat 1 kelas untuk export laporan tahunan Matrix',
        }
      } else if (watchedValues.selectedClasses.length > 1) {
        return {
          disabled: true,
          tooltip: `Laporan tahunan Matrix hanya mendukung 1 kelas (saat ini ${watchedValues.selectedClasses.length} kelas dipilih)`,
        }
      }
    }

    return {
      disabled: false,
      tooltip: 'Klik untuk mengekspor laporan ke Excel',
    }
  }

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setValidationErrors([])
      setDateRangeWarning('')
      form.reset({
        // Matrix format only - summary export functionality removed
        exportFormat: 'matrix',
        dateRange: 'monthly', // Default to monthly as per specifications
        selectedMonth: new Date().getMonth() + 1,
        selectedYear: new Date().getFullYear(),
        selectedClasses: [],
        includeMetadata: true,
        includeCharts: false,
        enableFormatting: true,
      })
    }
  }, [open, reportType, form])

  // Enhanced validation logic with format-specific checks
  const validateForm = (data: FormData): string[] => {
    const errors: string[] = []

    // Matrix format validations
    // Check yearly matrix requirements
    if (data.dateRange === 'yearly' && data.selectedClasses.length !== 1) {
      if (data.selectedClasses.length === 0) {
        errors.push('Laporan tahunan Matrix memerlukan pemilihan tepat 1 kelas.')
      } else {
        errors.push(
          `Laporan tahunan Matrix hanya mendukung 1 kelas. Saat ini ${data.selectedClasses.length} kelas dipilih.`
        )
      }
    }

    // General validations
    if (data.dateRange === 'monthly' && !data.selectedMonth) {
      errors.push('Pilih bulan untuk laporan bulanan')
    }

    if (['monthly', 'yearly'].includes(data.dateRange) && !data.selectedYear) {
      errors.push('Pilih tahun untuk laporan')
    }

    if (data.selectedYear && (data.selectedYear < 2020 || data.selectedYear > 2030)) {
      errors.push('Tahun harus antara 2020-2030')
    }

    // Matrix format performance warning for large datasets
    if (rawData.length > 5000) {
      errors.push(
        'Format Matrix dengan data besar (>5000 siswa) mungkin memerlukan waktu lama. Pertimbangkan filter kelas untuk performa optimal.'
      )
    }

    if (rawData.length === 0) {
      errors.push('Tidak ada data untuk diekspor')
    }

    return errors
  }

  // Calculate estimated records
  const getEstimatedRecords = (): number => {
    // Filter by selected classes if any
    let filteredData = rawData
    if (watchedValues.selectedClasses.length > 0) {
      // Convert class IDs to class names for filtering
      const selectedClassNames = watchedValues.selectedClasses
        .map(classId => {
          const classObj = availableClasses.find(cls => cls.id === classId)
          return classObj?.name
        })
        .filter((name): name is string => name !== undefined)

      filteredData = rawData.filter(item => selectedClassNames.includes(item.className || ''))
    }

    // For matrix format, show student count (more efficient)
    if (watchedValues.exportFormat === 'matrix') {
      // Matrix format: 1 row per student per period
      const uniqueStudents = new Set(filteredData.map(item => item.nis || item.uniqueCode)).size
      return uniqueStudents
    }

    // For summary format, show actual record count
    return filteredData.length
  }

  // Generate preview text
  const getPreviewText = (): string => {
    const typeLabel = reportType === 'prayer' ? 'Shalat' : 'Sekolah'
    const formatLabel =
      watchedValues.exportFormat === 'matrix' ? 'Matrix Report' : 'Ringkasan/Rekap'
    const dateOption = dateRangeOptions.find(opt => opt.value === watchedValues.dateRange)
    const dateLabel = dateOption?.label || ''
    const estimatedRecords = getEstimatedRecords()

    let periodText = dateLabel
    if (
      watchedValues.dateRange === 'monthly' &&
      watchedValues.selectedMonth &&
      watchedValues.selectedYear
    ) {
      const monthLabel = MONTH_OPTIONS.find(m => m.value === watchedValues.selectedMonth)?.label
      periodText = `${monthLabel} ${watchedValues.selectedYear}`
    } else if (watchedValues.dateRange === 'yearly' && watchedValues.selectedYear) {
      periodText = `Tahun ${watchedValues.selectedYear}`
    }

    // Class filter info
    let classInfo = ''
    if (watchedValues.selectedClasses.length === 0) {
      classInfo = 'Semua kelas'
    } else if (watchedValues.selectedClasses.length === availableClasses.length) {
      classInfo = 'Semua kelas'
    } else {
      classInfo = `${watchedValues.selectedClasses.length} kelas dipilih`
    }

    let additionalInfo = ''
    if (watchedValues.exportFormat === 'matrix') {
      // Special handling for yearly matrix with 1 class
      if (watchedValues.dateRange === 'yearly' && watchedValues.selectedClasses.length === 1) {
        const selectedClassName =
          availableClasses.find(cls => cls.id === watchedValues.selectedClasses[0])?.name ||
          'kelas dipilih'
        additionalInfo = ` (Format Matrix Tahunan: ${selectedClassName})`
      } else {
        const totalRecords = rawData.length
        const efficiency =
          totalRecords > estimatedRecords
            ? ` (${Math.round((1 - estimatedRecords / totalRecords) * 100)}% lebih efisien)`
            : ''
        additionalInfo = ` (Format Matrix: ${estimatedRecords} baris siswa${efficiency})`
      }
    }

    return `Laporan ${typeLabel} ${formatLabel} untuk periode ${periodText}. Filter: ${classInfo}${additionalInfo}.`
  }

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    try {
      setValidationErrors([])

      // Validate form
      const errors = validateForm(data)
      if (errors.length > 0) {
        setValidationErrors(errors)
        return
      }

      // Build export configuration - Matrix format only
      const config: ExcelExportConfig = {
        reportType: reportType, // Use reportType from context, not from form
        exportFormat: data.exportFormat,
        dateRange: data.dateRange,
        selectedMonth: data.selectedMonth,
        selectedYear: data.selectedYear,
        selectedClasses: data.selectedClasses,
        includeMetadata: data.includeMetadata,
        includeCharts: data.includeCharts,
        enableFormatting: data.enableFormatting,
        rawData,
        availableClasses,
      }

      await onExport(config)
    } catch (error) {
      setValidationErrors([
        error instanceof Error ? error.message : 'Terjadi kesalahan saat mengekspor laporan',
      ])
    }
  }

  // Show month/year selectors - Matrix format only supports monthly and yearly
  const showMonthSelector = watchedValues.dateRange === 'monthly'
  const showYearSelector = ['monthly', 'yearly'].includes(watchedValues.dateRange)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[85vh] overflow-y-auto sm:max-w-lg">
        <DialogHeader className="space-y-2 pb-4">
          <DialogTitle className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-50 dark:bg-green-900/20">
              <FileSpreadsheet className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Export Laporan Excel
              </h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Unduh laporan kehadiran dalam format Excel
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-inside list-disc space-y-1">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Date Range Warning */}
            {dateRangeWarning && (
              <Alert variant="default" className="border-amber-200 bg-amber-50 text-amber-800">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <AlertDescription className="text-amber-800">{dateRangeWarning}</AlertDescription>
              </Alert>
            )}

            {/* Report Type Display */}
            <div className="flex items-center justify-between rounded-lg border bg-gray-50/50 p-3 dark:bg-gray-800/50">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Jenis Laporan
                </span>
              </div>
              <Badge
                variant="secondary"
                className={`flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium ${
                  reportType === 'prayer'
                    ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                    : 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                }`}
              >
                <div
                  className={`h-1.5 w-1.5 rounded-full ${
                    reportType === 'prayer' ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                ></div>
                {reportType === 'prayer' ? 'Laporan Shalat' : 'Laporan Sekolah'}
              </Badge>
            </div>

            {/* Configuration Section */}
            <div className="space-y-4">
              {/* Format Info - Matrix Only */}
              <div className="rounded-md border border-blue-200 bg-blue-50/50 p-3 dark:border-blue-800 dark:bg-blue-900/10">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    Format Matrix Report
                  </span>
                </div>
                <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                  Format matrix dengan hover details untuk analisis mendalam
                </p>
              </div>

              {/* Date Range Selection */}
              <div className="space-y-2">
                <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  Periode Laporan
                </label>
                <FormField
                  control={form.control}
                  name="dateRange"
                  render={({ field }) => (
                    <FormItem>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-10">
                            <SelectValue placeholder="Pilih rentang tanggal">
                              {field.value && (
                                <span className="font-medium">
                                  {dateRangeOptions.find(opt => opt.value === field.value)?.label}
                                </span>
                              )}
                            </SelectValue>
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {dateRangeOptions.map(option => (
                            <SelectItem
                              key={option.value}
                              value={option.value}
                              className="cursor-pointer"
                            >
                              <div className="py-1">
                                <div className="flex items-center gap-2 font-medium">
                                  {option.label}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {option.description}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Contextual Guidance for Yearly Matrix */}
              {(() => {
                const requirement = getYearlyMatrixRequirement()
                if (!requirement) return null

                const bgColor = {
                  info: 'bg-blue-50/50 border-blue-200 dark:border-blue-800 dark:bg-blue-900/10',
                  warning:
                    'bg-amber-50/50 border-amber-200 dark:border-amber-800 dark:bg-amber-900/10',
                  success:
                    'bg-green-50/50 border-green-200 dark:border-green-800 dark:bg-green-900/10',
                }[requirement.type]

                const textColor = {
                  info: 'text-blue-700 dark:text-blue-300',
                  warning: 'text-amber-700 dark:text-amber-300',
                  success: 'text-green-700 dark:text-green-300',
                }[requirement.type]

                return (
                  <div className={`rounded-md border p-3 ${bgColor}`}>
                    <p className={`text-sm ${textColor}`}>{requirement.message}</p>
                  </div>
                )
              })()}
            </div>

            {/* Month/Year Selectors */}
            {(showMonthSelector || showYearSelector) && (
              <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                {showMonthSelector && (
                  <FormField
                    control={form.control}
                    name="selectedMonth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Bulan
                        </FormLabel>
                        <Select
                          onValueChange={value => field.onChange(parseInt(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger className="h-10">
                              <SelectValue placeholder="Pilih bulan" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {MONTH_OPTIONS.map(month => (
                              <SelectItem key={month.value} value={month.value.toString()}>
                                {month.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {showYearSelector && (
                  <FormField
                    control={form.control}
                    name="selectedYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Tahun
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="2020"
                            max="2030"
                            placeholder="2024"
                            className="h-10"
                            {...field}
                            onChange={e => field.onChange(parseInt(e.target.value) || undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}

            {/* Class Filter */}
            {availableClasses.length > 0 && (
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="selectedClasses"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        <Filter className="h-4 w-4 text-gray-500" />
                        Filter Kelas
                      </FormLabel>
                      <FormControl>
                        <ClassMultiSelect
                          availableClasses={availableClasses}
                          selectedClasses={field.value}
                          onSelectionChange={field.onChange}
                          isYearlyMatrix={
                            watchedValues.exportFormat === 'matrix' &&
                            watchedValues.dateRange === 'yearly'
                          }
                        />
                      </FormControl>
                      <div className="space-y-1">
                        {(() => {
                          // Dynamic guidance based on format and date range
                          if (
                            watchedValues.exportFormat === 'matrix' &&
                            watchedValues.dateRange === 'yearly'
                          ) {
                            return (
                              <div className="rounded-md bg-blue-50 p-2 dark:bg-blue-900/20">
                                <p className="text-xs font-medium text-blue-700 dark:text-blue-300">
                                  📋 Laporan Tahunan Matrix
                                </p>
                                <p className="text-xs text-blue-600 dark:text-blue-400">
                                  Pilih tepat 1 kelas untuk melanjutkan export
                                </p>
                              </div>
                            )
                          } else if (watchedValues.exportFormat === 'matrix') {
                            return (
                              <p className="text-xs text-muted-foreground">
                                Kosongkan untuk semua kelas, atau pilih kelas tertentu
                              </p>
                            )
                          } else {
                            return (
                              <p className="text-xs text-muted-foreground">
                                Kosongkan untuk mengekspor semua kelas
                              </p>
                            )
                          }
                        })()}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Preview */}
            <div className="rounded-lg border border-green-200 bg-green-50/50 p-3 dark:border-green-800 dark:bg-green-900/10">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="text-sm font-medium text-green-700 dark:text-green-300">
                  Siap untuk Export
                </span>
              </div>
              <p className="mt-1 text-xs text-green-600 dark:text-green-400">{getPreviewText()}</p>
              <div className="mt-2 flex flex-wrap items-center gap-1.5">
                <Badge
                  variant="outline"
                  className="border-green-300 bg-white text-green-700 dark:border-green-700 dark:bg-green-900/20 dark:text-green-300"
                >
                  <FileSpreadsheet className="mr-1 h-3 w-3" />
                  {getEstimatedRecords()} record
                </Badge>
                <Badge
                  variant="outline"
                  className="border-green-300 bg-white text-green-700 dark:border-green-700 dark:bg-green-900/20 dark:text-green-300"
                >
                  Excel (.xlsx)
                </Badge>
                {watchedValues.selectedClasses.length > 0 && (
                  <Badge
                    variant="outline"
                    className="border-green-300 bg-white text-green-700 dark:border-green-700 dark:bg-green-900/20 dark:text-green-300"
                  >
                    {watchedValues.selectedClasses.length} kelas
                  </Badge>
                )}
              </div>
            </div>

            {/* Export Progress */}
            {isExporting && (
              <div className="rounded-lg border bg-blue-50/50 p-3 dark:border-blue-800 dark:bg-blue-900/10">
                <div className="flex items-center justify-between text-sm font-medium">
                  <span className="text-blue-700 dark:text-blue-400">Mengekspor laporan...</span>
                  <span className="text-blue-600 dark:text-blue-400">
                    {Math.round(exportProgress)}%
                  </span>
                </div>
                <Progress value={exportProgress} className="mt-2 h-2" />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isExporting}
                className="h-9 px-4"
              >
                Batal
              </Button>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="submit"
                      disabled={getExportButtonState().disabled}
                      className="h-9 bg-green-600 px-4 hover:bg-green-700 disabled:cursor-not-allowed"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      {isExporting ? 'Mengekspor...' : 'Export Excel'}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-xs">
                    <p className="text-sm">{getExportButtonState().tooltip}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
