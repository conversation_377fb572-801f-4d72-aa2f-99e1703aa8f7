/**
 * School Attendance Status Component
 *
 * Main component for displaying comprehensive school attendance status
 * with proper loading states, error handling, and responsive design.
 */

'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { useBasicSchoolAttendance, useSchoolAttendance } from '@/lib/hooks/useSchoolAttendance'
import {
  convertSchoolAttendanceToItems,
  groupSchoolAttendanceItems,
  isSchoolAttendanceEmpty,
  calculateSchoolAttendanceSummary,
} from '@/lib/utils/school-attendance'
import {
  SchoolAttendanceSection,
  SchoolAttendanceEmpty,
  SchoolAttendanceLoading,
  SchoolAttendanceError,
} from './SchoolAttendanceItem'

/**
 * Props for SchoolAttendanceStatus component
 */
interface SchoolAttendanceStatusProps {
  uniqueCode: string
  attendanceData?: any // External attendance data to prevent duplicate API calls
  loading?: boolean // External loading state
  showTime?: boolean
  showReason?: boolean
  compact?: boolean
  className?: string
  onRefresh?: () => void
}

/**
 * Main school attendance status component
 */
export function SchoolAttendanceStatus({
  uniqueCode,
  attendanceData,
  loading: externalLoading,
  showTime = true,
  showReason = true,
  compact = false,
  className = '',
  onRefresh,
}: SchoolAttendanceStatusProps) {
  // Use external data if provided, otherwise fetch with hook
  const shouldFetchData = !attendanceData

  const {
    data: hookData,
    loading: hookLoading,
    error,
    refresh,
    hasData: hookHasData,
    isEmpty: hookIsEmpty,
    isRefreshing,
    isInitialLoading: hookIsInitialLoading,
    hasError,
    lastUpdated,
  } = useSchoolAttendance({
    uniqueCode,
    autoRefresh: shouldFetchData, // Only auto-refresh if not using external data
    refreshInterval: shouldFetchData ? 60000 : 0, // 60 seconds or disabled
  })

  // Use external data if provided, otherwise use hook data
  const data = attendanceData || hookData
  const loading = externalLoading !== undefined ? externalLoading : hookLoading
  const hasData = attendanceData ? !!attendanceData : hookHasData
  const isEmpty = attendanceData ? !attendanceData : hookIsEmpty
  const isInitialLoading = externalLoading !== undefined ? externalLoading : hookIsInitialLoading

  // Handle refresh action
  const handleRefresh = () => {
    refresh()
    onRefresh?.()
  }

  // Show loading state for initial load
  if (isInitialLoading) {
    return (
      <div className={className}>
        <SchoolAttendanceLoading itemCount={6} compact={compact} />
      </div>
    )
  }

  // Show error state
  if (hasError && error) {
    return (
      <div className={className}>
        <SchoolAttendanceError error={error} onRetry={handleRefresh} />
      </div>
    )
  }

  // Show empty state if no data
  if (!hasData || !data || isEmpty) {
    return (
      <div className={className}>
        <SchoolAttendanceEmpty />
      </div>
    )
  }

  // Convert data to display items
  const items = convertSchoolAttendanceToItems(data)
  const sections = groupSchoolAttendanceItems(items)
  const summary = calculateSchoolAttendanceSummary(data)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-100">
            Status Absensi Sekolah
          </h3>
          {lastUpdated && (
            <p className="text-xs text-slate-500 dark:text-slate-400">
              Terakhir diperbarui: {lastUpdated.toLocaleTimeString('id-ID')}
            </p>
          )}
        </div>

        <Button
          variant="ghost"
          size="icon"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="h-8 w-8"
          title="Perbarui status"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={`${isRefreshing ? 'animate-spin' : ''}`}
          >
            <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
            <path d="M3 3v5h5" />
            <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
            <path d="M16 16h5v5" />
          </svg>
        </Button>
      </div>

      {/* Summary stats (optional, for compact mode) */}
      {compact && summary.completedItems > 0 && (
        <div className="rounded-lg bg-slate-50 p-3 dark:bg-slate-800/50">
          <p className="text-sm text-slate-600 dark:text-slate-300">
            <span className="font-medium">{summary.completedItems}</span> dari{' '}
            <span className="font-medium">{summary.totalItems}</span> status tercatat
            {summary.completionRate > 0 && (
              <span className="ml-2 text-xs text-slate-500">({summary.completionRate}%)</span>
            )}
          </p>
        </div>
      )}

      {/* Attendance sections */}
      <div className="space-y-6">
        {sections.map(section => (
          <SchoolAttendanceSection
            key={section.id}
            title={section.title}
            items={section.items}
            showTime={showTime}
            showReason={showReason}
            compact={compact}
          />
        ))}
      </div>

      {/* Loading overlay for refresh */}
      {isRefreshing && (
        <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-white/80 dark:bg-slate-900/80">
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-300">
            <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-indigo-600"></div>
            Memperbarui...
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Props for SchoolAttendanceCard component
 */
interface SchoolAttendanceCardProps {
  uniqueCode: string
  title?: string
  showTime?: boolean
  showReason?: boolean
  compact?: boolean
  className?: string
}

/**
 * Card wrapper component for school attendance status
 */
export function SchoolAttendanceCard({
  uniqueCode,
  title = 'Status Sekolah Hari Ini',
  showTime = true,
  showReason = true,
  compact = false,
  className = '',
}: SchoolAttendanceCardProps) {
  return (
    <div className={`rounded-lg border bg-white p-6 shadow-sm dark:bg-slate-900 ${className}`}>
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-slate-800 dark:text-slate-100">{title}</h2>
      </div>

      <SchoolAttendanceStatus
        uniqueCode={uniqueCode}
        showTime={showTime}
        showReason={showReason}
        compact={compact}
      />
    </div>
  )
}

/**
 * Props for SchoolAttendanceQuickView component
 */
interface SchoolAttendanceQuickViewProps {
  uniqueCode: string
  className?: string
}

/**
 * Quick view component showing only primary school attendance status
 */
export function SchoolAttendanceQuickView({
  uniqueCode,
  className = '',
}: SchoolAttendanceQuickViewProps) {
  const { data, loading, error } = useBasicSchoolAttendance(uniqueCode)

  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-indigo-600"></div>
        <span className="text-sm text-slate-500">Memuat...</span>
      </div>
    )
  }

  if (error || !data) {
    return <div className={`text-sm text-red-500 ${className}`}>Gagal memuat status</div>
  }

  // Determine primary status
  let primaryStatus = 'Belum ada data'
  let statusColor = 'text-slate-500'

  if (data.entry) {
    primaryStatus = 'Masuk'
    statusColor = 'text-green-600'
  } else if (data.lateEntry) {
    primaryStatus = 'Terlambat'
    statusColor = 'text-yellow-600'
  } else if (data.excusedAbsence) {
    primaryStatus = 'Izin'
    statusColor = 'text-blue-600'
  } else if (data.sick) {
    primaryStatus = 'Sakit'
    statusColor = 'text-blue-600'
  } else if (data.temporaryLeave) {
    primaryStatus = 'Izin Keluar'
    statusColor = 'text-blue-600'
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className={`h-2 w-2 rounded-full ${statusColor.replace('text-', 'bg-')}`} />
      <span className={`text-sm font-medium ${statusColor}`}>{primaryStatus}</span>
    </div>
  )
}

/**
 * Export all components
 */
export default SchoolAttendanceStatus
