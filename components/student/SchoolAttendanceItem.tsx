/**
 * School Attendance Item Component
 *
 * Reusable component for displaying individual school attendance status items
 * following existing design patterns and best practices.
 */

'use client'

import React from 'react'
import {
  CheckCircle,
  XCircle,
  LogIn,
  Clock,
  FileText,
  Heart,
  ArrowRight,
  ArrowLeft,
  LucideIcon,
} from 'lucide-react'
import { SchoolAttendanceItem as SchoolAttendanceItemType } from '@/lib/types/student-attendance'

/**
 * Icon mapping for school attendance types
 */
const ICON_MAP: Record<string, LucideIcon> = {
  LogIn,
  Clock,
  FileText,
  Heart,
  ArrowRight,
  ArrowLeft,
}

/**
 * Props for SchoolAttendanceItem component
 */
interface SchoolAttendanceItemProps {
  item: SchoolAttendanceItemType
  showTime?: boolean
  showReason?: boolean
  compact?: boolean
  className?: string
}

/**
 * Individual school attendance status item component
 */
export function SchoolAttendanceItem({
  item,
  showTime = true,
  showReason = true,
  compact = false,
  className = '',
}: SchoolAttendanceItemProps) {
  // Get the appropriate icon component
  const IconComponent = ICON_MAP[item.icon] || FileText

  // Determine status icon and colors
  const StatusIcon = item.status ? CheckCircle : XCircle
  const statusColor = item.status ? 'text-green-500' : 'text-slate-400'

  // Determine background color based on variant
  const getBackgroundColor = () => {
    if (!item.status) return 'bg-slate-100 dark:bg-slate-800'

    switch (item.variant) {
      case 'success':
        return 'bg-green-50 dark:bg-green-900/20'
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/20'
      case 'info':
        return 'bg-blue-50 dark:bg-blue-900/20'
      default:
        return 'bg-slate-100 dark:bg-slate-800'
    }
  }

  // Determine icon color based on variant
  const getIconColor = () => {
    if (!item.status) return 'text-slate-400 dark:text-slate-500'

    switch (item.variant) {
      case 'success':
        return 'text-green-600 dark:text-green-400'
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400'
      case 'info':
        return 'text-blue-600 dark:text-blue-400'
      default:
        return 'text-slate-600 dark:text-slate-400'
    }
  }

  return (
    <div
      className={`flex items-center justify-between rounded-lg p-3 transition-colors ${getBackgroundColor()} ${className} `}
    >
      {/* Left side: Icon and label */}
      <div className="flex items-center gap-3">
        {/* Attendance type icon */}
        <div
          className={`rounded-full p-2 ${
            item.status ? 'bg-white dark:bg-slate-700' : 'bg-slate-200 dark:bg-slate-700'
          } `}
        >
          <IconComponent className={`h-4 w-4 ${getIconColor()}`} />
        </div>

        {/* Label and details */}
        <div className={compact ? 'space-y-0' : 'space-y-1'}>
          <h3 className="font-medium text-slate-800 dark:text-slate-100">{item.label}</h3>

          {/* Time display */}
          {showTime && item.time && !compact && (
            <p className="text-xs text-slate-500 dark:text-slate-400">{item.time} WITA</p>
          )}

          {/* Reason display */}
          {showReason && item.reason && !compact && (
            <p className="text-xs italic text-slate-600 dark:text-slate-300">{item.reason}</p>
          )}

          {/* Compact mode: show time inline */}
          {compact && item.time && (
            <p className="text-xs text-slate-500 dark:text-slate-400">{item.time}</p>
          )}
        </div>
      </div>

      {/* Right side: Status indicator */}
      <StatusIcon className={`h-6 w-6 ${statusColor}`} />
    </div>
  )
}

/**
 * Props for SchoolAttendanceSection component
 */
interface SchoolAttendanceSectionProps {
  title: string
  items: SchoolAttendanceItemType[]
  showTime?: boolean
  showReason?: boolean
  compact?: boolean
  className?: string
}

/**
 * Section component for grouping related school attendance items
 */
export function SchoolAttendanceSection({
  title,
  items,
  showTime = true,
  showReason = true,
  compact = false,
  className = '',
}: SchoolAttendanceSectionProps) {
  if (items.length === 0) return null

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Section title */}
      <h4 className="text-sm font-semibold uppercase tracking-wide text-slate-700 dark:text-slate-300">
        {title}
      </h4>

      {/* Section items */}
      <div className="space-y-2">
        {items.map(item => (
          <SchoolAttendanceItem
            key={item.id}
            item={item}
            showTime={showTime}
            showReason={showReason}
            compact={compact}
          />
        ))}
      </div>
    </div>
  )
}

/**
 * Props for SchoolAttendanceGrid component
 */
interface SchoolAttendanceGridProps {
  items: SchoolAttendanceItemType[]
  showTime?: boolean
  showReason?: boolean
  compact?: boolean
  columns?: 1 | 2 | 3
  className?: string
}

/**
 * Grid layout component for school attendance items
 */
export function SchoolAttendanceGrid({
  items,
  showTime = true,
  showReason = true,
  compact = false,
  columns = 1,
  className = '',
}: SchoolAttendanceGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
  }

  return (
    <div className={`grid gap-3 ${gridCols[columns]} ${className}`}>
      {items.map(item => (
        <SchoolAttendanceItem
          key={item.id}
          item={item}
          showTime={showTime}
          showReason={showReason}
          compact={compact}
        />
      ))}
    </div>
  )
}

/**
 * Props for SchoolAttendanceEmpty component
 */
interface SchoolAttendanceEmptyProps {
  message?: string
  className?: string
}

/**
 * Empty state component for when no school attendance data is available
 */
export function SchoolAttendanceEmpty({
  message = 'Belum ada data absensi sekolah hari ini',
  className = '',
}: SchoolAttendanceEmptyProps) {
  return (
    <div className={`flex flex-col items-center justify-center py-8 text-center ${className} `}>
      <div className="rounded-full bg-slate-100 p-3 dark:bg-slate-800">
        <FileText className="h-8 w-8 text-slate-400" />
      </div>
      <p className="mt-4 text-sm text-slate-500 dark:text-slate-400">{message}</p>
    </div>
  )
}

/**
 * Props for SchoolAttendanceLoading component
 */
interface SchoolAttendanceLoadingProps {
  itemCount?: number
  compact?: boolean
  className?: string
}

/**
 * Loading skeleton component for school attendance items
 */
export function SchoolAttendanceLoading({
  itemCount = 3,
  compact = false,
  className = '',
}: SchoolAttendanceLoadingProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <div
          key={index}
          className="flex items-center justify-between rounded-lg bg-slate-100 p-3 dark:bg-slate-800"
        >
          {/* Left side skeleton */}
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 animate-pulse rounded-full bg-slate-200 dark:bg-slate-700" />
            <div className="space-y-1">
              <div className="h-4 w-24 animate-pulse rounded bg-slate-200 dark:bg-slate-700" />
              {!compact && (
                <div className="h-3 w-16 animate-pulse rounded bg-slate-200 dark:bg-slate-700" />
              )}
            </div>
          </div>

          {/* Right side skeleton */}
          <div className="h-6 w-6 animate-pulse rounded-full bg-slate-200 dark:bg-slate-700" />
        </div>
      ))}
    </div>
  )
}

/**
 * Props for SchoolAttendanceError component
 */
interface SchoolAttendanceErrorProps {
  error: string
  onRetry?: () => void
  className?: string
}

/**
 * Error state component for school attendance
 */
export function SchoolAttendanceError({
  error,
  onRetry,
  className = '',
}: SchoolAttendanceErrorProps) {
  return (
    <div className={`flex flex-col items-center justify-center py-8 text-center ${className} `}>
      <div className="rounded-full bg-red-100 p-3 dark:bg-red-900/20">
        <XCircle className="h-8 w-8 text-red-500" />
      </div>
      <p className="mt-4 text-sm text-red-600 dark:text-red-400">{error}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="mt-3 rounded-md bg-red-600 px-3 py-1 text-xs text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600"
        >
          Coba Lagi
        </button>
      )}
    </div>
  )
}
