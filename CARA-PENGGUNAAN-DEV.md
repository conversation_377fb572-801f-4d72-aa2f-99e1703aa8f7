# 🎯 Cara Penggunaan PostgreSQL Development ShalatYuk

## 🚀 Quick Start (Sudah Siap Digunakan!)

Berdasarkan output yang Anda berikan, setup sudah berhasil! Berikut cara menggunakannya:

### ✅ Status Saat Ini

- ✅ Database migration berhasil
- ✅ Development server berjalan di http://localhost:3000
- ✅ PostgreSQL siap digunakan

## 📋 Panduan Lengkap Penggunaan

### 1. 🔄 Setup Awal (Hanya Sekali)

```bash
# Setup environment development
./scripts/dev-setup.sh

# Jalankan database migration
npm run db:migrate

# Start development server
npm run dev
```

### 2. 🌐 Akses Aplikasi

Setelah setup, Anda dapat mengakses:

- **🌐 Aplikasi ShalatYuk**: http://localhost:3000
- **🗄️ Database GUI (Adminer)**: http://localhost:8080
- **📊 PostgreSQL**: localhost:5433
- **🔄 Redis**: localhost:6380

### 3. 🗄️ Mengakses Database

#### Via Adminer (GUI - Mudah)

1. Buka http://localhost:8080
2. <PERSON>gin dengan:
   - **System**: PostgreSQL
   - **Server**: `postgres-dev` (PENTING: gunakan nama container, bukan localhost!)
   - **Username**: shalatdev
   - **Password**: shalatdev123
   - **Database**: shalat_yuk_dev (opsional)

**⚠️ CATATAN PENTING**: Jangan gunakan `localhost` di field Server! Gunakan `postgres-dev` karena Adminer berjalan di dalam container Docker.

#### Via Command Line

```bash
# Akses PostgreSQL CLI
docker exec -it shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev

# Contoh query
SELECT * FROM users LIMIT 5;
\dt  # List semua tables
\q   # Keluar
```

#### Via External Tool (TablePlus, DBeaver, etc.)

```
Host: localhost
Port: 5433
Database: shalat_yuk_dev
Username: shalatdev
Password: shalatdev123
```

## 🔧 Commands Harian Development

### Menjalankan Development

```bash
# Start semua containers development
docker compose -f docker-compose.dev.yml up -d

# Start development server Next.js
npm run dev

# Akses aplikasi di http://localhost:3000
```

### Database Operations

```bash
# Run migration baru
npm run db:migrate

# Generate migration dari schema changes
npm run db:generate

# Reset database (hati-hati - hapus semua data)
npm run db:reset

# Load dummy data
npm run db:seed
```

### Docker Management

```bash
# Lihat status containers
docker ps

# Stop semua development containers
docker compose -f docker-compose.dev.yml down

# Restart PostgreSQL saja
docker compose -f docker-compose.dev.yml restart postgres-dev

# Restart Adminer jika ada masalah koneksi
docker compose -f docker-compose.dev.yml restart adminer

# Lihat logs
docker compose -f docker-compose.dev.yml logs -f

# Lihat logs PostgreSQL saja
docker compose -f docker-compose.dev.yml logs -f postgres-dev
```

## 🛠️ Workflow Development Harian

### Pagi - Mulai Development

```bash
# 1. Start containers (jika belum jalan)
docker compose -f docker-compose.dev.yml up -d

# 2. Check database connection
docker exec shalatYuk-postgres-dev pg_isready -U shalatdev

# 3. Start development server
npm run dev
```

### Sore - Selesai Development

```bash
# Stop development server (Ctrl+C)

# (Optional) Stop containers untuk save resource
docker compose -f docker-compose.dev.yml down
```

## 🔍 Testing & Debugging

### Cek Status Services

```bash
# Cek semua containers berjalan
docker ps

# Cek connection ke PostgreSQL
docker exec shalatYuk-postgres-dev pg_isready -U shalatdev -d shalat_yuk_dev

# Cek Redis connection
docker exec shalatYuk-redis-dev redis-cli ping

# Test network connectivity Adminer ke PostgreSQL
docker exec shalatYuk-adminer ping postgres-dev
```

### Debug Database Issues

```bash
# Lihat logs PostgreSQL
docker compose -f docker-compose.dev.yml logs postgres-dev

# Restart database jika ada masalah
docker compose -f docker-compose.dev.yml restart postgres-dev

# Reset complete database
docker compose -f docker-compose.dev.yml down -v
docker compose -f docker-compose.dev.yml up -d
npm run db:migrate
```

### 🚨 Troubleshooting Adminer

Jika Adminer menampilkan error "connection refused":

1. **Pastikan menggunakan kredensial yang benar**:

   ```
   System: PostgreSQL
   Server: postgres-dev (BUKAN localhost!)
   Username: shalatdev
   Password: shalatdev123
   Database: shalat_yuk_dev
   ```

2. **Restart Adminer container**:

   ```bash
   docker compose -f docker-compose.dev.yml restart adminer
   ```

3. **Cek network connectivity**:

   ```bash
   docker exec shalatYuk-adminer ping postgres-dev
   ```

4. **Lihat panduan lengkap**: Baca `ADMINER-SETUP-GUIDE.md`

## 📊 Management Data Development

### Backup Data Development

```bash
# Backup database
docker exec shalatYuk-postgres-dev pg_dump -U shalatdev shalat_yuk_dev > backup_$(date +%Y%m%d).sql

# Backup dengan compress
docker exec shalatYuk-postgres-dev pg_dump -U shalatdev shalat_yuk_dev | gzip > backup_$(date +%Y%m%d).sql.gz
```

### Restore Data

```bash
# Restore dari backup
docker exec -i shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev < backup_20241201.sql

# Restore dari compressed backup
gunzip -c backup_20241201.sql.gz | docker exec -i shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev
```

### Load Fresh Dummy Data

```bash
# Reset database dan load dummy data
npm run db:reset
npm run db:seed

# Atau manual load dummy data
docker exec -i shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev < dummy-data.sql
```

## 🚨 Troubleshooting Common Issues

### 1. Container tidak bisa start

```bash
# Cek apakah port sudah digunakan
lsof -i :5433  # PostgreSQL
lsof -i :6380  # Redis
lsof -i :8080  # Adminer

# Stop process yang menggunakan port
kill -9 <PID>

# Atau ubah port di docker-compose.dev.yml
```

### 2. Database connection error

```bash
# Reset containers
docker compose -f docker-compose.dev.yml down -v
docker compose -f docker-compose.dev.yml up -d

# Wait dan test connection
sleep 10
docker exec shalatYuk-postgres-dev pg_isready -U shalatdev
```

### 3. Migration error

```bash
# Check migration status
npx drizzle-kit push:pg

# Force migration
npm run db:reset
npm run db:migrate
```

### 4. Development server tidak bisa connect ke database

```bash
# Check environment variables
cat .env.local

# Pastikan DATABASE_URL benar
DATABASE_URL=postgres://shalatdev:shalatdev123@localhost:5433/shalat_yuk_dev

# Restart development server
# Ctrl+C
npm run dev
```

### 5. Adminer connection refused

**Masalah paling umum**: Menggunakan `localhost` sebagai server di Adminer.

**Solusi**:

- Server: `postgres-dev` (nama container)
- Username: `shalatdev`
- Password: `shalatdev123`
- Database: `shalat_yuk_dev`

Baca panduan lengkap di: `ADMINER-SETUP-GUIDE.md`

## 💡 Tips Development

### 1. Database GUI Tools

- **Adminer** (http://localhost:8080) - Ringan, web-based
  - Server: `postgres-dev` (untuk container)
- **TablePlus** - Native macOS app, lebih feature-rich
  - Host: `localhost:5433` (untuk external)
- **DBeaver** - Cross-platform, gratis
  - Host: `localhost:5433` (untuk external)
- **pgAdmin** - PostgreSQL specialist tool
  - Host: `localhost:5433` (untuk external)

### 2. Monitoring & Logs

```bash
# Monitor real-time logs semua services
docker compose -f docker-compose.dev.yml logs -f

# Monitor hanya PostgreSQL
docker compose -f docker-compose.dev.yml logs -f postgres-dev

# Monitor aplikasi Next.js
npm run dev
```

### 3. Performance Tips

- Gunakan Redis untuk caching session
- Index database queries yang sering digunakan
- Monitor slow queries via Adminer
- Use connection pooling di production

### 4. Security Development

- Jangan commit `.env.local` ke git
- Gunakan credentials berbeda untuk production
- Rotate JWT secrets regularly
- Monitor access logs

## 🔄 Reset Complete Environment

Jika ada masalah besar dan perlu reset total:

```bash
# 1. Stop semua containers
docker compose -f docker-compose.dev.yml down -v

# 2. Remove containers dan volumes
docker system prune -a --volumes

# 3. Setup ulang dari awal
./scripts/dev-setup.sh
npm run db:migrate
npm run dev
```

## 📞 Need Help?

Jika masih ada masalah:

1. **Check Logs**: `docker compose -f docker-compose.dev.yml logs`
2. **Check Container Status**: `docker ps -a`
3. **Check Port Usage**: `lsof -i :5433`
4. **Reset Environment**: Gunakan langkah reset di atas
5. **Baca Documentation**: `README-DEVELOPMENT.md`
6. **Adminer Issues**: Baca `ADMINER-SETUP-GUIDE.md`

---

## 🎯 Summary Commands

```bash
# Daily workflow
./scripts/dev-setup.sh    # Setup awal (sekali)
npm run dev              # Start development
docker compose -f docker-compose.dev.yml down  # Stop containers

# Database operations
npm run db:migrate       # Run migrations
npm run db:reset        # Reset database
docker exec -it shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev  # CLI access

# Troubleshooting
docker compose -f docker-compose.dev.yml logs -f  # View logs
docker compose -f docker-compose.dev.yml restart postgres-dev  # Restart DB
docker compose -f docker-compose.dev.yml restart adminer      # Restart Adminer
```

## 📋 Kredensial Reference

**Adminer (Container-to-Container):**

```
System: PostgreSQL
Server: postgres-dev
Username: shalatdev
Password: shalatdev123
Database: shalat_yuk_dev
```

**External Tools (Host-to-Container):**

```
Host: localhost
Port: 5433
Username: shalatdev
Password: shalatdev123
Database: shalat_yuk_dev
```

**Happy coding! 🚀**
