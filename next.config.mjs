/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  poweredByHeader: false,
  // Aktifkan mode standalone untuk deployment Docker
  output: 'standalone',
  trailingSlash: false,
  // Webpack configuration to handle Node.js modules in browser
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Fallback for Node.js modules in browser
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      }
    }

    // Suppress Edge Runtime warnings for Node.js packages (cosmetic only)
    config.ignoreWarnings = [
      /A Node\.js API is used.*which is not supported in the Edge Runtime/,
      /A Node\.js module is loaded.*which is not supported in the Edge Runtime/,
    ]

    return config
  },
  // External packages that should run in Node.js runtime (not Edge Runtime)
  serverExternalPackages: ['redis', 'bcryptjs', 'jsonwebtoken', '@redis/client', 'jws'],

  // Experimental features for bundle optimization
  experimental: {
    // Enable optimizations for production builds
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  // Domain configuration
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains',
          },
        ],
      },
    ]
  },
}

export default nextConfig
