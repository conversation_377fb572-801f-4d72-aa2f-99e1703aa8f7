# 🕌 Script Pemberian Ijin Otomatis untuk Siswa yang Sudah Zuhur

## 📋 Deskripsi

Script ini digunakan untuk memberikan ijin secara otomatis kepada siswa yang sudah melakukan shalat Zuhur pada hari yang sama. Script ini berguna untuk situasi di mana siswa yang sudah melakukan Zuhur perlu diberikan ijin untuk tidak melakukan Asr (misalnya karena kondisi khusus atau kebijakan sekolah).

## 🚀 Cara Penggunaan

### 1. Persiapan

Pastikan Anda berada di direktori root project ShalatYuk:

```bash
cd /path/to/ShalatYuk
```

### 2. <PERSON><PERSON><PERSON><PERSON>

```bash
./give-ijin-to-zuhr-students.sh
```

### 3. Proses Eksekusi

Script akan melakukan langkah-langkah berikut:

1. **Load Environment Variables**: Memuat DATABASE_URL dari `.env.local`
2. **Check Database Connection**: Memverifikasi koneksi ke database PostgreSQL
3. **Preview Students**: Menampilkan daftar siswa yang akan mendapat ijin
4. **User Confirmation**: Meminta konfirmasi dari user sebelum eksekusi
5. **Execute Operation**: Menjalankan operasi pemberian ijin
6. **Show Results**: Menampilkan hasil dan statistik
7. **Provide Rollback Info**: Memberikan informasi cara rollback jika diperlukan

## 📊 Kriteria Siswa yang Mendapat Ijin

Script akan memberikan ijin kepada siswa yang memenuhi kriteria:

- ✅ Sudah melakukan shalat Zuhur pada hari yang sama
- ✅ Belum memiliki record 'Ijin' pada hari yang sama
- ✅ Data tercatat dalam tabel `absences` dengan type 'Zuhr'

## 🔍 Contoh Output

```
==================================================
🕌 MEMBERIKAN IJIN KEPADA SISWA YANG SUDAH ZUHUR
==================================================
Tanggal: 2025-07-18 14:44:12 WITA

✅ DATABASE_URL loaded from .env.local
ℹ️  Checking database connection...
✅ Database connection successful
ℹ️  Mencari siswa yang sudah melakukan Zuhur hari ini...

📋 PREVIEW: Siswa yang akan mendapat ijin:
============================================
• Putri Maharani (0075686335) - XII A-BRCF - Zuhur: 2025-07-18 04:00:00+00
• NIA NORAINI (0089057149) - XII A-DKV - Zuhur: 2025-07-18 04:00:00+00
• EKA NOR SIFA (0071815353) - XII A-HTL - Zuhur: 2025-07-18 04:00:00+00

ℹ️  Total siswa yang akan mendapat ijin: 3

⚠️  KONFIRMASI DIPERLUKAN
Ketik 'YA' untuk melanjutkan: YA

✅ Berhasil memberikan ijin kepada 3 siswa
```

## 🗄️ Database Operations

### Query yang Dijalankan

```sql
INSERT INTO absences (unique_code, type, recorded_at, reason)
SELECT 
    unique_code,
    'Ijin' as type,
    (CURRENT_DATE + INTERVAL '15 hours')::timestamptz as recorded_at,
    'Ijin otomatis - sudah melakukan Zuhur hari ini' as reason
FROM absences 
WHERE type = 'Zuhr' 
    AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
    AND unique_code NOT IN (
        SELECT unique_code 
        FROM absences 
        WHERE type = 'Ijin' 
            AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
    );
```

### Timezone Handling

Script menggunakan timezone WITA (Asia/Makassar) untuk memastikan konsistensi dengan sistem:

- **Input Time**: `(CURRENT_DATE + INTERVAL '15 hours')::timestamptz` (15:00 WITA)
- **Date Filter**: `DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE`

## 🔄 Rollback

Jika Anda perlu membatalkan operasi, gunakan command berikut:

```bash
export DATABASE_URL=$(grep "^DATABASE_URL=" .env.local | head -1 | cut -d'=' -f2-)

psql "$DATABASE_URL" -c "
DELETE FROM absences 
WHERE type = 'Ijin' 
    AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = CURRENT_DATE
    AND reason LIKE '%sudah melakukan Zuhur%';
"
```

## 📝 Logging

Script mencatat operasi dalam file `ijin_operations.log`:

```
2025-07-18 14:44:20 - Ijin diberikan kepada 3 siswa
```

## ⚠️ Keamanan dan Validasi

### Validasi yang Dilakukan

1. **Environment Check**: Memastikan `.env.local` dan `DATABASE_URL` tersedia
2. **Database Connection**: Memverifikasi koneksi database sebelum operasi
3. **Data Preview**: Menampilkan data yang akan diproses sebelum eksekusi
4. **User Confirmation**: Meminta konfirmasi eksplisit dari user
5. **Duplicate Prevention**: Mencegah duplikasi record ijin untuk siswa yang sama

### Fitur Keamanan

- ✅ **Safe Environment Loading**: Menghindari error parsing dengan environment variables
- ✅ **Connection Validation**: Memastikan database dapat diakses
- ✅ **Transaction Safety**: Menggunakan single query untuk konsistensi
- ✅ **Rollback Information**: Menyediakan cara untuk membatalkan operasi
- ✅ **Audit Trail**: Mencatat reason yang jelas untuk tracking

## 🛠️ Troubleshooting

### Error: "DATABASE_URL not found"

```bash
# Pastikan .env.local ada dan berisi DATABASE_URL
ls -la .env.local
grep DATABASE_URL .env.local
```

### Error: "Failed to connect to database"

```bash
# Pastikan database service berjalan
docker ps | grep postgres
# atau
npm run tunnel
```

### Error: "Tidak ada siswa yang memenuhi kriteria"

Ini normal jika:
- Tidak ada siswa yang melakukan Zuhur hari ini
- Semua siswa yang Zuhur sudah memiliki record Ijin

## 📚 File Terkait

- `give-ijin-to-zuhr-students.sh` - Script utama
- `test-ijin-yesterday.sh` - Script test untuk data kemarin
- `ijin_operations.log` - Log file operasi
- `.env.local` - Environment variables
- `README-IJIN-SCRIPT.md` - Dokumentasi ini

## 🎯 Use Cases

1. **Kondisi Darurat**: Memberikan ijin massal saat ada situasi khusus
2. **Kebijakan Sekolah**: Implementasi kebijakan ijin otomatis
3. **Maintenance**: Operasi bulk untuk data correction
4. **Testing**: Validasi sistem dengan data real

## 📞 Support

Jika mengalami masalah, periksa:

1. Log file: `ijin_operations.log`
2. Database connection: `npm run tunnel`
3. Environment variables: `.env.local`
4. Script permissions: `chmod +x give-ijin-to-zuhr-students.sh`

---

**Dibuat oleh**: ShalatYuk Development Team  
**Tanggal**: 2025-07-18  
**Versi**: 1.0
