import { test, expect } from '@playwright/test'
import { AdminUsersPage, UserFormData } from './page-objects/admin/admin-users-page'
import { AdminLoginPage } from './page-objects/admin/admin-login-page'
import { testUsers } from './test-data/users'

test.describe('Admin User Management', () => {
  let adminUsersPage: AdminUsersPage
  let adminLoginPage: AdminLoginPage

  test.beforeEach(async ({ page }) => {
    adminUsersPage = new AdminUsersPage(page)
    adminLoginPage = new AdminLoginPage(page)

    // Login as Super Admin for full access
    await adminLoginPage.goto()
    await adminLoginPage.login(testUsers.superAdmin)
  })

  test.describe('Page Access and Authentication', () => {
    test('should allow Super Admin access to user management', async () => {
      await adminUsersPage.goto()
      await adminUsersPage.verifyPageAccess(true)
      await adminUsersPage.verifySuperAdminFeatures()
    })

    test('should deny access for non-Super Admin roles', async ({ page }) => {
      // Test with regular Admin
      await adminLoginPage.goto()
      await adminLoginPage.login(testUsers.admin)

      await page.goto('/admin/users')
      await adminUsersPage.verifyPageAccess(false)
    })

    test('should redirect unauthorized users', async ({ page }) => {
      // Test with Teacher role
      await adminLoginPage.goto()
      await adminLoginPage.login(testUsers.guru)

      await page.goto('/admin/users')
      await expect(page).toHaveURL(/\/admin\/home/<USER>
    })
  })

  test.describe('User CRUD Operations', () => {
    const testStudent: UserFormData = {
      name: 'Test Student E2E',
      username: 'test_student_e2e',
      password: 'TestPassword123!',
      role: 'student',
      classId: '1', // Assuming class ID 1 exists
      gender: 'male',
      nis: '2024001',
    }

    test('should successfully add a new student', async () => {
      await adminUsersPage.goto()

      // Add new student
      await adminUsersPage.addUser(testStudent)
      await adminUsersPage.waitForSuccessToast()

      // Verify student appears in table
      const userExists = await adminUsersPage.verifyUserInTable({
        name: testStudent.name,
        username: testStudent.username,
      })
      expect(userExists).toBe(true)
    })

    test('should validate required fields when adding user', async () => {
      await adminUsersPage.goto()

      // Try to add user with empty fields
      await adminUsersPage.addUserButton.click()
      await adminUsersPage.submitButton.click()

      // Should show validation errors
      const errors = await adminUsersPage.getValidationErrors()
      expect(errors.length).toBeGreaterThan(0)
    })

    test('should successfully edit existing user', async () => {
      await adminUsersPage.goto()

      // First add a user to edit
      await adminUsersPage.addUser(testStudent)
      await adminUsersPage.waitForSuccessToast()

      // Find and edit the user
      const userRow = await adminUsersPage.findUserRowByName(testStudent.name)
      expect(userRow).toBeGreaterThanOrEqual(0)

      const updatedData = {
        name: 'Updated Test Student',
        gender: 'female' as const,
      }

      await adminUsersPage.editUser(userRow, updatedData)
      await adminUsersPage.waitForSuccessToast()

      // Verify update
      const updatedUserExists = await adminUsersPage.verifyUserInTable({
        name: updatedData.name,
        username: testStudent.username,
      })
      expect(updatedUserExists).toBe(true)
    })

    test('should successfully delete user', async () => {
      await adminUsersPage.goto()

      // First add a user to delete
      const userToDelete = {
        ...testStudent,
        name: 'User To Delete',
        username: 'user_to_delete',
      }

      await adminUsersPage.addUser(userToDelete)
      await adminUsersPage.waitForSuccessToast()

      // Find and delete the user
      const userRow = await adminUsersPage.findUserRowByName(userToDelete.name)
      expect(userRow).toBeGreaterThanOrEqual(0)

      await adminUsersPage.deleteUser(userRow)
      await adminUsersPage.waitForSuccessToast()

      // Verify deletion
      const userExists = await adminUsersPage.verifyUserInTable({
        name: userToDelete.name,
        username: userToDelete.username,
      })
      expect(userExists).toBe(false)
    })

    test('should prevent duplicate usernames', async () => {
      await adminUsersPage.goto()

      // Add first user
      await adminUsersPage.addUser(testStudent)
      await adminUsersPage.waitForSuccessToast()

      // Try to add another user with same username
      const duplicateUser = {
        ...testStudent,
        name: 'Different Name',
      }

      await adminUsersPage.addUser(duplicateUser)
      await adminUsersPage.waitForErrorToast()
    })
  })

  test.describe('Search and Filter Functionality', () => {
    test('should search users by name', async () => {
      await adminUsersPage.goto()

      // Search for existing user
      await adminUsersPage.searchUsers('Ahmad')

      const rowCount = await adminUsersPage.getTableRowCount()
      expect(rowCount).toBeGreaterThan(0)

      // Clear search
      await adminUsersPage.clearSearch()
    })

    test('should search users by username', async () => {
      await adminUsersPage.goto()

      await adminUsersPage.searchUsers('siswa001')

      const rowCount = await adminUsersPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(0)
    })

    test('should filter users by class', async () => {
      await adminUsersPage.goto()

      // Filter by specific class
      await adminUsersPage.filterByClass('1')

      const rowCount = await adminUsersPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(0)

      // Reset filter
      await adminUsersPage.filterByClass('all')
    })

    test('should filter users by gender', async () => {
      await adminUsersPage.goto()

      await adminUsersPage.filterByGender('male')

      const rowCount = await adminUsersPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(0)

      // Reset filter
      await adminUsersPage.filterByGender('all')
    })

    test('should handle empty search results', async () => {
      await adminUsersPage.goto()

      await adminUsersPage.searchUsers('nonexistentuser12345')

      await expect(adminUsersPage.emptyState).toBeVisible()
    })
  })

  test.describe('Bulk Operations', () => {
    test('should upload users via CSV', async () => {
      await adminUsersPage.goto()

      const csvContent = `name,username,password,role,classId,gender,nis
Bulk User 1,bulk_user_1,password123,student,1,male,2024101
Bulk User 2,bulk_user_2,password123,student,1,female,2024102`

      await adminUsersPage.bulkUploadUsers(csvContent)

      // Verify users were added
      const user1Exists = await adminUsersPage.verifyUserInTable({
        name: 'Bulk User 1',
        username: 'bulk_user_1',
      })
      expect(user1Exists).toBe(true)
    })

    test('should select and delete multiple users', async () => {
      await adminUsersPage.goto()

      // Add test users for bulk deletion
      const testUsers = [
        { ...testStudent, name: 'Bulk Delete 1', username: 'bulk_delete_1' },
        { ...testStudent, name: 'Bulk Delete 2', username: 'bulk_delete_2' },
      ]

      for (const user of testUsers) {
        await adminUsersPage.addUser(user)
        await adminUsersPage.waitForSuccessToast()
      }

      // Select users for deletion
      await adminUsersPage.selectAllUsers()
      const selectedCount = await adminUsersPage.getSelectedUserCount()
      expect(selectedCount).toBeGreaterThan(0)

      // Delete selected users
      await adminUsersPage.deleteSelectedUsers()
    })

    test('should download CSV template', async () => {
      await adminUsersPage.goto()

      const filename = await adminUsersPage.downloadTemplate()
      expect(filename).toContain('.csv')
    })

    test('should export users to CSV', async () => {
      await adminUsersPage.goto()

      const filename = await adminUsersPage.exportToCSV()
      expect(filename).toContain('.csv')
    })
  })

  test.describe('QR Code Features', () => {
    test('should view individual user QR code', async () => {
      await adminUsersPage.goto()

      const rowCount = await adminUsersPage.getTableRowCount()
      if (rowCount > 0) {
        await adminUsersPage.viewUserQR(0)
        await expect(adminUsersPage.qrCodeModal).toBeVisible()
        await expect(adminUsersPage.qrCodeImage).toBeVisible()

        // Download QR code
        const filename = await adminUsersPage.downloadUserQR()
        expect(filename).toMatch(/\.(png|jpg|jpeg)$/i)
      }
    })

    test('should download all QR codes', async () => {
      await adminUsersPage.goto()

      const filename = await adminUsersPage.downloadAllQRCodes()
      expect(filename).toContain('.zip')
    })
  })

  test.describe('Pagination', () => {
    test('should navigate through pages', async () => {
      await adminUsersPage.goto()

      // Test pagination navigation
      const paginationInfo = await adminUsersPage.getPaginationInfo()
      expect(paginationInfo).toContain('dari')

      // Change items per page
      await adminUsersPage.changeItemsPerPage('5')

      const newRowCount = await adminUsersPage.getTableRowCount()
      expect(newRowCount).toBeLessThanOrEqual(5)
    })

    test('should maintain filter state across pages', async () => {
      await adminUsersPage.goto()

      // Apply filter
      await adminUsersPage.searchUsers('Ahmad')

      // Navigate to next page if available
      if (await adminUsersPage.nextPageButton.isEnabled()) {
        await adminUsersPage.goToNextPage()

        // Search should still be active
        const searchValue = await adminUsersPage.searchInput.inputValue()
        expect(searchValue).toBe('Ahmad')
      }
    })
  })

  test.describe('Security Testing', () => {
    test('should prevent XSS attacks in user forms', async () => {
      await adminUsersPage.goto()

      const xssPayload = '<script>alert("xss")</script>'
      await adminUsersPage.testXSSInUserForm(xssPayload)
    })

    test('should prevent SQL injection in search', async () => {
      await adminUsersPage.goto()

      const sqlPayload = "'; DROP TABLE users; --"
      await adminUsersPage.testSQLInjectionInSearch(sqlPayload)
    })

    test('should validate password strength', async () => {
      await adminUsersPage.goto()

      const weakPassword: UserFormData = {
        name: 'Test User',
        username: 'test_weak_pwd',
        password: '123', // Too weak
        role: 'student',
        classId: '1',
        gender: 'male',
      }

      await adminUsersPage.addUser(weakPassword)

      // Should show password validation error
      const errors = await adminUsersPage.getValidationErrors()
      expect(errors.some(error => error.toLowerCase().includes('password'))).toBe(true)
    })

    test('should validate email format if present', async () => {
      await adminUsersPage.goto()

      // Test with invalid email format if email field exists
      const invalidEmailUser: UserFormData = {
        name: 'Test User',
        username: 'test_invalid_email',
        password: 'ValidPassword123!',
        role: 'student',
        classId: '1',
        gender: 'male',
      }

      await adminUsersPage.addUser(invalidEmailUser)
      // Email validation would happen server-side
    })
  })

  test.describe('Performance Testing', () => {
    test('should load page within acceptable time', async () => {
      const loadTime = await adminUsersPage.measurePageLoadTime()
      expect(loadTime).toBeLessThan(5000) // 5 seconds
    })

    test('should search users efficiently', async () => {
      await adminUsersPage.goto()

      const searchTime = await adminUsersPage.measureSearchTime('Ahmad')
      expect(searchTime).toBeLessThan(2000) // 2 seconds
    })

    test('should handle large datasets efficiently', async () => {
      await adminUsersPage.goto()

      // Test with maximum items per page
      await adminUsersPage.changeItemsPerPage('100')

      const startTime = Date.now()
      await adminUsersPage.waitForTableLoad()
      const loadTime = Date.now() - startTime

      expect(loadTime).toBeLessThan(3000) // 3 seconds
    })
  })

  test.describe('Accessibility Testing', () => {
    test('should meet accessibility standards', async () => {
      await adminUsersPage.goto()
      await adminUsersPage.verifyAccessibility()
    })

    test('should support keyboard navigation', async () => {
      await adminUsersPage.goto()
      await adminUsersPage.testKeyboardNavigation()
    })

    test('should have proper ARIA labels', async () => {
      await adminUsersPage.goto()

      // Check for ARIA labels on interactive elements
      await expect(adminUsersPage.searchInput).toHaveAttribute('aria-label')
      await expect(adminUsersPage.usersTable).toHaveAttribute('role', 'table')
    })

    test('should support screen readers', async () => {
      await adminUsersPage.goto()

      // Check for proper heading structure
      await expect(adminUsersPage.pageTitle).toBeVisible()

      // Check for proper table headers
      const headers = await adminUsersPage.tableHeaders.count()
      expect(headers).toBeGreaterThan(0)
    })
  })

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async () => {
      await adminUsersPage.goto()
      await adminUsersPage.verifyResponsiveDesign()
    })

    test('should maintain functionality on tablet', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await adminUsersPage.goto()

      await expect(adminUsersPage.pageTitle).toBeVisible()
      await expect(adminUsersPage.addUserButton).toBeVisible()
      await expect(adminUsersPage.usersTable).toBeVisible()
    })

    test('should adapt table layout for mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await adminUsersPage.goto()

      // Table should still be accessible, possibly with horizontal scroll
      await expect(adminUsersPage.usersTable).toBeVisible()
    })
  })

  test.describe('Data Validation and Edge Cases', () => {
    test('should handle special characters in names', async () => {
      await adminUsersPage.goto()

      const specialCharUser: UserFormData = {
        name: "O'Connor-Smith",
        username: 'special_char_user',
        password: 'ValidPassword123!',
        role: 'student',
        classId: '1',
        gender: 'male',
      }

      await adminUsersPage.addUser(specialCharUser)
      await adminUsersPage.waitForSuccessToast()

      const userExists = await adminUsersPage.verifyUserInTable({
        name: specialCharUser.name,
        username: specialCharUser.username,
      })
      expect(userExists).toBe(true)
    })

    test('should validate NIS format', async () => {
      await adminUsersPage.goto()

      const invalidNISUser: UserFormData = {
        name: 'Test User',
        username: 'test_invalid_nis',
        password: 'ValidPassword123!',
        role: 'student',
        classId: '1',
        gender: 'male',
        nis: 'INVALID_NIS_FORMAT',
      }

      await adminUsersPage.addUser(invalidNISUser)
      // Should validate NIS format server-side
    })

    test('should handle maximum field lengths', async () => {
      await adminUsersPage.goto()

      const maxLengthUser: UserFormData = {
        name: 'A'.repeat(100), // Very long name
        username: 'very_long_username_test',
        password: 'ValidPassword123!',
        role: 'student',
        classId: '1',
        gender: 'male',
      }

      await adminUsersPage.addUser(maxLengthUser)
      // Should handle or validate maximum lengths
    })

    test('should preserve data during page refresh', async ({ page }) => {
      await adminUsersPage.goto()

      // Add search term
      await adminUsersPage.searchUsers('Ahmad')

      // Refresh page
      await page.reload()
      await adminUsersPage.waitForLoad()

      // Search should be cleared after refresh
      const searchValue = await adminUsersPage.searchInput.inputValue()
      expect(searchValue).toBe('')
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      await adminUsersPage.goto()

      // Simulate network failure
      await page.route('**/api/users', route => route.abort())

      await adminUsersPage.addUserButton.click()
      await adminUsersPage.submitButton.click()

      // Should show appropriate error message
      await expect(adminUsersPage.errorMessage).toBeVisible()
    })

    test('should handle server errors gracefully', async ({ page }) => {
      await adminUsersPage.goto()

      // Simulate server error
      await page.route('**/api/users', route =>
        route.fulfill({ status: 500, body: 'Internal Server Error' })
      )

      await adminUsersPage.addUserButton.click()
      await adminUsersPage.submitButton.click()

      // Should show appropriate error message
      await expect(adminUsersPage.errorMessage).toBeVisible()
    })

    test('should validate file upload errors', async () => {
      await adminUsersPage.goto()

      // Try to upload invalid CSV
      const invalidCSV = 'invalid,csv,format,without,proper,headers'
      await adminUsersPage.bulkUploadUsers(invalidCSV)

      // Should show validation error
      await adminUsersPage.waitForErrorToast()
    })
  })

  test.describe('Integration Testing', () => {
    test('should sync with authentication system', async ({ page }) => {
      // Test that user creation integrates properly with auth
      await adminUsersPage.goto()

      const newUser: UserFormData = {
        name: 'Integration Test User',
        username: 'integration_test',
        password: 'IntegrationTest123!',
        role: 'student',
        classId: '1',
        gender: 'male',
      }

      await adminUsersPage.addUser(newUser)
      await adminUsersPage.waitForSuccessToast()

      // Verify user can login (basic integration test)
      // This would typically involve logging out and attempting login
    })

    test('should integrate with class management', async () => {
      await adminUsersPage.goto()

      // Verify that class dropdown contains valid options
      await adminUsersPage.addUserButton.click()
      await expect(adminUsersPage.classSelect).toBeVisible()

      // Should have class options
      const classOptions = await adminUsersPage.classSelect.locator('option').count()
      expect(classOptions).toBeGreaterThan(1) // At least "Select Class" + actual classes
    })
  })

  // Clean up test data
  test.afterEach(async () => {
    // Clean up any test users created during tests
    const testUserPatterns = [
      'Test Student E2E',
      'Updated Test Student',
      'User To Delete',
      'Bulk User',
      'Bulk Delete',
      'Integration Test User',
    ]

    for (const pattern of testUserPatterns) {
      try {
        const userRow = await adminUsersPage.findUserRowByName(pattern)
        if (userRow >= 0) {
          await adminUsersPage.deleteUser(userRow)
          await adminUsersPage.waitForSuccessToast()
        }
      } catch (error) {
        // Ignore cleanup errors
        console.log(`Cleanup error for ${pattern}: ${error}`)
      }
    }
  })
})
