import { FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E Test Teardown...')

  // Clean up test data
  await cleanupTestData()

  // Generate test summary
  await generateTestSummary()

  // Archive old test results if needed
  await archiveOldResults()

  console.log('✅ Global teardown completed')
}

async function cleanupTestData() {
  console.log('🗄️  Cleaning up test data...')

  // This would typically involve:
  // 1. Removing test users from database
  // 2. Cleaning up test attendance records
  // 3. Resetting any modified configurations

  console.log('   - Test users: Cleaned')
  console.log('   - Test records: Cleaned')
  console.log('   - Test sessions: Cleared')
}

async function generateTestSummary() {
  console.log('📊 Generating test summary...')

  const fs = require('fs')
  const path = require('path')

  try {
    // Check if results.json exists
    const resultsPath = path.join('test-results', 'results.json')

    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'))

      const summary = {
        timestamp: new Date().toISOString(),
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
      }

      console.log(`   - Total tests: ${summary.total}`)
      console.log(`   - Passed: ${summary.passed}`)
      console.log(`   - Failed: ${summary.failed}`)
      console.log(`   - Skipped: ${summary.skipped}`)
      console.log(`   - Duration: ${Math.round(summary.duration / 1000)}s`)

      // Save summary
      const summaryPath = path.join('test-results', 'summary.json')
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2))
    } else {
      console.log('   - No test results found')
    }
  } catch (error) {
    console.error('   ❌ Error generating summary:', error)
  }
}

async function archiveOldResults() {
  console.log('📦 Archiving old test results...')

  const fs = require('fs')
  const path = require('path')

  try {
    const resultsDir = 'test-results'
    const archiveDir = path.join(resultsDir, 'archive')

    // Create archive directory if it doesn't exist
    if (!fs.existsSync(archiveDir)) {
      fs.mkdirSync(archiveDir, { recursive: true })
    }

    // Archive screenshots older than 7 days
    const screenshotsDir = path.join(resultsDir, 'screenshots')

    if (fs.existsSync(screenshotsDir)) {
      const files = fs.readdirSync(screenshotsDir)
      const weekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000

      let archivedCount = 0

      for (const file of files) {
        const filePath = path.join(screenshotsDir, file)
        const stats = fs.statSync(filePath)

        if (stats.mtime.getTime() < weekAgo) {
          const archivePath = path.join(archiveDir, file)
          fs.renameSync(filePath, archivePath)
          archivedCount++
        }
      }

      if (archivedCount > 0) {
        console.log(`   - Archived ${archivedCount} old screenshots`)
      } else {
        console.log('   - No old screenshots to archive')
      }
    }
  } catch (error) {
    console.error('   ❌ Error archiving results:', error)
  }
}

export default globalTeardown
