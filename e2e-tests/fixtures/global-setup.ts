import { FullConfig } from '@playwright/test'
import { testUsers } from '../test-data/users'
import { Pool } from 'pg'
import bcrypt from 'bcryptjs'
import { v4 as uuidv4 } from 'uuid'
import path from 'path'

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env.local') })

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E Test Setup...')

  // Create required directories
  const fs = require('fs')

  const directories = [
    'test-results/screenshots',
    'test-results/videos',
    'test-results/traces',
    'e2e-tests/fixtures/auth',
  ]

  for (const dir of directories) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
      console.log(`📁 Created directory: ${dir}`)
    }
  }

  // Log test configuration
  console.log(`🌐 Base URL: ${config.use?.baseURL || 'http://localhost:3000'}`)
  console.log(`🧪 Test Directory: ${config.testDir}`)
  console.log(`⚡ Workers: ${config.workers || 'auto'}`)
  console.log(`🔄 Retries: ${config.retries || 0}`)

  // Log available test users
  console.log('👥 Test Users Available:')
  Object.entries(testUsers).forEach(([key, user]) => {
    console.log(`  - ${key}: ${user.username} (${user.role})`)
  })

  // Validate environment variables
  const requiredEnvVars = ['NODE_ENV']

  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])

  if (missingEnvVars.length > 0) {
    console.warn(`⚠️  Missing environment variables: ${missingEnvVars.join(', ')}`)
  }

  // Check if application server is running
  const baseURL = config.use?.baseURL || 'http://localhost:3000'

  try {
    const response = await fetch(baseURL)
    if (response.ok) {
      console.log(`✅ Application server is running at ${baseURL}`)
    } else {
      console.warn(`⚠️  Application server responded with status ${response.status}`)
    }
  } catch (error) {
    console.warn(`⚠️  Could not reach application server at ${baseURL}`)
    console.log('   Make sure to run "npm run dev" in a separate terminal')
  }

  // Setup database test data if needed
  await setupTestData()

  console.log('✅ Global setup completed')
}

async function setupTestData() {
  console.log('🗄️  Setting up test data...')

  // Connect to database
  const databaseUrl = process.env.DATABASE_URL
  if (!databaseUrl) {
    console.warn('⚠️  DATABASE_URL not found, skipping test data setup')
    return
  }

  const pool = new Pool({
    connectionString: databaseUrl,
  })

  try {
    const client = await pool.connect()

    // First, ensure we have a default class for students
    console.log('   - Setting up default class...')
    try {
      const existingClass = await client.query('SELECT id FROM classes WHERE name = $1', [
        'XII IPA 1',
      ])

      let classId
      if (existingClass.rows.length === 0) {
        const newClass = await client.query(
          'INSERT INTO classes (name, created_at) VALUES ($1, NOW()) RETURNING id',
          ['XII IPA 1']
        )
        classId = newClass.rows[0].id
        console.log(`     ✅ Created default class: XII IPA 1`)
      } else {
        classId = existingClass.rows[0].id
        console.log(`     ℹ️  Default class exists: XII IPA 1`)
      }

      // Create admin class for non-student users
      const existingAdminClass = await client.query('SELECT id FROM classes WHERE name = $1', [
        'Admin',
      ])

      let adminClassId
      if (existingAdminClass.rows.length === 0) {
        const newAdminClass = await client.query(
          'INSERT INTO classes (name, created_at) VALUES ($1, NOW()) RETURNING id',
          ['Admin']
        )
        adminClassId = newAdminClass.rows[0].id
        console.log(`     ✅ Created admin class: Admin`)
      } else {
        adminClassId = existingAdminClass.rows[0].id
        console.log(`     ℹ️  Admin class exists: Admin`)
      }

      // Create test users
      console.log('   - Creating test users...')

      for (const [key, user] of Object.entries(testUsers)) {
        try {
          // Check if user already exists
          const existingUser = await client.query('SELECT id FROM users WHERE username = $1', [
            user.username,
          ])

          if (existingUser.rows.length === 0) {
            // Hash password
            const passwordHash = await bcrypt.hash(user.password, 10)

            // Generate unique code for students
            const uniqueCode = user.role === 'student' ? uuidv4() : null

            // Set class_id: students get XII IPA 1, others get Admin Staff
            const userClassId = user.role === 'student' ? classId : adminClassId

            // Insert new test user
            await client.query(
              `INSERT INTO users (username, password_hash, name, google_email, role, class_id, whatsapp, unique_code) 
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
              [
                user.username,
                passwordHash,
                user.name || user.username,
                user.email || `${user.username}@test.com`,
                user.role === 'guru'
                  ? 'teacher'
                  : user.role === 'resepsionis'
                    ? 'receptionist'
                    : user.role,
                userClassId,
                user.whatsapp || null,
                uniqueCode,
              ]
            )
            console.log(`     ✅ Created user: ${user.username} (${user.role})`)
          } else {
            console.log(`     ℹ️  User exists: ${user.username}`)
          }
        } catch (error) {
          console.log(`     ❌ Failed to create user ${user.username}:`, error.message)
        }
      }
    } catch (error) {
      console.log(`     ❌ Failed to setup class:`, error.message)
    }

    client.release()
    console.log('   - Test users: Ready')
    console.log('   - Attendance types: Ready')
    console.log('   - Test scenarios: Ready')
  } catch (error) {
    console.error('❌ Database setup failed:', error.message)
  } finally {
    await pool.end()
  }
}

export default globalSetup
