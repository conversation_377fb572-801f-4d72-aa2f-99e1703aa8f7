import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './base-page'

export class StudentResetPasswordPage extends BasePage {
  // Page URL
  readonly url = '/student/reset-password'

  // Header elements
  readonly header: Locator
  readonly pageTitle: Locator
  readonly backButton: Locator
  readonly themeToggle: Locator

  // Form elements
  readonly resetPasswordForm: Locator
  readonly newPasswordInput: Locator
  readonly confirmPasswordInput: Locator
  readonly submitButton: Locator

  // Password strength indicator
  readonly passwordStrengthContainer: Locator
  readonly passwordStrengthProgress: Locator
  readonly passwordStrengthText: Locator
  readonly passwordRequirements: Locator

  // Password visibility toggles
  readonly newPasswordToggle: Locator
  readonly confirmPasswordToggle: Locator

  // Error and success alerts
  readonly errorAlert: Locator
  readonly successAlert: Locator
  readonly rateLimitAlert: Locator
  readonly invalidOtpAlert: Locator

  // Loading state
  readonly loadingSpinner: Locator
  readonly submitLoadingState: Locator

  // Parameter validation
  readonly invalidParamsAlert: Locator

  constructor(page: Page) {
    super(page)

    // Header elements
    this.header = page.locator('[data-testid="reset-password-header"]')
    this.pageTitle = page
      .locator('h1, h2')
      .filter({ hasText: /reset|ubah|password/i })
      .first()
    this.backButton = page.locator('[data-testid="back-button"]')
    this.themeToggle = page.locator('[data-testid="theme-toggle"]')

    // Form elements
    this.resetPasswordForm = page
      .locator('form')
      .filter({ has: page.locator('input[type="password"]') })
    this.newPasswordInput = page.locator('input[type="password"]').first()
    this.confirmPasswordInput = page.locator('input[type="password"]').nth(1)
    this.submitButton = page.locator('button[type="submit"]')

    // Password strength indicator
    this.passwordStrengthContainer = page.locator('[data-testid="password-strength"]')
    this.passwordStrengthProgress = page.locator('[data-testid="password-strength-progress"]')
    this.passwordStrengthText = page.locator('[data-testid="password-strength-text"]')
    this.passwordRequirements = page.locator('[data-testid="password-requirements"]')

    // Password visibility toggles
    this.newPasswordToggle = page.locator('[data-testid="new-password-toggle"]')
    this.confirmPasswordToggle = page.locator('[data-testid="confirm-password-toggle"]')

    // Error and success alerts
    this.errorAlert = page.locator('[role="alert"]').filter({ hasText: /error|gagal|salah/i })
    this.successAlert = page.locator('[role="alert"]').filter({ hasText: /success|berhasil/i })
    this.rateLimitAlert = page
      .locator('[role="alert"]')
      .filter({ hasText: /rate.?limit|terlalu.?sering/i })
    this.invalidOtpAlert = page
      .locator('[role="alert"]')
      .filter({ hasText: /otp.?invalid|kode.?salah/i })

    // Loading state
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]')
    this.submitLoadingState = page.locator('button[type="submit"] svg.animate-spin')

    // Parameter validation
    this.invalidParamsAlert = page
      .locator('[role="alert"]')
      .filter({ hasText: /invalid.*parameter|parameter.*tidak.*valid/i })
  }

  /**
   * Navigate to reset password page with specific parameters
   */
  async goto(params?: {
    method?: 'whatsapp' | 'token'
    token?: string
    whatsapp?: string
    otp?: string
  }): Promise<void> {
    let url = this.url

    if (params) {
      const searchParams = new URLSearchParams()
      if (params.method) searchParams.set('method', params.method)
      if (params.token) searchParams.set('token', params.token)
      if (params.whatsapp) searchParams.set('whatsapp', params.whatsapp)
      if (params.otp) searchParams.set('otp', params.otp)

      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`
      }
    }

    await this.page.goto(url)
    await this.waitForLoad()
  }

  /**
   * Wait for page to load completely
   */
  async waitForLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle')

    // Wait for form to be visible or for redirect in case of invalid params
    try {
      await this.resetPasswordForm.waitFor({ state: 'visible', timeout: 5000 })
    } catch {
      // Check if we were redirected due to invalid parameters
      const currentUrl = this.page.url()
      if (currentUrl.includes('/student/forgot-password') || currentUrl.includes('/student')) {
        // Redirected due to invalid parameters
        return
      }
      throw new Error('Reset password form not found and no redirect detected')
    }
  }

  /**
   * Reset password with new password
   */
  async resetPassword(newPassword: string, confirmPassword: string): Promise<void> {
    await this.newPasswordInput.fill(newPassword)
    await this.confirmPasswordInput.fill(confirmPassword)

    // Wait for password strength calculation
    await this.page.waitForTimeout(500)

    await this.submitButton.click()

    // Wait for form submission to complete
    await this.waitForSubmissionComplete()
  }

  /**
   * Get password strength information
   */
  async getPasswordStrength(): Promise<{
    score: number
    feedback: string
    color: string
    isVisible: boolean
  }> {
    const isVisible = await this.passwordStrengthContainer.isVisible()

    if (!isVisible) {
      return { score: 0, feedback: '', color: '', isVisible: false }
    }

    const progressElement = this.passwordStrengthProgress
    const feedbackElement = this.passwordStrengthText

    // Get progress value
    const progressValue = (await progressElement.getAttribute('value')) || '0'
    const score = parseInt(progressValue, 10)

    // Get feedback text
    const feedback = (await feedbackElement.textContent()) || ''

    // Determine color based on score or CSS classes
    const className = (await progressElement.getAttribute('class')) || ''
    let color = 'gray'

    if (className.includes('red') || score <= 20) color = 'red'
    else if (className.includes('orange') || score <= 40) color = 'orange'
    else if (className.includes('yellow') || score <= 60) color = 'yellow'
    else if (className.includes('lime') || score <= 80) color = 'lime'
    else if (className.includes('green') || score > 80) color = 'green'

    return { score, feedback: feedback.trim(), color, isVisible: true }
  }

  /**
   * Check if password meets requirements
   */
  async checkPasswordRequirements(password: string): Promise<{
    minLength: boolean
    hasUppercase: boolean
    hasLowercase: boolean
    hasNumber: boolean
    hasSpecialChar: boolean
    overallValid: boolean
  }> {
    await this.newPasswordInput.fill(password)
    await this.page.waitForTimeout(500) // Wait for validation

    const requirements = {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecialChar: /[^A-Za-z0-9]/.test(password),
    }

    const overallValid = Object.values(requirements).every(req => req)

    return { ...requirements, overallValid }
  }

  /**
   * Toggle password visibility
   */
  async toggleNewPasswordVisibility(): Promise<void> {
    await this.newPasswordToggle.click()
  }

  async toggleConfirmPasswordVisibility(): Promise<void> {
    await this.confirmPasswordToggle.click()
  }

  /**
   * Check if passwords are visible (type="text" instead of type="password")
   */
  async isNewPasswordVisible(): Promise<boolean> {
    const type = await this.newPasswordInput.getAttribute('type')
    return type === 'text'
  }

  async isConfirmPasswordVisible(): Promise<boolean> {
    const type = await this.confirmPasswordInput.getAttribute('type')
    return type === 'text'
  }

  /**
   * Verify password match validation
   */
  async verifyPasswordMatch(newPassword: string, confirmPassword: string): Promise<boolean> {
    await this.newPasswordInput.fill(newPassword)
    await this.confirmPasswordInput.fill(confirmPassword)
    await this.confirmPasswordInput.blur()

    // Wait for validation
    await this.page.waitForTimeout(500)

    // Check for validation error
    const hasMatchError = await this.page
      .locator('[data-testid="password-match-error"]')
      .isVisible()
    return !hasMatchError && newPassword === confirmPassword
  }

  /**
   * Wait for form submission to complete
   */
  async waitForSubmissionComplete(): Promise<void> {
    // Wait for loading state to appear and then disappear
    try {
      await this.submitLoadingState.waitFor({ state: 'visible', timeout: 2000 })
      await this.submitLoadingState.waitFor({ state: 'hidden', timeout: 10000 })
    } catch {
      // Loading state might not appear for fast responses
    }
  }

  /**
   * Check for various error states
   */
  async getErrorState(): Promise<{
    hasError: boolean
    type: 'rate_limit' | 'invalid_otp' | 'invalid_params' | 'general' | null
    message: string
  }> {
    // Check for rate limit error
    if (await this.rateLimitAlert.isVisible()) {
      const message = (await this.rateLimitAlert.textContent()) || ''
      return { hasError: true, type: 'rate_limit', message: message.trim() }
    }

    // Check for invalid OTP error
    if (await this.invalidOtpAlert.isVisible()) {
      const message = (await this.invalidOtpAlert.textContent()) || ''
      return { hasError: true, type: 'invalid_otp', message: message.trim() }
    }

    // Check for invalid parameters error
    if (await this.invalidParamsAlert.isVisible()) {
      const message = (await this.invalidParamsAlert.textContent()) || ''
      return { hasError: true, type: 'invalid_params', message: message.trim() }
    }

    // Check for general error
    if (await this.errorAlert.isVisible()) {
      const message = (await this.errorAlert.textContent()) || ''
      return { hasError: true, type: 'general', message: message.trim() }
    }

    return { hasError: false, type: null, message: '' }
  }

  /**
   * Check for success state
   */
  async getSuccessState(): Promise<{ hasSuccess: boolean; message: string }> {
    const hasSuccess = await this.successAlert.isVisible()
    const message = hasSuccess ? (await this.successAlert.textContent()) || '' : ''

    return { hasSuccess, message: message.trim() }
  }

  /**
   * Verify form validation
   */
  async verifyFormValidation(): Promise<{
    passwordRequired: boolean
    confirmPasswordRequired: boolean
    passwordTooWeak: boolean
    passwordMismatch: boolean
  }> {
    // Test empty passwords
    await this.newPasswordInput.fill('')
    await this.confirmPasswordInput.fill('')
    await this.submitButton.click()

    const passwordRequired = await this.page
      .locator('[data-testid="password-required-error"]')
      .isVisible()
    const confirmPasswordRequired = await this.page
      .locator('[data-testid="confirm-password-required-error"]')
      .isVisible()

    // Test weak password
    await this.newPasswordInput.fill('123')
    await this.confirmPasswordInput.fill('123')
    await this.submitButton.click()

    const passwordTooWeak = await this.page
      .locator('[data-testid="password-weak-error"]')
      .isVisible()

    // Test password mismatch
    await this.newPasswordInput.fill('StrongPass123!')
    await this.confirmPasswordInput.fill('DifferentPass123!')
    await this.submitButton.click()

    const passwordMismatch = await this.page
      .locator('[data-testid="password-mismatch-error"]')
      .isVisible()

    return { passwordRequired, confirmPasswordRequired, passwordTooWeak, passwordMismatch }
  }

  /**
   * Verify URL parameters are valid
   */
  async verifyUrlParameters(): Promise<{
    hasValidParams: boolean
    method: string | null
    hasToken: boolean
    hasWhatsapp: boolean
    hasOtp: boolean
  }> {
    const url = new URL(this.page.url())
    const params = url.searchParams

    const method = params.get('method')
    const token = params.get('token')
    const whatsapp = params.get('whatsapp')
    const otp = params.get('otp')

    let hasValidParams = false

    if (method === 'whatsapp' && whatsapp && otp) {
      hasValidParams = true
    } else if (method === 'token' && token) {
      hasValidParams = true
    } else if (!method && token) {
      hasValidParams = true
    }

    return {
      hasValidParams,
      method,
      hasToken: Boolean(token),
      hasWhatsapp: Boolean(whatsapp),
      hasOtp: Boolean(otp),
    }
  }

  /**
   * Navigate back to previous page
   */
  async goBack(): Promise<void> {
    await this.backButton.click()
  }

  /**
   * Toggle theme
   */
  async toggleTheme(): Promise<void> {
    await this.themeToggle.click()
    await this.page.waitForTimeout(300)
  }

  /**
   * Verify accessibility features
   */
  async verifyAccessibility(): Promise<void> {
    // Check for proper labels
    await expect(this.newPasswordInput).toHaveAttribute('aria-label')
    await expect(this.confirmPasswordInput).toHaveAttribute('aria-label')

    // Check for form association
    await expect(this.newPasswordInput).toHaveAttribute('id')
    await expect(this.confirmPasswordInput).toHaveAttribute('id')

    // Check for error announcements
    const errorElements = this.page.locator('[role="alert"]')
    if ((await errorElements.count()) > 0) {
      await expect(errorElements.first()).toHaveAttribute('aria-live', 'polite')
    }
  }

  /**
   * Verify responsive design
   */
  async verifyResponsiveDesign(): Promise<void> {
    // Test mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 })
    await expect(this.resetPasswordForm).toBeVisible()
    await expect(this.newPasswordInput).toBeVisible()
    await expect(this.confirmPasswordInput).toBeVisible()

    // Test tablet viewport
    await this.page.setViewportSize({ width: 768, height: 1024 })
    await expect(this.resetPasswordForm).toBeVisible()

    // Test desktop viewport
    await this.page.setViewportSize({ width: 1024, height: 768 })
    await expect(this.resetPasswordForm).toBeVisible()
  }

  /**
   * Test password strength with various passwords
   */
  async testPasswordStrengthScenarios(): Promise<
    Array<{
      password: string
      expectedStrength: 'very_weak' | 'weak' | 'medium' | 'strong' | 'very_strong'
      actualScore: number
    }>
  > {
    const testCases = [
      { password: '123', expectedStrength: 'very_weak' as const },
      { password: 'password', expectedStrength: 'weak' as const },
      { password: 'Password1', expectedStrength: 'medium' as const },
      { password: 'Password123', expectedStrength: 'strong' as const },
      { password: 'StrongPass123!', expectedStrength: 'very_strong' as const },
    ]

    const results = []

    for (const testCase of testCases) {
      await this.newPasswordInput.fill(testCase.password)
      await this.page.waitForTimeout(500)

      const strength = await this.getPasswordStrength()
      results.push({
        password: testCase.password,
        expectedStrength: testCase.expectedStrength,
        actualScore: strength.score,
      })
    }

    return results
  }

  /**
   * Verify security headers and CSRF protection
   */
  async verifySecurityFeatures(): Promise<void> {
    // Check for CSRF token in form
    const csrfToken = await this.page
      .locator('input[name="_token"], input[name="csrf_token"]')
      .isVisible()
    expect(csrfToken).toBeTruthy()

    // Check for secure form submission
    const formAction = await this.resetPasswordForm.getAttribute('action')
    if (formAction) {
      expect(formAction).toMatch(/^https:\/\/|^\//)
    }
  }
}
