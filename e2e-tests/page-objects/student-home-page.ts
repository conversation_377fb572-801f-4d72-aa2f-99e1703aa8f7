import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './base-page'

export class StudentHomePage extends BasePage {
  // Page URL
  readonly url = '/student/home'

  // Header elements
  readonly header: Locator
  readonly currentDate: Locator
  readonly currentTime: Locator
  readonly studentName: Locator
  readonly logoutButton: Locator

  // QR Code section
  readonly qrCodeContainer: Locator
  readonly qrCodeElement: Locator
  readonly downloadQrButton: Locator
  readonly uniqueCodeText: Locator

  // Attendance status cards
  readonly attendanceCards: Locator
  readonly zuhurCard: Locator
  readonly zuhurStatus: Locator
  readonly zuhurTime: Locator
  readonly asrCard: Locator
  readonly asrStatus: Locator
  readonly asrTime: Locator
  readonly pulangCard: Locator
  readonly pulangStatus: Locator
  readonly pulangTime: Locator

  // Tabs
  readonly tabsList: Locator
  readonly qrCodeTab: Locator
  readonly attendanceTab: Locator
  readonly profileTab: Locator

  // Loading states
  readonly loadingSpinner: Locator
  readonly profileLoadFailedAlert: Locator
  readonly retryButton: Locator

  // Bottom navigation
  readonly bottomNav: Locator
  readonly homeNavButton: Locator
  readonly historyNavButton: Locator
  readonly profileNavButton: Locator

  // Theme toggle
  readonly themeToggle: Locator

  constructor(page: Page) {
    super(page)

    // Header elements
    this.header = page.locator('[data-testid="student-header"]')
    this.currentDate = page.locator('[data-testid="current-date"]')
    this.currentTime = page.locator('[data-testid="current-time"]')
    this.studentName = page.locator('[data-testid="student-name"]')
    this.logoutButton = page.locator('[data-testid="logout-button"]')

    // QR Code section
    this.qrCodeContainer = page.locator('[data-testid="qr-code-container"]')
    this.qrCodeElement = page.locator('[data-testid="qr-code"]')
    this.downloadQrButton = page.locator('[data-testid="download-qr-button"]')
    this.uniqueCodeText = page.locator('[data-testid="unique-code"]')

    // Attendance status cards
    this.attendanceCards = page.locator('[data-testid="attendance-cards"]')
    this.zuhurCard = page.locator('[data-testid="zuhur-card"]')
    this.zuhurStatus = page.locator('[data-testid="zuhur-status"]')
    this.zuhurTime = page.locator('[data-testid="zuhur-time"]')
    this.asrCard = page.locator('[data-testid="asr-card"]')
    this.asrStatus = page.locator('[data-testid="asr-status"]')
    this.asrTime = page.locator('[data-testid="asr-time"]')
    this.pulangCard = page.locator('[data-testid="pulang-card"]')
    this.pulangStatus = page.locator('[data-testid="pulang-status"]')
    this.pulangTime = page.locator('[data-testid="pulang-time"]')

    // Tabs
    this.tabsList = page.locator('[role="tablist"]')
    this.qrCodeTab = page.locator('[data-testid="qr-code-tab"]')
    this.attendanceTab = page.locator('[data-testid="attendance-tab"]')
    this.profileTab = page.locator('[data-testid="profile-tab"]')

    // Loading states
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]')
    this.profileLoadFailedAlert = page.locator('[data-testid="profile-load-failed"]')
    this.retryButton = page.locator('[data-testid="retry-button"]')

    // Bottom navigation
    this.bottomNav = page.locator('[data-testid="bottom-nav"]')
    this.homeNavButton = page.locator('[data-testid="nav-home"]')
    this.historyNavButton = page.locator('[data-testid="nav-history"]')
    this.profileNavButton = page.locator('[data-testid="nav-profile"]')

    // Theme toggle
    this.themeToggle = page.locator('[data-testid="theme-toggle"]')
  }

  /**
   * Navigate to student home page
   */
  async goto(): Promise<void> {
    await this.page.goto(this.url)
    await this.waitForLoad()
  }

  /**
   * Wait for page to load completely
   */
  async waitForLoad(): Promise<void> {
    // Wait for loading spinner to disappear
    await this.page.waitForLoadState('networkidle')
    await this.loadingSpinner.waitFor({ state: 'hidden', timeout: 10000 })

    // Wait for essential elements to be visible
    await this.qrCodeContainer.waitFor({ state: 'visible' })
    await this.attendanceCards.waitFor({ state: 'visible' })
  }

  /**
   * Get current date and time display
   */
  async getCurrentDateTime(): Promise<{ date: string; time: string }> {
    const date = await this.currentDate.textContent()
    const time = await this.currentTime.textContent()
    return {
      date: date?.trim() || '',
      time: time?.trim() || '',
    }
  }

  /**
   * Get student information
   */
  async getStudentInfo(): Promise<{ name: string; uniqueCode: string }> {
    const name = await this.studentName.textContent()
    const uniqueCode = await this.uniqueCodeText.textContent()
    return {
      name: name?.trim() || '',
      uniqueCode: uniqueCode?.trim() || '',
    }
  }

  /**
   * Check if QR code is displayed and valid
   */
  async isQrCodeVisible(): Promise<boolean> {
    try {
      await this.qrCodeElement.waitFor({ state: 'visible', timeout: 5000 })

      // Check if QR code has proper attributes
      const qrElement = this.qrCodeElement
      const hasSize = await qrElement.getAttribute('width')
      const hasValue = await qrElement.getAttribute('value')

      return Boolean(hasSize && hasValue)
    } catch {
      return false
    }
  }

  /**
   * Get QR code value
   */
  async getQrCodeValue(): Promise<string> {
    const value = await this.qrCodeElement.getAttribute('value')
    return value || ''
  }

  /**
   * Download QR code
   */
  async downloadQrCode(): Promise<void> {
    // Set up download promise before clicking
    const downloadPromise = this.page.waitForEvent('download')
    await this.downloadQrButton.click()
    const download = await downloadPromise

    // Verify download
    expect(download.suggestedFilename()).toMatch(/qr-code-.*\.(png|jpg|jpeg)$/i)
  }

  /**
   * Get attendance status for all prayer types
   */
  async getAttendanceStatus(): Promise<{
    zuhur: { status: boolean; time: string | null }
    asr: { status: boolean; time: string | null }
    pulang: { status: boolean; time: string | null }
  }> {
    // Wait for attendance cards to load
    await this.attendanceCards.waitFor({ state: 'visible' })

    const getCardStatus = async (statusLocator: Locator, timeLocator: Locator) => {
      const statusElement = statusLocator.first()
      const timeElement = timeLocator.first()

      const statusIcon =
        (await statusElement.locator('svg').first().getAttribute('data-icon')) ||
        (await statusElement.locator('[data-lucide]').first().getAttribute('data-lucide'))
      const status = statusIcon === 'check-circle' || statusIcon === 'check'

      const timeText = await timeElement.textContent()
      const time = timeText?.trim() || null

      return { status, time }
    }

    const zuhur = await getCardStatus(this.zuhurStatus, this.zuhurTime)
    const asr = await getCardStatus(this.asrStatus, this.asrTime)
    const pulang = await getCardStatus(this.pulangStatus, this.pulangTime)

    return { zuhur, asr, pulang }
  }

  /**
   * Switch between tabs
   */
  async switchToTab(tab: 'qr-code' | 'attendance' | 'profile'): Promise<void> {
    switch (tab) {
      case 'qr-code':
        await this.qrCodeTab.click()
        break
      case 'attendance':
        await this.attendanceTab.click()
        break
      case 'profile':
        await this.profileTab.click()
        break
    }

    // Wait for tab content to be visible
    await this.page.waitForTimeout(500)
  }

  /**
   * Logout from the application
   */
  async logout(): Promise<void> {
    await this.logoutButton.click()

    // Wait for redirect to login page
    await this.page.waitForURL('**/student', { timeout: 10000 })
  }

  /**
   * Navigate using bottom navigation
   */
  async navigateToHistory(): Promise<void> {
    await this.historyNavButton.click()
    await this.page.waitForURL('**/student/history')
  }

  async navigateToProfile(): Promise<void> {
    await this.profileNavButton.click()
    await this.page.waitForURL('**/student/profile')
  }

  /**
   * Toggle theme
   */
  async toggleTheme(): Promise<void> {
    await this.themeToggle.click()
    // Wait for theme change to take effect
    await this.page.waitForTimeout(300)
  }

  /**
   * Check if profile failed to load and retry if needed
   */
  async handleProfileLoadFailure(): Promise<boolean> {
    try {
      await this.profileLoadFailedAlert.waitFor({ state: 'visible', timeout: 2000 })
      await this.retryButton.click()
      await this.waitForLoad()
      return true
    } catch {
      return false
    }
  }

  /**
   * Verify page accessibility
   */
  async verifyAccessibility(): Promise<void> {
    // Check for proper ARIA labels and roles
    await expect(this.qrCodeElement).toHaveAttribute('role', 'img')
    await expect(this.qrCodeElement).toHaveAttribute('aria-label')

    // Check for proper heading structure
    const headings = this.page.locator('h1, h2, h3, h4, h5, h6')
    await expect(headings.first()).toBeVisible()

    // Check for keyboard navigation
    await this.tabsList.press('Tab')
    await expect(this.qrCodeTab).toBeFocused()
  }

  /**
   * Verify responsive design
   */
  async verifyResponsiveDesign(): Promise<void> {
    // Test mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 })
    await expect(this.qrCodeContainer).toBeVisible()
    await expect(this.attendanceCards).toBeVisible()
    await expect(this.bottomNav).toBeVisible()

    // Test tablet viewport
    await this.page.setViewportSize({ width: 768, height: 1024 })
    await expect(this.qrCodeContainer).toBeVisible()
    await expect(this.attendanceCards).toBeVisible()

    // Test desktop viewport
    await this.page.setViewportSize({ width: 1024, height: 768 })
    await expect(this.qrCodeContainer).toBeVisible()
    await expect(this.attendanceCards).toBeVisible()
  }

  /**
   * Verify real-time updates (simulate waiting for status change)
   */
  async waitForAttendanceUpdate(timeout = 30000): Promise<void> {
    // This would be used in integration tests where attendance status changes
    const initialStatus = await this.getAttendanceStatus()

    await this.page.waitForFunction(
      initial => {
        // In a real scenario, this would check for actual status changes
        // For now, we'll just verify the function works
        return JSON.stringify(initial) !== JSON.stringify(initial)
      },
      initialStatus,
      { timeout }
    )
  }

  /**
   * Verify WITA timezone display
   */
  async verifyTimezoneDisplay(): Promise<void> {
    const { time } = await this.getCurrentDateTime()

    // Verify time format includes WITA or proper timezone indicator
    expect(time).toMatch(/\d{2}:\d{2}/)

    // Verify date format
    const { date } = await this.getCurrentDateTime()
    expect(date).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4}/)
  }
}
