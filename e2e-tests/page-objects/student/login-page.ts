import { Page, Locator } from '@playwright/test'
import { BasePage } from '../base-page'
import { TestUser } from '../../test-data/users'

export class StudentLoginPage extends BasePage {
  constructor(page: Page) {
    super(page)
  }

  // Page elements
  get pageTitle(): Locator {
    return this.page.locator('div.font-bold:has-text("Masuk ke Akun Siswa")')
  }

  get usernameField(): Locator {
    return this.page.locator('input#username')
  }

  get passwordField(): Locator {
    return this.page.locator('input#password')
  }

  get loginButton(): Locator {
    return this.page.locator('button[type="submit"]:has-text("Login")')
  }

  get forgotPasswordLink(): Locator {
    return this.page.locator('a[href="/student/forgot-password"]:has-text("Lupa password?")')
  }

  get showPasswordButton(): Locator {
    return this.page.locator('button[aria-label*="password"]')
  }

  get errorMessage(): Locator {
    return this.page.locator(
      '.bg-red-50 .text-red-800, .text-red-800:visible, div.text-red-800, div.text-sm.text-red-800'
    )
  }

  get loadingSpinner(): Locator {
    return this.page.locator('button:has-text("Memproses...")')
  }

  // Form validation messages - these might not exist in current implementation
  get usernameValidation(): Locator {
    return this.page.locator('.text-red-500:near(input#username)')
  }

  get passwordValidation(): Locator {
    return this.page.locator('.text-red-500:near(input#password)')
  }

  // Page actions
  async visitLoginPage(): Promise<void> {
    await this.goto('/student')
    await this.waitForPageLoad()
    await this.assertElementVisible(this.pageTitle)
  }

  async fillUsername(username: string): Promise<void> {
    await this.fillAndValidate(this.usernameField, username)
  }

  async fillPassword(password: string): Promise<void> {
    await this.fillAndValidate(this.passwordField, password)
  }

  async clickLogin(): Promise<void> {
    await this.clickAndWait(this.loginButton)
  }

  async togglePasswordVisibility(): Promise<void> {
    if (await this.showPasswordButton.isVisible()) {
      await this.clickAndWait(this.showPasswordButton)
    }
  }

  async clickForgotPassword(): Promise<void> {
    if (await this.forgotPasswordLink.isVisible()) {
      await this.clickAndWait(this.forgotPasswordLink)
      await this.assertPageUrl(/\/student\/forgot-password/)
    } else {
      console.warn('Forgot password link not found - feature may not be implemented')
    }
  }

  async login(user: TestUser): Promise<void> {
    await this.visitLoginPage()
    await this.fillUsername(user.username)
    await this.fillPassword(user.password)
    await this.clickLogin()

    // Wait for either successful redirect or error message
    await Promise.race([
      this.page.waitForURL(/\/student\/home/<USER>
      this.errorMessage.waitFor({ state: 'visible', timeout: 5000 }).catch(() => {}),
    ])

    // Check if we successfully redirected
    const currentUrl = this.page.url()
    console.log(`Current URL after login attempt: ${currentUrl}`)

    const hasError = await this.errorMessage.isVisible()
    console.log(`Error message visible: ${hasError}`)

    if (hasError) {
      const errorText = await this.errorMessage.textContent()
      console.log(`Error message text: ${errorText}`)
    }

    if (currentUrl.includes('/student/home')) {
      // Success - we're on the home page
      console.log('Login successful - redirected to home page')
      return
    } else if (hasError) {
      // Login failed with error message
      const errorText = await this.errorMessage.textContent()
      throw new Error(`Login failed - ${errorText || 'invalid credentials or server error'}`)
    } else {
      // Check the current URL to understand what happened
      throw new Error(`Login failed - unexpected redirect to: ${currentUrl}`)
    }
  }

  async attemptInvalidLogin(username: string, password: string): Promise<void> {
    await this.visitLoginPage()
    await this.fillUsername(username)
    await this.fillPassword(password)
    await this.clickLogin()

    // Wait for either error message or remaining on login page
    await Promise.race([
      this.errorMessage.waitFor({ state: 'visible', timeout: 10000 }),
      this.page.waitForTimeout(5000),
    ])

    // Check if error message is visible or we're still on login page
    const hasError = await this.errorMessage.isVisible()
    const currentUrl = this.page.url()

    if (!hasError && !currentUrl.includes('/student')) {
      throw new Error('Expected error message or to remain on login page')
    }
  }

  async testFormValidation(): Promise<void> {
    await this.visitLoginPage()

    // Test empty form submission
    await this.clickLogin()

    // Wait a bit for any validation to appear
    await this.page.waitForTimeout(2000)

    // The form should either show validation messages or prevent submission
    const currentUrl = this.page.url()
    if (currentUrl.includes('/student/home')) {
      throw new Error('Form should not submit with empty credentials')
    }

    // Test with only username filled
    await this.fillUsername('testuser')
    await this.clickLogin()

    await this.page.waitForTimeout(2000)

    // Should still be on login page
    const urlAfterPartialFill = this.page.url()
    if (urlAfterPartialFill.includes('/student/home')) {
      throw new Error('Form should not submit with only username')
    }
  }

  async testPasswordVisibilityToggle(): Promise<void> {
    await this.visitLoginPage()
    await this.fillPassword('testpassword')

    if (await this.showPasswordButton.isVisible()) {
      // Check initial password field type
      const initialType = await this.getElementAttribute(this.passwordField, 'type')
      if (initialType !== 'password') {
        throw new Error('Password field should initially be of type "password"')
      }

      // Toggle visibility
      await this.togglePasswordVisibility()
      await this.page.waitForTimeout(500) // Wait for toggle to take effect

      // Check if password is now visible
      const newType = await this.getElementAttribute(this.passwordField, 'type')
      if (newType !== 'text') {
        throw new Error('Password field should be of type "text" after toggle')
      }

      // Toggle back
      await this.togglePasswordVisibility()
      await this.page.waitForTimeout(500)

      // Should be hidden again
      const finalType = await this.getElementAttribute(this.passwordField, 'type')
      if (finalType !== 'password') {
        throw new Error('Password field should be of type "password" after second toggle')
      }
    } else {
      console.warn('Password visibility toggle not found - feature may not be implemented')
    }
  }

  async testSingleDeviceEnforcement(user: TestUser): Promise<void> {
    // This feature may not be fully implemented yet
    console.warn('Single device enforcement test - feature may not be fully implemented')

    // First login
    await this.login(user)

    // Get session token for verification
    const firstSessionToken = await this.getLocalStorageItem('auth_token')

    // Open new page/context to simulate second device
    const secondPage = await this.page.context().newPage()
    const secondLoginPage = new StudentLoginPage(secondPage)

    try {
      // Login from second "device"
      await secondLoginPage.login(user)

      // Return to first page and verify session is invalidated
      await this.page.reload()
      await this.page.waitForTimeout(3000)

      // Check if we're redirected to login page due to session invalidation
      const currentUrl = this.page.url()
      if (!currentUrl.includes('/student') || currentUrl.includes('/student/home')) {
        console.warn('Single device enforcement may not be implemented - user not logged out')
      }
    } finally {
      // Clean up
      await secondPage.close()
    }
  }

  async testLoginWithInvalidCredentials(): Promise<void> {
    const invalidCredentials = [
      { username: 'invalid_user', password: 'wrong_password', description: 'both invalid' },
      { username: 'siswa001', password: 'wrong_password', description: 'wrong password' },
      { username: 'invalid_user', password: 'password123', description: 'wrong username' },
      { username: '', password: '', description: 'empty credentials' },
      { username: 'siswa001', password: '', description: 'empty password' },
      { username: '', password: 'password123', description: 'empty username' },
    ]

    for (const cred of invalidCredentials) {
      try {
        await this.attemptInvalidLogin(cred.username, cred.password)

        // Take screenshot for debugging
        await this.takeScreenshot(`login-error-${cred.description.replace(/\s+/g, '-')}`)

        // Verify we're still on login page or got an error
        const hasError = await this.errorMessage.isVisible()
        const currentUrl = this.page.url()

        if (!hasError && !currentUrl.includes('/student')) {
          console.warn(`Expected error for ${cred.description} but none found`)
        }
      } catch (error) {
        console.warn(`Error testing invalid credentials for ${cred.description}:`, error)
      }

      // Navigate back to clean login page
      await this.visitLoginPage()
    }
  }

  async testLoginRateLimiting(): Promise<void> {
    // This feature may not be implemented yet
    console.warn('Login rate limiting test - feature may not be fully implemented')

    const user = { username: 'test_user', password: 'wrong_password' }

    // Attempt multiple failed logins
    for (let i = 0; i < 6; i++) {
      try {
        await this.attemptInvalidLogin(user.username, user.password)
        await this.page.waitForTimeout(1000) // Small delay between attempts
        await this.visitLoginPage() // Reset form
      } catch (error) {
        console.warn(`Rate limiting test attempt ${i + 1} failed:`, error)
      }
    }
  }

  async verifyAccessibilityFeatures(): Promise<void> {
    await this.visitLoginPage()

    // Check for proper labels and aria-labels
    const usernameLabel = await this.getElementAttribute(this.usernameField, 'aria-label')
    const passwordLabel = await this.getElementAttribute(this.passwordField, 'aria-label')

    if (!usernameLabel) {
      // Check for associated label element
      const usernameId = await this.getElementAttribute(this.usernameField, 'id')
      if (usernameId) {
        const labelElement = this.page.locator(`label[for="${usernameId}"]`)
        await this.assertElementVisible(labelElement)
      }
    }

    if (!passwordLabel) {
      const passwordId = await this.getElementAttribute(this.passwordField, 'id')
      if (passwordId) {
        const labelElement = this.page.locator(`label[for="${passwordId}"]`)
        await this.assertElementVisible(labelElement)
      }
    }

    // Test keyboard navigation
    await this.usernameField.focus()
    await this.pressKey('Tab')

    // Should focus password field or toggle button
    const focusedElement = await this.page.evaluate(
      () => document.activeElement?.id || document.activeElement?.tagName
    )
    if (!focusedElement) {
      throw new Error('Tab navigation not working properly')
    }
  }

  async performFullLoginValidation(user: TestUser): Promise<void> {
    // Test successful login
    await this.login(user)

    // Test form validation
    await this.testFormValidation()

    // Test password visibility toggle
    await this.testPasswordVisibilityToggle()

    // Test invalid credentials
    await this.testLoginWithInvalidCredentials()

    // Test accessibility
    await this.verifyAccessibilityFeatures()
  }
}
