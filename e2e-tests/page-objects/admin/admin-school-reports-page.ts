import { Locator, Page, expect } from '@playwright/test'
import { BasePage } from '../base-page'

export class AdminSchoolReportsPage extends BasePage {
  // Page Elements
  readonly pageTitle: Locator
  readonly dateRangeFilter: Locator
  readonly startDateInput: Locator
  readonly endDateInput: Locator
  readonly classFilter: Locator
  readonly gradeFilter: Locator
  readonly attendanceTypeFilter: Locator
  readonly applyFilterButton: Locator
  readonly resetFilterButton: Locator
  readonly exportButton: Locator
  readonly exportCsvButton: Locator
  readonly exportPdfButton: Locator
  readonly exportExcelButton: Locator
  readonly bulkExportButton: Locator
  readonly reportsTable: Locator
  readonly tableHeaders: Locator
  readonly tableRows: Locator
  readonly noDataMessage: Locator
  readonly loadingSpinner: Locator
  readonly paginationContainer: Locator
  readonly prevPageButton: Locator
  readonly nextPageButton: Locator
  readonly pageNumbers: Locator
  readonly rowsPerPageSelect: Locator
  readonly totalRecordsCount: Locator

  // Analytics Cards
  readonly analyticsCards: Locator
  readonly totalStudentsCard: Locator
  readonly entryStudentsCard: Locator
  readonly lateEntryStudentsCard: Locator
  readonly excusedAbsenceCard: Locator
  readonly temporaryLeaveCard: Locator
  readonly sickLeaveCard: Locator
  readonly schoolAttendanceRateCard: Locator

  // Chart Elements
  readonly entryChart: Locator
  readonly classAttendanceChart: Locator
  readonly gradeComparisonChart: Locator
  readonly weeklyTrendChart: Locator
  readonly monthlyTrendChart: Locator
  readonly chartToggleButtons: Locator

  // Class-specific Elements
  readonly classSelector: Locator
  readonly gradeSelector: Locator
  readonly classAnalytics: Locator
  readonly classRankingTable: Locator

  // Permission Elements
  readonly unauthorizedMessage: Locator
  readonly accessDeniedAlert: Locator

  constructor(page: Page) {
    super(page)

    // Page Elements
    this.pageTitle = page.locator('h1', { hasText: 'School Reports' })
    this.dateRangeFilter = page.locator('[data-testid="date-range-filter"]')
    this.startDateInput = page.locator('[data-testid="start-date-input"]')
    this.endDateInput = page.locator('[data-testid="end-date-input"]')
    this.classFilter = page.locator('[data-testid="class-filter"]')
    this.gradeFilter = page.locator('[data-testid="grade-filter"]')
    this.attendanceTypeFilter = page.locator('[data-testid="attendance-type-filter"]')
    this.applyFilterButton = page.locator('[data-testid="apply-filter-button"]')
    this.resetFilterButton = page.locator('[data-testid="reset-filter-button"]')
    this.exportButton = page.locator('[data-testid="export-button"]')
    this.exportCsvButton = page.locator('[data-testid="export-csv-button"]')
    this.exportPdfButton = page.locator('[data-testid="export-pdf-button"]')
    this.exportExcelButton = page.locator('[data-testid="export-excel-button"]')
    this.bulkExportButton = page.locator('[data-testid="bulk-export-button"]')
    this.reportsTable = page.locator('[data-testid="school-reports-table"]')
    this.tableHeaders = page.locator('[data-testid="school-reports-table"] thead th')
    this.tableRows = page.locator('[data-testid="school-reports-table"] tbody tr')
    this.noDataMessage = page.locator('[data-testid="no-data-message"]')
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]')
    this.paginationContainer = page.locator('[data-testid="pagination-container"]')
    this.prevPageButton = page.locator('[data-testid="prev-page-button"]')
    this.nextPageButton = page.locator('[data-testid="next-page-button"]')
    this.pageNumbers = page.locator('[data-testid="page-number"]')
    this.rowsPerPageSelect = page.locator('[data-testid="rows-per-page-select"]')
    this.totalRecordsCount = page.locator('[data-testid="total-records-count"]')

    // Analytics Cards
    this.analyticsCards = page.locator('[data-testid="analytics-cards"]')
    this.totalStudentsCard = page.locator('[data-testid="total-students-card"]')
    this.entryStudentsCard = page.locator('[data-testid="entry-students-card"]')
    this.lateEntryStudentsCard = page.locator('[data-testid="late-entry-students-card"]')
    this.excusedAbsenceCard = page.locator('[data-testid="excused-absence-card"]')
    this.temporaryLeaveCard = page.locator('[data-testid="temporary-leave-card"]')
    this.sickLeaveCard = page.locator('[data-testid="sick-leave-card"]')
    this.schoolAttendanceRateCard = page.locator('[data-testid="school-attendance-rate-card"]')

    // Chart Elements
    this.entryChart = page.locator('[data-testid="entry-chart"]')
    this.classAttendanceChart = page.locator('[data-testid="class-attendance-chart"]')
    this.gradeComparisonChart = page.locator('[data-testid="grade-comparison-chart"]')
    this.weeklyTrendChart = page.locator('[data-testid="weekly-trend-chart"]')
    this.monthlyTrendChart = page.locator('[data-testid="monthly-trend-chart"]')
    this.chartToggleButtons = page.locator('[data-testid="chart-toggle-buttons"]')

    // Class-specific Elements
    this.classSelector = page.locator('[data-testid="class-selector"]')
    this.gradeSelector = page.locator('[data-testid="grade-selector"]')
    this.classAnalytics = page.locator('[data-testid="class-analytics"]')
    this.classRankingTable = page.locator('[data-testid="class-ranking-table"]')

    // Permission Elements
    this.unauthorizedMessage = page.locator('[data-testid="unauthorized-message"]')
    this.accessDeniedAlert = page.locator('[data-testid="access-denied-alert"]')
  }

  // Navigation Methods
  async navigateTo(): Promise<void> {
    await this.page.goto('/admin/school-reports')
    await this.waitForPageLoad()
  }

  async waitForPageLoad(): Promise<void> {
    await this.pageTitle.waitFor({ state: 'visible' })
    await this.waitForNetworkIdle()
  }

  // Filter Methods
  async setDateRange(startDate: string, endDate: string): Promise<void> {
    await this.startDateInput.fill(startDate)
    await this.endDateInput.fill(endDate)
  }

  async selectClass(className: string): Promise<void> {
    await this.classFilter.click()
    await this.page.locator(`[data-value="${className}"]`).click()
  }

  async selectGrade(grade: string): Promise<void> {
    await this.gradeFilter.click()
    await this.page.locator(`[data-value="${grade}"]`).click()
  }

  async selectAttendanceType(type: string): Promise<void> {
    await this.attendanceTypeFilter.click()
    await this.page.locator(`[data-value="${type}"]`).click()
  }

  async applyFilters(): Promise<void> {
    await this.applyFilterButton.click()
    await this.waitForTableLoad()
  }

  async resetFilters(): Promise<void> {
    await this.resetFilterButton.click()
    await this.waitForTableLoad()
  }

  async waitForTableLoad(): Promise<void> {
    await this.loadingSpinner.waitFor({ state: 'hidden' })
    await this.waitForNetworkIdle()
  }

  // Data Verification Methods
  async verifyAnalyticsCards(): Promise<void> {
    await expect(this.analyticsCards).toBeVisible()
    await expect(this.totalStudentsCard).toBeVisible()
    await expect(this.entryStudentsCard).toBeVisible()
    await expect(this.lateEntryStudentsCard).toBeVisible()
    await expect(this.excusedAbsenceCard).toBeVisible()
    await expect(this.temporaryLeaveCard).toBeVisible()
    await expect(this.sickLeaveCard).toBeVisible()
    await expect(this.schoolAttendanceRateCard).toBeVisible()
  }

  async getSchoolAnalyticsData(): Promise<{
    totalStudents: number
    entryStudents: number
    lateEntryStudents: number
    excusedAbsence: number
    temporaryLeave: number
    sickLeave: number
    schoolAttendanceRate: string
  }> {
    const totalStudents = parseInt(
      (await this.totalStudentsCard.locator('.stat-value').textContent()) || '0'
    )
    const entryStudents = parseInt(
      (await this.entryStudentsCard.locator('.stat-value').textContent()) || '0'
    )
    const lateEntryStudents = parseInt(
      (await this.lateEntryStudentsCard.locator('.stat-value').textContent()) || '0'
    )
    const excusedAbsence = parseInt(
      (await this.excusedAbsenceCard.locator('.stat-value').textContent()) || '0'
    )
    const temporaryLeave = parseInt(
      (await this.temporaryLeaveCard.locator('.stat-value').textContent()) || '0'
    )
    const sickLeave = parseInt(
      (await this.sickLeaveCard.locator('.stat-value').textContent()) || '0'
    )
    const schoolAttendanceRate =
      (await this.schoolAttendanceRateCard.locator('.stat-value').textContent()) || '0%'

    return {
      totalStudents,
      entryStudents,
      lateEntryStudents,
      excusedAbsence,
      temporaryLeave,
      sickLeave,
      schoolAttendanceRate,
    }
  }

  async verifyTableHeaders(): Promise<void> {
    const expectedHeaders = [
      'Date',
      'Student Name',
      'Class',
      'Grade',
      'Entry',
      'Late Entry',
      'Excused',
      'Temp Leave',
      'Sick',
      'Actions',
    ]
    const headers = await this.tableHeaders.allTextContents()

    expectedHeaders.forEach(header => {
      expect(headers).toContain(header)
    })
  }

  async getTableRowCount(): Promise<number> {
    return await this.tableRows.count()
  }

  async getTableData(rowIndex: number): Promise<{
    date: string
    studentName: string
    class: string
    grade: string
    entry: string
    lateEntry: string
    excused: string
    tempLeave: string
    sick: string
  }> {
    const row = this.tableRows.nth(rowIndex)
    const cells = row.locator('td')

    return {
      date: (await cells.nth(0).textContent()) || '',
      studentName: (await cells.nth(1).textContent()) || '',
      class: (await cells.nth(2).textContent()) || '',
      grade: (await cells.nth(3).textContent()) || '',
      entry: (await cells.nth(4).textContent()) || '',
      lateEntry: (await cells.nth(5).textContent()) || '',
      excused: (await cells.nth(6).textContent()) || '',
      tempLeave: (await cells.nth(7).textContent()) || '',
      sick: (await cells.nth(8).textContent()) || '',
    }
  }

  // Export Methods
  async exportToCsv(): Promise<void> {
    const downloadPromise = this.page.waitForEvent('download')
    await this.exportCsvButton.click()
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('.csv')
  }

  async exportToPdf(): Promise<void> {
    const downloadPromise = this.page.waitForEvent('download')
    await this.exportPdfButton.click()
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('.pdf')
  }

  async exportToExcel(): Promise<void> {
    const downloadPromise = this.page.waitForEvent('download')
    await this.exportExcelButton.click()
    const download = await downloadPromise
    expect(download.suggestedFilename()).toMatch(/\.(xlsx|xls)$/)
  }

  async bulkExportAllClasses(): Promise<void> {
    const downloadPromise = this.page.waitForEvent('download')
    await this.bulkExportButton.click()
    await this.page.locator('[data-testid="export-all-classes"]').click()
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('bulk-export')
  }

  // Class-Based Analysis Methods
  async selectClassForAnalysis(className: string): Promise<void> {
    await this.classSelector.click()
    await this.page.locator(`[data-value="${className}"]`).click()
    await this.waitForTableLoad()
  }

  async selectGradeForAnalysis(grade: string): Promise<void> {
    await this.gradeSelector.click()
    await this.page.locator(`[data-value="${grade}"]`).click()
    await this.waitForTableLoad()
  }

  async verifyClassAnalytics(): Promise<void> {
    await expect(this.classAnalytics).toBeVisible()
    await expect(this.classRankingTable).toBeVisible()
  }

  async getClassRankingData(): Promise<
    Array<{
      rank: number
      className: string
      attendanceRate: string
      totalStudents: number
    }>
  > {
    const rankings = []
    const rows = await this.classRankingTable.locator('tbody tr').count()

    for (let i = 0; i < rows; i++) {
      const row = this.classRankingTable.locator('tbody tr').nth(i)
      const cells = row.locator('td')

      rankings.push({
        rank: parseInt((await cells.nth(0).textContent()) || '0'),
        className: (await cells.nth(1).textContent()) || '',
        attendanceRate: (await cells.nth(2).textContent()) || '0%',
        totalStudents: parseInt((await cells.nth(3).textContent()) || '0'),
      })
    }

    return rankings
  }

  // Pagination Methods
  async goToNextPage(): Promise<void> {
    await this.nextPageButton.click()
    await this.waitForTableLoad()
  }

  async goToPreviousPage(): Promise<void> {
    await this.prevPageButton.click()
    await this.waitForTableLoad()
  }

  async goToPage(pageNumber: number): Promise<void> {
    await this.pageNumbers.filter({ hasText: pageNumber.toString() }).click()
    await this.waitForTableLoad()
  }

  async changeRowsPerPage(count: string): Promise<void> {
    await this.rowsPerPageSelect.selectOption(count)
    await this.waitForTableLoad()
  }

  // Chart Methods
  async verifyChartsVisible(): Promise<void> {
    await expect(this.entryChart).toBeVisible()
    await expect(this.classAttendanceChart).toBeVisible()
    await expect(this.gradeComparisonChart).toBeVisible()
    await expect(this.weeklyTrendChart).toBeVisible()
  }

  async toggleChart(chartType: 'entry' | 'class' | 'grade' | 'weekly' | 'monthly'): Promise<void> {
    await this.chartToggleButtons.filter({ hasText: chartType }).click()
    await this.page.waitForTimeout(1000) // Wait for chart animation
  }

  async verifyMonthlyTrendChart(): Promise<void> {
    await this.toggleChart('monthly')
    await expect(this.monthlyTrendChart).toBeVisible()
  }

  // Role-Based Access Methods
  async verifyAdminAccess(): Promise<void> {
    await expect(this.pageTitle).toBeVisible()
    await expect(this.unauthorizedMessage).not.toBeVisible()
    await expect(this.exportButton).toBeVisible()
    await expect(this.bulkExportButton).toBeVisible()
  }

  async verifyTeacherAccess(): Promise<void> {
    // Teachers should have read-only access to their class data
    await expect(this.pageTitle).toBeVisible()
    await expect(this.exportButton).toBeVisible()
    await expect(this.bulkExportButton).not.toBeVisible() // No bulk export for teachers
  }

  async verifyReceptionistNoAccess(): Promise<void> {
    await expect(this.unauthorizedMessage).toBeVisible()
    await expect(this.pageTitle).not.toBeVisible()
  }

  async verifyStudentNoAccess(): Promise<void> {
    await expect(this.accessDeniedAlert).toBeVisible()
  }

  // Search and Filter Validation
  async searchStudent(studentName: string): Promise<void> {
    const searchInput = this.page.locator('[data-testid="student-search-input"]')
    await searchInput.fill(studentName)
    await this.page.keyboard.press('Enter')
    await this.waitForTableLoad()
  }

  async verifyFilteredResults(filterType: string, filterValue: string): Promise<void> {
    const rowCount = await this.getTableRowCount()

    if (rowCount > 0) {
      // Verify all visible rows match the filter
      for (let i = 0; i < Math.min(rowCount, 5); i++) {
        const rowData = await this.getTableData(i)
        switch (filterType) {
          case 'class':
            expect(rowData.class.toLowerCase()).toContain(filterValue.toLowerCase())
            break
          case 'grade':
            expect(rowData.grade.toLowerCase()).toContain(filterValue.toLowerCase())
            break
          case 'date':
            expect(rowData.date).toContain(filterValue)
            break
        }
      }
    }
  }

  // Advanced Analytics Methods
  async verifyAttendanceTypeBreakdown(className: string): Promise<void> {
    await this.selectClassForAnalysis(className)
    const analytics = await this.getSchoolAnalyticsData()

    // Verify data consistency
    expect(analytics.totalStudents).toBeGreaterThanOrEqual(0)
    expect(analytics.entryStudents + analytics.lateEntryStudents).toBeLessThanOrEqual(
      analytics.totalStudents
    )
  }

  async compareClassPerformance(): Promise<void> {
    const rankings = await this.getClassRankingData()

    // Verify rankings are sorted correctly
    for (let i = 0; i < rankings.length - 1; i++) {
      expect(rankings[i].rank).toBeLessThan(rankings[i + 1].rank)
    }
  }

  // Performance and Error Handling
  async verifyPageLoadPerformance(): Promise<void> {
    const startTime = Date.now()
    await this.navigateTo()
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(5000) // Page should load within 5 seconds
  }

  async handleNetworkError(): Promise<void> {
    await this.page.route('**/api/admin/school-reports**', route => {
      route.abort('failed')
    })

    await this.navigateTo()
    const errorMessage = this.page.locator('[data-testid="error-message"]')
    await expect(errorMessage).toBeVisible()
  }

  async verifyDataConsistency(): Promise<void> {
    const analytics = await this.getSchoolAnalyticsData()
    expect(analytics.totalStudents).toBeGreaterThanOrEqual(0)
    expect(analytics.entryStudents).toBeLessThanOrEqual(analytics.totalStudents)
    expect(analytics.lateEntryStudents).toBeLessThanOrEqual(analytics.totalStudents)
    expect(
      analytics.excusedAbsence + analytics.temporaryLeave + analytics.sickLeave
    ).toBeLessThanOrEqual(analytics.totalStudents)
  }

  // Advanced Features
  async verifyTimeBasedFiltering(): Promise<void> {
    // Test weekly filtering
    await this.setDateRange('2024-01-01', '2024-01-07')
    await this.applyFilters()
    await this.waitForTableLoad()

    // Test monthly filtering
    await this.setDateRange('2024-01-01', '2024-01-31')
    await this.applyFilters()
    await this.waitForTableLoad()
  }

  async verifyMultiLevelFiltering(): Promise<void> {
    // Apply multiple filters simultaneously
    await this.selectGrade('10')
    await this.selectClass('10A')
    await this.selectAttendanceType('Entry')
    await this.applyFilters()
    await this.verifyFilteredResults('grade', '10')
    await this.verifyFilteredResults('class', '10A')
  }
}
