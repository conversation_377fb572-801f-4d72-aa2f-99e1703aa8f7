import { Locat<PERSON>, Page, expect } from '@playwright/test'
import { BasePage } from '../base-page'

export interface UserFormData {
  name: string
  username: string
  password: string
  role: 'student' | 'admin' | 'super_admin'
  classId?: string
  gender?: 'male' | 'female'
  nis?: string
}

export interface UserTableRow {
  id: number
  name: string
  username: string
  role: string
  className?: string
  gender?: string
  nis?: string
}

export class AdminUsersPage extends BasePage {
  // Header elements
  readonly pageTitle: Locator
  readonly themeToggle: Locator

  // Action buttons
  readonly addUserButton: Locator
  readonly bulkUploadButton: Locator
  readonly downloadQRButton: Locator
  readonly exportCSVButton: Locator
  readonly deleteSelectedButton: Locator

  // Search and filter
  readonly searchInput: Locator
  readonly searchClearButton: Locator
  readonly classFilter: Locator
  readonly genderFilter: Locator
  readonly itemsPerPageSelect: Locator

  // Table elements
  readonly usersTable: Locator
  readonly tableHeaders: Locator
  readonly tableRows: Locator
  readonly selectAllCheckbox: Locator
  readonly loadingSpinner: Locator
  readonly emptyState: Locator

  // Dialog elements
  readonly addEditDialog: Locator
  readonly deleteDialog: Locator
  readonly bulkUploadDialog: Locator

  // Form elements in dialog
  readonly nameInput: Locator
  readonly usernameInput: Locator
  readonly passwordInput: Locator
  readonly roleSelect: Locator
  readonly classSelect: Locator
  readonly genderSelect: Locator
  readonly nisInput: Locator
  readonly submitButton: Locator
  readonly cancelButton: Locator

  // Bulk upload elements
  readonly csvFileInput: Locator
  readonly uploadButton: Locator
  readonly downloadTemplateButton: Locator
  readonly uploadProgress: Locator

  // Error and success messages
  readonly errorMessage: Locator
  readonly successMessage: Locator
  readonly validationErrors: Locator
  readonly toast: Locator

  // Pagination
  readonly paginationInfo: Locator
  readonly paginationButtons: Locator
  readonly previousPageButton: Locator
  readonly nextPageButton: Locator

  // QR Code elements
  readonly qrCodeModal: Locator
  readonly qrCodeImage: Locator
  readonly downloadQRSingleButton: Locator

  constructor(page: Page) {
    super(page)

    // Header elements
    this.pageTitle = page.locator('h1:has-text("Manajemen User"), h1:has-text("Manajemen Siswa")')
    this.themeToggle = page.locator('[data-testid="theme-toggle"]')

    // Action buttons
    this.addUserButton = page.locator('button:has-text("Tambah"), button:has-text("Tambah User")')
    this.bulkUploadButton = page.locator(
      'button:has-text("Import"), button:has-text("Bulk Upload")'
    )
    this.downloadQRButton = page.locator('button:has-text("Download QR")')
    this.exportCSVButton = page.locator('button:has-text("Export"), button:has-text("CSV")')
    this.deleteSelectedButton = page.locator('button:has-text("Hapus Terpilih")')

    // Search and filter
    this.searchInput = page.locator('input[placeholder*="Cari"], [data-testid="search-input"]')
    this.searchClearButton = page.locator(
      'button[aria-label*="clear"], button:has([data-testid="x"])'
    )
    this.classFilter = page.locator('select:near(:text("Kelas")), [data-testid="class-filter"]')
    this.genderFilter = page.locator('select:near(:text("Gender")), [data-testid="gender-filter"]')
    this.itemsPerPageSelect = page.locator('select:near(:text("per halaman"))')

    // Table elements
    this.usersTable = page.locator('table, [data-testid="users-table"]')
    this.tableHeaders = page.locator('table th')
    this.tableRows = page.locator('table tbody tr')
    this.selectAllCheckbox = page.locator('table thead input[type="checkbox"]')
    this.loadingSpinner = page.locator('[data-testid="loading"], .loading')
    this.emptyState = page.locator(':text("Tidak ada"), :text("Belum ada")')

    // Dialog elements
    this.addEditDialog = page.locator('[role="dialog"], .dialog')
    this.deleteDialog = page.locator('[role="alertdialog"], .alert-dialog')
    this.bulkUploadDialog = page.locator(
      '[role="dialog"]:has-text("Import"), [role="dialog"]:has-text("Upload")'
    )

    // Form elements in dialog
    this.nameInput = page.locator('input[name="name"], input[placeholder*="Nama"]')
    this.usernameInput = page.locator('input[name="username"], input[placeholder*="Username"]')
    this.passwordInput = page.locator('input[name="password"], input[placeholder*="Password"]')
    this.roleSelect = page.locator('select[name="role"], [data-testid="role-select"]')
    this.classSelect = page.locator('select[name="classId"], [data-testid="class-select"]')
    this.genderSelect = page.locator('select[name="gender"], [data-testid="gender-select"]')
    this.nisInput = page.locator('input[name="nis"], input[placeholder*="NIS"]')
    this.submitButton = page.locator('button[type="submit"], button:has-text("Simpan")')
    this.cancelButton = page.locator('button:has-text("Batal"), button:has-text("Cancel")')

    // Bulk upload elements
    this.csvFileInput = page.locator('input[type="file"]')
    this.uploadButton = page.locator('button:has-text("Upload")')
    this.downloadTemplateButton = page.locator(
      'button:has-text("Template"), a:has-text("Template")'
    )
    this.uploadProgress = page.locator('[data-testid="upload-progress"], .progress')

    // Error and success messages
    this.errorMessage = page.locator('[role="alert"]:has-text("error"), .error')
    this.successMessage = page.locator('[role="alert"]:has-text("success"), .success')
    this.validationErrors = page.locator('.field-error, [data-testid="validation-error"]')
    this.toast = page.locator('[data-testid="toast"], .toast')

    // Pagination
    this.paginationInfo = page.locator(':text("dari"), :text("total")')
    this.paginationButtons = page.locator('[data-testid="pagination"] button')
    this.previousPageButton = page.locator(
      'button:has-text("Previous"), button:has-text("Sebelumnya")'
    )
    this.nextPageButton = page.locator('button:has-text("Next"), button:has-text("Selanjutnya")')

    // QR Code elements
    this.qrCodeModal = page.locator('[role="dialog"]:has-text("QR")')
    this.qrCodeImage = page.locator('img[alt*="QR"], canvas')
    this.downloadQRSingleButton = page.locator('button:has-text("Download QR")')
  }

  async goto() {
    await this.page.goto('/admin/users')
    await this.waitForLoad()
  }

  async waitForLoad() {
    await expect(this.pageTitle).toBeVisible()
    await expect(this.addUserButton).toBeVisible()
    // Wait for table to load
    await this.page.waitForLoadState('networkidle')
  }

  async waitForTableLoad() {
    await expect(this.loadingSpinner).toBeHidden()
    await this.page.waitForTimeout(500)
  }

  // User management methods
  async addUser(userData: UserFormData) {
    await this.addUserButton.click()
    await expect(this.addEditDialog).toBeVisible()

    await this.fillUserForm(userData)
    await this.submitButton.click()

    // Wait for success or error
    await this.page.waitForTimeout(1000)
  }

  async editUser(userRow: number, userData: Partial<UserFormData>) {
    const editButton = this.tableRows
      .nth(userRow)
      .locator('button:has([data-testid="edit"]), button:has-text("Edit")')
    await editButton.click()
    await expect(this.addEditDialog).toBeVisible()

    await this.fillUserForm(userData, true)
    await this.submitButton.click()

    // Wait for success or error
    await this.page.waitForTimeout(1000)
  }

  async deleteUser(userRow: number) {
    const deleteButton = this.tableRows
      .nth(userRow)
      .locator('button:has([data-testid="delete"]), button:has-text("Hapus")')
    await deleteButton.click()
    await expect(this.deleteDialog).toBeVisible()

    const confirmButton = this.deleteDialog.locator(
      'button:has-text("Hapus"), button:has-text("Ya")'
    )
    await confirmButton.click()

    // Wait for deletion to complete
    await this.page.waitForTimeout(1000)
  }

  async fillUserForm(userData: Partial<UserFormData>, isEdit: boolean = false) {
    if (userData.name) {
      await this.nameInput.fill(userData.name)
    }

    if (userData.username && !isEdit) {
      await this.usernameInput.fill(userData.username)
    }

    if (userData.password) {
      await this.passwordInput.fill(userData.password)
    }

    if (userData.role) {
      await this.roleSelect.selectOption(userData.role)
    }

    if (userData.classId) {
      await this.classSelect.selectOption(userData.classId)
    }

    if (userData.gender) {
      await this.genderSelect.selectOption(userData.gender)
    }

    if (userData.nis) {
      await this.nisInput.fill(userData.nis)
    }
  }

  // Search and filter methods
  async searchUsers(query: string) {
    await this.searchInput.fill(query)
    await this.page.waitForTimeout(500)
    await this.waitForTableLoad()
  }

  async clearSearch() {
    if (await this.searchClearButton.isVisible()) {
      await this.searchClearButton.click()
    } else {
      await this.searchInput.clear()
    }
    await this.waitForTableLoad()
  }

  async filterByClass(className: string) {
    await this.classFilter.selectOption(className)
    await this.waitForTableLoad()
  }

  async filterByGender(gender: string) {
    await this.genderFilter.selectOption(gender)
    await this.waitForTableLoad()
  }

  async changeItemsPerPage(itemCount: string) {
    await this.itemsPerPageSelect.selectOption(itemCount)
    await this.waitForTableLoad()
  }

  // Table interaction methods
  async getTableRowCount(): Promise<number> {
    await this.waitForTableLoad()
    return await this.tableRows.count()
  }

  async getUserData(row: number): Promise<UserTableRow> {
    const rowElement = this.tableRows.nth(row)
    const cells = rowElement.locator('td')

    return {
      id: parseInt((await cells.nth(0).getAttribute('data-user-id')) || '0'),
      name: (await cells.nth(1).textContent()) || '',
      username: (await cells.nth(2).textContent()) || '',
      role: (await cells.nth(3).textContent()) || '',
      className: (await cells.nth(4).textContent()) || '',
      gender: (await cells.nth(5).textContent()) || '',
      nis: (await cells.nth(6).textContent()) || '',
    }
  }

  async selectUser(row: number) {
    const checkbox = this.tableRows.nth(row).locator('input[type="checkbox"]')
    await checkbox.check()
  }

  async selectAllUsers() {
    await this.selectAllCheckbox.check()
  }

  async getSelectedUserCount(): Promise<number> {
    const checkedBoxes = this.tableRows.locator('input[type="checkbox"]:checked')
    return await checkedBoxes.count()
  }

  // Bulk operations
  async bulkUploadUsers(csvContent: string) {
    await this.bulkUploadButton.click()
    await expect(this.bulkUploadDialog).toBeVisible()

    // Create a temporary file
    const buffer = Buffer.from(csvContent, 'utf-8')
    await this.csvFileInput.setInputFiles({
      name: 'test-users.csv',
      mimeType: 'text/csv',
      buffer: buffer,
    })

    await this.uploadButton.click()
    await this.waitForUploadProgress()
  }

  async waitForUploadProgress() {
    // Wait for upload to complete
    await expect(this.uploadProgress).toBeVisible()
    await expect(this.uploadProgress).toBeHidden({ timeout: 30000 })
  }

  async downloadTemplate() {
    const downloadPromise = this.page.waitForDownload()
    await this.downloadTemplateButton.click()
    const download = await downloadPromise
    return download.suggestedFilename()
  }

  async exportToCSV() {
    const downloadPromise = this.page.waitForDownload()
    await this.exportCSVButton.click()
    const download = await downloadPromise
    return download.suggestedFilename()
  }

  async deleteSelectedUsers() {
    await this.deleteSelectedButton.click()
    await expect(this.deleteDialog).toBeVisible()

    const confirmButton = this.deleteDialog.locator(
      'button:has-text("Hapus"), button:has-text("Ya")'
    )
    await confirmButton.click()

    // Wait for deletion to complete
    await this.page.waitForTimeout(2000)
  }

  // QR Code methods
  async viewUserQR(row: number) {
    const qrButton = this.tableRows.nth(row).locator('button:has-text("QR")')
    await qrButton.click()
    await expect(this.qrCodeModal).toBeVisible()
  }

  async downloadUserQR() {
    const downloadPromise = this.page.waitForDownload()
    await this.downloadQRSingleButton.click()
    const download = await downloadPromise
    return download.suggestedFilename()
  }

  async downloadAllQRCodes() {
    const downloadPromise = this.page.waitForDownload()
    await this.downloadQRButton.click()
    const download = await downloadPromise
    return download.suggestedFilename()
  }

  // Pagination methods
  async goToNextPage() {
    await this.nextPageButton.click()
    await this.waitForTableLoad()
  }

  async goToPreviousPage() {
    await this.previousPageButton.click()
    await this.waitForTableLoad()
  }

  async goToPage(pageNumber: number) {
    const pageButton = this.paginationButtons.filter({ hasText: pageNumber.toString() })
    await pageButton.click()
    await this.waitForTableLoad()
  }

  async getPaginationInfo(): Promise<string> {
    return (await this.paginationInfo.textContent()) || ''
  }

  // Validation and error methods
  async getValidationErrors(): Promise<string[]> {
    const errors = await this.validationErrors.allTextContents()
    return errors.filter(error => error.trim().length > 0)
  }

  async getToastMessage(): Promise<string> {
    await expect(this.toast).toBeVisible()
    return (await this.toast.textContent()) || ''
  }

  async waitForSuccessToast() {
    await expect(this.toast).toBeVisible()
    await expect(this.toast).toContainText('berhasil')
  }

  async waitForErrorToast() {
    await expect(this.toast).toBeVisible()
    await expect(this.toast).toContainText('gagal')
  }

  // Role-based access verification
  async verifyPageAccess(expectedAccess: boolean) {
    if (expectedAccess) {
      await expect(this.pageTitle).toBeVisible()
      await expect(this.addUserButton).toBeVisible()
    } else {
      // Should show access denied or redirect
      await expect(this.page.locator(':has-text("Akses Ditolak")')).toBeVisible()
    }
  }

  async verifySuperAdminFeatures() {
    await expect(this.addUserButton).toBeVisible()
    await expect(this.bulkUploadButton).toBeVisible()
    await expect(this.deleteSelectedButton).toBeVisible()
  }

  // Data validation methods
  async verifyUserInTable(userData: Partial<UserTableRow>): Promise<boolean> {
    await this.waitForTableLoad()
    const rowCount = await this.getTableRowCount()

    for (let i = 0; i < rowCount; i++) {
      const rowData = await this.getUserData(i)

      if (
        userData.name &&
        rowData.name.includes(userData.name) &&
        userData.username &&
        rowData.username.includes(userData.username)
      ) {
        return true
      }
    }
    return false
  }

  async findUserRowByName(name: string): Promise<number> {
    const rowCount = await this.getTableRowCount()

    for (let i = 0; i < rowCount; i++) {
      const rowData = await this.getUserData(i)
      if (rowData.name.includes(name)) {
        return i
      }
    }
    return -1
  }

  // Security testing methods
  async testXSSInUserForm(xssPayload: string) {
    await this.addUserButton.click()
    await expect(this.addEditDialog).toBeVisible()

    await this.nameInput.fill(xssPayload)
    await this.usernameInput.fill('test_user')
    await this.passwordInput.fill('password123')

    // Should escape or sanitize the input
    const nameValue = await this.nameInput.inputValue()
    expect(nameValue).not.toContain('<script>')
  }

  async testSQLInjectionInSearch(sqlPayload: string) {
    await this.searchUsers(sqlPayload)

    // Should not cause database errors
    await expect(this.errorMessage).not.toBeVisible()
    await this.clearSearch()
  }

  // Performance testing
  async measurePageLoadTime(): Promise<number> {
    const startTime = Date.now()
    await this.goto()
    await this.waitForTableLoad()
    return Date.now() - startTime
  }

  async measureSearchTime(query: string): Promise<number> {
    const startTime = Date.now()
    await this.searchUsers(query)
    return Date.now() - startTime
  }

  // Accessibility methods
  async verifyAccessibility() {
    // Check for proper ARIA labels
    await expect(this.searchInput).toHaveAttribute('aria-label')
    await expect(this.usersTable).toHaveAttribute('role', 'table')

    // Check keyboard navigation
    await this.searchInput.focus()
    await this.page.keyboard.press('Tab')
    await expect(this.addUserButton).toBeFocused()
  }

  async testKeyboardNavigation() {
    // Test tab navigation through main elements
    await this.searchInput.focus()
    await this.page.keyboard.press('Tab')
    await expect(this.classFilter).toBeFocused()

    await this.page.keyboard.press('Tab')
    await expect(this.addUserButton).toBeFocused()
  }

  // Responsive design verification
  async verifyResponsiveDesign() {
    // Test mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 })
    await expect(this.pageTitle).toBeVisible()
    await expect(this.addUserButton).toBeVisible()

    // Test tablet viewport
    await this.page.setViewportSize({ width: 768, height: 1024 })
    await expect(this.usersTable).toBeVisible()

    // Reset to desktop
    await this.page.setViewportSize({ width: 1280, height: 720 })
  }
}
