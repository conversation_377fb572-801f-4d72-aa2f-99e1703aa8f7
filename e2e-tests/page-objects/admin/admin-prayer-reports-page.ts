import { Locator, Page, expect } from '@playwright/test'
import { BasePage } from '../base-page'

export class AdminPrayerReportsPage extends BasePage {
  // Page Elements
  readonly pageTitle: Locator
  readonly dateRangeFilter: Locator
  readonly startDateInput: Locator
  readonly endDateInput: Locator
  readonly classFilter: Locator
  readonly attendanceTypeFilter: Locator
  readonly applyFilterButton: Locator
  readonly resetFilterButton: Locator
  readonly exportButton: Locator
  readonly exportCsvButton: Locator
  readonly exportPdfButton: Locator
  readonly reportsTable: Locator
  readonly tableHeaders: Locator
  readonly tableRows: Locator
  readonly noDataMessage: Locator
  readonly loadingSpinner: Locator
  readonly paginationContainer: Locator
  readonly prevPageButton: Locator
  readonly nextPageButton: Locator
  readonly pageNumbers: Locator
  readonly rowsPerPageSelect: Locator
  readonly totalRecordsCount: Locator

  // Analytics Cards
  readonly analyticsCards: Locator
  readonly totalStudentsCard: Locator
  readonly presentStudentsCard: Locator
  readonly absentStudentsCard: Locator
  readonly attendanceRateCard: Locator

  // Chart Elements
  readonly attendanceChart: Locator
  readonly classComparisonChart: Locator
  readonly weeklyTrendChart: Locator
  readonly chartToggleButtons: Locator

  // Permission Elements
  readonly unauthorizedMessage: Locator
  readonly accessDeniedAlert: Locator

  constructor(page: Page) {
    super(page)

    // Page Elements
    this.pageTitle = page.locator('h1', { hasText: 'Prayer Reports' })
    this.dateRangeFilter = page.locator('[data-testid="date-range-filter"]')
    this.startDateInput = page.locator('[data-testid="start-date-input"]')
    this.endDateInput = page.locator('[data-testid="end-date-input"]')
    this.classFilter = page.locator('[data-testid="class-filter"]')
    this.attendanceTypeFilter = page.locator('[data-testid="attendance-type-filter"]')
    this.applyFilterButton = page.locator('[data-testid="apply-filter-button"]')
    this.resetFilterButton = page.locator('[data-testid="reset-filter-button"]')
    this.exportButton = page.locator('[data-testid="export-button"]')
    this.exportCsvButton = page.locator('[data-testid="export-csv-button"]')
    this.exportPdfButton = page.locator('[data-testid="export-pdf-button"]')
    this.reportsTable = page.locator('[data-testid="reports-table"]')
    this.tableHeaders = page.locator('[data-testid="reports-table"] thead th')
    this.tableRows = page.locator('[data-testid="reports-table"] tbody tr')
    this.noDataMessage = page.locator('[data-testid="no-data-message"]')
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]')
    this.paginationContainer = page.locator('[data-testid="pagination-container"]')
    this.prevPageButton = page.locator('[data-testid="prev-page-button"]')
    this.nextPageButton = page.locator('[data-testid="next-page-button"]')
    this.pageNumbers = page.locator('[data-testid="page-number"]')
    this.rowsPerPageSelect = page.locator('[data-testid="rows-per-page-select"]')
    this.totalRecordsCount = page.locator('[data-testid="total-records-count"]')

    // Analytics Cards
    this.analyticsCards = page.locator('[data-testid="analytics-cards"]')
    this.totalStudentsCard = page.locator('[data-testid="total-students-card"]')
    this.presentStudentsCard = page.locator('[data-testid="present-students-card"]')
    this.absentStudentsCard = page.locator('[data-testid="absent-students-card"]')
    this.attendanceRateCard = page.locator('[data-testid="attendance-rate-card"]')

    // Chart Elements
    this.attendanceChart = page.locator('[data-testid="attendance-chart"]')
    this.classComparisonChart = page.locator('[data-testid="class-comparison-chart"]')
    this.weeklyTrendChart = page.locator('[data-testid="weekly-trend-chart"]')
    this.chartToggleButtons = page.locator('[data-testid="chart-toggle-buttons"]')

    // Permission Elements
    this.unauthorizedMessage = page.locator('[data-testid="unauthorized-message"]')
    this.accessDeniedAlert = page.locator('[data-testid="access-denied-alert"]')
  }

  // Navigation Methods
  async navigateTo(): Promise<void> {
    await this.page.goto('/admin/prayer-reports')
    await this.waitForPageLoad()
  }

  async waitForPageLoad(): Promise<void> {
    await this.pageTitle.waitFor({ state: 'visible' })
    await this.waitForNetworkIdle()
  }

  // Filter Methods
  async setDateRange(startDate: string, endDate: string): Promise<void> {
    await this.startDateInput.fill(startDate)
    await this.endDateInput.fill(endDate)
  }

  async selectClass(className: string): Promise<void> {
    await this.classFilter.click()
    await this.page.locator(`[data-value="${className}"]`).click()
  }

  async selectAttendanceType(type: string): Promise<void> {
    await this.attendanceTypeFilter.click()
    await this.page.locator(`[data-value="${type}"]`).click()
  }

  async applyFilters(): Promise<void> {
    await this.applyFilterButton.click()
    await this.waitForTableLoad()
  }

  async resetFilters(): Promise<void> {
    await this.resetFilterButton.click()
    await this.waitForTableLoad()
  }

  async waitForTableLoad(): Promise<void> {
    await this.loadingSpinner.waitFor({ state: 'hidden' })
    await this.waitForNetworkIdle()
  }

  // Data Verification Methods
  async verifyAnalyticsCards(): Promise<void> {
    await expect(this.analyticsCards).toBeVisible()
    await expect(this.totalStudentsCard).toBeVisible()
    await expect(this.presentStudentsCard).toBeVisible()
    await expect(this.absentStudentsCard).toBeVisible()
    await expect(this.attendanceRateCard).toBeVisible()
  }

  async getAnalyticsData(): Promise<{
    totalStudents: number
    presentStudents: number
    absentStudents: number
    attendanceRate: string
  }> {
    const totalStudents = parseInt(
      (await this.totalStudentsCard.locator('.stat-value').textContent()) || '0'
    )
    const presentStudents = parseInt(
      (await this.presentStudentsCard.locator('.stat-value').textContent()) || '0'
    )
    const absentStudents = parseInt(
      (await this.absentStudentsCard.locator('.stat-value').textContent()) || '0'
    )
    const attendanceRate =
      (await this.attendanceRateCard.locator('.stat-value').textContent()) || '0%'

    return {
      totalStudents,
      presentStudents,
      absentStudents,
      attendanceRate,
    }
  }

  async verifyTableHeaders(): Promise<void> {
    const expectedHeaders = [
      'Date',
      'Student Name',
      'Class',
      'Zuhr',
      'Asr',
      'Pulang',
      'Ijin',
      'Actions',
    ]
    const headers = await this.tableHeaders.allTextContents()

    expectedHeaders.forEach(header => {
      expect(headers).toContain(header)
    })
  }

  async getTableRowCount(): Promise<number> {
    return await this.tableRows.count()
  }

  async getTableData(rowIndex: number): Promise<{
    date: string
    studentName: string
    class: string
    zuhr: string
    asr: string
    pulang: string
    ijin: string
  }> {
    const row = this.tableRows.nth(rowIndex)
    const cells = row.locator('td')

    return {
      date: (await cells.nth(0).textContent()) || '',
      studentName: (await cells.nth(1).textContent()) || '',
      class: (await cells.nth(2).textContent()) || '',
      zuhr: (await cells.nth(3).textContent()) || '',
      asr: (await cells.nth(4).textContent()) || '',
      pulang: (await cells.nth(5).textContent()) || '',
      ijin: (await cells.nth(6).textContent()) || '',
    }
  }

  // Export Methods
  async exportToCsv(): Promise<void> {
    const downloadPromise = this.page.waitForEvent('download')
    await this.exportCsvButton.click()
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('.csv')
  }

  async exportToPdf(): Promise<void> {
    const downloadPromise = this.page.waitForEvent('download')
    await this.exportPdfButton.click()
    const download = await downloadPromise
    expect(download.suggestedFilename()).toContain('.pdf')
  }

  // Pagination Methods
  async goToNextPage(): Promise<void> {
    await this.nextPageButton.click()
    await this.waitForTableLoad()
  }

  async goToPreviousPage(): Promise<void> {
    await this.prevPageButton.click()
    await this.waitForTableLoad()
  }

  async goToPage(pageNumber: number): Promise<void> {
    await this.pageNumbers.filter({ hasText: pageNumber.toString() }).click()
    await this.waitForTableLoad()
  }

  async changeRowsPerPage(count: string): Promise<void> {
    await this.rowsPerPageSelect.selectOption(count)
    await this.waitForTableLoad()
  }

  // Chart Methods
  async verifyChartsVisible(): Promise<void> {
    await expect(this.attendanceChart).toBeVisible()
    await expect(this.classComparisonChart).toBeVisible()
    await expect(this.weeklyTrendChart).toBeVisible()
  }

  async toggleChart(chartType: 'attendance' | 'comparison' | 'trend'): Promise<void> {
    await this.chartToggleButtons.filter({ hasText: chartType }).click()
    await this.page.waitForTimeout(1000) // Wait for chart animation
  }

  // Role-Based Access Methods
  async verifyAdminAccess(): Promise<void> {
    await expect(this.pageTitle).toBeVisible()
    await expect(this.unauthorizedMessage).not.toBeVisible()
  }

  async verifyTeacherAccess(): Promise<void> {
    // Teachers should have read-only access
    await expect(this.pageTitle).toBeVisible()
    await expect(this.exportButton).toBeVisible()
    // Verify no edit/delete actions available
  }

  async verifyReceptionistNoAccess(): Promise<void> {
    await expect(this.unauthorizedMessage).toBeVisible()
    await expect(this.pageTitle).not.toBeVisible()
  }

  async verifyStudentNoAccess(): Promise<void> {
    await expect(this.accessDeniedAlert).toBeVisible()
  }

  // Search and Filter Validation
  async searchStudent(studentName: string): Promise<void> {
    const searchInput = this.page.locator('[data-testid="student-search-input"]')
    await searchInput.fill(studentName)
    await this.page.keyboard.press('Enter')
    await this.waitForTableLoad()
  }

  async verifyFilteredResults(filterType: string, filterValue: string): Promise<void> {
    const rowCount = await this.getTableRowCount()

    if (rowCount > 0) {
      // Verify all visible rows match the filter
      for (let i = 0; i < Math.min(rowCount, 5); i++) {
        const rowData = await this.getTableData(i)
        switch (filterType) {
          case 'class':
            expect(rowData.class.toLowerCase()).toContain(filterValue.toLowerCase())
            break
          case 'date':
            expect(rowData.date).toContain(filterValue)
            break
        }
      }
    }
  }

  // Performance and Error Handling
  async verifyPageLoadPerformance(): Promise<void> {
    const startTime = Date.now()
    await this.navigateTo()
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(5000) // Page should load within 5 seconds
  }

  async handleNetworkError(): Promise<void> {
    await this.page.route('**/api/admin/prayer-reports**', route => {
      route.abort('failed')
    })

    await this.navigateTo()
    const errorMessage = this.page.locator('[data-testid="error-message"]')
    await expect(errorMessage).toBeVisible()
  }

  async verifyDataConsistency(): Promise<void> {
    const analytics = await this.getAnalyticsData()
    expect(analytics.totalStudents).toBeGreaterThanOrEqual(0)
    expect(analytics.presentStudents).toBeLessThanOrEqual(analytics.totalStudents)
    expect(analytics.absentStudents).toBeLessThanOrEqual(analytics.totalStudents)
    expect(analytics.presentStudents + analytics.absentStudents).toBeLessThanOrEqual(
      analytics.totalStudents
    )
  }
}
