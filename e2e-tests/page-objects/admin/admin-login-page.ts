import { Locat<PERSON>, Page, expect } from '@playwright/test'
import { BasePage } from '../base-page'
import { TestUser } from '../../test-data/users'

export class AdminLoginPage extends BasePage {
  // Form elements
  readonly usernameInput: Locator
  readonly passwordInput: Locator
  readonly loginButton: Locator
  readonly showPasswordButton: Locator

  // Page elements
  readonly pageTitle: Locator
  readonly loginForm: Locator
  readonly logoImage: Locator
  readonly themeToggle: Locator

  // Error and validation - override the base class getters
  get errorMessage(): Locator {
    return this.page.locator(
      '[role="alert"] .text-red-600, .text-red-600, div[data-state="open"] .text-red-600'
    )
  }

  get loadingSpinner(): Locator {
    return this.page.locator(
      'button:has-text("Memproses..."), [data-testid="loading"], .loading, .spinner'
    )
  }

  readonly validationErrors: Locator

  // <PERSON>s
  readonly forgotPasswordLink: Locator
  readonly studentLoginLink: Locator

  constructor(page: Page) {
    super(page)

    // Form elements based on actual implementation
    this.usernameInput = page.locator('input#username')
    this.passwordInput = page.locator('input#password')
    this.loginButton = page.locator('button[type="submit"]:has-text("Masuk")')
    this.showPasswordButton = page.locator('button[aria-label*="password"]')

    // Page elements
    this.pageTitle = page.locator(
      'h1:has-text("Masuk ke Akun Admin"), h2:has-text("Masuk ke Akun Admin")'
    )
    this.loginForm = page.locator('form')
    this.logoImage = page.locator('img[alt*="logo"]')
    this.themeToggle = page.locator('[data-testid="theme-toggle"]')

    // Validation elements
    this.validationErrors = page.locator('.field-error, .text-red-500')

    // Links (these may not exist in current implementation)
    this.forgotPasswordLink = page.locator('a:has-text("Lupa Password")')
    this.studentLoginLink = page.locator('a:has-text("Siswa")')
  }

  async goto() {
    await this.page.goto('/admin')
    await this.waitForLoad()
  }

  async waitForLoad() {
    await expect(this.loginForm).toBeVisible()
    await expect(this.usernameInput).toBeVisible()
    await expect(this.passwordInput).toBeVisible()
    await expect(this.loginButton).toBeVisible()
  }

  async login(user: TestUser) {
    console.log(`Attempting admin login with username: ${user.username}, role: ${user.role}`)

    await this.usernameInput.fill(user.username)
    await this.passwordInput.fill(user.password)

    // Take screenshot before clicking login
    await this.takeScreenshot(`admin-login-before-submit-${user.username}`)

    // Wait for the form to be ready
    await expect(this.loginButton).toBeEnabled()

    console.log('Clicking login button...')
    await this.loginButton.click()

    // Wait a bit for form submission to process
    await this.page.waitForTimeout(3000)

    // Take screenshot after clicking login
    await this.takeScreenshot(`admin-login-after-submit-${user.username}`)

    // Check current state
    const currentUrl = this.page.url()
    console.log(`Admin login - Current URL after submission: ${currentUrl}`)

    const hasError = await this.errorMessage.isVisible()
    console.log(`Admin login - Error message visible: ${hasError}`)

    const hasLoading = await this.loadingSpinner.isVisible()
    console.log(`Admin login - Loading spinner visible: ${hasLoading}`)

    if (hasError) {
      const errorText = await this.errorMessage.textContent()
      console.log(`Admin login - Error message text: ${errorText}`)
      throw new Error(`Admin login failed - ${errorText || 'invalid credentials or server error'}`)
    }

    // Check if we're still on login page - this might indicate form validation issues
    if (currentUrl.includes('/admin') && !currentUrl.includes('/admin/home')) {
      // Check if the form fields still have the values (indicating no submission)
      const usernameValue = await this.usernameInput.inputValue()
      const passwordValue = await this.passwordInput.inputValue()

      console.log(`Username field value: ${usernameValue}`)
      console.log(`Password field value: ${passwordValue}`)

      // Check for any console errors
      const logs = await this.page.evaluate(() => {
        return console
      })

      // If we're still on login page and no error message, something might be preventing submission
      if (usernameValue && passwordValue) {
        throw new Error(
          `Admin login failed - form appears to not have submitted. Current URL: ${currentUrl}`
        )
      }
    }

    // Try waiting for redirect with longer timeout
    try {
      await this.page.waitForURL(/\/admin\/home/<USER>
      console.log('Admin login successful - redirected to home page')
      return
    } catch (redirectError) {
      const finalUrl = this.page.url()
      console.log(`Admin login - Final URL after redirect timeout: ${finalUrl}`)

      // Check if we ended up somewhere else that might be valid
      if (
        finalUrl.includes('/admin/') &&
        !finalUrl.includes('/admin/login') &&
        !finalUrl.endsWith('/admin')
      ) {
        console.log('Admin login successful - redirected to admin area')
        return
      }

      throw new Error(`Admin login failed - no redirect to admin home. Final URL: ${finalUrl}`)
    }
  }

  async attemptLogin(username: string, password: string) {
    await this.usernameInput.fill(username)
    await this.passwordInput.fill(password)
    await this.loginButton.click()
  }

  async loginWithValidation(user: TestUser) {
    await this.usernameInput.fill(user.username)
    await this.passwordInput.fill(user.password)

    // Submit without waiting for navigation
    await this.loginButton.click()
  }

  async getErrorMessage(): Promise<string> {
    if (await this.errorMessage.isVisible()) {
      return (await this.errorMessage.textContent()) || ''
    }
    return ''
  }

  async getValidationErrors(): Promise<string[]> {
    const errors = await this.validationErrors.allTextContents()
    return errors.filter(error => error.trim().length > 0)
  }

  async clearForm() {
    await this.usernameInput.clear()
    await this.passwordInput.clear()
  }

  async togglePasswordVisibility() {
    if (await this.showPasswordButton.isVisible()) {
      const passwordType = await this.passwordInput.getAttribute('type')
      await this.showPasswordButton.click()
      await this.page.waitForTimeout(500)

      // Verify password visibility toggle
      const newPasswordType = await this.passwordInput.getAttribute('type')
      expect(newPasswordType).not.toBe(passwordType)
    } else {
      console.warn('Password visibility toggle not found - feature may not be implemented')
    }
  }

  async submitEmptyForm() {
    await this.loginButton.click()
    // Should stay on login page
    await expect(this.loginForm).toBeVisible()
  }

  async validateFormFields() {
    // Test empty form submission
    await this.submitEmptyForm()

    // Should show validation errors or remain on page
    const currentUrl = this.page.url()
    if (currentUrl.includes('/admin/home')) {
      throw new Error('Form should not submit with empty credentials')
    }
  }

  async testInvalidCredentials() {
    await this.attemptLogin('invalid_user', 'invalid_password')

    // Wait for error message or remaining on login page
    await Promise.race([
      this.errorMessage.waitFor({ state: 'visible', timeout: 10000 }),
      this.page.waitForTimeout(5000),
    ])

    const hasError = await this.errorMessage.isVisible()
    const currentUrl = this.page.url()

    if (!hasError && !currentUrl.includes('/admin')) {
      throw new Error('Expected error message or to remain on admin login page')
    }
  }

  async navigateToStudentLogin() {
    if (await this.studentLoginLink.isVisible()) {
      await this.studentLoginLink.click()
      await expect(this.page).toHaveURL(/\/student/)
    } else {
      console.warn('Student login link not found - feature may not be implemented')
    }
  }

  async navigateToForgotPassword() {
    if (await this.forgotPasswordLink.isVisible()) {
      await this.forgotPasswordLink.click()
      await expect(this.page).toHaveURL(/\/admin\/forgot-password/)
    } else {
      console.warn('Forgot password link not found - feature may not be implemented')
    }
  }

  // Theme and UI methods
  async toggleTheme() {
    if (await this.themeToggle.isVisible()) {
      const currentTheme = await this.page.locator('html').getAttribute('class')
      await this.themeToggle.click()
      await this.page.waitForTimeout(500)

      const newTheme = await this.page.locator('html').getAttribute('class')
      expect(newTheme).not.toBe(currentTheme)
    } else {
      console.warn('Theme toggle not found - feature may not be implemented')
    }
  }

  // Security testing methods
  async testSQLInjection() {
    const sqlPayloads = ["' OR '1'='1", "'; DROP TABLE users; --", "' UNION SELECT * FROM users --"]

    for (const payload of sqlPayloads) {
      await this.attemptLogin(payload, 'password')

      // Should not allow SQL injection
      const hasError = await this.errorMessage.isVisible()
      const currentUrl = this.page.url()

      if (!hasError && !currentUrl.includes('/admin')) {
        console.warn(`SQL injection payload might have been processed: ${payload}`)
      }

      await this.clearForm()
    }
  }

  async testXSSAttack() {
    const xssPayloads = [
      '<script>alert("xss")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert("xss")',
    ]

    for (const payload of xssPayloads) {
      await this.attemptLogin(payload, 'password')

      // Should escape or sanitize the input
      const usernameValue = await this.usernameInput.inputValue()
      expect(usernameValue).not.toContain('<script>')
      await this.clearForm()
    }
  }

  async testCSRFProtection() {
    // Test CSRF token presence (if implemented)
    const csrfToken = await this.page.locator('input[name="_token"], meta[name="csrf-token"]')

    if (await csrfToken.isVisible()) {
      const tokenValue = await csrfToken.getAttribute('value')
      expect(tokenValue).toBeTruthy()
    } else {
      console.warn('CSRF token not found - may be handled differently or not implemented')
    }
  }

  async verifyAccessibility() {
    // Check for proper labels
    const usernameLabel = await this.page.locator('label[for="username"]')
    const passwordLabel = await this.page.locator('label[for="password"]')

    await expect(usernameLabel).toBeVisible()
    await expect(passwordLabel).toBeVisible()

    // Test keyboard navigation
    await this.usernameInput.focus()
    await this.page.keyboard.press('Tab')

    // Should focus password field
    const focusedElement = await this.page.evaluate(() => document.activeElement?.id)
    expect(['password', '']).toContain(focusedElement || '')
  }

  async verifyResponsiveDesign() {
    const viewports = [
      { width: 320, height: 568 }, // Mobile
      { width: 768, height: 1024 }, // Tablet
      { width: 1280, height: 720 }, // Desktop
    ]

    for (const viewport of viewports) {
      await this.page.setViewportSize(viewport)
      await this.page.waitForTimeout(500)

      // All essential elements should still be visible
      await expect(this.loginForm).toBeVisible()
      await expect(this.usernameInput).toBeVisible()
      await expect(this.passwordInput).toBeVisible()
      await expect(this.loginButton).toBeVisible()
    }

    // Reset to default
    await this.page.setViewportSize({ width: 1280, height: 720 })
  }

  async measureLoadTime(): Promise<number> {
    const startTime = Date.now()
    await this.goto()
    const endTime = Date.now()
    return endTime - startTime
  }

  async testFormPerformance() {
    const iterations = 5
    const times: number[] = []

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now()

      await this.usernameInput.fill('testuser')
      await this.passwordInput.fill('testpassword')
      await this.clearForm()

      const endTime = Date.now()
      times.push(endTime - startTime)
    }

    const averageTime = times.reduce((a, b) => a + b, 0) / times.length
    console.log(`Average form interaction time: ${averageTime}ms`)

    // Should be reasonably fast (less than 1 second)
    expect(averageTime).toBeLessThan(1000)
  }

  // Helper methods for different user types
  async loginAsAdmin() {
    const admin = {
      username: 'admin001',
      password: 'Admin123!',
      role: 'admin' as const,
    }
    await this.login(admin)
  }

  async loginAsSuperAdmin() {
    const superAdmin = {
      username: 'superadmin',
      password: 'SuperAdmin123!',
      role: 'super_admin' as const,
    }
    await this.login(superAdmin)
  }

  async loginAsTeacher() {
    const teacher = {
      username: 'guru001',
      password: 'Guru123!',
      role: 'guru' as const,
    }
    await this.login(teacher)
  }

  async loginAsReceptionist() {
    const receptionist = {
      username: 'resepsionis001',
      password: 'Resepsionis123!',
      role: 'resepsionis' as const,
    }
    await this.login(receptionist)
  }
}
