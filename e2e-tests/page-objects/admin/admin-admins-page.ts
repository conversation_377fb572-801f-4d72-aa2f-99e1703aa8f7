import { Locat<PERSON>, <PERSON>, expect } from '@playwright/test'
import { BasePage } from '../base-page'

export interface AdminFormData {
  name: string
  username: string
  password: string
  role: 'admin' | 'super_admin' | 'teacher' | 'receptionist'
}

export interface AdminTableRow {
  id: number
  name: string
  username: string
  role: string
}

export class AdminAdminsPage extends BasePage {
  // Header elements
  readonly pageTitle: Locator
  readonly themeToggle: Locator

  // Action buttons
  readonly addAdminButton: Locator

  // Search and filter
  readonly searchInput: Locator
  readonly searchClearButton: Locator
  readonly roleFilter: Locator

  // Table elements
  readonly adminsTable: Locator
  readonly tableHeaders: Locator
  readonly tableRows: Locator
  readonly loadingSpinner: Locator
  readonly emptyState: Locator

  // Dialog elements
  readonly addEditDialog: Locator
  readonly deleteDialog: Locator

  // Form elements in dialog
  readonly nameInput: Locator
  readonly usernameInput: Locator
  readonly passwordInput: Locator
  readonly roleSelect: Locator
  readonly submitButton: Locator
  readonly cancelButton: Locator

  // Error and success messages
  readonly errorMessage: Locator
  readonly successMessage: Locator
  readonly validationErrors: Locator
  readonly toast: Locator

  // Role icons and badges
  readonly superAdminIcon: Locator
  readonly adminIcon: Locator
  readonly teacherIcon: Locator
  readonly receptionistIcon: Locator

  constructor(page: Page) {
    super(page)

    // Header elements
    this.pageTitle = page.locator('h1:has-text("Manajemen Admin")')
    this.themeToggle = page.locator('[data-testid="theme-toggle"]')

    // Action buttons
    this.addAdminButton = page.locator('button:has-text("Tambah Admin")')

    // Search and filter
    this.searchInput = page.locator(
      'input[placeholder*="Cari admin"], [data-testid="search-input"]'
    )
    this.searchClearButton = page.locator(
      'button[aria-label*="clear"], button:has([data-testid="x"])'
    )
    this.roleFilter = page.locator('select:near(:text("Filter Role")), [data-testid="role-filter"]')

    // Table elements
    this.adminsTable = page.locator('table, [data-testid="admins-table"]')
    this.tableHeaders = page.locator('table th')
    this.tableRows = page.locator('table tbody tr')
    this.loadingSpinner = page.locator('[data-testid="loading"], .loading')
    this.emptyState = page.locator(':text("Tidak ada"), :text("Belum ada")')

    // Dialog elements
    this.addEditDialog = page.locator('[role="dialog"], .dialog')
    this.deleteDialog = page.locator('[role="alertdialog"], .alert-dialog')

    // Form elements in dialog
    this.nameInput = page.locator('input[name="name"], input[placeholder*="Nama"]')
    this.usernameInput = page.locator('input[name="username"], input[placeholder*="Username"]')
    this.passwordInput = page.locator('input[name="password"], input[placeholder*="Password"]')
    this.roleSelect = page.locator('select[name="role"], [data-testid="role-select"]')
    this.submitButton = page.locator('button[type="submit"], button:has-text("Simpan")')
    this.cancelButton = page.locator('button:has-text("Batal"), button:has-text("Cancel")')

    // Error and success messages
    this.errorMessage = page.locator('[role="alert"]:has-text("error"), .error')
    this.successMessage = page.locator('[role="alert"]:has-text("success"), .success')
    this.validationErrors = page.locator('.field-error, [data-testid="validation-error"]')
    this.toast = page.locator('[data-testid="toast"], .toast')

    // Role icons and badges
    this.superAdminIcon = page.locator('[data-testid="super-admin-icon"], .text-red-500')
    this.adminIcon = page.locator('[data-testid="admin-icon"], .text-blue-500')
    this.teacherIcon = page.locator('[data-testid="teacher-icon"], .text-green-500')
    this.receptionistIcon = page.locator('[data-testid="receptionist-icon"], .text-purple-500')
  }

  async goto() {
    await this.page.goto('/admin/admins')
    await this.waitForLoad()
  }

  async waitForLoad() {
    await expect(this.pageTitle).toBeVisible()
    await expect(this.addAdminButton).toBeVisible()
    // Wait for table to load
    await this.page.waitForLoadState('networkidle')
  }

  async waitForTableLoad() {
    await expect(this.loadingSpinner).toBeHidden()
    await this.page.waitForTimeout(500)
  }

  // Admin management methods
  async addAdmin(adminData: AdminFormData) {
    await this.addAdminButton.click()
    await expect(this.addEditDialog).toBeVisible()

    await this.fillAdminForm(adminData)
    await this.submitButton.click()

    // Wait for success or error
    await this.page.waitForTimeout(1000)
  }

  async editAdmin(adminRow: number, adminData: Partial<AdminFormData>) {
    const editButton = this.tableRows
      .nth(adminRow)
      .locator('button:has([data-testid="edit"]), button:has-text("Edit")')
    await editButton.click()
    await expect(this.addEditDialog).toBeVisible()

    await this.fillAdminForm(adminData, true)
    await this.submitButton.click()

    // Wait for success or error
    await this.page.waitForTimeout(1000)
  }

  async deleteAdmin(adminRow: number) {
    const deleteButton = this.tableRows
      .nth(adminRow)
      .locator('button:has([data-testid="delete"]), button:has-text("Hapus")')
    await deleteButton.click()
    await expect(this.deleteDialog).toBeVisible()

    const confirmButton = this.deleteDialog.locator(
      'button:has-text("Hapus"), button:has-text("Ya")'
    )
    await confirmButton.click()

    // Wait for deletion to complete
    await this.page.waitForTimeout(1000)
  }

  async fillAdminForm(adminData: Partial<AdminFormData>, isEdit: boolean = false) {
    if (adminData.name) {
      await this.nameInput.fill(adminData.name)
    }

    if (adminData.username && !isEdit) {
      await this.usernameInput.fill(adminData.username)
    }

    if (adminData.password) {
      await this.passwordInput.fill(adminData.password)
    }

    if (adminData.role) {
      await this.roleSelect.selectOption(adminData.role)
    }
  }

  // Search and filter methods
  async searchAdmins(query: string) {
    await this.searchInput.fill(query)
    await this.page.waitForTimeout(500)
    await this.waitForTableLoad()
  }

  async clearSearch() {
    if (await this.searchClearButton.isVisible()) {
      await this.searchClearButton.click()
    } else {
      await this.searchInput.clear()
    }
    await this.waitForTableLoad()
  }

  async filterByRole(role: string) {
    await this.roleFilter.selectOption(role)
    await this.waitForTableLoad()
  }

  // Table interaction methods
  async getTableRowCount(): Promise<number> {
    await this.waitForTableLoad()
    return await this.tableRows.count()
  }

  async getAdminData(row: number): Promise<AdminTableRow> {
    const rowElement = this.tableRows.nth(row)
    const cells = rowElement.locator('td')

    return {
      id: parseInt((await cells.nth(0).getAttribute('data-admin-id')) || '0'),
      name: (await cells.nth(0).textContent()) || '',
      username: (await cells.nth(1).textContent()) || '',
      role: (await cells.nth(2).textContent()) || '',
    }
  }

  // Role-based verification methods
  async verifyPageAccess(expectedAccess: boolean) {
    if (expectedAccess) {
      await expect(this.pageTitle).toBeVisible()
      await expect(this.addAdminButton).toBeVisible()
    } else {
      // Should show access denied or redirect
      await expect(this.page.locator(':has-text("Akses Ditolak")')).toBeVisible()
    }
  }

  async verifySuperAdminFeatures() {
    await expect(this.addAdminButton).toBeVisible()
    await expect(this.adminsTable).toBeVisible()
  }

  async verifyRoleIcons() {
    // Check that role icons are displayed correctly
    const rowCount = await this.getTableRowCount()

    for (let i = 0; i < rowCount; i++) {
      const adminData = await this.getAdminData(i)
      const row = this.tableRows.nth(i)

      if (adminData.role.includes('Super Admin')) {
        await expect(row.locator('.text-red-500, .text-red-600')).toBeVisible()
      } else if (adminData.role.includes('Admin')) {
        await expect(row.locator('.text-blue-500, .text-blue-600')).toBeVisible()
      } else if (adminData.role.includes('Teacher')) {
        await expect(row.locator('.text-green-500, .text-green-600')).toBeVisible()
      } else if (adminData.role.includes('Receptionist')) {
        await expect(row.locator('.text-purple-500, .text-purple-600')).toBeVisible()
      }
    }
  }

  // Data validation methods
  async verifyAdminInTable(adminData: Partial<AdminTableRow>): Promise<boolean> {
    await this.waitForTableLoad()
    const rowCount = await this.getTableRowCount()

    for (let i = 0; i < rowCount; i++) {
      const rowData = await this.getAdminData(i)

      if (
        adminData.name &&
        rowData.name.includes(adminData.name) &&
        adminData.username &&
        rowData.username.includes(adminData.username)
      ) {
        return true
      }
    }
    return false
  }

  async findAdminRowByName(name: string): Promise<number> {
    const rowCount = await this.getTableRowCount()

    for (let i = 0; i < rowCount; i++) {
      const rowData = await this.getAdminData(i)
      if (rowData.name.includes(name)) {
        return i
      }
    }
    return -1
  }

  async findAdminRowByUsername(username: string): Promise<number> {
    const rowCount = await this.getTableRowCount()

    for (let i = 0; i < rowCount; i++) {
      const rowData = await this.getAdminData(i)
      if (rowData.username.includes(username)) {
        return i
      }
    }
    return -1
  }

  // Form validation methods
  async getValidationErrors(): Promise<string[]> {
    const errors = await this.validationErrors.allTextContents()
    return errors.filter(error => error.trim().length > 0)
  }

  async getToastMessage(): Promise<string> {
    await expect(this.toast).toBeVisible()
    return (await this.toast.textContent()) || ''
  }

  async waitForSuccessToast() {
    await expect(this.toast).toBeVisible()
    await expect(this.toast).toContainText('berhasil')
  }

  async waitForErrorToast() {
    await expect(this.toast).toBeVisible()
    await expect(this.toast).toContainText('gagal')
  }

  // Security testing methods
  async testXSSInAdminForm(xssPayload: string) {
    await this.addAdminButton.click()
    await expect(this.addEditDialog).toBeVisible()

    await this.nameInput.fill(xssPayload)
    await this.usernameInput.fill('test_admin')
    await this.passwordInput.fill('password123')

    // Should escape or sanitize the input
    const nameValue = await this.nameInput.inputValue()
    expect(nameValue).not.toContain('<script>')
  }

  async testSQLInjectionInSearch(sqlPayload: string) {
    await this.searchAdmins(sqlPayload)

    // Should not cause database errors
    await expect(this.errorMessage).not.toBeVisible()
    await this.clearSearch()
  }

  // Performance testing
  async measurePageLoadTime(): Promise<number> {
    const startTime = Date.now()
    await this.goto()
    await this.waitForTableLoad()
    return Date.now() - startTime
  }

  async measureSearchTime(query: string): Promise<number> {
    const startTime = Date.now()
    await this.searchAdmins(query)
    return Date.now() - startTime
  }

  // Accessibility methods
  async verifyAccessibility() {
    // Check for proper ARIA labels
    await expect(this.searchInput).toHaveAttribute('aria-label')
    await expect(this.adminsTable).toHaveAttribute('role', 'table')

    // Check keyboard navigation
    await this.searchInput.focus()
    await this.page.keyboard.press('Tab')
    await expect(this.addAdminButton).toBeFocused()
  }

  async testKeyboardNavigation() {
    // Test tab navigation through main elements
    await this.searchInput.focus()
    await this.page.keyboard.press('Tab')
    await expect(this.roleFilter).toBeFocused()

    await this.page.keyboard.press('Tab')
    await expect(this.addAdminButton).toBeFocused()
  }

  // Responsive design verification
  async verifyResponsiveDesign() {
    // Test mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 })
    await expect(this.pageTitle).toBeVisible()
    await expect(this.addAdminButton).toBeVisible()

    // Test tablet viewport
    await this.page.setViewportSize({ width: 768, height: 1024 })
    await expect(this.adminsTable).toBeVisible()

    // Reset to desktop
    await this.page.setViewportSize({ width: 1280, height: 720 })
  }

  // Role-specific testing methods
  async testRolePermissions() {
    // Verify that different roles have appropriate permissions
    const rowCount = await this.getTableRowCount()

    for (let i = 0; i < rowCount; i++) {
      const adminData = await this.getAdminData(i)

      // Verify role-specific constraints and validations
      if (adminData.role.includes('Super Admin')) {
        // Super Admin should have all permissions
        expect(adminData.role).toContain('Super Admin')
      } else if (adminData.role.includes('Admin')) {
        // Admin should have limited permissions
        expect(adminData.role).toContain('Admin')
      } else if (adminData.role.includes('Teacher')) {
        // Teacher should have teaching-specific permissions
        expect(adminData.role).toContain('Teacher')
      } else if (adminData.role.includes('Receptionist')) {
        // Receptionist should have reception-specific permissions
        expect(adminData.role).toContain('Receptionist')
      }
    }
  }

  async validateFormConstraints() {
    await this.addAdminButton.click()
    await expect(this.addEditDialog).toBeVisible()

    // Test empty form submission
    await this.submitButton.click()

    // Should show validation errors
    const errors = await this.getValidationErrors()
    expect(errors.length).toBeGreaterThan(0)

    // Test minimum password requirements
    await this.nameInput.fill('Test Admin')
    await this.usernameInput.fill('test_admin')
    await this.passwordInput.fill('123') // Too short
    await this.roleSelect.selectOption('admin')

    await this.submitButton.click()

    // Should show password validation error
    const passwordErrors = await this.getValidationErrors()
    expect(passwordErrors.some(error => error.toLowerCase().includes('password'))).toBe(true)
  }

  async testUniqueUsername() {
    // Get existing admin data
    const existingAdmin = await this.getAdminData(0)

    await this.addAdminButton.click()
    await expect(this.addEditDialog).toBeVisible()

    // Try to create admin with existing username
    await this.nameInput.fill('New Admin')
    await this.usernameInput.fill(existingAdmin.username)
    await this.passwordInput.fill('password123')
    await this.roleSelect.selectOption('admin')

    await this.submitButton.click()

    // Should show username already exists error
    await this.waitForErrorToast()
  }

  async cancelFormOperation() {
    await this.addAdminButton.click()
    await expect(this.addEditDialog).toBeVisible()

    await this.nameInput.fill('Test Data')
    await this.cancelButton.click()

    // Dialog should close
    await expect(this.addEditDialog).toBeHidden()
  }

  // Special admin operations
  async preventSelfDeletion(currentAdminUsername: string) {
    const currentAdminRow = await this.findAdminRowByUsername(currentAdminUsername)

    if (currentAdminRow >= 0) {
      const deleteButton = this.tableRows
        .nth(currentAdminRow)
        .locator('button:has([data-testid="delete"])')

      // Button should be disabled or not visible
      if (await deleteButton.isVisible()) {
        await expect(deleteButton).toBeDisabled()
      }
    }
  }

  async verifyLastSuperAdminProtection() {
    // Count super admins
    const rowCount = await this.getTableRowCount()
    let superAdminCount = 0

    for (let i = 0; i < rowCount; i++) {
      const adminData = await this.getAdminData(i)
      if (adminData.role.includes('Super Admin')) {
        superAdminCount++
      }
    }

    // If only one super admin exists, deletion should be prevented
    if (superAdminCount === 1) {
      for (let i = 0; i < rowCount; i++) {
        const adminData = await this.getAdminData(i)
        if (adminData.role.includes('Super Admin')) {
          const deleteButton = this.tableRows.nth(i).locator('button:has([data-testid="delete"])')
          if (await deleteButton.isVisible()) {
            await expect(deleteButton).toBeDisabled()
          }
          break
        }
      }
    }
  }
}
