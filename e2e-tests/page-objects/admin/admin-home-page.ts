import { Locator, Page, expect } from '@playwright/test'
import { BasePage } from '../base-page'
import { AttendanceType } from '../../test-data/attendance'

export class AdminHomePage extends BasePage {
  // Page elements
  readonly pageTitle: Locator
  readonly themeToggle: Locator
  readonly attendanceTypeSelect: Locator
  readonly currentDate: Locator
  readonly currentTime: Locator

  // Scanner elements
  readonly scannerCard: Locator
  readonly cameraView: Locator
  readonly startScanButton: Locator
  readonly pauseScanButton: Locator
  readonly scannerStatus: Locator

  // Manual Entry elements (for roles with manual entry access)
  readonly tabsContainer: Locator
  readonly scannerTab: Locator
  readonly manualEntryTab: Locator
  readonly studentSearchInput: Locator
  readonly manualAttendanceTypeSelect: Locator
  readonly reasonInput: Locator
  readonly submitManualButton: Locator

  // Dialogs and modals
  readonly confirmationDialog: Locator
  readonly successDialog: Locator
  readonly errorDialog: Locator
  readonly ijinConfirmDialog: Locator
  readonly loadingSpinner: Locator

  // Messages and alerts
  readonly successMessage: Locator
  readonly errorMessage: Locator
  readonly duplicateWarning: Locator
  readonly validationError: Locator

  // Navigation
  readonly bottomNav: Locator
  readonly profileButton: Locator
  readonly reportsButton: Locator
  readonly logoutButton: Locator

  constructor(page: Page) {
    super(page)

    // Page elements
    this.pageTitle = page.locator('h1:has-text("Scanner Admin")')
    this.themeToggle = page.locator('[data-testid="theme-toggle"]')
    this.attendanceTypeSelect = page.locator(
      'select[name="attendanceType"], [data-testid="attendance-type-select"]'
    )
    this.currentDate = page.locator('[data-testid="current-date"]')
    this.currentTime = page.locator('[data-testid="current-time"]')

    // Scanner elements
    this.scannerCard = page.locator('[data-testid="scanner-card"]')
    this.cameraView = page.locator('[data-testid="camera-view"], canvas, video')
    this.startScanButton = page.locator('button:has-text("Mulai Pemindaian")')
    this.pauseScanButton = page.locator('[data-testid="pause-scanner"]')
    this.scannerStatus = page.locator('[data-testid="scanner-status"]')

    // Manual Entry elements
    this.tabsContainer = page.locator('[data-testid="tabs-container"]')
    this.scannerTab = page.locator('[data-testid="scanner-tab"], button:has-text("Scanner QR")')
    this.manualEntryTab = page.locator(
      '[data-testid="manual-entry-tab"], button:has-text("Entry Manual")'
    )
    this.studentSearchInput = page.locator(
      'input[placeholder*="Cari siswa"], [data-testid="student-search"]'
    )
    this.manualAttendanceTypeSelect = page.locator('[data-testid="manual-attendance-type"]')
    this.reasonInput = page.locator('textarea[name="reason"], [data-testid="reason-input"]')
    this.submitManualButton = page.locator(
      'button:has-text("Submit"), [data-testid="submit-manual"]'
    )

    // Dialogs and modals
    this.confirmationDialog = page.locator('[role="dialog"]:has-text("Konfirmasi")')
    this.successDialog = page.locator('[role="dialog"]:has-text("Berhasil")')
    this.errorDialog = page.locator('[role="dialog"]:has-text("Error")')
    this.ijinConfirmDialog = page.locator('[role="dialog"]:has-text("Ijin")')
    this.loadingSpinner = page.locator('[data-testid="loading"], .loading, [aria-label*="loading"]')

    // Messages and alerts
    this.successMessage = page.locator(
      '[data-testid="success-message"], .success, [role="alert"]:has-text("berhasil")'
    )
    this.errorMessage = page.locator(
      '[data-testid="error-message"], .error, [role="alert"]:has-text("error")'
    )
    this.duplicateWarning = page.locator(
      '[data-testid="duplicate-warning"], :has-text("sudah tercatat")'
    )
    this.validationError = page.locator('[data-testid="validation-error"], .validation-error')

    // Navigation
    this.bottomNav = page.locator('[data-testid="bottom-nav"], .bottom-nav')
    this.profileButton = page.locator('button:has-text("Profil"), [data-testid="profile-nav"]')
    this.reportsButton = page.locator('button:has-text("Laporan"), [data-testid="reports-nav"]')
    this.logoutButton = page.locator('button:has-text("Logout"), [data-testid="logout"]')
  }

  async goto() {
    await this.page.goto('/admin/home')
    await this.waitForLoad()
  }

  async waitForLoad() {
    await expect(this.pageTitle).toBeVisible()
    // Wait for attendance type selector to be ready
    await expect(this.attendanceTypeSelect).toBeVisible()
  }

  async selectAttendanceType(type: AttendanceType) {
    await this.attendanceTypeSelect.selectOption(type)
    await this.page.waitForTimeout(500) // Allow UI to update
  }

  async getAvailableAttendanceTypes(): Promise<string[]> {
    await this.attendanceTypeSelect.click()

    const options = await this.page.locator('option, [role="option"]').allTextContents()

    // Close dropdown if needed
    await this.page.keyboard.press('Escape')

    return options.filter(option => option.trim().length > 0)
  }

  async startScanning() {
    await this.startScanButton.click()
    await expect(this.cameraView).toBeVisible({ timeout: 10000 })
  }

  async pauseScanning() {
    await this.pauseScanButton.click()
    await expect(this.scannerStatus).toContainText('Jeda')
  }

  async simulateQRScan(qrCode: string) {
    // This would typically involve mocking the QR scanner
    // For E2E tests, we might need to use a test QR code image
    // or mock the scanner callback
    await this.page.evaluate(code => {
      // Simulate QR scan result
      window.dispatchEvent(
        new CustomEvent('qr-scanned', {
          detail: { code },
        })
      )
    }, qrCode)
  }

  async waitForScanResult() {
    // Wait for either success or error dialog
    await Promise.race([
      this.successDialog.waitFor({ state: 'visible' }),
      this.errorDialog.waitFor({ state: 'visible' }),
      this.duplicateWarning.waitFor({ state: 'visible' }),
    ])
  }

  async getScanResultMessage(): Promise<{
    type: 'success' | 'error' | 'duplicate'
    message: string
  }> {
    if (await this.successDialog.isVisible()) {
      const message = await this.successDialog.textContent()
      return { type: 'success', message: message || '' }
    }

    if (await this.errorDialog.isVisible()) {
      const message = await this.errorDialog.textContent()
      return { type: 'error', message: message || '' }
    }

    if (await this.duplicateWarning.isVisible()) {
      const message = await this.duplicateWarning.textContent()
      return { type: 'duplicate', message: message || '' }
    }

    return { type: 'error', message: 'No result dialog found' }
  }

  async closeScanResultDialog() {
    // Close any open dialog
    const closeButton = this.page.locator(
      '[role="dialog"] button:has-text("OK"), [role="dialog"] button:has-text("Tutup")'
    )
    if (await closeButton.isVisible()) {
      await closeButton.click()
    }
  }

  // Manual Entry Methods
  async switchToManualEntry() {
    if (await this.manualEntryTab.isVisible()) {
      await this.manualEntryTab.click()
      await expect(this.studentSearchInput).toBeVisible()
    } else {
      throw new Error('Manual entry tab not available for this role')
    }
  }

  async searchStudent(query: string) {
    await this.studentSearchInput.fill(query)
    await this.page.waitForTimeout(500) // Allow search results to load

    // Look for search results dropdown
    const searchResults = this.page.locator('[data-testid="search-results"], .search-results')
    await expect(searchResults).toBeVisible()
  }

  async selectStudentFromSearch(studentName: string) {
    const studentOption = this.page.locator(
      `[data-testid="student-option"]:has-text("${studentName}")`
    )
    await expect(studentOption).toBeVisible()
    await studentOption.click()
  }

  async submitManualEntry(studentQuery: string, attendanceType: AttendanceType, reason?: string) {
    await this.searchStudent(studentQuery)
    await this.selectStudentFromSearch(studentQuery)

    await this.manualAttendanceTypeSelect.selectOption(attendanceType)

    if (reason && (await this.reasonInput.isVisible())) {
      await this.reasonInput.fill(reason)
    }

    await this.submitManualButton.click()
    await this.waitForScanResult()
  }

  // Confirmation Methods
  async confirmIjinRequest(confirm: boolean = true) {
    await expect(this.ijinConfirmDialog).toBeVisible()

    const confirmButton = this.page.locator(
      '[role="dialog"] button:has-text("Ya"), [role="dialog"] button:has-text("Confirm")'
    )
    const cancelButton = this.page.locator(
      '[role="dialog"] button:has-text("Tidak"), [role="dialog"] button:has-text("Cancel")'
    )

    if (confirm) {
      await confirmButton.click()
    } else {
      await cancelButton.click()
    }

    await expect(this.ijinConfirmDialog).toBeHidden()
  }

  // Validation Methods
  async validateRoleAccess(expectedAttendanceTypes: AttendanceType[]) {
    const availableTypes = await this.getAvailableAttendanceTypes()

    for (const type of expectedAttendanceTypes) {
      expect(availableTypes.some(option => option.includes(type))).toBe(true)
    }
  }

  async validateManualEntryAccess(shouldHaveAccess: boolean) {
    if (shouldHaveAccess) {
      await expect(this.tabsContainer).toBeVisible()
      await expect(this.manualEntryTab).toBeVisible()
    } else {
      // Should only see scanner tab or no tabs at all
      if (await this.tabsContainer.isVisible()) {
        await expect(this.manualEntryTab).not.toBeVisible()
      }
    }
  }

  // Navigation Methods
  async navigateToReports() {
    await this.reportsButton.click()
    await expect(this.page).toHaveURL(/\/admin\/.*reports/)
  }

  async navigateToProfile() {
    await this.profileButton.click()
    await expect(this.page).toHaveURL(/\/admin\/profile/)
  }

  async logout() {
    await this.logoutButton.click()
    await expect(this.page).toHaveURL(/\/admin$/)
  }

  // Theme and UI Methods
  async toggleTheme() {
    const currentTheme = await this.page.locator('html').getAttribute('class')
    await this.themeToggle.click()
    await this.page.waitForTimeout(500)

    const newTheme = await this.page.locator('html').getAttribute('class')
    expect(newTheme).not.toBe(currentTheme)
  }

  async getCurrentDateTime(): Promise<{ date: string; time: string }> {
    const date = (await this.currentDate.textContent()) || ''
    const time = (await this.currentTime.textContent()) || ''

    return { date: date.trim(), time: time.trim() }
  }

  // Error handling
  async handleNetworkError() {
    await this.page.route('**/api/**', route => route.abort())
  }

  async clearNetworkMocks() {
    await this.page.unroute('**/api/**')
  }

  // Accessibility methods
  async verifyAccessibility() {
    // Check ARIA labels
    await expect(this.attendanceTypeSelect).toHaveAttribute('aria-label')
    await expect(this.startScanButton).toHaveAttribute('aria-label')

    // Check keyboard navigation
    await this.attendanceTypeSelect.press('Tab')
    await expect(this.startScanButton).toBeFocused()
  }

  async verifyResponsiveDesign() {
    // Test mobile view
    await this.page.setViewportSize({ width: 375, height: 667 })
    await expect(this.pageTitle).toBeVisible()
    await expect(this.scannerCard).toBeVisible()

    // Test tablet view
    await this.page.setViewportSize({ width: 768, height: 1024 })
    await expect(this.pageTitle).toBeVisible()
    await expect(this.scannerCard).toBeVisible()

    // Test desktop view
    await this.page.setViewportSize({ width: 1200, height: 800 })
    await expect(this.pageTitle).toBeVisible()
    await expect(this.scannerCard).toBeVisible()
  }
}
