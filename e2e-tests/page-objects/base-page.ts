import { Page, Locator, expect } from '@playwright/test'

export abstract class BasePage {
  protected page: Page

  constructor(page: Page) {
    this.page = page
  }

  // Common locators
  get loadingSpinner(): Locator {
    return this.page.locator('[data-testid="loading"], .loading, .spinner')
  }

  get errorMessage(): Locator {
    return this.page.locator('[data-testid="error"], .error-message, [role="alert"]')
  }

  get successMessage(): Locator {
    return this.page.locator('[data-testid="success"], .success-message')
  }

  get toast(): Locator {
    return this.page.locator('[data-testid="toast"], .toast, [role="alert"]')
  }

  // Common actions
  async goto(url: string): Promise<void> {
    await this.page.goto(url)
    await this.waitForPageLoad()
  }

  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle')

    // Wait for loading spinner to disappear if present
    if (await this.loadingSpinner.isVisible()) {
      await expect(this.loadingSpinner).toBeHidden({ timeout: 10000 })
    }
  }

  async takeScreenshot(name: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    await this.page.screenshot({
      path: `test-results/screenshots/${name}-${timestamp}.png`,
      fullPage: true,
    })
  }

  async waitForToast(message?: string, timeout: number = 5000): Promise<void> {
    await expect(this.toast).toBeVisible({ timeout })

    if (message) {
      await expect(this.toast).toContainText(message)
    }

    // Wait for toast to disappear
    await expect(this.toast).toBeHidden({ timeout: 10000 })
  }

  async clickAndWait(locator: Locator, waitForResponse?: string): Promise<void> {
    await expect(locator).toBeVisible()
    await expect(locator).toBeEnabled()

    if (waitForResponse) {
      await Promise.all([
        this.page.waitForResponse(
          response => response.url().includes(waitForResponse) && response.status() === 200
        ),
        locator.click(),
      ])
    } else {
      await locator.click()
    }
  }

  async fillAndValidate(locator: Locator, value: string): Promise<void> {
    await expect(locator).toBeVisible()
    await locator.clear()
    await locator.fill(value)
    await expect(locator).toHaveValue(value)
  }

  async selectOption(locator: Locator, value: string): Promise<void> {
    await expect(locator).toBeVisible()
    await locator.selectOption(value)
  }

  async checkCheckbox(locator: Locator, checked: boolean = true): Promise<void> {
    await expect(locator).toBeVisible()

    if (checked) {
      await locator.check()
      await expect(locator).toBeChecked()
    } else {
      await locator.uncheck()
      await expect(locator).not.toBeChecked()
    }
  }

  async uploadFile(locator: Locator, filePath: string): Promise<void> {
    await expect(locator).toBeVisible()
    await locator.setInputFiles(filePath)
  }

  async waitForElementToAppear(selector: string, timeout: number = 10000): Promise<void> {
    await expect(this.page.locator(selector)).toBeVisible({ timeout })
  }

  async waitForElementToDisappear(selector: string, timeout: number = 10000): Promise<void> {
    await expect(this.page.locator(selector)).toBeHidden({ timeout })
  }

  async getElementText(locator: Locator): Promise<string> {
    await expect(locator).toBeVisible()
    return (await locator.textContent()) || ''
  }

  async getElementAttribute(locator: Locator, attribute: string): Promise<string> {
    await expect(locator).toBeVisible()
    return (await locator.getAttribute(attribute)) || ''
  }

  async scrollToElement(locator: Locator): Promise<void> {
    await locator.scrollIntoViewIfNeeded()
  }

  async hover(locator: Locator): Promise<void> {
    await expect(locator).toBeVisible()
    await locator.hover()
  }

  async doubleClick(locator: Locator): Promise<void> {
    await expect(locator).toBeVisible()
    await locator.dblclick()
  }

  async rightClick(locator: Locator): Promise<void> {
    await expect(locator).toBeVisible()
    await locator.click({ button: 'right' })
  }

  async pressKey(key: string): Promise<void> {
    await this.page.keyboard.press(key)
  }

  async typeText(text: string, delay?: number): Promise<void> {
    await this.page.keyboard.type(text, { delay })
  }

  // Validation helpers
  async assertElementVisible(locator: Locator): Promise<void> {
    await expect(locator).toBeVisible()
  }

  async assertElementHidden(locator: Locator): Promise<void> {
    await expect(locator).toBeHidden()
  }

  async assertElementText(locator: Locator, expectedText: string): Promise<void> {
    await expect(locator).toHaveText(expectedText)
  }

  async assertElementContainsText(locator: Locator, expectedText: string): Promise<void> {
    await expect(locator).toContainText(expectedText)
  }

  async assertElementEnabled(locator: Locator): Promise<void> {
    await expect(locator).toBeEnabled()
  }

  async assertElementDisabled(locator: Locator): Promise<void> {
    await expect(locator).toBeDisabled()
  }

  async assertPageUrl(expectedUrl: string | RegExp): Promise<void> {
    await expect(this.page).toHaveURL(expectedUrl)
  }

  async assertPageTitle(expectedTitle: string | RegExp): Promise<void> {
    await expect(this.page).toHaveTitle(expectedTitle)
  }

  // Wait for network requests
  async waitForApiCall(apiPath: string, timeout: number = 10000): Promise<void> {
    await this.page.waitForResponse(
      response => response.url().includes(apiPath) && response.status() === 200,
      { timeout }
    )
  }

  // Mocking utilities
  async mockApiResponse(
    apiPath: string,
    responseData: any,
    statusCode: number = 200
  ): Promise<void> {
    await this.page.route(`**${apiPath}`, async route => {
      await route.fulfill({
        status: statusCode,
        contentType: 'application/json',
        body: JSON.stringify(responseData),
      })
    })
  }

  // Cookie and storage management
  async clearCookies(): Promise<void> {
    await this.page.context().clearCookies()
  }

  async clearLocalStorage(): Promise<void> {
    await this.page.evaluate(() => localStorage.clear())
  }

  async clearSessionStorage(): Promise<void> {
    await this.page.evaluate(() => sessionStorage.clear())
  }

  async setLocalStorageItem(key: string, value: string): Promise<void> {
    await this.page.evaluate(
      ({ key, value }) => {
        localStorage.setItem(key, value)
      },
      { key, value }
    )
  }

  async getLocalStorageItem(key: string): Promise<string | null> {
    return await this.page.evaluate(key => localStorage.getItem(key), key)
  }
}
