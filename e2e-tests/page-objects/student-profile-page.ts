import { Page, Locator, expect } from '@playwright/test'
import { BasePage } from './base-page'

export class StudentProfilePage extends BasePage {
  // Page URL
  readonly url = '/student/profile'

  // Header elements
  readonly header: Locator
  readonly backButton: Locator
  readonly themeToggle: Locator

  // Profile information display
  readonly profileCard: Locator
  readonly studentName: Locator
  readonly studentEmail: Locator
  readonly studentNis: Locator
  readonly studentClass: Locator
  readonly studentWhatsapp: Locator
  readonly uniqueCode: Locator

  // Edit buttons
  readonly editProfileButton: Locator
  readonly changePasswordButton: Locator
  readonly changeWhatsappButton: Locator
  readonly removeWhatsappButton: Locator

  // Edit Profile Dialog
  readonly editProfileDialog: Locator
  readonly nameInput: Locator
  readonly emailInput: Locator
  readonly nisInput: Locator
  readonly saveProfileButton: Locator
  readonly cancelProfileButton: Locator

  // Change Password Dialog
  readonly changePasswordDialog: Locator
  readonly currentPasswordInput: Locator
  readonly newPasswordInput: Locator
  readonly confirmPasswordInput: Locator
  readonly passwordStrengthIndicator: Locator
  readonly passwordStrengthText: Locator
  readonly savePasswordButton: Locator
  readonly cancelPasswordButton: Locator

  // WhatsApp OTP Dialog
  readonly whatsappOtpDialog: Locator
  readonly whatsappPhoneInput: Locator
  readonly whatsappPhonePrefix: Locator
  readonly sendOtpButton: Locator
  readonly otpInput: Locator
  readonly verifyOtpButton: Locator
  readonly resendOtpButton: Locator
  readonly cancelOtpButton: Locator
  readonly rateLimitTimer: Locator
  readonly otpInstructions: Locator

  // Remove WhatsApp Confirmation Dialog
  readonly removeWhatsappDialog: Locator
  readonly confirmRemoveButton: Locator
  readonly cancelRemoveButton: Locator

  // Status indicators
  readonly loadingSpinner: Locator
  readonly verificationBadge: Locator
  readonly errorAlert: Locator
  readonly successAlert: Locator

  // Form validation messages
  readonly validationErrors: Locator
  readonly emailValidationError: Locator
  readonly phoneValidationError: Locator
  readonly passwordValidationError: Locator

  // Bottom navigation
  readonly bottomNav: Locator
  readonly homeNavButton: Locator
  readonly historyNavButton: Locator
  readonly profileNavButton: Locator

  // Logout section
  readonly logoutSection: Locator
  readonly logoutButton: Locator

  constructor(page: Page) {
    super(page)

    // Header elements
    this.header = page.locator('[data-testid="profile-header"]')
    this.backButton = page.locator('[data-testid="back-button"]')
    this.themeToggle = page.locator('[data-testid="theme-toggle"]')

    // Profile information display
    this.profileCard = page.locator('[data-testid="profile-card"]')
    this.studentName = page.locator('[data-testid="student-name"]')
    this.studentEmail = page.locator('[data-testid="student-email"]')
    this.studentNis = page.locator('[data-testid="student-nis"]')
    this.studentClass = page.locator('[data-testid="student-class"]')
    this.studentWhatsapp = page.locator('[data-testid="student-whatsapp"]')
    this.uniqueCode = page.locator('[data-testid="unique-code"]')

    // Edit buttons
    this.editProfileButton = page.locator('[data-testid="edit-profile-button"]')
    this.changePasswordButton = page.locator('[data-testid="change-password-button"]')
    this.changeWhatsappButton = page.locator('[data-testid="change-whatsapp-button"]')
    this.removeWhatsappButton = page.locator('[data-testid="remove-whatsapp-button"]')

    // Edit Profile Dialog
    this.editProfileDialog = page.locator('[data-testid="edit-profile-dialog"]')
    this.nameInput = page.locator('[data-testid="name-input"]')
    this.emailInput = page.locator('[data-testid="email-input"]')
    this.nisInput = page.locator('[data-testid="nis-input"]')
    this.saveProfileButton = page.locator('[data-testid="save-profile-button"]')
    this.cancelProfileButton = page.locator('[data-testid="cancel-profile-button"]')

    // Change Password Dialog
    this.changePasswordDialog = page.locator('[data-testid="change-password-dialog"]')
    this.currentPasswordInput = page.locator('[data-testid="current-password-input"]')
    this.newPasswordInput = page.locator('[data-testid="new-password-input"]')
    this.confirmPasswordInput = page.locator('[data-testid="confirm-password-input"]')
    this.passwordStrengthIndicator = page.locator('[data-testid="password-strength-indicator"]')
    this.passwordStrengthText = page.locator('[data-testid="password-strength-text"]')
    this.savePasswordButton = page.locator('[data-testid="save-password-button"]')
    this.cancelPasswordButton = page.locator('[data-testid="cancel-password-button"]')

    // WhatsApp OTP Dialog
    this.whatsappOtpDialog = page.locator('[data-testid="whatsapp-otp-dialog"]')
    this.whatsappPhoneInput = page.locator('[data-testid="whatsapp-phone-input"]')
    this.whatsappPhonePrefix = page.locator('[data-testid="phone-prefix"]')
    this.sendOtpButton = page.locator('[data-testid="send-otp-button"]')
    this.otpInput = page.locator('[data-testid="otp-input"]')
    this.verifyOtpButton = page.locator('[data-testid="verify-otp-button"]')
    this.resendOtpButton = page.locator('[data-testid="resend-otp-button"]')
    this.cancelOtpButton = page.locator('[data-testid="cancel-otp-button"]')
    this.rateLimitTimer = page.locator('[data-testid="rate-limit-timer"]')
    this.otpInstructions = page.locator('[data-testid="otp-instructions"]')

    // Remove WhatsApp Confirmation Dialog
    this.removeWhatsappDialog = page.locator('[data-testid="remove-whatsapp-dialog"]')
    this.confirmRemoveButton = page.locator('[data-testid="confirm-remove-button"]')
    this.cancelRemoveButton = page.locator('[data-testid="cancel-remove-button"]')

    // Status indicators
    this.loadingSpinner = page.locator('[data-testid="loading-spinner"]')
    this.verificationBadge = page.locator('[data-testid="verification-badge"]')
    this.errorAlert = page.locator('[data-testid="error-alert"]')
    this.successAlert = page.locator('[data-testid="success-alert"]')

    // Form validation messages
    this.validationErrors = page.locator('[data-testid="validation-error"]')
    this.emailValidationError = page.locator('[data-testid="email-validation-error"]')
    this.phoneValidationError = page.locator('[data-testid="phone-validation-error"]')
    this.passwordValidationError = page.locator('[data-testid="password-validation-error"]')

    // Bottom navigation
    this.bottomNav = page.locator('[data-testid="bottom-nav"]')
    this.homeNavButton = page.locator('[data-testid="nav-home"]')
    this.historyNavButton = page.locator('[data-testid="nav-history"]')
    this.profileNavButton = page.locator('[data-testid="nav-profile"]')

    // Logout section
    this.logoutSection = page.locator('[data-testid="logout-section"]')
    this.logoutButton = page.locator('[data-testid="logout-button"]')
  }

  /**
   * Navigate to student profile page
   */
  async goto(): Promise<void> {
    await this.page.goto(this.url)
    await this.waitForLoad()
  }

  /**
   * Wait for page to load completely
   */
  async waitForLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle')
    await this.loadingSpinner.waitFor({ state: 'hidden', timeout: 10000 })
    await this.profileCard.waitFor({ state: 'visible' })
  }

  /**
   * Get current profile information
   */
  async getProfileInfo(): Promise<{
    name: string
    email: string
    nis: string
    class: string
    whatsapp: string
    uniqueCode: string
    isWhatsappVerified: boolean
  }> {
    const name = (await this.studentName.textContent()) || ''
    const email = (await this.studentEmail.textContent()) || ''
    const nis = (await this.studentNis.textContent()) || ''
    const studentClass = (await this.studentClass.textContent()) || ''
    const whatsapp = (await this.studentWhatsapp.textContent()) || ''
    const uniqueCode = (await this.uniqueCode.textContent()) || ''

    // Check if WhatsApp is verified by looking for verification badge
    const isWhatsappVerified = await this.verificationBadge.isVisible()

    return {
      name: name.trim(),
      email: email.trim(),
      nis: nis.trim(),
      class: studentClass.trim(),
      whatsapp: whatsapp.trim(),
      uniqueCode: uniqueCode.trim(),
      isWhatsappVerified,
    }
  }

  /**
   * Edit profile information
   */
  async editProfile(data: { name?: string; email?: string; nis?: string }): Promise<void> {
    await this.editProfileButton.click()
    await this.editProfileDialog.waitFor({ state: 'visible' })

    if (data.name !== undefined) {
      await this.nameInput.fill(data.name)
    }
    if (data.email !== undefined) {
      await this.emailInput.fill(data.email)
    }
    if (data.nis !== undefined) {
      await this.nisInput.fill(data.nis)
    }

    await this.saveProfileButton.click()

    // Wait for dialog to close
    await this.editProfileDialog.waitFor({ state: 'hidden' })
  }

  /**
   * Cancel profile editing
   */
  async cancelProfileEdit(): Promise<void> {
    await this.editProfileButton.click()
    await this.editProfileDialog.waitFor({ state: 'visible' })
    await this.cancelProfileButton.click()
    await this.editProfileDialog.waitFor({ state: 'hidden' })
  }

  /**
   * Change password
   */
  async changePassword(
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
  ): Promise<void> {
    await this.changePasswordButton.click()
    await this.changePasswordDialog.waitFor({ state: 'visible' })

    await this.currentPasswordInput.fill(currentPassword)
    await this.newPasswordInput.fill(newPassword)
    await this.confirmPasswordInput.fill(confirmPassword)

    await this.savePasswordButton.click()

    // Wait for dialog to close
    await this.changePasswordDialog.waitFor({ state: 'hidden' })
  }

  /**
   * Get password strength information
   */
  async getPasswordStrength(): Promise<{ score: number; text: string; color: string }> {
    const strengthBar = this.passwordStrengthIndicator
    const strengthText = (await this.passwordStrengthText.textContent()) || ''

    // Get the progress value if available
    const progressValue = (await strengthBar.getAttribute('value')) || '0'
    const score = parseInt(progressValue, 10)

    // Get color from CSS classes or style
    const colorClass = (await strengthBar.getAttribute('class')) || ''
    let color = 'gray'
    if (colorClass.includes('red')) color = 'red'
    else if (colorClass.includes('orange')) color = 'orange'
    else if (colorClass.includes('yellow')) color = 'yellow'
    else if (colorClass.includes('green')) color = 'green'

    return { score, text: strengthText.trim(), color }
  }

  /**
   * Set up WhatsApp with OTP verification
   */
  async setupWhatsapp(phoneNumber: string, otp: string): Promise<void> {
    await this.changeWhatsappButton.click()
    await this.whatsappOtpDialog.waitFor({ state: 'visible' })

    // Enter phone number (without +62 prefix as it's added automatically)
    const phoneWithoutPrefix = phoneNumber.startsWith('62') ? phoneNumber.substring(2) : phoneNumber
    await this.whatsappPhoneInput.fill(phoneWithoutPrefix)

    // Send OTP
    await this.sendOtpButton.click()

    // Wait for OTP input to be visible
    await this.otpInput.waitFor({ state: 'visible' })

    // Enter OTP
    await this.otpInput.fill(otp)

    // Verify OTP
    await this.verifyOtpButton.click()

    // Wait for dialog to close
    await this.whatsappOtpDialog.waitFor({ state: 'hidden' })
  }

  /**
   * Remove WhatsApp number
   */
  async removeWhatsapp(): Promise<void> {
    await this.removeWhatsappButton.click()
    await this.removeWhatsappDialog.waitFor({ state: 'visible' })
    await this.confirmRemoveButton.click()
    await this.removeWhatsappDialog.waitFor({ state: 'hidden' })
  }

  /**
   * Cancel WhatsApp removal
   */
  async cancelWhatsappRemoval(): Promise<void> {
    await this.removeWhatsappButton.click()
    await this.removeWhatsappDialog.waitFor({ state: 'visible' })
    await this.cancelRemoveButton.click()
    await this.removeWhatsappDialog.waitFor({ state: 'hidden' })
  }

  /**
   * Resend OTP
   */
  async resendOtp(): Promise<void> {
    await this.resendOtpButton.click()

    // Wait for rate limit timer if it appears
    try {
      await this.rateLimitTimer.waitFor({ state: 'visible', timeout: 2000 })
    } catch {
      // Timer might not appear if no rate limit
    }
  }

  /**
   * Get rate limit remaining time
   */
  async getRateLimitTime(): Promise<number> {
    try {
      const timerText = await this.rateLimitTimer.textContent()
      const match = timerText?.match(/(\d+)/)
      return match ? parseInt(match[1], 10) : 0
    } catch {
      return 0
    }
  }

  /**
   * Validate email format
   */
  async validateEmailFormat(email: string): Promise<boolean> {
    await this.editProfileButton.click()
    await this.editProfileDialog.waitFor({ state: 'visible' })

    await this.emailInput.fill(email)
    await this.emailInput.blur()

    // Check for validation error
    const hasError = await this.emailValidationError.isVisible()

    await this.cancelProfileButton.click()
    await this.editProfileDialog.waitFor({ state: 'hidden' })

    return !hasError
  }

  /**
   * Validate phone number format
   */
  async validatePhoneFormat(phone: string): Promise<boolean> {
    await this.changeWhatsappButton.click()
    await this.whatsappOtpDialog.waitFor({ state: 'visible' })

    await this.whatsappPhoneInput.fill(phone)
    await this.whatsappPhoneInput.blur()

    // Check for validation error
    const hasError = await this.phoneValidationError.isVisible()

    await this.cancelOtpButton.click()
    await this.whatsappOtpDialog.waitFor({ state: 'hidden' })

    return !hasError
  }

  /**
   * Check if phone number is already taken
   */
  async checkPhoneAvailability(phone: string): Promise<boolean> {
    await this.changeWhatsappButton.click()
    await this.whatsappOtpDialog.waitFor({ state: 'visible' })

    await this.whatsappPhoneInput.fill(phone)

    // Wait for debounced availability check
    await this.page.waitForTimeout(1500)

    const hasError = await this.phoneValidationError.isVisible()

    await this.cancelOtpButton.click()
    await this.whatsappOtpDialog.waitFor({ state: 'hidden' })

    return !hasError
  }

  /**
   * Logout from the application
   */
  async logout(): Promise<void> {
    await this.logoutButton.click()

    // Wait for redirect to login page
    await this.page.waitForURL('**/student', { timeout: 10000 })
  }

  /**
   * Navigate using bottom navigation
   */
  async navigateToHome(): Promise<void> {
    await this.homeNavButton.click()
    await this.page.waitForURL('**/student/home')
  }

  async navigateToHistory(): Promise<void> {
    await this.historyNavButton.click()
    await this.page.waitForURL('**/student/history')
  }

  /**
   * Go back to previous page
   */
  async goBack(): Promise<void> {
    await this.backButton.click()
  }

  /**
   * Toggle theme
   */
  async toggleTheme(): Promise<void> {
    await this.themeToggle.click()
    await this.page.waitForTimeout(300)
  }

  /**
   * Verify form validation messages
   */
  async verifyValidationMessages(): Promise<{
    hasEmailError: boolean
    hasPhoneError: boolean
    hasPasswordError: boolean
  }> {
    const hasEmailError = await this.emailValidationError.isVisible()
    const hasPhoneError = await this.phoneValidationError.isVisible()
    const hasPasswordError = await this.passwordValidationError.isVisible()

    return { hasEmailError, hasPhoneError, hasPasswordError }
  }

  /**
   * Verify accessibility features
   */
  async verifyAccessibility(): Promise<void> {
    // Check for proper ARIA labels
    await expect(this.nameInput).toHaveAttribute('aria-label')
    await expect(this.emailInput).toHaveAttribute('aria-label')
    await expect(this.whatsappPhoneInput).toHaveAttribute('aria-label')

    // Check for proper form associations
    await expect(this.nameInput).toHaveAttribute('id')
    await expect(this.emailInput).toHaveAttribute('id')

    // Check keyboard navigation
    await this.editProfileButton.press('Tab')
    await expect(this.changePasswordButton).toBeFocused()
  }

  /**
   * Verify responsive design
   */
  async verifyResponsiveDesign(): Promise<void> {
    // Test mobile viewport
    await this.page.setViewportSize({ width: 375, height: 667 })
    await expect(this.profileCard).toBeVisible()
    await expect(this.bottomNav).toBeVisible()

    // Test tablet viewport
    await this.page.setViewportSize({ width: 768, height: 1024 })
    await expect(this.profileCard).toBeVisible()

    // Test desktop viewport
    await this.page.setViewportSize({ width: 1024, height: 768 })
    await expect(this.profileCard).toBeVisible()
  }

  /**
   * Wait for WhatsApp verification to complete
   */
  async waitForWhatsappVerification(timeout = 30000): Promise<void> {
    await this.verificationBadge.waitFor({ state: 'visible', timeout })
  }

  /**
   * Verify phone number format display
   */
  async verifyPhoneNumberFormat(): Promise<void> {
    const phoneDisplay = await this.studentWhatsapp.textContent()

    // Should display with +62 prefix
    if (phoneDisplay && phoneDisplay.trim() !== '-') {
      expect(phoneDisplay).toMatch(/^\+62\d+$/)
    }
  }

  /**
   * Test password strength requirements
   */
  async testPasswordStrength(password: string): Promise<{
    score: number
    isStrong: boolean
    feedback: string
  }> {
    await this.changePasswordButton.click()
    await this.changePasswordDialog.waitFor({ state: 'visible' })

    await this.newPasswordInput.fill(password)
    await this.page.waitForTimeout(500) // Wait for strength calculation

    const strength = await this.getPasswordStrength()
    const isStrong = strength.score >= 60

    await this.cancelPasswordButton.click()
    await this.changePasswordDialog.waitFor({ state: 'hidden' })

    return {
      score: strength.score,
      isStrong,
      feedback: strength.text,
    }
  }
}
