import { type Locator, type Page } from '@playwright/test'
import { BasePage } from './base-page'

export class LandingPage extends BasePage {
  // Navigation elements
  readonly header: Locator
  readonly logo: Locator
  readonly themeToggle: Locator

  // Hero section elements
  readonly heroSection: Locator
  readonly heroTitle: Locator
  readonly heroSubtitle: Locator
  readonly heroImage: Locator

  // About section
  readonly aboutSection: Locator
  readonly aboutTitle: Locator
  readonly aboutText: Locator

  // Features section
  readonly featuresSection: Locator
  readonly featuresTitle: Locator
  readonly featureCards: Locator
  readonly qrCodeCard: Locator
  readonly scannerCard: Locator
  readonly reportsCard: Locator

  // Footer elements
  readonly footer: Locator
  readonly footerText: Locator

  constructor(page: Page) {
    super(page)

    // Navigation
    this.header = page.locator('header')
    this.logo = page.locator('header h1:has-text("ShalatYuk")')
    this.themeToggle = page.locator('header button:has(svg)')

    // Hero section
    this.heroSection = page.locator('section').first()
    this.heroTitle = page.locator('h1:has-text("ShalatYuk: Absensi Shalat Modern")')
    this.heroSubtitle = page.locator('p:has-text("Mendukung pelajar dalam ibadah")')
    this.heroImage = page.locator('img[alt="Masjid"]')

    // About section
    this.aboutSection = page.locator('section:has(h2:has-text("Tentang Aplikasi"))')
    this.aboutTitle = page.locator('h2:has-text("Tentang Aplikasi")')
    this.aboutText = page.locator('p:has-text("ShalatYuk memudahkan pencatatan")')

    // Features section
    this.featuresSection = page.locator('section:has(h2:has-text("Fitur Utama"))')
    this.featuresTitle = page.locator('h2:has-text("Fitur Utama")')
    this.featureCards = page.locator('section:has(h2:has-text("Fitur Utama")) .grid > div')
    this.qrCodeCard = page.locator('h3:has-text("QR Code Siswa")').locator('..')
    this.scannerCard = page.locator('h3:has-text("Scanner Admin")').locator('..')
    this.reportsCard = page.locator('h3:has-text("Laporan Harian")').locator('..')

    // Footer
    this.footer = page.locator('footer')
    this.footerText = page.locator('footer div:has-text("Banjarmasin High School")')
  }

  // Navigation methods
  async navigate(): Promise<void> {
    await this.page.goto('/')
    await this.waitForPageLoad()
  }

  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle')
    await this.assertElementVisible(this.header)
    await this.assertElementVisible(this.heroTitle)
  }

  // Hero section validation
  async verifyHeroSection(): Promise<void> {
    await this.assertElementVisible(this.heroTitle)
    await this.assertElementVisible(this.heroSubtitle)

    // Check title content
    const titleText = await this.getElementText(this.heroTitle)
    if (!titleText.includes('ShalatYuk')) {
      throw new Error('Hero title should contain "ShalatYuk"')
    }

    // Check subtitle content
    const subtitleText = await this.getElementText(this.heroSubtitle)
    if (!subtitleText.includes('Mendukung pelajar')) {
      throw new Error('Hero subtitle should mention supporting students')
    }
  }

  // About section validation
  async verifyAboutSection(): Promise<void> {
    await this.scrollToElement(this.aboutSection)
    await this.assertElementVisible(this.aboutTitle)
    await this.assertElementVisible(this.aboutText)

    const aboutText = await this.getElementText(this.aboutText)
    if (!aboutText.includes('ShalatYuk memudahkan')) {
      throw new Error('About text should describe ShalatYuk functionality')
    }
  }

  // Features section validation
  async verifyFeaturesSection(): Promise<void> {
    await this.scrollToElement(this.featuresSection)
    await this.assertElementVisible(this.featuresTitle)

    // Check that all 3 feature cards are present
    const cardCount = await this.featureCards.count()
    if (cardCount !== 3) {
      throw new Error(`Expected 3 feature cards, found ${cardCount}`)
    }

    // Verify each feature card
    await this.assertElementVisible(this.qrCodeCard)
    await this.assertElementVisible(this.scannerCard)
    await this.assertElementVisible(this.reportsCard)
  }

  // Footer validation
  async verifyFooter(): Promise<void> {
    await this.scrollToElement(this.footer)
    await this.assertElementVisible(this.footer)
    await this.assertElementVisible(this.footerText)

    const footerText = await this.getElementText(this.footerText)
    if (!footerText.includes('Banjarmasin High School')) {
      throw new Error('Footer should contain school information')
    }
  }

  // Theme toggle functionality
  async verifyThemeToggle(): Promise<void> {
    await this.assertElementVisible(this.themeToggle)
  }

  async toggleTheme(): Promise<void> {
    await this.click(this.themeToggle)
    await this.page.waitForTimeout(500) // Wait for theme transition
  }

  // Responsive design testing
  async testResponsiveDesign(): Promise<void> {
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' },
    ]

    for (const viewport of viewports) {
      await this.page.setViewportSize(viewport)
      await this.page.waitForTimeout(500)

      // Verify essential elements are still visible
      await this.assertElementVisible(this.header)
      await this.assertElementVisible(this.heroTitle)
      await this.assertElementVisible(this.footer)
    }
  }

  // Accessibility testing
  async verifyAccessibility(): Promise<void> {
    // Check for proper heading hierarchy
    const h1Count = await this.page.locator('h1').count()
    if (h1Count === 0) {
      throw new Error('Page should have at least one h1 element')
    }

    // Check for alt text on images
    const images = this.page.locator('img')
    const imageCount = await images.count()

    for (let i = 0; i < imageCount; i++) {
      const altText = await images.nth(i).getAttribute('alt')
      if (!altText || altText.trim() === '') {
        throw new Error(`Image ${i + 1} is missing alt text`)
      }
    }
  }

  // Performance testing
  async verifyPerformance(): Promise<number> {
    const startTime = Date.now()
    await this.navigate()
    const loadTime = Date.now() - startTime

    if (loadTime > 5000) {
      throw new Error(`Page load time (${loadTime}ms) exceeds 5 seconds`)
    }

    return loadTime
  }

  // Navigation link verification
  async verifyNoUnauthorizedLinks(): Promise<void> {
    // Check that there are no direct links to protected routes
    const unauthorizedLinks = ['/student/home', '/admin/home', '/admin/users', '/admin/reports']

    for (const link of unauthorizedLinks) {
      const linkElement = this.page.locator(`a[href="${link}"]`)
      const isVisible = await linkElement.isVisible()

      if (isVisible) {
        throw new Error(`Unauthorized link found: ${link}`)
      }
    }
  }

  // Protected route access testing
  async testProtectedRouteAccess(): Promise<void> {
    const protectedRoutes = ['/student/home', '/admin/home', '/admin/users', '/admin/reports']

    for (const route of protectedRoutes) {
      await this.page.goto(route)

      // Should redirect to login or show access denied
      const currentUrl = this.page.url()
      const isRedirected =
        currentUrl.includes('/login') ||
        currentUrl.includes('/admin') ||
        currentUrl.includes('/student') ||
        currentUrl === this.page.url()

      if (!isRedirected && currentUrl.includes(route)) {
        throw new Error(`Protected route ${route} is accessible without authentication`)
      }
    }

    // Return to landing page
    await this.navigate()
  }

  // Comprehensive validation method
  async performFullLandingPageValidation(): Promise<void> {
    await this.navigate()
    await this.verifyHeroSection()
    await this.verifyAboutSection()
    await this.verifyFeaturesSection()
    await this.verifyFooter()
    await this.verifyThemeToggle()
    await this.verifyAccessibility()
    await this.verifyNoUnauthorizedLinks()
  }

  // Helper method to check if specific content exists
  async hasContent(text: string): Promise<boolean> {
    const element = this.page.locator(`text=${text}`)
    return await element.isVisible()
  }

  // Helper method to get all navigation links
  async getNavigationLinks(): Promise<string[]> {
    const links = await this.page.locator('a').all()
    const hrefs: string[] = []

    for (const link of links) {
      const href = await link.getAttribute('href')
      if (href) {
        hrefs.push(href)
      }
    }

    return hrefs
  }
}
