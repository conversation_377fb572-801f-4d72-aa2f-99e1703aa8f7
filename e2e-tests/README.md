# ShalatYuk E2E Testing Suite

Comprehensive End-to-End testing implementation for the ShalatYuk prayer attendance application using Playwright.

## 🎯 Overview

This E2E testing suite provides complete coverage for all application features including:

- **Landing Page** - Public informational page with security validation
- **Student App** - Login, home, profile, and attendance features
- **Admin App** - Multi-role admin interface with QR scanning and reports
- **API Integration** - Backend service testing
- **Security & Performance** - Comprehensive validation

## 🏗️ Architecture

### Page Object Model (POM)

Following Playwright best practices with clean architecture:

```
e2e-tests/
├── page-objects/           # Page Object Models
│   ├── base-page.ts       # Common functionality
│   ├── landing-page.ts    # Landing page interactions
│   ├── student/           # Student app pages
│   └── admin/             # Admin app pages
├── fixtures/              # Test setup and configuration
│   ├── auth/             # Authentication states
│   ├── global-setup.ts   # Environment preparation
│   └── global-teardown.ts # Cleanup and reporting
├── test-data/            # Test data management
│   ├── users.ts          # User accounts and roles
│   └── attendance.ts     # Attendance types and scenarios
├── utils/                # Testing utilities
│   └── test-helpers.ts   # Common functions
└── *.spec.ts            # Test specifications
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- Application server running on `http://localhost:3000`
- Test database configured

### Installation

```bash
# Install dependencies (if not already done)
npm install

# Install Playwright browsers
npm run test:e2e:install
```

### Running Tests

```bash
# Run all E2E tests
npm run test:e2e

# Run with interactive UI
npm run test:e2e:ui

# Run in headed mode (visible browser)
npm run test:e2e:headed

# Run specific test files
npm run test:e2e:landing
npm run test:e2e:login
npm run test:e2e:student-home
npm run test:e2e:student-profile
npm run test:e2e:student-reset
npm run test:e2e:student
```

## 📋 Test Coverage

### ✅ Completed Features

#### Landing Page (100% Coverage)

- Hero section with title and imagery
- About section with application description
- Features section with QR Code, Scanner, and Reports cards
- Footer with school information and contact
- Theme toggle (light/dark mode)
- Security validation (no unauthorized links)
- Responsive design across devices
- Accessibility compliance
- Performance optimization

#### Student Login (100% Coverage)

- Valid/invalid credential scenarios
- Form validation and error handling
- Password visibility toggle
- Single device enforcement
- Forgot password navigation
- Accessibility features
- Mobile responsiveness
- Network error handling
- Security measures (CSRF, headers)
- Session management

### 🚧 In Development

#### Student App Features

- [ ] Home page with QR code display
- [ ] Profile management with WhatsApp OTP
- [ ] Password reset workflow
- [ ] Real-time attendance status

#### Admin App Features

- [ ] Multi-role login system
- [ ] QR code scanner with WebRTC
- [ ] Reports with filtering and export
- [ ] User and class management
- [ ] Session management

## 🔧 Configuration

### Playwright Configuration

```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './e2e-tests',
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,

  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },

  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } },
    { name: 'Mobile Chrome', use: { ...devices['Pixel 5'] } },
    { name: 'Mobile Safari', use: { ...devices['iPhone 12'] } },
  ],
})
```

### Test Data Management

```typescript
// e2e-tests/test-data/users.ts
export const testUsers = {
  student1: {
    username: 'siswa001',
    password: 'password123',
    role: 'student',
    // ... additional properties
  },
  admin: {
    username: 'admin001',
    password: 'Admin123!',
    role: 'admin',
  },
  // ... more test users
}
```

## 🧪 Testing Patterns

### Page Object Example

```typescript
export class StudentLoginPage extends BasePage {
  // Locators
  get usernameField() {
    return this.page.locator('input[name="username"]')
  }

  // Actions
  async login(user: TestUser): Promise<void> {
    await this.visitLoginPage()
    await this.fillUsername(user.username)
    await this.fillPassword(user.password)
    await this.clickLogin()
    await this.assertPageUrl(/\/student\/home/<USER>
  }
}
```

### Test Structure

```typescript
test.describe('Feature Name', () => {
  let pageObject: FeaturePage

  test.beforeEach(async ({ page }) => {
    pageObject = new FeaturePage(page)
  })

  test('should perform action successfully', async () => {
    // Arrange
    await pageObject.navigateToPage()

    // Act
    await pageObject.performAction()

    // Assert
    await pageObject.verifyResult()
  })
})
```

## 🎛️ Available Commands

### Basic Testing

```bash
npm run test:e2e                 # Run all tests
npm run test:e2e:ui              # Interactive mode
npm run test:e2e:headed          # Visible browser
npm run test:e2e:debug           # Debug mode
```

### Browser-Specific

```bash
npm run test:e2e:chromium        # Chrome only
npm run test:e2e:firefox         # Firefox only
npm run test:e2e:webkit          # Safari only
npm run test:e2e:mobile          # Mobile devices
```

### Advanced Options

```bash
npm run test:e2e:trace           # With trace recording
npm run test:e2e:record          # With video recording
npm run test:e2e:report          # View HTML report
```

### Specific Test Suites

```bash
npm run test:e2e:landing         # Landing page tests
npm run test:e2e:login           # Login functionality
```

## 📊 Reporting

### HTML Reports

After running tests, view detailed reports:

```bash
npm run test:e2e:report
```

### Screenshots and Videos

- **Screenshots**: Captured on test failures
- **Videos**: Recorded for failed tests (when enabled)
- **Traces**: Detailed execution traces for debugging

### Test Results Structure

```
test-results/
├── results.json          # JSON test results
├── results.xml           # JUnit XML format
├── screenshots/          # Failure screenshots
├── videos/              # Test execution videos
└── traces/              # Playwright traces
```

## 🔐 Security Testing

### Authentication

- JWT token validation
- Session management
- Single device enforcement
- CSRF protection

### Authorization

- Role-based access control
- Route protection
- API endpoint security

### Data Protection

- Input sanitization
- XSS prevention
- SQL injection protection

## 📱 Mobile Testing

### Responsive Design

Automated testing across viewports:

- Mobile phones (320px - 414px)
- Tablets (768px - 1024px)
- Desktop (1024px+)

### Touch Interactions

- Tap gestures
- Swipe navigation
- Mobile-specific UI elements

## ♿ Accessibility Testing

### WCAG 2.1 Compliance

- Semantic HTML validation
- ARIA attributes verification
- Keyboard navigation testing
- Color contrast checking
- Screen reader compatibility

### Implementation

```typescript
await checkAccessibility(page) // Custom utility function
```

## 🚀 Performance Testing

### Metrics Monitored

- Page load times (< 3 seconds target)
- API response times
- Resource optimization
- Core Web Vitals

### Implementation

```typescript
test('should load within performance budget', async ({ page }) => {
  const startTime = Date.now()
  await page.goto('/')
  const loadTime = Date.now() - startTime
  expect(loadTime).toBeLessThan(3000)
})
```

## 🛠️ Development Guidelines

### Writing New Tests

1. **Follow Page Object Model**: Create page objects for new features
2. **Use Test Data**: Leverage existing test data or extend it
3. **Implement Cleanup**: Ensure tests clean up after themselves
4. **Add Documentation**: Document complex test scenarios

### Best Practices

- ✅ Use descriptive test names
- ✅ Implement proper wait strategies
- ✅ Handle async operations correctly
- ✅ Use web-first assertions
- ✅ Avoid hardcoded delays
- ✅ Mock external dependencies when needed

### Code Review Checklist

- [ ] Page object pattern followed
- [ ] Proper error handling
- [ ] Cross-browser compatibility
- [ ] Mobile responsiveness
- [ ] Accessibility compliance
- [ ] Performance considerations

## 🐛 Debugging

### Debug Mode

```bash
npm run test:e2e:debug
```

### Trace Viewer

```bash
npm run test:e2e:trace
npx playwright show-trace test-results/traces/trace.zip
```

### Visual Debugging

```bash
npm run test:e2e:headed
```

## 🔄 CI/CD Integration

### GitHub Actions (Future)

```yaml
name: E2E Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:e2e
```

## 📞 Support

### Common Issues

1. **Tests failing randomly**: Check for race conditions and proper waits
2. **Authentication issues**: Verify test user credentials
3. **Element not found**: Update selectors or check page load timing

### Getting Help

- Check existing issues in project repository
- Review test documentation
- Contact development team for application-specific questions

---

## 🏆 Quality Standards

This testing suite follows SQA professional standards:

- **Comprehensive Coverage**: All critical user journeys tested
- **Risk-Based Approach**: Priority testing for high-risk features
- **Maintainable Code**: Clean architecture with proper documentation
- **Continuous Improvement**: Regular updates and optimization

**Created by:** E2E Testing Team  
**Last Updated:** 2024-12-21  
**Version:** 1.0.0

## Test Suites

### Landing Page Tests (`01-landing-page.spec.ts`)

- Hero section validation
- Features and about sections
- Theme toggle functionality
- Responsive design testing
- Accessibility compliance
- SEO validation

### Student Login Tests (`02-student-login.spec.ts`)

- Valid/invalid login scenarios
- Form validation testing
- Password visibility toggle
- Single device enforcement
- Security measures validation
- Mobile responsiveness

### Student Home Page Tests (`03-student-home.spec.ts`)

- QR code display and generation
- Attendance status indicators (Zuhr, Asr, Pulang)
- Real-time status updates
- Date/time display with WITA timezone
- Navigation and logout functionality
- Theme and accessibility features
- Performance and security testing

### Student Profile Tests (`04-student-profile.spec.ts`)

- Profile information display and editing
- WhatsApp OTP verification and management
- Password strength validation and changes
- Form validation and error handling
- Phone number format validation
- Email uniqueness checking
- Data persistence and integrity

### Student Reset Password Tests (`05-student-reset-password.spec.ts`)

- URL parameter validation (WhatsApp OTP / Token methods)
- Password strength indicator and requirements
- Password visibility toggle functionality
- Form validation and error handling
- Security features (CSRF, XSS protection)
- Rate limiting and timing attack prevention
- Integration with login flow
