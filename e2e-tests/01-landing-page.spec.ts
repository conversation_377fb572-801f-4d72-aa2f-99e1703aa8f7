import { test, expect } from '@playwright/test'
import { LandingPage } from './page-objects/landing-page'

test.describe('Landing Page', () => {
  let landingPage: LandingPage

  test.beforeEach(async ({ page }) => {
    landingPage = new LandingPage(page)
  })

  test('should display all required sections', async () => {
    await landingPage.navigate()

    // Verify hero section
    await landingPage.verifyHeroSection()

    // Verify about section
    await landingPage.verifyAboutSection()

    // Verify features section
    await landingPage.verifyFeaturesSection()

    // Verify footer
    await landingPage.verifyFooter()
  })

  test('should have proper hero content', async () => {
    await landingPage.navigate()

    // Check hero title contains expected text
    await expect(landingPage.heroTitle).toContainText(
      'ShalatYuk: Absensi Shalat Modern untuk Masa Depan Pendidikan'
    )

    // Check hero subtitle exists
    await expect(landingPage.heroSubtitle).toBeVisible()

    // Check hero image if present
    if (await landingPage.heroImage.isVisible()) {
      await expect(landingPage.heroImage).toHaveAttribute('alt', 'Masjid')
    }
  })

  test('should display three feature cards with icons', async () => {
    await landingPage.navigate()

    await landingPage.scrollToElement(landingPage.featuresSection)

    // Check features section exists
    await expect(landingPage.featuresSection).toBeVisible()

    // Check feature cards count
    const cardCount = await landingPage.featureCards.count()
    expect(cardCount).toBe(3)

    // Check individual feature cards
    await expect(landingPage.qrCodeCard).toBeVisible()
    await expect(landingPage.scannerCard).toBeVisible()
    await expect(landingPage.reportsCard).toBeVisible()

    // Verify feature titles
    await expect(landingPage.page.locator('h3:has-text("QR Code Siswa")')).toBeVisible()
    await expect(landingPage.page.locator('h3:has-text("Scanner Admin")')).toBeVisible()
    await expect(landingPage.page.locator('h3:has-text("Laporan Harian")')).toBeVisible()
  })

  test('should have correct footer information', async () => {
    await landingPage.navigate()

    await landingPage.scrollToElement(landingPage.footer)

    const footerText = await landingPage.getElementText(landingPage.footerText)
    expect(footerText).toContain('Banjarmasin High School')
    expect(footerText).toContain('2025')
  })

  test('should toggle theme correctly', async () => {
    await landingPage.navigate()

    await landingPage.verifyThemeToggle()
  })

  test('should NOT have unauthorized navigation links', async () => {
    await landingPage.navigate()

    await landingPage.verifyNoUnauthorizedLinks()
  })

  test('should be responsive on different screen sizes', async ({ page }) => {
    await landingPage.navigate()

    const viewports = [
      { width: 320, height: 568, name: 'mobile-small' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1024, height: 768, name: 'desktop-small' },
      { width: 1920, height: 1080, name: 'desktop-large' },
    ]

    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height })
      await page.waitForTimeout(300)

      // Verify key elements are still visible
      await expect(landingPage.heroSection).toBeVisible()
      await expect(landingPage.featuresSection).toBeVisible()
      await expect(landingPage.footer).toBeVisible()

      // Take screenshot for visual regression
      await page.screenshot({
        path: `test-results/screenshots/landing-${viewport.name}.png`,
        fullPage: true,
      })
    }
  })

  test('should have proper page title and meta tags', async ({ page }) => {
    await landingPage.navigate()

    // Check page title
    await expect(page).toHaveTitle(/Prayer Attendance|Absensi Shalat|ShalatYuk/)

    // Check meta description exists
    const metaDescription = page.locator('meta[name="description"]')
    await expect(metaDescription).toHaveAttribute('content', /.+/)
  })

  test('should load without performance issues', async ({ page }) => {
    const startTime = Date.now()

    await landingPage.navigate()

    const loadTime = Date.now() - startTime

    // Page should load within 5 seconds
    expect(loadTime).toBeLessThan(5000)

    // Check that all major sections loaded
    await expect(landingPage.heroSection).toBeVisible()
    await expect(landingPage.featuresSection).toBeVisible()
    await expect(landingPage.footer).toBeVisible()

    // Performance metrics
    const performanceMetrics = await page.evaluate(() =>
      JSON.stringify(performance.getEntriesByType('navigation'))
    )
    const metrics = JSON.parse(performanceMetrics)[0] as PerformanceNavigationTiming

    console.log(`Page load time: ${loadTime}ms`)
    console.log(
      `DOM Content Loaded: ${metrics.domContentLoadedEventEnd - metrics.domContentLoadedEventStart}ms`
    )
  })

  test('should have proper accessibility attributes', async ({ page }) => {
    await landingPage.navigate()

    // Check for proper heading structure
    const h1Count = await page.locator('h1').count()
    expect(h1Count).toBeGreaterThan(0)

    // Check for alt text on images
    const images = page.locator('img')
    const imageCount = await images.count()

    if (imageCount > 0) {
      for (let i = 0; i < imageCount; i++) {
        const altText = await images.nth(i).getAttribute('alt')
        expect(altText).toBeTruthy()
        expect(altText?.trim()).not.toBe('')
      }
    }

    // Check for ARIA labels where needed
    const buttons = page.locator('button')
    const buttonCount = await buttons.count()

    for (let i = 0; i < buttonCount; i++) {
      const ariaLabel = await buttons.nth(i).getAttribute('aria-label')
      const textContent = await buttons.nth(i).textContent()

      // Button should have either text content or aria-label
      expect(ariaLabel || textContent?.trim()).toBeTruthy()
    }
  })

  test('should handle keyboard navigation', async ({ page }) => {
    await landingPage.navigate()

    // Test theme toggle keyboard navigation
    await landingPage.themeToggle.focus()
    await page.keyboard.press('Enter')

    // Wait for theme to change
    await page.waitForTimeout(500)

    // Test tab navigation through focusable elements
    await page.keyboard.press('Tab')

    // Verify focus is moving correctly - just check that some element has focus
    const focusedElement = await page.evaluate(() => document.activeElement?.tagName)
    expect(focusedElement).toBeTruthy()
    console.log(`Focused element: ${focusedElement}`)
  })

  test('should not allow direct access to protected routes', async ({ page }) => {
    const protectedRoutes = ['/student/home', '/admin/home', '/admin/users', '/admin/scanner']

    for (const route of protectedRoutes) {
      await page.goto(route)

      // Should redirect to login or not show protected content
      const currentUrl = page.url()
      const hasLoginForm = await page.locator('input[type="password"]').isVisible()

      // Either redirected to login page or showing login form
      expect(hasLoginForm || !currentUrl.includes(route.replace('/', ''))).toBe(true)
    }
  })

  test('should perform complete landing page validation', async () => {
    await landingPage.performFullLandingPageValidation()
  })
})
