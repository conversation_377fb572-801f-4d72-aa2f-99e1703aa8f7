import { test, expect } from '@playwright/test'
import { AdminLoginPage } from './page-objects/admin/admin-login-page'
import { testUsers } from './test-data/users'

test.describe('Admin Login Page', () => {
  let adminLoginPage: AdminLoginPage

  test.beforeEach(async ({ page }) => {
    adminLoginPage = new AdminLoginPage(page)
    await adminLoginPage.goto()
  })

  test.describe('Page Load and UI', () => {
    test('should load login page correctly', async () => {
      await expect(adminLoginPage.pageTitle).toBeVisible()
      await expect(adminLoginPage.loginForm).toBeVisible()
      await expect(adminLoginPage.usernameInput).toBeVisible()
      await expect(adminLoginPage.passwordInput).toBeVisible()
      await expect(adminLoginPage.loginButton).toBeVisible()
    })

    test('should display logo and branding', async () => {
      if (await adminLoginPage.logoImage.isVisible()) {
        await expect(adminLoginPage.logoImage).toBeVisible()
      }
    })

    test('should show theme toggle', async () => {
      if (await adminLoginPage.themeToggle.isVisible()) {
        await expect(adminLoginPage.themeToggle).toBeVisible()
      }
    })

    test('should display navigation links', async () => {
      if (await adminLoginPage.studentLoginLink.isVisible()) {
        await expect(adminLoginPage.studentLoginLink).toBeVisible()
      }
    })
  })

  test.describe('Super Admin Authentication', () => {
    test('should login successfully as Super Admin', async () => {
      const superAdmin = testUsers.superAdmin
      await adminLoginPage.login(superAdmin)

      // Should redirect to admin home
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })

    test('should maintain Super Admin session', async () => {
      await adminLoginPage.loginAsSuperAdmin()

      // Navigate to another page and back
      await adminLoginPage.page.goto('/admin/profile')
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/profile/)

      // Should still be authenticated
      await adminLoginPage.page.goto('/admin/home')
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })
  })

  test.describe('Admin Authentication', () => {
    test('should login successfully as Admin', async () => {
      const admin = testUsers.admin
      await adminLoginPage.login(admin)

      // Should redirect to admin home
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })

    test('should login with different admin credentials', async () => {
      // Test with kepsek account
      await adminLoginPage.loginAsAdmin()
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })
  })

  test.describe('Teacher Authentication', () => {
    test('should login successfully as Teacher', async () => {
      const teacher = testUsers.guru
      await adminLoginPage.login(teacher)

      // Should redirect to admin home
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })

    test('should login with teacher credentials', async () => {
      await adminLoginPage.loginAsTeacher()
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })
  })

  test.describe('Receptionist Authentication', () => {
    test('should login successfully as Receptionist', async () => {
      const receptionist = testUsers.resepsionis
      await adminLoginPage.login(receptionist)

      // Should redirect to admin home
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })

    test('should login with receptionist credentials', async () => {
      await adminLoginPage.loginAsReceptionist()
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })
  })

  test.describe('Form Validation', () => {
    test('should validate required fields', async () => {
      await adminLoginPage.submitEmptyForm()

      // Should show validation errors or stay on login page
      await expect(adminLoginPage.loginForm).toBeVisible()
    })

    test('should validate username field', async () => {
      await adminLoginPage.passwordInput.fill('password123')
      await adminLoginPage.loginButton.click()

      // Should stay on login page without valid username
      await expect(adminLoginPage.loginForm).toBeVisible()
    })

    test('should validate password field', async () => {
      await adminLoginPage.usernameInput.fill('testuser')
      await adminLoginPage.loginButton.click()

      // Should stay on login page without valid password
      await expect(adminLoginPage.loginForm).toBeVisible()
    })

    test('should handle invalid credentials', async () => {
      await adminLoginPage.attemptLogin('invalid_user', 'invalid_password')

      // Should show error message
      await expect(adminLoginPage.errorMessage).toBeVisible()
      const errorText = await adminLoginPage.getErrorMessage()
      expect(errorText.toLowerCase()).toContain('invalid')
    })

    test('should validate minimum field lengths', async () => {
      await adminLoginPage.attemptLogin('a', 'b')

      // Should show validation error or stay on login page
      await expect(adminLoginPage.loginForm).toBeVisible()
    })
  })

  test.describe('Password Visibility Toggle', () => {
    test('should toggle password visibility', async () => {
      await adminLoginPage.passwordInput.fill('testpassword')

      if (await adminLoginPage.showPasswordButton.isVisible()) {
        await adminLoginPage.togglePasswordVisibility()

        // Password should now be visible
        const passwordType = await adminLoginPage.passwordInput.getAttribute('type')
        expect(passwordType).toBe('text')

        // Toggle back
        await adminLoginPage.togglePasswordVisibility()
        const hiddenPasswordType = await adminLoginPage.passwordInput.getAttribute('type')
        expect(hiddenPasswordType).toBe('password')
      }
    })
  })

  test.describe('Navigation Links', () => {
    test('should navigate to student login', async () => {
      if (await adminLoginPage.studentLoginLink.isVisible()) {
        await adminLoginPage.navigateToStudentLogin()
      }
    })

    test('should navigate to forgot password', async () => {
      if (await adminLoginPage.forgotPasswordLink.isVisible()) {
        await adminLoginPage.navigateToForgotPassword()
      }
    })
  })

  test.describe('Theme and UI Features', () => {
    test('should toggle theme', async () => {
      if (await adminLoginPage.themeToggle.isVisible()) {
        await adminLoginPage.toggleTheme()
      }
    })

    test('should maintain theme across page reloads', async () => {
      if (await adminLoginPage.themeToggle.isVisible()) {
        const initialTheme = await adminLoginPage.page.locator('html').getAttribute('class')

        await adminLoginPage.toggleTheme()
        await adminLoginPage.page.reload()

        const reloadedTheme = await adminLoginPage.page.locator('html').getAttribute('class')
        // Theme should be maintained (implementation dependent)
      }
    })
  })

  test.describe('Security Features', () => {
    test('should prevent SQL injection attacks', async () => {
      await adminLoginPage.testSQLInjection()
    })

    test('should prevent XSS attacks', async () => {
      await adminLoginPage.testXSSAttack()
    })

    test('should implement CSRF protection', async () => {
      await adminLoginPage.testCSRFProtection()
    })

    test('should handle brute force protection', async () => {
      // Attempt multiple failed logins
      for (let i = 0; i < 5; i++) {
        await adminLoginPage.attemptLogin('invalid_user', 'invalid_password')
        await adminLoginPage.page.waitForTimeout(1000)
        await adminLoginPage.clearForm()
      }

      // Should show rate limiting or account lockout
      // Implementation dependent
    })

    test('should secure password field', async () => {
      await adminLoginPage.passwordInput.fill('secretpassword')

      // Password field should be of type 'password'
      const passwordType = await adminLoginPage.passwordInput.getAttribute('type')
      expect(passwordType).toBe('password')

      // Password should not be visible in page source
      const pageContent = await adminLoginPage.page.content()
      expect(pageContent).not.toContain('secretpassword')
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors', async () => {
      // Mock network failure
      await adminLoginPage.page.route('**/api/auth/**', route => route.abort())

      await adminLoginPage.attemptLogin('admin', 'password')

      // Should show network error
      await expect(adminLoginPage.errorMessage).toBeVisible()

      // Clear mock
      await adminLoginPage.page.unroute('**/api/auth/**')
    })

    test('should handle server errors', async () => {
      // Mock server error
      await adminLoginPage.page.route('**/api/auth/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' }),
        })
      })

      await adminLoginPage.attemptLogin('admin', 'password')

      // Should show server error
      await expect(adminLoginPage.errorMessage).toBeVisible()

      // Clear mock
      await adminLoginPage.page.unroute('**/api/auth/**')
    })

    test('should handle timeout errors', async () => {
      // Mock slow response
      await adminLoginPage.page.route('**/api/auth/**', route => {
        setTimeout(
          () =>
            route.fulfill({
              status: 200,
              contentType: 'application/json',
              body: JSON.stringify({ success: true }),
            }),
          10000
        )
      })

      await adminLoginPage.attemptLogin('admin', 'password')

      // Should handle timeout gracefully
      await adminLoginPage.page.waitForTimeout(5000)

      // Clear mock
      await adminLoginPage.page.unroute('**/api/auth/**')
    })
  })

  test.describe('Form Behavior', () => {
    test('should clear form when needed', async () => {
      await adminLoginPage.usernameInput.fill('testuser')
      await adminLoginPage.passwordInput.fill('testpassword')

      await adminLoginPage.clearForm()

      await expect(adminLoginPage.usernameInput).toHaveValue('')
      await expect(adminLoginPage.passwordInput).toHaveValue('')
    })

    test('should maintain focus order', async () => {
      // Test tab order
      await adminLoginPage.usernameInput.press('Tab')
      await expect(adminLoginPage.passwordInput).toBeFocused()

      await adminLoginPage.passwordInput.press('Tab')
      await expect(adminLoginPage.loginButton).toBeFocused()
    })

    test('should submit on Enter key', async () => {
      await adminLoginPage.usernameInput.fill('admin')
      await adminLoginPage.passwordInput.fill('password')

      // Press Enter in password field
      await adminLoginPage.passwordInput.press('Enter')

      // Should trigger form submission
      await adminLoginPage.page.waitForTimeout(1000)
    })

    test('should handle autofill and autocomplete', async () => {
      // Test that form works with browser autofill
      const username = 'testuser'
      const password = 'testpassword'

      await adminLoginPage.usernameInput.fill(username)
      await adminLoginPage.passwordInput.fill(password)

      await expect(adminLoginPage.usernameInput).toHaveValue(username)
      await expect(adminLoginPage.passwordInput).toHaveValue(password)
    })
  })

  test.describe('Accessibility', () => {
    test('should meet accessibility standards', async () => {
      await adminLoginPage.verifyAccessibility()
    })

    test('should support keyboard navigation', async () => {
      // Tab through all interactive elements
      await adminLoginPage.usernameInput.press('Tab')
      await expect(adminLoginPage.passwordInput).toBeFocused()

      await adminLoginPage.passwordInput.press('Tab')
      await expect(adminLoginPage.loginButton).toBeFocused()

      if (await adminLoginPage.showPasswordButton.isVisible()) {
        await adminLoginPage.loginButton.press('Tab')
        await expect(adminLoginPage.showPasswordButton).toBeFocused()
      }
    })

    test('should have proper ARIA labels', async () => {
      // Check form accessibility
      await expect(adminLoginPage.loginForm).toHaveAttribute('role')
      await expect(adminLoginPage.usernameInput).toHaveAttribute('aria-label')
      await expect(adminLoginPage.passwordInput).toHaveAttribute('aria-label')
    })

    test('should provide error announcements', async () => {
      await adminLoginPage.attemptLogin('invalid', 'invalid')

      if (await adminLoginPage.errorMessage.isVisible()) {
        // Error message should be announced to screen readers
        await expect(adminLoginPage.errorMessage).toHaveAttribute('role', 'alert')
      }
    })
  })

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async () => {
      await adminLoginPage.verifyResponsiveDesign()
    })

    test('should adapt to different screen sizes', async () => {
      // Test various viewports
      const viewports = [
        { width: 320, height: 568 }, // iPhone SE
        { width: 375, height: 667 }, // iPhone 8
        { width: 768, height: 1024 }, // iPad
        { width: 1200, height: 800 }, // Desktop
      ]

      for (const viewport of viewports) {
        await adminLoginPage.page.setViewportSize(viewport)

        // All elements should remain accessible
        await expect(adminLoginPage.loginForm).toBeVisible()
        await expect(adminLoginPage.usernameInput).toBeVisible()
        await expect(adminLoginPage.passwordInput).toBeVisible()
        await expect(adminLoginPage.loginButton).toBeVisible()
      }
    })
  })

  test.describe('Performance', () => {
    test('should load quickly', async () => {
      const loadTime = await adminLoginPage.measureLoadTime()

      // Page should load within reasonable time
      expect(loadTime).toBeLessThan(3000)
    })

    test('should handle form interactions efficiently', async () => {
      await adminLoginPage.testFormPerformance()
    })

    test('should not memory leak on repeated interactions', async () => {
      // Test multiple form submissions
      for (let i = 0; i < 10; i++) {
        await adminLoginPage.usernameInput.fill(`user${i}`)
        await adminLoginPage.passwordInput.fill(`pass${i}`)
        await adminLoginPage.clearForm()
      }

      // Memory usage should remain stable
      // This is more of an integration test that would need monitoring tools
    })
  })

  test.describe('Session Management', () => {
    test('should handle existing sessions', async () => {
      // Login first
      await adminLoginPage.loginAsAdmin()
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>

      // Go back to login page
      await adminLoginPage.page.goto('/admin')

      // Should redirect to home if already logged in
      await expect(adminLoginPage.page).toHaveURL(/\/admin\/home/<USER>
    })

    test('should handle expired sessions', async () => {
      // This would require implementing session expiry testing
      // Could involve manipulating session cookies or waiting for timeout
    })

    test('should handle concurrent login attempts', async () => {
      // Test logging in from multiple tabs/windows
      const newPage = await adminLoginPage.page.context().newPage()
      const newLoginPage = new AdminLoginPage(newPage)

      await newLoginPage.goto()
      await newLoginPage.loginAsAdmin()

      // Both sessions should be handled appropriately
      await expect(newPage).toHaveURL(/\/admin\/home/<USER>

      await newPage.close()
    })
  })
})
