import { test as setup, expect } from '@playwright/test'
import { testUsers } from './test-data/users'

const authFile = 'e2e-tests/fixtures/auth/student.json'
const adminAuthFile = 'e2e-tests/fixtures/auth/admin.json'

setup('authenticate as student', async ({ page }) => {
  const student = testUsers.student1

  // Navigate to student login
  await page.goto('/student')

  // Wait for page to load and form to be visible
  await page.waitForSelector('#username')

  // Fill login form using ID selectors
  await page.locator('#username').fill(student.username)
  await page.locator('#password').fill(student.password)

  // Submit and wait for redirect
  await Promise.all([
    page.waitForURL(/\/student\/home/<USER>
    page.locator('button[type="submit"]').click(),
  ])

  // Verify successful login
  await expect(page).toHaveURL(/\/student\/home/<USER>

  // Save authentication state
  await page.context().storageState({ path: authFile })

  console.log('✅ Student authentication setup completed')
})

setup('authenticate as admin', async ({ page }) => {
  const admin = testUsers.admin

  // Navigate to admin login
  await page.goto('/admin')

  // Wait for page to load and form to be visible
  await page.waitForSelector('#username')

  // Fill login form using ID selectors
  await page.locator('#username').fill(admin.username)
  await page.locator('#password').fill(admin.password)

  // Submit and wait for redirect
  await Promise.all([page.waitForURL(/\/admin/), page.locator('button[type="submit"]').click()])

  // Verify successful login
  await expect(page).toHaveURL(/\/admin/)

  // Save authentication state
  await page.context().storageState({ path: adminAuthFile })

  console.log('✅ Admin authentication setup completed')
})

setup('authenticate as super admin', async ({ page }) => {
  const superAdmin = testUsers.superAdmin

  // Navigate to admin login
  await page.goto('/admin')

  // Wait for page to load and form to be visible
  await page.waitForSelector('#username')

  // Fill login form using ID selectors
  await page.locator('#username').fill(superAdmin.username)
  await page.locator('#password').fill(superAdmin.password)

  // Submit and wait for redirect
  await Promise.all([page.waitForURL(/\/admin/), page.locator('button[type="submit"]').click()])

  // Verify successful login
  await expect(page).toHaveURL(/\/admin/)

  // Save authentication state for super admin
  await page.context().storageState({ path: 'e2e-tests/fixtures/auth/super-admin.json' })

  console.log('✅ Super Admin authentication setup completed')
})
