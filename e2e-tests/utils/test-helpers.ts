import { Page, expect } from '@playwright/test'
import { TestUser } from '../test-data/users'

/**
 * Wait for element to be visible with custom timeout
 */
export async function waitForElement(
  page: Page,
  selector: string,
  timeout: number = 10000
): Promise<void> {
  await expect(page.locator(selector)).toBeVisible({ timeout })
}

/**
 * Wait for text to appear in element
 */
export async function waitForText(
  page: Page,
  selector: string,
  text: string,
  timeout: number = 10000
): Promise<void> {
  await expect(page.locator(selector)).toContainText(text, { timeout })
}

/**
 * Take screenshot with timestamp
 */
export async function takeScreenshot(page: Page, name: string): Promise<void> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  await page.screenshot({
    path: `test-results/screenshots/${name}-${timestamp}.png`,
    fullPage: true,
  })
}

/**
 * Take accessibility snapshot for testing
 */
export async function takeAccessibilitySnapshot(page: Page, name: string): Promise<void> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')

  // Take screenshot for accessibility testing
  await page.screenshot({
    path: `test-results/accessibility/${name}-accessibility-${timestamp}.png`,
    fullPage: true,
  })

  // You can extend this to use actual accessibility testing tools
  // like axe-core if needed in the future
}

/**
 * Fill form field with validation
 */
export async function fillField(
  page: Page,
  selector: string,
  value: string,
  shouldValidate: boolean = true
): Promise<void> {
  const field = page.locator(selector)
  await expect(field).toBeVisible()
  await field.clear()
  await field.fill(value)

  if (shouldValidate) {
    await expect(field).toHaveValue(value)
  }
}

/**
 * Click button and wait for navigation or response
 */
export async function clickButton(
  page: Page,
  selector: string,
  waitForNavigation: boolean = false
): Promise<void> {
  const button = page.locator(selector)
  await expect(button).toBeVisible()
  await expect(button).toBeEnabled()

  if (waitForNavigation) {
    await Promise.all([page.waitForNavigation(), button.click()])
  } else {
    await button.click()
  }
}

/**
 * Wait for toast message to appear and disappear
 */
export async function waitForToast(
  page: Page,
  message: string,
  type: 'success' | 'error' | 'warning' | 'info' = 'success'
): Promise<void> {
  const toastSelector = `[data-testid="toast"], .toast, [role="alert"]`

  // Wait for toast to appear
  await expect(page.locator(toastSelector)).toBeVisible()

  // Check if it contains the expected message
  await expect(page.locator(toastSelector)).toContainText(message)

  // Optionally wait for it to disappear
  await expect(page.locator(toastSelector)).toBeHidden({ timeout: 10000 })
}

/**
 * Login helper for different user types
 */
export async function login(page: Page, user: TestUser, baseUrl: string = ''): Promise<void> {
  let loginUrl = ''

  switch (user.role) {
    case 'student':
      loginUrl = `${baseUrl}/student`
      break
    default:
      loginUrl = `${baseUrl}/admin`
      break
  }

  await page.goto(loginUrl)

  // Fill login form
  await fillField(page, '[data-testid="username"], input[name="username"]', user.username)
  await fillField(page, '[data-testid="password"], input[name="password"]', user.password)

  // Submit form
  await clickButton(page, '[data-testid="login-button"], button[type="submit"]', true)

  // Wait for successful login
  if (user.role === 'student') {
    await expect(page).toHaveURL(/\/student\/home/<USER>
  } else {
    await expect(page).toHaveURL(/\/admin/)
  }
}

/**
 * Login as student helper - simplified version for student tests
 */
export async function loginAsStudent(page: Page, user: TestUser): Promise<void> {
  await page.goto('/student')

  // Fill login form
  await page.locator('input[name="username"]').fill(user.username)
  await page.locator('input[name="password"]').fill(user.password)

  // Submit and wait for redirect
  await Promise.all([
    page.waitForURL(/\/student\/home/<USER>
    page.locator('button[type="submit"]').click(),
  ])

  // Verify successful login
  await expect(page).toHaveURL(/\/student\/home/<USER>
}

/**
 * Logout helper
 */
export async function logout(page: Page): Promise<void> {
  // Look for logout button or menu
  const logoutButton = page.locator(
    '[data-testid="logout"], button:has-text("Logout"), button:has-text("Keluar")'
  )

  if (await logoutButton.isVisible()) {
    await logoutButton.click()
  } else {
    // Try to find in dropdown menu
    const profileMenu = page.locator('[data-testid="profile-menu"], [data-testid="user-menu"]')
    if (await profileMenu.isVisible()) {
      await profileMenu.click()
      await logoutButton.click()
    }
  }

  // Wait for redirect to login page
  await expect(page).toHaveURL(/\/(student|admin)?$/)
}

/**
 * Generate unique test data
 */
export function generateTestData() {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(7)

  return {
    timestamp,
    random,
    email: `test.${random}@example.com`,
    phone: `+628${Math.floor(Math.random() * 1000000000)}`,
    nis: `2024${Math.floor(Math.random() * 1000)}`,
    uniqueId: `test_${timestamp}_${random}`,
  }
}

/**
 * Wait for API response
 */
export async function waitForApiResponse(
  page: Page,
  apiUrl: string,
  timeout: number = 10000
): Promise<void> {
  await page.waitForResponse(
    response => response.url().includes(apiUrl) && response.status() === 200,
    { timeout }
  )
}

/**
 * Mock API response for testing
 */
export async function mockApiResponse(
  page: Page,
  apiUrl: string,
  responseData: any,
  statusCode: number = 200
): Promise<void> {
  await page.route(`**${apiUrl}`, async route => {
    await route.fulfill({
      status: statusCode,
      contentType: 'application/json',
      body: JSON.stringify(responseData),
    })
  })
}

/**
 * Clear browser storage
 */
export async function clearStorage(page: Page): Promise<void> {
  await page.context().clearCookies()
  await page.evaluate(() => {
    localStorage.clear()
    sessionStorage.clear()
  })
}

/**
 * Check responsive design
 */
export async function testResponsiveDesign(
  page: Page,
  viewports: Array<{ width: number; height: number; name: string }>
): Promise<void> {
  for (const viewport of viewports) {
    await page.setViewportSize(viewport)
    await page.waitForTimeout(1000) // Allow layout to settle

    // Take screenshot for visual regression testing
    await takeScreenshot(page, `responsive-${viewport.name}`)

    // Basic checks for responsive layout
    await expect(page.locator('body')).toBeVisible()
  }
}

/**
 * Accessibility testing helper
 */
export async function checkAccessibility(page: Page): Promise<void> {
  // Check for basic accessibility attributes
  const headings = page.locator('h1, h2, h3, h4, h5, h6')
  const buttons = page.locator('button')
  const inputs = page.locator('input, textarea, select')

  // Ensure headings have proper text
  const headingCount = await headings.count()
  for (let i = 0; i < headingCount; i++) {
    await expect(headings.nth(i)).not.toBeEmpty()
  }

  // Ensure buttons have proper text or aria-label
  const buttonCount = await buttons.count()
  for (let i = 0; i < buttonCount; i++) {
    const button = buttons.nth(i)
    const text = await button.textContent()
    const ariaLabel = await button.getAttribute('aria-label')

    if (!text?.trim() && !ariaLabel?.trim()) {
      throw new Error(`Button at index ${i} has no accessible text or aria-label`)
    }
  }

  // Ensure form inputs have proper labels
  const inputCount = await inputs.count()
  for (let i = 0; i < inputCount; i++) {
    const input = inputs.nth(i)
    const id = await input.getAttribute('id')
    const ariaLabel = await input.getAttribute('aria-label')
    const placeholder = await input.getAttribute('placeholder')

    if (id) {
      const label = page.locator(`label[for="${id}"]`)
      if (!(await label.isVisible()) && !ariaLabel && !placeholder) {
        throw new Error(`Input with id "${id}" has no associated label or aria-label`)
      }
    }
  }
}
