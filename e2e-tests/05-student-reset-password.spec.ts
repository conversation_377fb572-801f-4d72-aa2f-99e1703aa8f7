import { test, expect } from '@playwright/test'
import { StudentResetPasswordPage } from './page-objects/student-reset-password-page'
import { StudentLoginPage } from './page-objects/student/login-page'
import { testUsers } from './test-data/users'
import { takeAccessibilitySnapshot, mockWhatsAppOTP } from './utils/test-helpers'

test.describe('Student Reset Password Page', () => {
  let resetPasswordPage: StudentResetPasswordPage
  let loginPage: StudentLoginPage

  test.beforeEach(async ({ page }) => {
    resetPasswordPage = new StudentResetPasswordPage(page)
    loginPage = new StudentLoginPage(page)
  })

  test.describe('URL Parameter Validation', () => {
    test('should handle WhatsApp OTP method with valid parameters', async () => {
      await resetPasswordPage.goto({
        method: 'whatsapp',
        whatsapp: '6281234567890',
        otp: '123456',
      })

      const urlParams = await resetPasswordPage.verifyUrlParameters()
      expect(urlParams.hasValidParams).toBe(true)
      expect(urlParams.method).toBe('whatsapp')
      expect(urlParams.hasWhatsapp).toBe(true)
      expect(urlParams.hasOtp).toBe(true)

      // Form should be visible
      await expect(resetPasswordPage.resetPasswordForm).toBeVisible()
    })

    test('should handle token method with valid parameters', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_reset_token_12345',
      })

      const urlParams = await resetPasswordPage.verifyUrlParameters()
      expect(urlParams.hasValidParams).toBe(true)
      expect(urlParams.method).toBe('token')
      expect(urlParams.hasToken).toBe(true)

      await expect(resetPasswordPage.resetPasswordForm).toBeVisible()
    })

    test('should handle legacy token method without explicit method parameter', async () => {
      await resetPasswordPage.goto({
        token: 'legacy_token_12345',
      })

      const urlParams = await resetPasswordPage.verifyUrlParameters()
      expect(urlParams.hasValidParams).toBe(true)
      expect(urlParams.hasToken).toBe(true)

      await expect(resetPasswordPage.resetPasswordForm).toBeVisible()
    })

    test('should redirect for missing required parameters', async () => {
      // Test missing OTP for WhatsApp method
      await resetPasswordPage.goto({
        method: 'whatsapp',
        whatsapp: '6281234567890',
        // Missing OTP
      })

      // Should redirect to forgot password page or show error
      await resetPasswordPage.page.waitForTimeout(2000)
      const currentUrl = resetPasswordPage.page.url()
      expect(currentUrl).toMatch(/\/(student\/forgot-password|student)/)
    })

    test('should redirect for missing token in token method', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        // Missing token
      })

      await resetPasswordPage.page.waitForTimeout(2000)
      const currentUrl = resetPasswordPage.page.url()
      expect(currentUrl).toMatch(/\/(student\/forgot-password|student)/)
    })

    test('should handle invalid method parameter', async () => {
      await resetPasswordPage.goto({
        method: 'invalid' as any,
        token: 'some_token',
      })

      await resetPasswordPage.page.waitForTimeout(2000)
      const currentUrl = resetPasswordPage.page.url()
      expect(currentUrl).toMatch(/\/(student\/forgot-password|student)/)
    })

    test('should handle malformed parameters', async () => {
      // Test with malformed WhatsApp number
      await resetPasswordPage.goto({
        method: 'whatsapp',
        whatsapp: 'invalid_phone',
        otp: '123456',
      })

      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)
      expect(errorState.type).toBe('invalid_params')
    })
  })

  test.describe('Password Strength Requirements', () => {
    test('should validate minimum password length', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const requirements = await resetPasswordPage.checkPasswordRequirements('123')
      expect(requirements.minLength).toBe(false)
      expect(requirements.overallValid).toBe(false)
    })

    test('should require uppercase letters', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const requirements = await resetPasswordPage.checkPasswordRequirements('password123!')
      expect(requirements.hasUppercase).toBe(false)
      expect(requirements.overallValid).toBe(false)
    })

    test('should require lowercase letters', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const requirements = await resetPasswordPage.checkPasswordRequirements('PASSWORD123!')
      expect(requirements.hasLowercase).toBe(false)
      expect(requirements.overallValid).toBe(false)
    })

    test('should require numbers', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const requirements = await resetPasswordPage.checkPasswordRequirements('Password!')
      expect(requirements.hasNumber).toBe(false)
      expect(requirements.overallValid).toBe(false)
    })

    test('should require special characters', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const requirements = await resetPasswordPage.checkPasswordRequirements('Password123')
      expect(requirements.hasSpecialChar).toBe(false)
      expect(requirements.overallValid).toBe(false)
    })

    test('should accept strong passwords meeting all criteria', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const strongPasswords = [
        'StrongPassword123!',
        'MySecure@Pass456',
        'Complex#Password789',
        'Valid$Password2024',
      ]

      for (const password of strongPasswords) {
        const requirements = await resetPasswordPage.checkPasswordRequirements(password)
        expect(requirements.minLength).toBe(true)
        expect(requirements.hasUppercase).toBe(true)
        expect(requirements.hasLowercase).toBe(true)
        expect(requirements.hasNumber).toBe(true)
        expect(requirements.hasSpecialChar).toBe(true)
        expect(requirements.overallValid).toBe(true)
      }
    })

    test('should show dynamic password strength indicator', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const testScenarios = await resetPasswordPage.testPasswordStrengthScenarios()

      for (const scenario of testScenarios) {
        // Verify strength calculation is reasonable
        if (scenario.expectedStrength === 'very_weak') {
          expect(scenario.actualScore).toBeLessThanOrEqual(20)
        } else if (scenario.expectedStrength === 'weak') {
          expect(scenario.actualScore).toBeGreaterThan(0)
          expect(scenario.actualScore).toBeLessThanOrEqual(40)
        } else if (scenario.expectedStrength === 'medium') {
          expect(scenario.actualScore).toBeGreaterThan(20)
          expect(scenario.actualScore).toBeLessThanOrEqual(60)
        } else if (scenario.expectedStrength === 'strong') {
          expect(scenario.actualScore).toBeGreaterThan(40)
          expect(scenario.actualScore).toBeLessThanOrEqual(80)
        } else if (scenario.expectedStrength === 'very_strong') {
          expect(scenario.actualScore).toBeGreaterThan(60)
        }
      }
    })
  })

  test.describe('Password Visibility Toggle', () => {
    test('should toggle new password visibility', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.newPasswordInput.fill('TestPassword123!')

      // Initially should be hidden
      expect(await resetPasswordPage.isNewPasswordVisible()).toBe(false)

      // Toggle to show
      await resetPasswordPage.toggleNewPasswordVisibility()
      expect(await resetPasswordPage.isNewPasswordVisible()).toBe(true)

      // Toggle back to hide
      await resetPasswordPage.toggleNewPasswordVisibility()
      expect(await resetPasswordPage.isNewPasswordVisible()).toBe(false)
    })

    test('should toggle confirm password visibility', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.confirmPasswordInput.fill('TestPassword123!')

      // Initially should be hidden
      expect(await resetPasswordPage.isConfirmPasswordVisible()).toBe(false)

      // Toggle to show
      await resetPasswordPage.toggleConfirmPasswordVisibility()
      expect(await resetPasswordPage.isConfirmPasswordVisible()).toBe(true)

      // Toggle back to hide
      await resetPasswordPage.toggleConfirmPasswordVisibility()
      expect(await resetPasswordPage.isConfirmPasswordVisible()).toBe(false)
    })

    test('should toggle independently for each field', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.newPasswordInput.fill('Password123!')
      await resetPasswordPage.confirmPasswordInput.fill('Password123!')

      // Show only new password
      await resetPasswordPage.toggleNewPasswordVisibility()
      expect(await resetPasswordPage.isNewPasswordVisible()).toBe(true)
      expect(await resetPasswordPage.isConfirmPasswordVisible()).toBe(false)

      // Show confirm password too
      await resetPasswordPage.toggleConfirmPasswordVisibility()
      expect(await resetPasswordPage.isNewPasswordVisible()).toBe(true)
      expect(await resetPasswordPage.isConfirmPasswordVisible()).toBe(true)

      // Hide new password
      await resetPasswordPage.toggleNewPasswordVisibility()
      expect(await resetPasswordPage.isNewPasswordVisible()).toBe(false)
      expect(await resetPasswordPage.isConfirmPasswordVisible()).toBe(true)
    })
  })

  test.describe('Form Validation', () => {
    test('should validate required fields', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const validation = await resetPasswordPage.verifyFormValidation()

      // Should require password fields
      expect(validation.passwordRequired || validation.confirmPasswordRequired).toBe(true)
    })

    test('should validate password confirmation match', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      // Test matching passwords
      const isMatch = await resetPasswordPage.verifyPasswordMatch(
        'StrongPassword123!',
        'StrongPassword123!'
      )
      expect(isMatch).toBe(true)

      // Test mismatching passwords
      const isMismatch = await resetPasswordPage.verifyPasswordMatch(
        'StrongPassword123!',
        'DifferentPassword123!'
      )
      expect(isMismatch).toBe(false)
    })

    test('should prevent weak passwords', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const validation = await resetPasswordPage.verifyFormValidation()
      expect(validation.passwordTooWeak).toBe(true)
    })

    test('should show appropriate validation messages', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      // Submit empty form
      await resetPasswordPage.resetPassword('', '')

      // Should show validation errors
      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)
    })
  })

  test.describe('Successful Password Reset', () => {
    test('should reset password successfully with valid token', async () => {
      // Mock successful password reset
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true, message: 'Password reset successful' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_reset_token',
      })

      const newPassword = 'NewStrongPassword123!'
      await resetPasswordPage.resetPassword(newPassword, newPassword)

      // Should show success message
      const successState = await resetPasswordPage.getSuccessState()
      expect(successState.hasSuccess).toBe(true)

      // Should redirect to login page
      await expect(resetPasswordPage.page).toHaveURL(/\/student/, { timeout: 10000 })

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should reset password successfully with WhatsApp OTP', async () => {
      // Mock successful password reset
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true, message: 'Password reset successful' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'whatsapp',
        whatsapp: '6281234567890',
        otp: '123456',
      })

      const newPassword = 'NewStrongPassword123!'
      await resetPasswordPage.resetPassword(newPassword, newPassword)

      const successState = await resetPasswordPage.getSuccessState()
      expect(successState.hasSuccess).toBe(true)

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should allow login with new password after reset', async () => {
      // Mock successful password reset
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true, message: 'Password reset successful' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const newPassword = 'NewStrongPassword123!'
      await resetPasswordPage.resetPassword(newPassword, newPassword)

      // Wait for redirect to login
      await expect(resetPasswordPage.page).toHaveURL(/\/student/, { timeout: 10000 })

      // Try to login with new password
      await loginPage.fillUsername('siswa001')
      await loginPage.fillPassword(newPassword)
      await loginPage.clickLogin()

      // Should redirect to home (mocked scenario)
      // In real test, this would verify actual authentication

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })
  })

  test.describe('Error Handling', () => {
    test('should handle expired tokens', async () => {
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Token has expired' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'expired_token',
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)
      expect(errorState.message).toContain('expired')

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should handle invalid tokens', async () => {
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid token' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'invalid_token',
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)
      expect(errorState.message).toContain('Invalid')

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should handle invalid OTP codes', async () => {
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid OTP code' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'whatsapp',
        whatsapp: '6281234567890',
        otp: '000000', // Invalid OTP
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)
      expect(errorState.type).toBe('invalid_otp')

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should handle rate limiting', async () => {
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 429,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Too many attempts. Please try again later.' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)
      expect(errorState.type).toBe('rate_limit')

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should handle network errors gracefully', async () => {
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.abort('failed')
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      // Should show error or handle gracefully
      await resetPasswordPage.page.waitForTimeout(3000)

      // Should remain on reset page or show error
      const currentUrl = resetPasswordPage.page.url()
      expect(currentUrl).toContain('reset-password')

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should handle server errors', async () => {
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })
  })

  test.describe('Navigation and UI', () => {
    test('should navigate back to previous page', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.goBack()

      // Should navigate away from reset password page
      const currentUrl = resetPasswordPage.page.url()
      expect(currentUrl).not.toContain('reset-password')
    })

    test('should toggle theme correctly', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const initialTheme = await resetPasswordPage.page.locator('html').getAttribute('class')

      await resetPasswordPage.toggleTheme()
      await resetPasswordPage.page.waitForTimeout(500)

      const newTheme = await resetPasswordPage.page.locator('html').getAttribute('class')
      expect(newTheme).not.toBe(initialTheme)
    })

    test('should show proper page title and header', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await expect(resetPasswordPage.pageTitle).toBeVisible()

      const titleText = await resetPasswordPage.pageTitle.textContent()
      expect(titleText?.toLowerCase()).toMatch(/(reset|ubah|password)/)
    })

    test('should display password requirements', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      if (await resetPasswordPage.passwordRequirements.isVisible()) {
        await expect(resetPasswordPage.passwordRequirements).toBeVisible()

        const requirementsText = await resetPasswordPage.passwordRequirements.textContent()
        expect(requirementsText).toBeTruthy()
      }
    })
  })

  test.describe('Accessibility and Responsive Design', () => {
    test('should meet accessibility standards', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.verifyAccessibility()
    })

    test('should be responsive on mobile devices', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.verifyResponsiveDesign()
    })

    test('should support keyboard navigation', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      // Test tab navigation
      await resetPasswordPage.newPasswordInput.press('Tab')
      await expect(resetPasswordPage.confirmPasswordInput).toBeFocused()

      await resetPasswordPage.confirmPasswordInput.press('Tab')
      await expect(resetPasswordPage.submitButton).toBeFocused()
    })

    test('should handle focus management properly', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      // Page should focus on first input field
      await resetPasswordPage.newPasswordInput.focus()
      await expect(resetPasswordPage.newPasswordInput).toBeFocused()
    })
  })

  test.describe('Security Features', () => {
    test('should include CSRF protection', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.verifySecurityFeatures()
    })

    test('should prevent XSS in form inputs', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const xssPayload = '<script>alert("xss")</script>'

      await resetPasswordPage.newPasswordInput.fill(xssPayload)
      await resetPasswordPage.confirmPasswordInput.fill(xssPayload)

      // Should not execute script
      const alertPromise = resetPasswordPage.page
        .waitForEvent('dialog', { timeout: 1000 })
        .catch(() => null)

      await resetPasswordPage.submitButton.click()

      const dialog = await alertPromise
      expect(dialog).toBeNull() // No alert should appear
    })

    test('should validate token/OTP on server side', async () => {
      // Mock server-side validation failure
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid or expired reset token' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'tampered_token', // Client could modify this
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      // Server should reject the tampered token
      const errorState = await resetPasswordPage.getErrorState()
      expect(errorState.hasError).toBe(true)

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should protect against timing attacks', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const startTime = Date.now()
      await resetPasswordPage.resetPassword('TestPassword123!', 'TestPassword123!')
      const endTime = Date.now()

      const processingTime = endTime - startTime

      // Processing should take reasonable time (not immediate which could indicate client-side only validation)
      expect(processingTime).toBeGreaterThan(100) // At least 100ms for server processing
    })

    test('should use secure form submission', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      // Check form method is POST
      const formMethod = await resetPasswordPage.resetPasswordForm.getAttribute('method')
      expect(formMethod?.toLowerCase()).toBe('post')

      // Check form action is secure
      const formAction = await resetPasswordPage.resetPasswordForm.getAttribute('action')
      if (formAction) {
        expect(formAction).toMatch(/^(https:\/\/|\/)/)
      }
    })
  })

  test.describe('Integration with Login Flow', () => {
    test('should redirect to login after successful reset', async () => {
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true, message: 'Password reset successful' }),
        })
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      // Should redirect to login with success message
      await expect(resetPasswordPage.page).toHaveURL(/\/student/, { timeout: 10000 })

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should handle reset link from forgot password flow', async () => {
      // Simulate coming from forgot password page
      await resetPasswordPage.page.goto('/student/forgot-password')

      // Click on a reset link (simulated)
      await resetPasswordPage.goto({
        method: 'whatsapp',
        whatsapp: '6281234567890',
        otp: '123456',
      })

      // Should load reset form properly
      await expect(resetPasswordPage.resetPasswordForm).toBeVisible()
    })

    test('should prevent access without valid parameters', async () => {
      // Try to access reset page directly without parameters
      await resetPasswordPage.goto()

      // Should redirect to forgot password or login
      await resetPasswordPage.page.waitForTimeout(2000)
      const currentUrl = resetPasswordPage.page.url()
      expect(currentUrl).toMatch(/\/(student\/forgot-password|student)/)
    })
  })

  test.describe('Performance and Loading', () => {
    test('should load within acceptable time', async () => {
      const startTime = Date.now()

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      const loadTime = Date.now() - startTime
      expect(loadTime).toBeLessThan(3000) // Should load within 3 seconds
    })

    test('should handle slow network conditions', async () => {
      // Simulate slow network
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ success: true }),
          })
        }, 3000) // 3 second delay
      })

      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.resetPassword('NewPassword123!', 'NewPassword123!')

      // Should show loading state and eventually complete
      await resetPasswordPage.waitForSubmissionComplete()

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })

    test('should provide visual feedback during submission', async () => {
      await resetPasswordPage.goto({
        method: 'token',
        token: 'valid_token',
      })

      await resetPasswordPage.newPasswordInput.fill('TestPassword123!')
      await resetPasswordPage.confirmPasswordInput.fill('TestPassword123!')

      // Mock slow response to see loading state
      await resetPasswordPage.page.route('**/api/student/reset-password', route => {
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ success: true }),
          })
        }, 1000)
      })

      await resetPasswordPage.submitButton.click()

      // Should show loading state
      await expect(resetPasswordPage.submitLoadingState).toBeVisible()

      await resetPasswordPage.page.unroute('**/api/student/reset-password')
    })
  })
})
