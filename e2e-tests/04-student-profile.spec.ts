import { test, expect } from '@playwright/test'
import { StudentProfilePage } from './page-objects/student-profile-page'
import { StudentLoginPage } from './page-objects/student/login-page'
import { testUsers } from './test-data/users'
import { loginAsStudent, takeAccessibilitySnapshot, mockWhatsAppOTP } from './utils/test-helpers'

test.describe('Student Profile Page', () => {
  let profilePage: StudentProfilePage
  let loginPage: StudentLoginPage

  test.beforeEach(async ({ page }) => {
    profilePage = new StudentProfilePage(page)
    loginPage = new StudentLoginPage(page)

    // Login as a test student before each test
    const student = testUsers.student1
    await loginPage.login(student)

    // Navigate to profile page
    await profilePage.goto()
  })

  test.describe('Profile Information Display', () => {
    test('should display correct user information', async () => {
      const student = testUsers.student1

      await expect(profilePage.nameField).toHaveValue(student.name || '')
      await expect(profilePage.nisField).toHaveValue(student.nis || '')
      await expect(profilePage.classField).toHaveValue(student.class || '')
      await expect(profilePage.whatsappField).toHaveValue(student.whatsapp || '')
      await expect(profilePage.emailField).toHaveValue(student.email || '')
    })

    test('should display profile picture or avatar', async () => {
      await expect(profilePage.profilePicture).toBeVisible()
    })

    test('should show proper page title and breadcrumbs', async () => {
      await expect(profilePage.pageTitle).toContainText('Profil')
      await expect(profilePage.breadcrumbs).toBeVisible()
    })
  })

  test.describe('Profile Information Editing', () => {
    test('should allow editing personal information', async () => {
      const newName = 'Ahmad Fauzi Updated'
      const newClass = 'XII IPA 1'

      await profilePage.updatePersonalInfo(newName, newClass)

      await expect(profilePage.nameField).toHaveValue(newName)
      await expect(profilePage.classField).toHaveValue(newClass)
    })

    test('should validate required fields', async () => {
      await profilePage.clearField(profilePage.nameField)
      await profilePage.saveProfile()

      await expect(profilePage.errorMessage).toContainText('Nama wajib diisi')
    })

    test('should prevent editing of readonly fields', async () => {
      await expect(profilePage.nisField).toBeDisabled()
    })
  })

  test.describe('WhatsApp Integration', () => {
    test('should show WhatsApp verification status', async () => {
      await expect(profilePage.whatsappStatus).toBeVisible()
    })

    test('should allow updating WhatsApp number', async () => {
      const newWhatsApp = '+6281234567999'

      await profilePage.updateWhatsAppNumber(newWhatsApp)
      await expect(profilePage.whatsappField).toHaveValue(newWhatsApp)
    })

    test('should handle WhatsApp OTP verification process', async () => {
      const newWhatsApp = '+6281234567888'

      await profilePage.updateWhatsAppNumber(newWhatsApp)
      await profilePage.verifyWhatsApp()

      await expect(profilePage.otpModal).toBeVisible()
      await expect(profilePage.otpInstructions).toContainText('Kode OTP telah dikirim')
    })

    test('should validate WhatsApp number format', async () => {
      await profilePage.whatsappField.fill('invalid-number')
      await profilePage.saveProfile()

      await expect(profilePage.errorMessage).toContainText('Format nomor WhatsApp tidak valid')
    })
  })

  test.describe('Password Management', () => {
    test('should allow changing password', async () => {
      const currentPassword = 'password123'
      const newPassword = 'newPassword123!'

      await profilePage.changePassword(currentPassword, newPassword, newPassword)
      await expect(profilePage.successMessage).toContainText('Password berhasil diubah')
    })

    test('should validate current password', async () => {
      await profilePage.changePassword('wrongpassword', 'newPassword123!', 'newPassword123!')
      await expect(profilePage.errorMessage).toContainText('Password lama tidak sesuai')
    })

    test('should enforce password strength requirements', async () => {
      await profilePage.changePassword('password123', 'weak', 'weak')
      await expect(profilePage.errorMessage).toContainText(
        'Password harus memiliki minimal 8 karakter'
      )
    })

    test('should validate password confirmation', async () => {
      await profilePage.changePassword('password123', 'newPassword123!', 'differentPassword!')
      await expect(profilePage.errorMessage).toContainText('Konfirmasi password tidak sesuai')
    })
  })

  test.describe('Form Validation', () => {
    test('should validate email format', async () => {
      await profilePage.emailField.fill('invalid-email')
      await profilePage.saveProfile()

      await expect(profilePage.errorMessage).toContainText('Format email tidak valid')
    })

    test('should handle network errors gracefully', async () => {
      // Mock network failure
      await profilePage.page.route('**/api/profile/**', route => route.abort())

      await profilePage.updatePersonalInfo('Test Name', 'Test Class')
      await expect(profilePage.errorMessage).toContainText('Terjadi kesalahan')
    })

    test('should reset form when cancel is clicked', async () => {
      const originalName = await profilePage.nameField.inputValue()

      await profilePage.nameField.fill('Changed Name')
      await profilePage.cancelButton.click()

      await expect(profilePage.nameField).toHaveValue(originalName)
    })
  })

  test.describe('UI/UX Features', () => {
    test('should show loading states during operations', async () => {
      await profilePage.saveButton.click()
      await expect(profilePage.loadingSpinner).toBeVisible()
    })

    test('should display success messages', async () => {
      await profilePage.updatePersonalInfo('Test Name', 'Test Class')
      await expect(profilePage.successMessage).toContainText('Profil berhasil diperbarui')
    })

    test('should handle responsive design', async () => {
      // Test mobile view
      await profilePage.page.setViewportSize({ width: 375, height: 667 })
      await expect(profilePage.mobileMenu).toBeVisible()

      // Test desktop view
      await profilePage.page.setViewportSize({ width: 1200, height: 800 })
      await expect(profilePage.desktopLayout).toBeVisible()
    })
  })

  test.describe('Security Features', () => {
    test('should log out after extended inactivity', async () => {
      // This would need to be implemented based on your session management
      await profilePage.page.waitForTimeout(30000) // Simulate inactivity
      // Check if auto-logout occurs
    })

    test('should prevent XSS in input fields', async () => {
      const maliciousScript = '<script>alert("xss")</script>'

      await profilePage.nameField.fill(maliciousScript)
      await profilePage.saveProfile()

      // Should be escaped and not executed
      await expect(profilePage.nameField).toHaveValue(maliciousScript)
    })
  })

  test.describe('Accessibility', () => {
    test('should have proper ARIA labels', async () => {
      await expect(profilePage.nameField).toHaveAttribute('aria-label')
      await expect(profilePage.saveButton).toHaveAttribute('aria-label')
    })

    test('should support keyboard navigation', async () => {
      await profilePage.nameField.press('Tab')
      await expect(profilePage.emailField).toBeFocused()

      await profilePage.emailField.press('Tab')
      await expect(profilePage.whatsappField).toBeFocused()
    })

    test('should have sufficient color contrast', async () => {
      // This would require accessibility testing tools
      // For now, we'll just verify elements are visible
      await expect(profilePage.saveButton).toBeVisible()
      await expect(profilePage.pageTitle).toBeVisible()
    })
  })
})
