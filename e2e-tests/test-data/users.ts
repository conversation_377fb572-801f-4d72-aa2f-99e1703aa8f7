export interface TestUser {
  username: string
  password: string
  role: 'student' | 'admin' | 'super_admin' | 'guru' | 'resepsionis'
  name?: string
  whatsapp?: string
  email?: string
  nis?: string
  class?: string
}

export const testUsers: Record<string, TestUser> = {
  // Students
  student1: {
    username: 'siswa001',
    password: 'password123',
    role: 'student',
    name: '<PERSON>',
    whatsapp: '+6281234567890',
    email: '<EMAIL>',
    nis: '2023001',
    class: 'XI IPA 1',
  },

  student2: {
    username: 'siswa002',
    password: 'password123',
    role: 'student',
    name: '<PERSON><PERSON>',
    whatsapp: '+6281234567891',
    email: '<EMAIL>',
    nis: '2023002',
    class: 'XI IPA 2',
  },

  // Admin roles
  superAdmin: {
    username: 'superadmin',
    password: 'SuperAdmin123!',
    role: 'super_admin',
    name: 'Super Administrator',
  },

  admin: {
    username: 'admin001',
    password: 'Admin123!',
    role: 'admin',
    name: '<PERSON><PERSON>',
  },

  guru: {
    username: 'guru001',
    password: '<PERSON>123!',
    role: 'guru',
    name: 'Pak Budi Santoso',
  },

  resepsionis: {
    username: 'resepsionis001',
    password: 'Resepsionis123!',
    role: 'resepsionis',
    name: 'Ibu Ani Lestari',
  },
}

export const getTestUser = (role: TestUser['role']): TestUser => {
  const user = Object.values(testUsers).find(u => u.role === role)
  if (!user) {
    throw new Error(`No test user found for role: ${role}`)
  }
  return user
}

export const getRandomStudent = (): TestUser => {
  const students = Object.values(testUsers).filter(u => u.role === 'student')
  return students[Math.floor(Math.random() * students.length)]
}
