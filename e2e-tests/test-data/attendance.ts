export interface AttendanceType {
  id: string
  name: string
  category: 'prayer' | 'school' | 'permission'
  allowedRoles: string[]
}

export const attendanceTypes: Record<string, AttendanceType> = {
  // Prayer attendance (4 implemented types)
  zuhr: {
    id: 'zuhr',
    name: '<PERSON><PERSON><PERSON>',
    category: 'prayer',
    allowedRoles: ['admin', 'super_admin'],
  },
  asr: {
    id: 'asr',
    name: '<PERSON>hala<PERSON>',
    category: 'prayer',
    allowedRoles: ['admin', 'super_admin'],
  },
  pulang: {
    id: 'pulang',
    name: 'Pulang',
    category: 'school',
    allowedRoles: ['admin', 'super_admin'],
  },
  ijin: {
    id: 'ijin',
    name: '<PERSON>jin Tidak Shalat',
    category: 'permission',
    allowedRoles: ['admin', 'super_admin'],
  },

  // School attendance (for Guru role)
  masuk: {
    id: 'masuk',
    name: 'Masuk',
    category: 'school',
    allowedRoles: ['guru'],
  },

  // Reception attendance (for Resepsionis role)
  masuk<PERSON><PERSON><PERSON>bat: {
    id: 'masuk_terlambat',
    name: '<PERSON><PERSON><PERSON>',
    category: 'school',
    allowedRoles: ['resepsionis'],
  },
  izin: {
    id: 'izin',
    name: 'Izin',
    category: 'permission',
    allowedRoles: ['resepsionis'],
  },
  izinSementara: {
    id: 'izin_sementara',
    name: 'Izin Sementara',
    category: 'permission',
    allowedRoles: ['resepsionis'],
  },
  kembaliDariIzin: {
    id: 'kembali_dari_izin',
    name: 'Kembali dari Izin',
    category: 'permission',
    allowedRoles: ['resepsionis'],
  },
  sakit: {
    id: 'sakit',
    name: 'Sakit',
    category: 'permission',
    allowedRoles: ['resepsionis'],
  },
}

export const getAttendanceTypesByRole = (role: string): AttendanceType[] => {
  return Object.values(attendanceTypes).filter(type => type.allowedRoles.includes(role))
}

export interface AttendanceScenario {
  name: string
  description: string
  steps: string[]
  expectedResult: string
  prerequisites?: string[]
}

export const attendanceScenarios: Record<string, AttendanceScenario> = {
  normalPrayerFlow: {
    name: 'Normal Prayer Attendance Flow',
    description: 'Student scans QR for Zuhr, then Asr, then Pulang',
    steps: [
      'Admin scans student QR for Zuhr prayer',
      'Admin scans same student QR for Asr prayer',
      'Admin scans same student QR for Pulang',
    ],
    expectedResult: 'All three attendance records created successfully',
  },

  duplicateAttendance: {
    name: 'Duplicate Attendance Prevention',
    description: 'Prevent duplicate attendance for same type on same day',
    steps: [
      'Admin scans student QR for Zuhr prayer',
      'Admin tries to scan same student QR for Zuhr prayer again',
    ],
    expectedResult: 'Second scan should show duplicate attendance warning',
  },

  invalidPrayerSequence: {
    name: 'Invalid Prayer Sequence Validation',
    description: 'Student tries to go home without completing prayers',
    steps: [
      'Admin scans student QR for Zuhr prayer only',
      'Admin tries to scan same student QR for Pulang',
    ],
    expectedResult: 'System should warn about missing Asr prayer',
    prerequisites: ['Zuhr attendance recorded'],
  },

  crossRoleAttendance: {
    name: 'Cross-Role Attendance Validation',
    description: 'Different roles can only access their designated attendance types',
    steps: [
      'Guru tries to scan for prayer attendance',
      'Admin tries to scan for school entry',
      'Resepsionis tries to scan for prayer attendance',
    ],
    expectedResult: 'Each role should only see their allowed attendance types',
  },
}
