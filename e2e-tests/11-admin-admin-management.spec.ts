import { test, expect } from '@playwright/test'
import { AdminAdminsPage, AdminFormData } from './page-objects/admin/admin-admins-page'
import { AdminLoginPage } from './page-objects/admin/admin-login-page'
import { testUsers } from './test-data/users'

test.describe('Admin Management', () => {
  let adminAdminsPage: AdminAdminsPage
  let adminLoginPage: AdminLoginPage

  test.beforeEach(async ({ page }) => {
    adminAdminsPage = new AdminAdminsPage(page)
    adminLoginPage = new AdminLoginPage(page)

    // Login as Super Admin for full access
    await adminLoginPage.goto()
    await adminLoginPage.login(testUsers.superAdmin)
  })

  test.describe('Page Access and Authentication', () => {
    test('should allow Super Admin access to admin management', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.verifyPageAccess(true)
      await adminAdminsPage.verifySuperAdminFeatures()
    })

    test('should deny access for non-Super Admin roles', async ({ page }) => {
      // Test with regular Admin
      await adminLoginPage.goto()
      await adminLoginPage.login(testUsers.admin)

      await page.goto('/admin/admins')
      await adminAdminsPage.verifyPageAccess(false)
    })

    test('should redirect unauthorized users', async ({ page }) => {
      // Test with Teacher role
      await adminLoginPage.goto()
      await adminLoginPage.login(testUsers.guru)

      await page.goto('/admin/admins')
      await expect(page).toHaveURL(/\/admin\/home/<USER>
    })

    test('should deny access to students', async ({ page }) => {
      // Test with Student role
      await adminLoginPage.goto()
      await adminLoginPage.login(testUsers.student1)

      await page.goto('/admin/admins')
      await expect(page).toHaveURL(/\/student/)
    })
  })

  test.describe('Admin CRUD Operations', () => {
    const testAdmin: AdminFormData = {
      name: 'Test Admin E2E',
      username: 'test_admin_e2e',
      password: 'TestAdminPassword123!',
      role: 'admin',
    }

    test('should successfully add a new admin', async () => {
      await adminAdminsPage.goto()

      // Add new admin
      await adminAdminsPage.addAdmin(testAdmin)
      await adminAdminsPage.waitForSuccessToast()

      // Verify admin appears in table
      const adminExists = await adminAdminsPage.verifyAdminInTable({
        name: testAdmin.name,
        username: testAdmin.username,
      })
      expect(adminExists).toBe(true)
    })

    test('should successfully add a teacher', async () => {
      const testTeacher: AdminFormData = {
        name: 'Test Teacher E2E',
        username: 'test_teacher_e2e',
        password: 'TestTeacherPassword123!',
        role: 'teacher',
      }

      await adminAdminsPage.goto()

      await adminAdminsPage.addAdmin(testTeacher)
      await adminAdminsPage.waitForSuccessToast()

      const teacherExists = await adminAdminsPage.verifyAdminInTable({
        name: testTeacher.name,
        username: testTeacher.username,
      })
      expect(teacherExists).toBe(true)
    })

    test('should successfully add a receptionist', async () => {
      const testReceptionist: AdminFormData = {
        name: 'Test Receptionist E2E',
        username: 'test_receptionist_e2e',
        password: 'TestReceptionistPassword123!',
        role: 'receptionist',
      }

      await adminAdminsPage.goto()

      await adminAdminsPage.addAdmin(testReceptionist)
      await adminAdminsPage.waitForSuccessToast()

      const receptionistExists = await adminAdminsPage.verifyAdminInTable({
        name: testReceptionist.name,
        username: testReceptionist.username,
      })
      expect(receptionistExists).toBe(true)
    })

    test('should validate required fields when adding admin', async () => {
      await adminAdminsPage.goto()

      // Try to add admin with empty fields
      await adminAdminsPage.addAdminButton.click()
      await adminAdminsPage.submitButton.click()

      // Should show validation errors
      const errors = await adminAdminsPage.getValidationErrors()
      expect(errors.length).toBeGreaterThan(0)
    })

    test('should successfully edit existing admin', async () => {
      await adminAdminsPage.goto()

      // First add an admin to edit
      await adminAdminsPage.addAdmin(testAdmin)
      await adminAdminsPage.waitForSuccessToast()

      // Find and edit the admin
      const adminRow = await adminAdminsPage.findAdminRowByName(testAdmin.name)
      expect(adminRow).toBeGreaterThanOrEqual(0)

      const updatedData = {
        name: 'Updated Test Admin',
        role: 'teacher' as const,
      }

      await adminAdminsPage.editAdmin(adminRow, updatedData)
      await adminAdminsPage.waitForSuccessToast()

      // Verify update
      const updatedAdminExists = await adminAdminsPage.verifyAdminInTable({
        name: updatedData.name,
        username: testAdmin.username,
      })
      expect(updatedAdminExists).toBe(true)
    })

    test('should successfully delete admin', async () => {
      await adminAdminsPage.goto()

      // First add an admin to delete
      const adminToDelete = {
        ...testAdmin,
        name: 'Admin To Delete',
        username: 'admin_to_delete',
      }

      await adminAdminsPage.addAdmin(adminToDelete)
      await adminAdminsPage.waitForSuccessToast()

      // Find and delete the admin
      const adminRow = await adminAdminsPage.findAdminRowByName(adminToDelete.name)
      expect(adminRow).toBeGreaterThanOrEqual(0)

      await adminAdminsPage.deleteAdmin(adminRow)
      await adminAdminsPage.waitForSuccessToast()

      // Verify deletion
      const adminExists = await adminAdminsPage.verifyAdminInTable({
        name: adminToDelete.name,
        username: adminToDelete.username,
      })
      expect(adminExists).toBe(false)
    })

    test('should prevent duplicate usernames', async () => {
      await adminAdminsPage.goto()

      // Add first admin
      await adminAdminsPage.addAdmin(testAdmin)
      await adminAdminsPage.waitForSuccessToast()

      // Try to add another admin with same username
      const duplicateAdmin = {
        ...testAdmin,
        name: 'Different Admin Name',
      }

      await adminAdminsPage.addAdmin(duplicateAdmin)
      await adminAdminsPage.waitForErrorToast()
    })
  })

  test.describe('Role Management and Validation', () => {
    test('should display correct role icons', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.verifyRoleIcons()
    })

    test('should validate role permissions', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.testRolePermissions()
    })

    test('should validate form constraints', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.validateFormConstraints()
    })

    test('should test unique username validation', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.testUniqueUsername()
    })

    test('should allow canceling form operations', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.cancelFormOperation()
    })
  })

  test.describe('Search and Filter Functionality', () => {
    test('should search admins by name', async () => {
      await adminAdminsPage.goto()

      // Search for existing admin
      await adminAdminsPage.searchAdmins('Super')

      const rowCount = await adminAdminsPage.getTableRowCount()
      expect(rowCount).toBeGreaterThan(0)

      // Clear search
      await adminAdminsPage.clearSearch()
    })

    test('should search admins by username', async () => {
      await adminAdminsPage.goto()

      await adminAdminsPage.searchAdmins('superadmin')

      const rowCount = await adminAdminsPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(0)
    })

    test('should filter admins by role', async () => {
      await adminAdminsPage.goto()

      // Filter by Super Admin role
      await adminAdminsPage.filterByRole('super_admin')

      const rowCount = await adminAdminsPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(1) // At least one super admin should exist

      // Reset filter
      await adminAdminsPage.filterByRole('all')
    })

    test('should filter by admin role', async () => {
      await adminAdminsPage.goto()

      await adminAdminsPage.filterByRole('admin')

      const rowCount = await adminAdminsPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(0)
    })

    test('should filter by teacher role', async () => {
      await adminAdminsPage.goto()

      await adminAdminsPage.filterByRole('teacher')

      const rowCount = await adminAdminsPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(0)
    })

    test('should filter by receptionist role', async () => {
      await adminAdminsPage.goto()

      await adminAdminsPage.filterByRole('receptionist')

      const rowCount = await adminAdminsPage.getTableRowCount()
      expect(rowCount).toBeGreaterThanOrEqual(0)
    })

    test('should handle empty search results', async () => {
      await adminAdminsPage.goto()

      await adminAdminsPage.searchAdmins('nonexistentadmin12345')

      await expect(adminAdminsPage.emptyState).toBeVisible()
    })
  })

  test.describe('Security and Protection Features', () => {
    test('should prevent self-deletion', async () => {
      await adminAdminsPage.goto()

      // Test that current super admin cannot delete themselves
      await adminAdminsPage.preventSelfDeletion('superadmin')
    })

    test('should protect last super admin from deletion', async () => {
      await adminAdminsPage.goto()

      // Verify protection of the last super admin
      await adminAdminsPage.verifyLastSuperAdminProtection()
    })

    test('should prevent XSS attacks in admin forms', async () => {
      await adminAdminsPage.goto()

      const xssPayload = '<script>alert("xss")</script>'
      await adminAdminsPage.testXSSInAdminForm(xssPayload)
    })

    test('should prevent SQL injection in search', async () => {
      await adminAdminsPage.goto()

      const sqlPayload = "'; DROP TABLE admins; --"
      await adminAdminsPage.testSQLInjectionInSearch(sqlPayload)
    })

    test('should validate password strength requirements', async () => {
      await adminAdminsPage.goto()

      const weakPasswordAdmin: AdminFormData = {
        name: 'Test Admin',
        username: 'test_weak_admin',
        password: '123', // Too weak
        role: 'admin',
      }

      await adminAdminsPage.addAdmin(weakPasswordAdmin)

      // Should show password validation error
      const errors = await adminAdminsPage.getValidationErrors()
      expect(errors.some(error => error.toLowerCase().includes('password'))).toBe(true)
    })

    test('should validate username format and constraints', async () => {
      await adminAdminsPage.goto()

      const invalidUsernameAdmin: AdminFormData = {
        name: 'Test Admin',
        username: 'invalid username!@#', // Invalid characters
        password: 'ValidPassword123!',
        role: 'admin',
      }

      await adminAdminsPage.addAdmin(invalidUsernameAdmin)
      // Should validate username format server-side
    })
  })

  test.describe('Performance Testing', () => {
    test('should load page within acceptable time', async () => {
      const loadTime = await adminAdminsPage.measurePageLoadTime()
      expect(loadTime).toBeLessThan(5000) // 5 seconds
    })

    test('should search admins efficiently', async () => {
      await adminAdminsPage.goto()

      const searchTime = await adminAdminsPage.measureSearchTime('Super')
      expect(searchTime).toBeLessThan(2000) // 2 seconds
    })

    test('should handle admin table efficiently', async () => {
      await adminAdminsPage.goto()

      const startTime = Date.now()
      await adminAdminsPage.waitForTableLoad()
      const loadTime = Date.now() - startTime

      expect(loadTime).toBeLessThan(2000) // 2 seconds for admin table
    })
  })

  test.describe('Accessibility Testing', () => {
    test('should meet accessibility standards', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.verifyAccessibility()
    })

    test('should support keyboard navigation', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.testKeyboardNavigation()
    })

    test('should have proper ARIA labels', async () => {
      await adminAdminsPage.goto()

      // Check for ARIA labels on interactive elements
      await expect(adminAdminsPage.searchInput).toHaveAttribute('aria-label')
      await expect(adminAdminsPage.adminsTable).toHaveAttribute('role', 'table')
    })

    test('should support screen readers', async () => {
      await adminAdminsPage.goto()

      // Check for proper heading structure
      await expect(adminAdminsPage.pageTitle).toBeVisible()

      // Check for proper table headers
      const headers = await adminAdminsPage.tableHeaders.count()
      expect(headers).toBeGreaterThan(0)
    })

    test('should have proper focus management in dialogs', async () => {
      await adminAdminsPage.goto()

      // Open add admin dialog
      await adminAdminsPage.addAdminButton.click()
      await expect(adminAdminsPage.addEditDialog).toBeVisible()

      // First input should be focused
      await expect(adminAdminsPage.nameInput).toBeFocused()
    })
  })

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async () => {
      await adminAdminsPage.goto()
      await adminAdminsPage.verifyResponsiveDesign()
    })

    test('should maintain functionality on tablet', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await adminAdminsPage.goto()

      await expect(adminAdminsPage.pageTitle).toBeVisible()
      await expect(adminAdminsPage.addAdminButton).toBeVisible()
      await expect(adminAdminsPage.adminsTable).toBeVisible()
    })

    test('should adapt table layout for mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await adminAdminsPage.goto()

      // Table should still be accessible, possibly with horizontal scroll
      await expect(adminAdminsPage.adminsTable).toBeVisible()
    })

    test('should maintain dialog usability on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await adminAdminsPage.goto()

      await adminAdminsPage.addAdminButton.click()
      await expect(adminAdminsPage.addEditDialog).toBeVisible()

      // Form elements should be visible and usable
      await expect(adminAdminsPage.nameInput).toBeVisible()
      await expect(adminAdminsPage.usernameInput).toBeVisible()
      await expect(adminAdminsPage.passwordInput).toBeVisible()
      await expect(adminAdminsPage.roleSelect).toBeVisible()
    })
  })

  test.describe('Data Validation and Edge Cases', () => {
    test('should handle special characters in names', async () => {
      await adminAdminsPage.goto()

      const specialCharAdmin: AdminFormData = {
        name: "O'Connor-Admin",
        username: 'special_char_admin',
        password: 'ValidPassword123!',
        role: 'admin',
      }

      await adminAdminsPage.addAdmin(specialCharAdmin)
      await adminAdminsPage.waitForSuccessToast()

      const adminExists = await adminAdminsPage.verifyAdminInTable({
        name: specialCharAdmin.name,
        username: specialCharAdmin.username,
      })
      expect(adminExists).toBe(true)
    })

    test('should handle maximum field lengths', async () => {
      await adminAdminsPage.goto()

      const maxLengthAdmin: AdminFormData = {
        name: 'A'.repeat(100), // Very long name
        username: 'very_long_admin_username',
        password: 'ValidPassword123!',
        role: 'admin',
      }

      await adminAdminsPage.addAdmin(maxLengthAdmin)
      // Should handle or validate maximum lengths
    })

    test('should preserve search state during operations', async () => {
      await adminAdminsPage.goto()

      // Apply search filter
      await adminAdminsPage.searchAdmins('Super')

      // Perform an operation (like opening and closing dialog)
      await adminAdminsPage.addAdminButton.click()
      await adminAdminsPage.cancelButton.click()

      // Search should still be active
      const searchValue = await adminAdminsPage.searchInput.inputValue()
      expect(searchValue).toBe('Super')
    })

    test('should handle international characters', async () => {
      await adminAdminsPage.goto()

      const internationalAdmin: AdminFormData = {
        name: 'José García',
        username: 'jose_garcia_admin',
        password: 'ValidPassword123!',
        role: 'admin',
      }

      await adminAdminsPage.addAdmin(internationalAdmin)
      await adminAdminsPage.waitForSuccessToast()

      const adminExists = await adminAdminsPage.verifyAdminInTable({
        name: internationalAdmin.name,
        username: internationalAdmin.username,
      })
      expect(adminExists).toBe(true)
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      await adminAdminsPage.goto()

      // Simulate network failure
      await page.route('**/api/admins', route => route.abort())

      await adminAdminsPage.addAdminButton.click()
      await adminAdminsPage.submitButton.click()

      // Should show appropriate error message
      await expect(adminAdminsPage.errorMessage).toBeVisible()
    })

    test('should handle server errors gracefully', async ({ page }) => {
      await adminAdminsPage.goto()

      // Simulate server error
      await page.route('**/api/admins', route =>
        route.fulfill({ status: 500, body: 'Internal Server Error' })
      )

      await adminAdminsPage.addAdminButton.click()
      await adminAdminsPage.submitButton.click()

      // Should show appropriate error message
      await expect(adminAdminsPage.errorMessage).toBeVisible()
    })

    test('should handle invalid API responses', async ({ page }) => {
      await adminAdminsPage.goto()

      // Simulate invalid JSON response
      await page.route('**/api/admins', route =>
        route.fulfill({ status: 200, body: 'invalid json response' })
      )

      await adminAdminsPage.addAdminButton.click()
      await adminAdminsPage.submitButton.click()

      // Should handle parsing error gracefully
      await expect(adminAdminsPage.errorMessage).toBeVisible()
    })

    test('should handle timeout errors', async ({ page }) => {
      await adminAdminsPage.goto()

      // Simulate slow response
      await page.route('**/api/admins', async route => {
        await new Promise(resolve => setTimeout(resolve, 10000)) // 10 second delay
        route.continue()
      })

      await adminAdminsPage.addAdminButton.click()
      await adminAdminsPage.submitButton.click()

      // Should handle timeout appropriately
      // Note: This test might need adjustment based on actual timeout handling
    })
  })

  test.describe('Integration Testing', () => {
    test('should integrate with authentication system', async ({ page }) => {
      // Test that admin creation integrates properly with auth
      await adminAdminsPage.goto()

      const newAdmin: AdminFormData = {
        name: 'Integration Test Admin',
        username: 'integration_admin',
        password: 'IntegrationTest123!',
        role: 'admin',
      }

      await adminAdminsPage.addAdmin(newAdmin)
      await adminAdminsPage.waitForSuccessToast()

      // Verify admin was created and can be found
      const adminExists = await adminAdminsPage.verifyAdminInTable({
        name: newAdmin.name,
        username: newAdmin.username,
      })
      expect(adminExists).toBe(true)
    })

    test('should maintain data consistency across operations', async () => {
      await adminAdminsPage.goto()

      const testAdmin: AdminFormData = {
        name: 'Consistency Test Admin',
        username: 'consistency_admin',
        password: 'ConsistencyTest123!',
        role: 'teacher',
      }

      // Create admin
      await adminAdminsPage.addAdmin(testAdmin)
      await adminAdminsPage.waitForSuccessToast()

      // Verify creation
      let adminExists = await adminAdminsPage.verifyAdminInTable({
        name: testAdmin.name,
        username: testAdmin.username,
      })
      expect(adminExists).toBe(true)

      // Edit admin
      const adminRow = await adminAdminsPage.findAdminRowByName(testAdmin.name)
      await adminAdminsPage.editAdmin(adminRow, { name: 'Updated Consistency Admin' })
      await adminAdminsPage.waitForSuccessToast()

      // Verify edit
      adminExists = await adminAdminsPage.verifyAdminInTable({
        name: 'Updated Consistency Admin',
        username: testAdmin.username,
      })
      expect(adminExists).toBe(true)

      // Delete admin
      const updatedAdminRow = await adminAdminsPage.findAdminRowByName('Updated Consistency Admin')
      await adminAdminsPage.deleteAdmin(updatedAdminRow)
      await adminAdminsPage.waitForSuccessToast()

      // Verify deletion
      adminExists = await adminAdminsPage.verifyAdminInTable({
        name: 'Updated Consistency Admin',
        username: testAdmin.username,
      })
      expect(adminExists).toBe(false)
    })
  })

  test.describe('Advanced Role Testing', () => {
    test('should validate role hierarchy', async () => {
      await adminAdminsPage.goto()

      // Super Admin should be able to create all role types
      const roleTests = [
        { role: 'admin' as const, name: 'Test Admin Role' },
        { role: 'teacher' as const, name: 'Test Teacher Role' },
        { role: 'receptionist' as const, name: 'Test Receptionist Role' },
      ]

      for (const roleTest of roleTests) {
        const admin: AdminFormData = {
          name: roleTest.name,
          username: `test_${roleTest.role}_hierarchy`,
          password: 'RoleTest123!',
          role: roleTest.role,
        }

        await adminAdminsPage.addAdmin(admin)
        await adminAdminsPage.waitForSuccessToast()

        const adminExists = await adminAdminsPage.verifyAdminInTable({
          name: admin.name,
          username: admin.username,
        })
        expect(adminExists).toBe(true)
      }
    })

    test('should display correct role badges and colors', async () => {
      await adminAdminsPage.goto()

      // Check that different roles have appropriate visual indicators
      const rowCount = await adminAdminsPage.getTableRowCount()

      if (rowCount > 0) {
        for (let i = 0; i < Math.min(rowCount, 10); i++) {
          const adminData = await adminAdminsPage.getAdminData(i)
          const row = adminAdminsPage.tableRows.nth(i)

          // Each role should have its distinctive color/icon
          if (adminData.role.includes('Super Admin')) {
            await expect(row.locator('.text-red-500, .text-red-600')).toBeVisible()
          } else if (adminData.role.includes('Admin')) {
            await expect(row.locator('.text-blue-500, .text-blue-600')).toBeVisible()
          } else if (adminData.role.includes('Teacher')) {
            await expect(row.locator('.text-green-500, .text-green-600')).toBeVisible()
          } else if (adminData.role.includes('Receptionist')) {
            await expect(row.locator('.text-purple-500, .text-purple-600')).toBeVisible()
          }
        }
      }
    })
  })

  // Clean up test data
  test.afterEach(async () => {
    // Clean up any test admins created during tests
    const testAdminPatterns = [
      'Test Admin E2E',
      'Test Teacher E2E',
      'Test Receptionist E2E',
      'Updated Test Admin',
      'Admin To Delete',
      'Integration Test Admin',
      'Consistency Test Admin',
      'Updated Consistency Admin',
      'Test Admin Role',
      'Test Teacher Role',
      'Test Receptionist Role',
    ]

    for (const pattern of testAdminPatterns) {
      try {
        const adminRow = await adminAdminsPage.findAdminRowByName(pattern)
        if (adminRow >= 0) {
          await adminAdminsPage.deleteAdmin(adminRow)
          await adminAdminsPage.waitForSuccessToast()
        }
      } catch (error) {
        // Ignore cleanup errors
        console.log(`Cleanup error for ${pattern}: ${error}`)
      }
    }

    // Also clean up by username patterns
    const testUsernamePatterns = [
      'test_admin_e2e',
      'test_teacher_e2e',
      'test_receptionist_e2e',
      'admin_to_delete',
      'integration_admin',
      'consistency_admin',
    ]

    for (const pattern of testUsernamePatterns) {
      try {
        const adminRow = await adminAdminsPage.findAdminRowByUsername(pattern)
        if (adminRow >= 0) {
          await adminAdminsPage.deleteAdmin(adminRow)
          await adminAdminsPage.waitForSuccessToast()
        }
      } catch (error) {
        // Ignore cleanup errors
        console.log(`Cleanup error for username ${pattern}: ${error}`)
      }
    }
  })
})
