import { test, expect } from '@playwright/test'
import { AdminSchoolReportsPage } from './page-objects/admin/admin-school-reports-page'
import { AdminLoginPage } from './page-objects/admin/admin-login-page'
import { takeAccessibilitySnapshot } from './utils/test-helpers'

test.describe('Admin School Reports', () => {
  let schoolReportsPage: AdminSchoolReportsPage
  let loginPage: AdminLoginPage

  test.beforeEach(async ({ page }) => {
    schoolReportsPage = new AdminSchoolReportsPage(page)
    loginPage = new AdminLoginPage(page)
  })

  test.describe('Role-Based Access Control', () => {
    test('Super Admin should have full access to school reports', async ({ page }) => {
      await loginPage.login('super_admin', 'superAdminPassword123!')
      await schoolReportsPage.navigateTo()

      await schoolReportsPage.verifyAdminAccess()
      await expect(schoolReportsPage.exportButton).toBeVisible()
      await expect(schoolReportsPage.bulkExportButton).toBeVisible()
      await expect(schoolReportsPage.exportExcelButton).toBeVisible()
    })

    test('Admin should have full access to school reports', async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()

      await schoolReportsPage.verifyAdminAccess()
      await expect(schoolReportsPage.pageTitle).toBeVisible()
      await expect(schoolReportsPage.analyticsCards).toBeVisible()
      await expect(schoolReportsPage.classRankingTable).toBeVisible()
    })

    test('Teacher should have limited access to school reports', async ({ page }) => {
      await loginPage.login('teacher', 'teacherPassword123!')
      await schoolReportsPage.navigateTo()

      await schoolReportsPage.verifyTeacherAccess()
      await expect(schoolReportsPage.pageTitle).toBeVisible()
      await expect(schoolReportsPage.exportButton).toBeVisible()
      // Teachers shouldn't have bulk export access
      await expect(schoolReportsPage.bulkExportButton).not.toBeVisible()
    })

    test('Receptionist should not have access to school reports', async ({ page }) => {
      await loginPage.login('receptionist', 'receptionistPassword123!')
      await schoolReportsPage.navigateTo()

      await schoolReportsPage.verifyReceptionistNoAccess()
    })

    test('Student should not have access to school reports', async ({ page }) => {
      await page.goto('/student/login')
      await page.fill('[data-testid="nis-input"]', '12345')
      await page.fill('[data-testid="password-input"]', 'studentPassword123!')
      await page.click('[data-testid="login-button"]')

      await schoolReportsPage.navigateTo()
      await schoolReportsPage.verifyStudentNoAccess()
    })
  })

  test.describe('Page Load and Analytics Display', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
    })

    test('should load school reports page successfully', async ({ page }) => {
      await schoolReportsPage.verifyPageLoadPerformance()

      await expect(schoolReportsPage.pageTitle).toBeVisible()
      await expect(schoolReportsPage.pageTitle).toHaveText('School Reports')
    })

    test('should display comprehensive analytics cards', async ({ page }) => {
      await schoolReportsPage.navigateTo()
      await schoolReportsPage.verifyAnalyticsCards()

      const analytics = await schoolReportsPage.getSchoolAnalyticsData()
      expect(analytics.totalStudents).toBeGreaterThanOrEqual(0)
      expect(analytics.entryStudents).toBeLessThanOrEqual(analytics.totalStudents)
      expect(analytics.lateEntryStudents).toBeLessThanOrEqual(analytics.totalStudents)
      expect(analytics.schoolAttendanceRate).toMatch(/^\d+%$/)
    })

    test('should verify school attendance data consistency', async ({ page }) => {
      await schoolReportsPage.navigateTo()
      await schoolReportsPage.verifyDataConsistency()
    })

    test('should display table with school-specific headers', async ({ page }) => {
      await schoolReportsPage.navigateTo()
      await schoolReportsPage.verifyTableHeaders()
    })

    test('should display class analytics and rankings', async ({ page }) => {
      await schoolReportsPage.navigateTo()
      await schoolReportsPage.verifyClassAnalytics()

      const rankings = await schoolReportsPage.getClassRankingData()
      expect(rankings.length).toBeGreaterThan(0)

      // Verify ranking data structure
      if (rankings.length > 0) {
        expect(rankings[0]).toHaveProperty('rank')
        expect(rankings[0]).toHaveProperty('className')
        expect(rankings[0]).toHaveProperty('attendanceRate')
        expect(rankings[0]).toHaveProperty('totalStudents')
      }
    })
  })

  test.describe('Class-Based Filtering and Analysis', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should filter by specific class', async ({ page }) => {
      await schoolReportsPage.selectClass('10A')
      await schoolReportsPage.applyFilters()

      await schoolReportsPage.verifyFilteredResults('class', '10A')
    })

    test('should filter by grade level', async ({ page }) => {
      await schoolReportsPage.selectGrade('10')
      await schoolReportsPage.applyFilters()

      await schoolReportsPage.verifyFilteredResults('grade', '10')
    })

    test('should filter by school attendance types', async ({ page }) => {
      // Test Entry attendance
      await schoolReportsPage.selectAttendanceType('Entry')
      await schoolReportsPage.applyFilters()

      const rowCount = await schoolReportsPage.getTableRowCount()
      if (rowCount > 0) {
        const firstRow = await schoolReportsPage.getTableData(0)
        expect(firstRow.entry).not.toBe('')
      }
    })

    test('should analyze specific class performance', async ({ page }) => {
      await schoolReportsPage.selectClassForAnalysis('10A')
      await schoolReportsPage.verifyAttendanceTypeBreakdown('10A')
    })

    test('should compare class performance rankings', async ({ page }) => {
      await schoolReportsPage.compareClassPerformance()
    })

    test('should support multi-level filtering', async ({ page }) => {
      await schoolReportsPage.verifyMultiLevelFiltering()
    })
  })

  test.describe('Date Range and Time-Based Analysis', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should filter by date range', async ({ page }) => {
      const startDate = '2024-01-01'
      const endDate = '2024-01-31'

      await schoolReportsPage.setDateRange(startDate, endDate)
      await schoolReportsPage.applyFilters()

      await schoolReportsPage.verifyFilteredResults('date', '2024-01')
    })

    test('should support time-based filtering analysis', async ({ page }) => {
      await schoolReportsPage.verifyTimeBasedFiltering()
    })

    test('should reset all filters correctly', async ({ page }) => {
      // Apply multiple filters
      await schoolReportsPage.setDateRange('2024-01-01', '2024-01-31')
      await schoolReportsPage.selectClass('10A')
      await schoolReportsPage.selectGrade('10')
      await schoolReportsPage.applyFilters()

      // Reset filters
      await schoolReportsPage.resetFilters()

      // Verify filters are reset
      await expect(schoolReportsPage.startDateInput).toHaveValue('')
      await expect(schoolReportsPage.endDateInput).toHaveValue('')
    })
  })

  test.describe('Charts and Advanced Visualization', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should display all school-specific charts', async ({ page }) => {
      await schoolReportsPage.verifyChartsVisible()
    })

    test('should toggle between different chart views', async ({ page }) => {
      await schoolReportsPage.toggleChart('entry')
      await expect(schoolReportsPage.entryChart).toBeVisible()

      await schoolReportsPage.toggleChart('class')
      await expect(schoolReportsPage.classAttendanceChart).toBeVisible()

      await schoolReportsPage.toggleChart('grade')
      await expect(schoolReportsPage.gradeComparisonChart).toBeVisible()

      await schoolReportsPage.toggleChart('weekly')
      await expect(schoolReportsPage.weeklyTrendChart).toBeVisible()
    })

    test('should display monthly trend analysis', async ({ page }) => {
      await schoolReportsPage.verifyMonthlyTrendChart()
    })

    test('should update charts based on class selection', async ({ page }) => {
      await schoolReportsPage.selectClassForAnalysis('10A')

      // Charts should update to reflect class-specific data
      await expect(schoolReportsPage.classAttendanceChart).toBeVisible()
      await expect(schoolReportsPage.gradeComparisonChart).toBeVisible()
    })
  })

  test.describe('Comprehensive Export Functionality', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should export school data to CSV', async ({ page }) => {
      await schoolReportsPage.exportToCsv()
    })

    test('should export school data to PDF', async ({ page }) => {
      await schoolReportsPage.exportToPdf()
    })

    test('should export school data to Excel', async ({ page }) => {
      await schoolReportsPage.exportToExcel()
    })

    test('should support bulk export for all classes', async ({ page }) => {
      await schoolReportsPage.bulkExportAllClasses()
    })

    test('should export filtered data', async ({ page }) => {
      // Apply class and grade filters
      await schoolReportsPage.selectClass('10A')
      await schoolReportsPage.selectGrade('10')
      await schoolReportsPage.applyFilters()

      // Export filtered data
      await schoolReportsPage.exportToCsv()
    })

    test('should handle export errors gracefully', async ({ page }) => {
      // Mock a failed export
      await page.route('**/api/admin/school-reports/export**', route => {
        route.abort('failed')
      })

      await page.click('[data-testid="export-csv-button"]')

      // Should show error message
      const errorMessage = page.locator('[data-testid="export-error-message"]')
      await expect(errorMessage).toBeVisible()
    })
  })

  test.describe('Search and Student Lookup', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should search for specific student', async ({ page }) => {
      await schoolReportsPage.searchStudent('John Doe')

      const rowCount = await schoolReportsPage.getTableRowCount()
      if (rowCount > 0) {
        const firstRow = await schoolReportsPage.getTableData(0)
        expect(firstRow.studentName.toLowerCase()).toContain('john')
      }
    })

    test('should handle no search results', async ({ page }) => {
      await schoolReportsPage.searchStudent('NonexistentStudent123')

      const rowCount = await schoolReportsPage.getTableRowCount()
      if (rowCount === 0) {
        await expect(schoolReportsPage.noDataMessage).toBeVisible()
      }
    })

    test('should search within filtered results', async ({ page }) => {
      // First apply a class filter
      await schoolReportsPage.selectClass('10A')
      await schoolReportsPage.applyFilters()

      // Then search for a student
      await schoolReportsPage.searchStudent('Jane')

      // Results should be both filtered by class AND student name
      const rowCount = await schoolReportsPage.getTableRowCount()
      if (rowCount > 0) {
        const firstRow = await schoolReportsPage.getTableData(0)
        expect(firstRow.class).toContain('10A')
        expect(firstRow.studentName.toLowerCase()).toContain('jane')
      }
    })
  })

  test.describe('Pagination and Data Management', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should navigate between pages', async ({ page }) => {
      const rowCount = await schoolReportsPage.getTableRowCount()

      if (rowCount > 0) {
        const isNextEnabled = await schoolReportsPage.nextPageButton.isEnabled()

        if (isNextEnabled) {
          await schoolReportsPage.goToNextPage()
          await expect(schoolReportsPage.reportsTable).toBeVisible()

          await schoolReportsPage.goToPreviousPage()
          await expect(schoolReportsPage.reportsTable).toBeVisible()
        }
      }
    })

    test('should change rows per page display', async ({ page }) => {
      await schoolReportsPage.changeRowsPerPage('100')

      await expect(schoolReportsPage.reportsTable).toBeVisible()
      await expect(schoolReportsPage.rowsPerPageSelect).toHaveValue('100')
    })

    test('should navigate to specific page number', async ({ page }) => {
      const pageNumbers = await schoolReportsPage.pageNumbers.count()

      if (pageNumbers > 1) {
        await schoolReportsPage.goToPage(2)
        await expect(schoolReportsPage.reportsTable).toBeVisible()
      }
    })
  })

  test.describe('Performance and Error Handling', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
    })

    test('should load page within performance threshold', async ({ page }) => {
      await schoolReportsPage.verifyPageLoadPerformance()
    })

    test('should handle network errors gracefully', async ({ page }) => {
      await schoolReportsPage.handleNetworkError()
    })

    test('should handle large school datasets efficiently', async ({ page }) => {
      // Mock a large dataset with multiple classes and grades
      await page.route('**/api/admin/school-reports**', route => {
        const largeDataset = {
          data: Array.from({ length: 2000 }, (_, i) => ({
            id: i,
            date: '2024-01-01',
            studentName: `Student ${i}`,
            class: `${Math.floor(i / 100) + 10}${String.fromCharCode(65 + (i % 5))}`, // 10A, 10B, 11A, etc.
            grade: Math.floor(i / 100) + 10,
            entry: i % 2 === 0 ? 'Present' : 'Absent',
            lateEntry: i % 5 === 0 ? 'Yes' : 'No',
            excused: i % 7 === 0 ? 'Yes' : 'No',
            tempLeave: i % 10 === 0 ? 'Yes' : 'No',
            sick: i % 15 === 0 ? 'Yes' : 'No',
          })),
          total: 2000,
          analytics: {
            totalStudents: 2000,
            entryStudents: 1800,
            lateEntryStudents: 400,
            excusedAbsence: 285,
            temporaryLeave: 200,
            sickLeave: 133,
            schoolAttendanceRate: '90%',
          },
          classRankings: Array.from({ length: 20 }, (_, i) => ({
            rank: i + 1,
            className: `${Math.floor(i / 5) + 10}${String.fromCharCode(65 + (i % 5))}`,
            attendanceRate: `${95 - i}%`,
            totalStudents: 100 - i * 2,
          })),
        }

        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeDataset),
        })
      })

      await schoolReportsPage.navigateTo()
      await expect(schoolReportsPage.reportsTable).toBeVisible()
      await expect(schoolReportsPage.classRankingTable).toBeVisible()
    })

    test('should handle concurrent filtering operations', async ({ page }) => {
      await schoolReportsPage.navigateTo()

      // Simulate concurrent filter actions
      const promises = [
        schoolReportsPage.selectClass('10A'),
        schoolReportsPage.selectGrade('10'),
        schoolReportsPage.setDateRange('2024-01-01', '2024-01-31'),
        schoolReportsPage.selectAttendanceType('Entry'),
      ]

      await Promise.all(promises)
      await schoolReportsPage.applyFilters()

      await expect(schoolReportsPage.reportsTable).toBeVisible()
    })
  })

  test.describe('Security Testing', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should prevent XSS attacks in search input', async ({ page }) => {
      const xssPayload = '<script>alert("XSS")</script>'
      await schoolReportsPage.searchStudent(xssPayload)

      // Verify script is not executed
      const alerts = []
      page.on('dialog', dialog => {
        alerts.push(dialog.message())
        dialog.dismiss()
      })

      await page.waitForTimeout(1000)
      expect(alerts).toHaveLength(0)
    })

    test('should validate date input format', async ({ page }) => {
      await schoolReportsPage.setDateRange('invalid-date', '2024-01-31')
      await schoolReportsPage.applyFilters()

      const errorMessage = page.locator('[data-testid="date-validation-error"]')
      await expect(errorMessage).toBeVisible()
    })

    test('should handle SQL injection attempts', async ({ page }) => {
      const sqlPayload = "'; DROP TABLE students; --"
      await schoolReportsPage.searchStudent(sqlPayload)

      // Page should still function normally
      await expect(schoolReportsPage.reportsTable).toBeVisible()
    })

    test('should validate class and grade filter inputs', async ({ page }) => {
      // Mock response with validation error
      await page.route('**/api/admin/school-reports**', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid class parameter' }),
        })
      })

      await schoolReportsPage.selectClass('InvalidClass')
      await schoolReportsPage.applyFilters()

      const errorMessage = page.locator('[data-testid="filter-validation-error"]')
      await expect(errorMessage).toBeVisible()
    })
  })

  test.describe('Accessibility Testing', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should meet accessibility standards', async ({ page }) => {
      await takeAccessibilitySnapshot(page, 'admin-school-reports-accessibility')
    })

    test('should support keyboard navigation', async ({ page }) => {
      // Tab through all interactive elements
      await page.keyboard.press('Tab') // Date filter
      await page.keyboard.press('Tab') // Class filter
      await page.keyboard.press('Tab') // Grade filter
      await page.keyboard.press('Tab') // Apply button

      const focusedElement = await page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    })

    test('should have proper ARIA labels for school-specific elements', async ({ page }) => {
      await expect(schoolReportsPage.classFilter).toHaveAttribute('aria-label')
      await expect(schoolReportsPage.gradeFilter).toHaveAttribute('aria-label')
      await expect(schoolReportsPage.classRankingTable).toHaveAttribute('role', 'table')
    })

    test('should support screen reader navigation for tables', async ({ page }) => {
      // Verify both main table and ranking table have proper structure
      await expect(schoolReportsPage.reportsTable).toHaveAttribute('role', 'table')
      await expect(schoolReportsPage.classRankingTable).toHaveAttribute('role', 'table')

      // Check for table headers
      const mainTableHeaders = schoolReportsPage.reportsTable.locator('th')
      const mainHeaderCount = await mainTableHeaders.count()
      expect(mainHeaderCount).toBeGreaterThan(0)

      const rankingTableHeaders = schoolReportsPage.classRankingTable.locator('th')
      const rankingHeaderCount = await rankingTableHeaders.count()
      expect(rankingHeaderCount).toBeGreaterThan(0)
    })
  })

  test.describe('Responsive Design', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
    })

    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await schoolReportsPage.navigateTo()

      await expect(schoolReportsPage.pageTitle).toBeVisible()
      await expect(schoolReportsPage.analyticsCards).toBeVisible()
    })

    test('should work on tablet devices', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await schoolReportsPage.navigateTo()

      await expect(schoolReportsPage.pageTitle).toBeVisible()
      await expect(schoolReportsPage.reportsTable).toBeVisible()
      await expect(schoolReportsPage.classRankingTable).toBeVisible()
    })

    test('should adapt complex tables for small screens', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await schoolReportsPage.navigateTo()

      // Both tables should be visible and accessible
      await expect(schoolReportsPage.reportsTable).toBeVisible()
      await expect(schoolReportsPage.classRankingTable).toBeVisible()
    })

    test('should maintain chart functionality on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await schoolReportsPage.navigateTo()

      await schoolReportsPage.toggleChart('entry')
      await expect(schoolReportsPage.entryChart).toBeVisible()
    })
  })

  test.describe('Data Validation and Edge Cases', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await schoolReportsPage.navigateTo()
    })

    test('should handle empty dataset gracefully', async ({ page }) => {
      // Mock empty response
      await page.route('**/api/admin/school-reports**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            data: [],
            total: 0,
            analytics: {
              totalStudents: 0,
              entryStudents: 0,
              lateEntryStudents: 0,
              excusedAbsence: 0,
              temporaryLeave: 0,
              sickLeave: 0,
              schoolAttendanceRate: '0%',
            },
            classRankings: [],
          }),
        })
      })

      await schoolReportsPage.navigateTo()
      await expect(schoolReportsPage.noDataMessage).toBeVisible()
    })

    test('should validate date range logic', async ({ page }) => {
      // End date before start date
      await schoolReportsPage.setDateRange('2024-01-31', '2024-01-01')
      await schoolReportsPage.applyFilters()

      const errorMessage = page.locator('[data-testid="date-range-error"]')
      await expect(errorMessage).toBeVisible()
    })

    test('should handle special characters in student names', async ({ page }) => {
      const specialNames = ["O'Connor-Smith", 'José María', '李小明', 'Müller']

      for (const name of specialNames) {
        await schoolReportsPage.searchStudent(name)
        await expect(schoolReportsPage.reportsTable).toBeVisible()
      }
    })

    test('should handle class names with special formatting', async ({ page }) => {
      // Mock classes with various formats
      await page.route('**/api/admin/school-reports**', route => {
        const data = {
          data: [
            { class: '10-A', grade: '10', studentName: 'Student 1' },
            { class: 'X IPA 1', grade: '10', studentName: 'Student 2' },
            { class: '11 Science', grade: '11', studentName: 'Student 3' },
          ],
          classRankings: [
            { className: '10-A', rank: 1 },
            { className: 'X IPA 1', rank: 2 },
            { className: '11 Science', rank: 3 },
          ],
        }

        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(data),
        })
      })

      await schoolReportsPage.navigateTo()
      await expect(schoolReportsPage.reportsTable).toBeVisible()
      await expect(schoolReportsPage.classRankingTable).toBeVisible()
    })
  })
})
