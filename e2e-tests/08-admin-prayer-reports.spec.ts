import { test, expect } from '@playwright/test'
import { AdminPrayerReportsPage } from './page-objects/admin/admin-prayer-reports-page'
import { AdminLoginPage } from './page-objects/admin/admin-login-page'
import { takeAccessibilitySnapshot } from './utils/test-helpers'

test.describe('Admin Prayer Reports', () => {
  let prayerReportsPage: AdminPrayerReportsPage
  let loginPage: AdminLoginPage

  test.beforeEach(async ({ page }) => {
    prayerReportsPage = new AdminPrayerReportsPage(page)
    loginPage = new AdminLoginPage(page)
  })

  test.describe('Role-Based Access Control', () => {
    test('Super Admin should have full access to prayer reports', async ({ page }) => {
      await loginPage.login('super_admin', 'superAdminPassword123!')
      await prayerReportsPage.navigateTo()

      await prayerReportsPage.verifyAdminAccess()
      await expect(prayerReportsPage.exportButton).toBeVisible()
      await expect(prayerReportsPage.exportCsvButton).toBeVisible()
      await expect(prayerReportsPage.exportPdfButton).toBeVisible()
    })

    test('Admin should have full access to prayer reports', async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()

      await prayerReportsPage.verifyAdminAccess()
      await expect(prayerReportsPage.pageTitle).toBeVisible()
      await expect(prayerReportsPage.analyticsCards).toBeVisible()
    })

    test('Teacher should have read-only access to prayer reports', async ({ page }) => {
      await loginPage.login('teacher', 'teacherPassword123!')
      await prayerReportsPage.navigateTo()

      await prayerReportsPage.verifyTeacherAccess()
      await expect(prayerReportsPage.pageTitle).toBeVisible()
      await expect(prayerReportsPage.exportButton).toBeVisible()
    })

    test('Receptionist should not have access to prayer reports', async ({ page }) => {
      await loginPage.login('receptionist', 'receptionistPassword123!')
      await prayerReportsPage.navigateTo()

      await prayerReportsPage.verifyReceptionistNoAccess()
    })

    test('Student should not have access to prayer reports', async ({ page }) => {
      await page.goto('/student/login')
      await page.fill('[data-testid="nis-input"]', '12345')
      await page.fill('[data-testid="password-input"]', 'studentPassword123!')
      await page.click('[data-testid="login-button"]')

      await prayerReportsPage.navigateTo()
      await prayerReportsPage.verifyStudentNoAccess()
    })
  })

  test.describe('Page Load and Analytics Display', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
    })

    test('should load prayer reports page successfully', async ({ page }) => {
      await prayerReportsPage.verifyPageLoadPerformance()

      await expect(prayerReportsPage.pageTitle).toBeVisible()
      await expect(prayerReportsPage.pageTitle).toHaveText('Prayer Reports')
    })

    test('should display analytics cards correctly', async ({ page }) => {
      await prayerReportsPage.navigateTo()
      await prayerReportsPage.verifyAnalyticsCards()

      const analytics = await prayerReportsPage.getAnalyticsData()
      expect(analytics.totalStudents).toBeGreaterThanOrEqual(0)
      expect(analytics.presentStudents).toBeLessThanOrEqual(analytics.totalStudents)
      expect(analytics.absentStudents).toBeLessThanOrEqual(analytics.totalStudents)
      expect(analytics.attendanceRate).toMatch(/^\d+%$/)
    })

    test('should verify data consistency in analytics', async ({ page }) => {
      await prayerReportsPage.navigateTo()
      await prayerReportsPage.verifyDataConsistency()
    })

    test('should display table with correct headers', async ({ page }) => {
      await prayerReportsPage.navigateTo()
      await prayerReportsPage.verifyTableHeaders()
    })

    test('should handle loading states properly', async ({ page }) => {
      await prayerReportsPage.navigateTo()

      // Verify loading spinner is hidden after page load
      await expect(prayerReportsPage.loadingSpinner).toBeHidden()
      await expect(prayerReportsPage.reportsTable).toBeVisible()
    })
  })

  test.describe('Filtering and Search Functionality', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()
    })

    test('should filter by date range', async ({ page }) => {
      const startDate = '2024-01-01'
      const endDate = '2024-01-31'

      await prayerReportsPage.setDateRange(startDate, endDate)
      await prayerReportsPage.applyFilters()

      await prayerReportsPage.verifyFilteredResults('date', '2024-01')
    })

    test('should filter by class', async ({ page }) => {
      await prayerReportsPage.selectClass('10A')
      await prayerReportsPage.applyFilters()

      await prayerReportsPage.verifyFilteredResults('class', '10A')
    })

    test('should filter by attendance type', async ({ page }) => {
      await prayerReportsPage.selectAttendanceType('Zuhr')
      await prayerReportsPage.applyFilters()

      // Verify table shows Zuhr attendance data
      const rowCount = await prayerReportsPage.getTableRowCount()
      if (rowCount > 0) {
        const firstRow = await prayerReportsPage.getTableData(0)
        expect(firstRow.zuhr).not.toBe('')
      }
    })

    test('should reset filters correctly', async ({ page }) => {
      // Apply multiple filters
      await prayerReportsPage.setDateRange('2024-01-01', '2024-01-31')
      await prayerReportsPage.selectClass('10A')
      await prayerReportsPage.applyFilters()

      // Reset filters
      await prayerReportsPage.resetFilters()

      // Verify filters are reset
      await expect(prayerReportsPage.startDateInput).toHaveValue('')
      await expect(prayerReportsPage.endDateInput).toHaveValue('')
    })

    test('should search for specific student', async ({ page }) => {
      await prayerReportsPage.searchStudent('John Doe')

      const rowCount = await prayerReportsPage.getTableRowCount()
      if (rowCount > 0) {
        const firstRow = await prayerReportsPage.getTableData(0)
        expect(firstRow.studentName.toLowerCase()).toContain('john')
      }
    })

    test('should handle no data scenarios', async ({ page }) => {
      // Apply filter that should return no results
      await prayerReportsPage.setDateRange('2030-01-01', '2030-01-02')
      await prayerReportsPage.applyFilters()

      const rowCount = await prayerReportsPage.getTableRowCount()
      if (rowCount === 0) {
        await expect(prayerReportsPage.noDataMessage).toBeVisible()
      }
    })
  })

  test.describe('Charts and Visualization', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()
    })

    test('should display all charts correctly', async ({ page }) => {
      await prayerReportsPage.verifyChartsVisible()
    })

    test('should toggle between different chart views', async ({ page }) => {
      await prayerReportsPage.toggleChart('attendance')
      await expect(prayerReportsPage.attendanceChart).toBeVisible()

      await prayerReportsPage.toggleChart('comparison')
      await expect(prayerReportsPage.classComparisonChart).toBeVisible()

      await prayerReportsPage.toggleChart('trend')
      await expect(prayerReportsPage.weeklyTrendChart).toBeVisible()
    })

    test('should update charts when filters are applied', async ({ page }) => {
      await prayerReportsPage.selectClass('10A')
      await prayerReportsPage.applyFilters()

      // Charts should update to reflect filtered data
      await expect(prayerReportsPage.attendanceChart).toBeVisible()
      await expect(prayerReportsPage.classComparisonChart).toBeVisible()
    })
  })

  test.describe('Export Functionality', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()
    })

    test('should export data to CSV', async ({ page }) => {
      await prayerReportsPage.exportToCsv()
      // Download is handled in the page object method
    })

    test('should export data to PDF', async ({ page }) => {
      await prayerReportsPage.exportToPdf()
      // Download is handled in the page object method
    })

    test('should export filtered data', async ({ page }) => {
      // Apply a filter first
      await prayerReportsPage.selectClass('10A')
      await prayerReportsPage.applyFilters()

      // Then export - should only export filtered data
      await prayerReportsPage.exportToCsv()
    })

    test('should handle export errors gracefully', async ({ page }) => {
      // Mock a failed export
      await page.route('**/api/admin/prayer-reports/export**', route => {
        route.abort('failed')
      })

      await page.click('[data-testid="export-csv-button"]')

      // Should show error message
      const errorMessage = page.locator('[data-testid="export-error-message"]')
      await expect(errorMessage).toBeVisible()
    })
  })

  test.describe('Pagination', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()
    })

    test('should navigate between pages', async ({ page }) => {
      const rowCount = await prayerReportsPage.getTableRowCount()

      if (rowCount > 0) {
        // Check if next page button is enabled
        const isNextEnabled = await prayerReportsPage.nextPageButton.isEnabled()

        if (isNextEnabled) {
          await prayerReportsPage.goToNextPage()
          await expect(prayerReportsPage.reportsTable).toBeVisible()

          await prayerReportsPage.goToPreviousPage()
          await expect(prayerReportsPage.reportsTable).toBeVisible()
        }
      }
    })

    test('should change rows per page', async ({ page }) => {
      await prayerReportsPage.changeRowsPerPage('50')

      // Verify table updates
      await expect(prayerReportsPage.reportsTable).toBeVisible()
      await expect(prayerReportsPage.rowsPerPageSelect).toHaveValue('50')
    })

    test('should navigate to specific page', async ({ page }) => {
      const pageNumbers = await prayerReportsPage.pageNumbers.count()

      if (pageNumbers > 1) {
        await prayerReportsPage.goToPage(2)
        await expect(prayerReportsPage.reportsTable).toBeVisible()
      }
    })
  })

  test.describe('Performance and Error Handling', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
    })

    test('should load page within performance threshold', async ({ page }) => {
      await prayerReportsPage.verifyPageLoadPerformance()
    })

    test('should handle network errors gracefully', async ({ page }) => {
      await prayerReportsPage.handleNetworkError()
    })

    test('should handle large datasets efficiently', async ({ page }) => {
      // Mock a large dataset response
      await page.route('**/api/admin/prayer-reports**', route => {
        const largeDataset = {
          data: Array.from({ length: 1000 }, (_, i) => ({
            id: i,
            date: '2024-01-01',
            studentName: `Student ${i}`,
            class: '10A',
            zuhr: i % 2 === 0 ? 'Present' : 'Absent',
            asr: i % 3 === 0 ? 'Present' : 'Absent',
            pulang: 'Present',
            ijin: i % 5 === 0 ? 'Yes' : 'No',
          })),
          total: 1000,
          analytics: {
            totalStudents: 1000,
            presentStudents: 800,
            absentStudents: 200,
            attendanceRate: '80%',
          },
        }

        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(largeDataset),
        })
      })

      await prayerReportsPage.navigateTo()
      await expect(prayerReportsPage.reportsTable).toBeVisible()
    })

    test('should handle concurrent user actions', async ({ page }) => {
      await prayerReportsPage.navigateTo()

      // Simulate concurrent actions
      const promises = [
        prayerReportsPage.selectClass('10A'),
        prayerReportsPage.setDateRange('2024-01-01', '2024-01-31'),
        prayerReportsPage.selectAttendanceType('Zuhr'),
      ]

      await Promise.all(promises)
      await prayerReportsPage.applyFilters()

      await expect(prayerReportsPage.reportsTable).toBeVisible()
    })
  })

  test.describe('Security Testing', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()
    })

    test('should prevent XSS attacks in search input', async ({ page }) => {
      const xssPayload = '<script>alert("XSS")</script>'
      await prayerReportsPage.searchStudent(xssPayload)

      // Verify script is not executed
      const alerts = []
      page.on('dialog', dialog => {
        alerts.push(dialog.message())
        dialog.dismiss()
      })

      await page.waitForTimeout(1000)
      expect(alerts).toHaveLength(0)
    })

    test('should validate date input format', async ({ page }) => {
      // Try invalid date format
      await prayerReportsPage.setDateRange('invalid-date', '2024-01-31')
      await prayerReportsPage.applyFilters()

      // Should show validation error
      const errorMessage = page.locator('[data-testid="date-validation-error"]')
      await expect(errorMessage).toBeVisible()
    })

    test('should handle SQL injection attempts', async ({ page }) => {
      const sqlPayload = "'; DROP TABLE students; --"
      await prayerReportsPage.searchStudent(sqlPayload)

      // Page should still function normally
      await expect(prayerReportsPage.reportsTable).toBeVisible()
    })
  })

  test.describe('Accessibility Testing', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()
    })

    test('should meet accessibility standards', async ({ page }) => {
      await takeAccessibilitySnapshot(page, 'admin-prayer-reports-accessibility')
    })

    test('should support keyboard navigation', async ({ page }) => {
      // Tab through filter controls
      await page.keyboard.press('Tab') // Date filter
      await page.keyboard.press('Tab') // Class filter
      await page.keyboard.press('Tab') // Apply button

      // Verify focus is visible
      const focusedElement = await page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    })

    test('should have proper ARIA labels', async ({ page }) => {
      await expect(prayerReportsPage.startDateInput).toHaveAttribute('aria-label')
      await expect(prayerReportsPage.endDateInput).toHaveAttribute('aria-label')
      await expect(prayerReportsPage.classFilter).toHaveAttribute('aria-label')
    })

    test('should support screen reader navigation', async ({ page }) => {
      // Verify heading structure
      const headings = page.locator('h1, h2, h3, h4, h5, h6')
      const headingCount = await headings.count()
      expect(headingCount).toBeGreaterThan(0)

      // Verify table has proper headers
      await expect(prayerReportsPage.reportsTable).toHaveAttribute('role', 'table')
    })
  })

  test.describe('Responsive Design', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
    })

    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await prayerReportsPage.navigateTo()

      await expect(prayerReportsPage.pageTitle).toBeVisible()
      await expect(prayerReportsPage.analyticsCards).toBeVisible()
    })

    test('should work on tablet devices', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })
      await prayerReportsPage.navigateTo()

      await expect(prayerReportsPage.pageTitle).toBeVisible()
      await expect(prayerReportsPage.reportsTable).toBeVisible()
    })

    test('should adapt table for small screens', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      await prayerReportsPage.navigateTo()

      // Verify table is scrollable or stacked appropriately
      await expect(prayerReportsPage.reportsTable).toBeVisible()
    })
  })

  test.describe('Data Validation and Edge Cases', () => {
    test.beforeEach(async ({ page }) => {
      await loginPage.login('admin', 'adminPassword123!')
      await prayerReportsPage.navigateTo()
    })

    test('should handle empty dataset gracefully', async ({ page }) => {
      // Mock empty response
      await page.route('**/api/admin/prayer-reports**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            data: [],
            total: 0,
            analytics: {
              totalStudents: 0,
              presentStudents: 0,
              absentStudents: 0,
              attendanceRate: '0%',
            },
          }),
        })
      })

      await prayerReportsPage.navigateTo()
      await expect(prayerReportsPage.noDataMessage).toBeVisible()
    })

    test('should validate date range logic', async ({ page }) => {
      // End date before start date
      await prayerReportsPage.setDateRange('2024-01-31', '2024-01-01')
      await prayerReportsPage.applyFilters()

      // Should show validation error
      const errorMessage = page.locator('[data-testid="date-range-error"]')
      await expect(errorMessage).toBeVisible()
    })

    test('should handle special characters in student names', async ({ page }) => {
      const specialName = "O'Connor-Smith"
      await prayerReportsPage.searchStudent(specialName)

      // Should handle search without errors
      await expect(prayerReportsPage.reportsTable).toBeVisible()
    })
  })
})
