import { test, expect } from '@playwright/test'
import { StudentHomePage } from './page-objects/student-home-page'
import { StudentLoginPage } from './page-objects/student/login-page'
import { testUsers } from './test-data/users'
import { loginAsStudent, takeAccessibilitySnapshot } from './utils/test-helpers'

test.describe('Student Home Page', () => {
  let homePage: StudentHomePage
  let loginPage: StudentLoginPage

  test.beforeEach(async ({ page }) => {
    homePage = new StudentHomePage(page)
    loginPage = new StudentLoginPage(page)

    // Login as a test student before each test
    await loginAsStudent(page, testUsers.students.verified)

    // Navigate to home page
    await homePage.goto()
  })

  test.describe('Page Load and Structure', () => {
    test('should load home page successfully', async () => {
      await expect(homePage.qrCodeContainer).toBeVisible()
      await expect(homePage.attendanceCards).toBeVisible()
      await expect(homePage.bottomNav).toBeVisible()
    })

    test('should display current date and time in WITA timezone', async () => {
      const { date, time } = await homePage.getCurrentDateTime()

      // Verify date format (DD/MM/YYYY or DD-MM-YYYY)
      expect(date).toMatch(/\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}/)

      // Verify time format (HH:MM)
      expect(time).toMatch(/\d{2}:\d{2}/)

      await homePage.verifyTimezoneDisplay()
    })

    test('should display student information correctly', async () => {
      const studentInfo = await homePage.getStudentInfo()

      expect(studentInfo.name).toBeTruthy()
      expect(studentInfo.uniqueCode).toBeTruthy()
      expect(studentInfo.uniqueCode).toMatch(/^[A-Z0-9]{6,}$/) // Unique code format
    })

    test('should handle profile load failures gracefully', async () => {
      // This test would need to mock a failed profile request
      // For now, we'll test the retry mechanism if it appears
      const retryHandled = await homePage.handleProfileLoadFailure()

      // If retry was available and handled, verify page loads properly after retry
      if (retryHandled) {
        await expect(homePage.qrCodeContainer).toBeVisible()
        await expect(homePage.attendanceCards).toBeVisible()
      }
    })
  })

  test.describe('QR Code Functionality', () => {
    test('should display QR code with valid data', async () => {
      const isVisible = await homePage.isQrCodeVisible()
      expect(isVisible).toBe(true)

      const qrValue = await homePage.getQrCodeValue()
      expect(qrValue).toBeTruthy()
      expect(qrValue).toContain(testUsers.students.verified.uniqueCode)
    })

    test('should allow QR code download', async ({ context }) => {
      // Set up download handling
      const downloadPromise = context.waitForEvent('download')

      await homePage.downloadQrCode()

      const download = await downloadPromise
      expect(download.suggestedFilename()).toMatch(/qr-code.*\.(png|jpg|jpeg)$/i)

      // Verify download size is reasonable (not empty)
      const downloadPath = await download.path()
      expect(downloadPath).toBeTruthy()
    })

    test('should maintain QR code visibility across different viewports', async () => {
      // Test mobile viewport
      await homePage.page.setViewportSize({ width: 375, height: 667 })
      expect(await homePage.isQrCodeVisible()).toBe(true)

      // Test tablet viewport
      await homePage.page.setViewportSize({ width: 768, height: 1024 })
      expect(await homePage.isQrCodeVisible()).toBe(true)

      // Test desktop viewport
      await homePage.page.setViewportSize({ width: 1024, height: 768 })
      expect(await homePage.isQrCodeVisible()).toBe(true)
    })
  })

  test.describe('Attendance Status Display', () => {
    test('should display all attendance types', async () => {
      const attendanceStatus = await homePage.getAttendanceStatus()

      expect(attendanceStatus).toHaveProperty('zuhur')
      expect(attendanceStatus).toHaveProperty('asr')
      expect(attendanceStatus).toHaveProperty('pulang')

      // Each attendance type should have status and time properties
      expect(typeof attendanceStatus.zuhur.status).toBe('boolean')
      expect(typeof attendanceStatus.asr.status).toBe('boolean')
      expect(typeof attendanceStatus.pulang.status).toBe('boolean')
    })

    test('should show attendance time when marked', async () => {
      const attendanceStatus = await homePage.getAttendanceStatus()

      // If any attendance is marked, it should have a valid time
      Object.values(attendanceStatus).forEach(({ status, time }) => {
        if (status) {
          expect(time).toBeTruthy()
          expect(time).toMatch(/\d{2}:\d{2}/) // Time format HH:MM
        }
      })
    })

    test('should update attendance status in real-time', async () => {
      // Get initial status
      const initialStatus = await homePage.getAttendanceStatus()

      // In a real scenario, this would test actual status changes
      // For now, we verify the status checking mechanism works
      await homePage.page.waitForTimeout(2000)
      const updatedStatus = await homePage.getAttendanceStatus()

      // Verify status structure remains consistent
      expect(typeof updatedStatus.zuhur.status).toBe('boolean')
      expect(typeof updatedStatus.asr.status).toBe('boolean')
      expect(typeof updatedStatus.pulang.status).toBe('boolean')
    })
  })

  test.describe('Tab Navigation', () => {
    test('should switch between tabs correctly', async () => {
      // Test QR Code tab
      await homePage.switchToTab('qr-code')
      await expect(homePage.qrCodeContainer).toBeVisible()

      // Test Attendance tab
      await homePage.switchToTab('attendance')
      await expect(homePage.attendanceCards).toBeVisible()

      // Test Profile tab if available
      try {
        await homePage.switchToTab('profile')
        // Profile content should be visible
      } catch {
        // Profile tab might not be available on home page
      }
    })

    test('should maintain tab state across interactions', async () => {
      await homePage.switchToTab('attendance')

      // Perform some interaction
      await homePage.page.reload()
      await homePage.waitForLoad()

      // Default tab should be active
      await expect(homePage.qrCodeContainer).toBeVisible()
    })
  })

  test.describe('Navigation and Logout', () => {
    test('should navigate to history page via bottom navigation', async () => {
      await homePage.navigateToHistory()
      await expect(homePage.page).toHaveURL(/.*\/student\/history/)
    })

    test('should navigate to profile page via bottom navigation', async () => {
      await homePage.navigateToProfile()
      await expect(homePage.page).toHaveURL(/.*\/student\/profile/)
    })

    test('should logout successfully', async () => {
      await homePage.logout()

      // Should redirect to login page
      await expect(homePage.page).toHaveURL(/.*\/student$/)

      // Login form should be visible
      await expect(loginPage.loginForm).toBeVisible()
    })

    test('should handle logout with network issues', async () => {
      // Simulate network issues
      await homePage.page.route('**/api/auth/logout**', route => {
        route.abort('failed')
      })

      try {
        await homePage.logout()
      } catch {
        // Should handle gracefully
      }

      // Clear the route for cleanup
      await homePage.page.unroute('**/api/auth/logout**')
    })
  })

  test.describe('Theme and Accessibility', () => {
    test('should toggle theme correctly', async () => {
      const initialTheme = await homePage.page.locator('html').getAttribute('class')

      await homePage.toggleTheme()
      await homePage.page.waitForTimeout(500)

      const newTheme = await homePage.page.locator('html').getAttribute('class')
      expect(newTheme).not.toBe(initialTheme)
    })

    test('should meet accessibility standards', async () => {
      await homePage.verifyAccessibility()

      // Take accessibility snapshot
      await takeAccessibilitySnapshot(homePage.page, 'student-home')
    })

    test('should support keyboard navigation', async () => {
      // Test tab navigation
      await homePage.page.keyboard.press('Tab')
      await homePage.page.keyboard.press('Tab')
      await homePage.page.keyboard.press('Tab')

      // Should be able to reach interactive elements
      const focusedElement = await homePage.page.locator(':focus')
      await expect(focusedElement).toBeVisible()
    })
  })

  test.describe('Responsive Design', () => {
    test('should adapt to mobile viewport', async () => {
      await homePage.page.setViewportSize({ width: 375, height: 667 })

      await expect(homePage.qrCodeContainer).toBeVisible()
      await expect(homePage.attendanceCards).toBeVisible()
      await expect(homePage.bottomNav).toBeVisible()

      // Elements should be properly sized for mobile
      const qrCodeSize = await homePage.qrCodeContainer.boundingBox()
      expect(qrCodeSize?.width).toBeLessThanOrEqual(375)
    })

    test('should adapt to tablet viewport', async () => {
      await homePage.page.setViewportSize({ width: 768, height: 1024 })

      await expect(homePage.qrCodeContainer).toBeVisible()
      await expect(homePage.attendanceCards).toBeVisible()

      // Should utilize more space on tablet
      const attendanceCardsBox = await homePage.attendanceCards.boundingBox()
      expect(attendanceCardsBox?.width).toBeGreaterThan(300)
    })

    test('should work properly on desktop', async () => {
      await homePage.page.setViewportSize({ width: 1024, height: 768 })

      await homePage.verifyResponsiveDesign()
    })
  })

  test.describe('Performance and Loading', () => {
    test('should load within acceptable time limits', async ({ page }) => {
      const startTime = Date.now()

      await homePage.goto()

      const loadTime = Date.now() - startTime
      expect(loadTime).toBeLessThan(5000) // Should load within 5 seconds
    })

    test('should handle slow network conditions', async ({ page }) => {
      // Simulate slow network
      await page.route('**/api/student/profile', route => {
        setTimeout(() => route.continue(), 2000) // 2 second delay
      })

      await homePage.goto()

      // Should eventually load
      await expect(homePage.qrCodeContainer).toBeVisible({ timeout: 10000 })

      // Clear route
      await page.unroute('**/api/student/profile')
    })

    test('should maintain functionality with JavaScript disabled', async ({ context }) => {
      // Create new page with JavaScript disabled
      const noJSPage = await context.newPage()
      await noJSPage.route('**/*.js', route => route.abort())

      const noJSHomePage = new StudentHomePage(noJSPage)

      // Login first (may need special handling without JS)
      await noJSPage.goto('/student/home')

      // Basic elements should still be visible
      await expect(noJSHomePage.page.locator('body')).toBeVisible()
    })
  })

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API failure
      await page.route('**/api/absence/check**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Server error' }),
        })
      })

      await homePage.goto()

      // Page should still be functional even with attendance API failure
      await expect(homePage.qrCodeContainer).toBeVisible()

      // Clear route
      await page.unroute('**/api/absence/check**')
    })

    test('should handle authentication expiry', async ({ page }) => {
      // Mock authentication failure
      await page.route('**/api/student/profile', route => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Unauthorized' }),
        })
      })

      await homePage.goto()

      // Should redirect to login or show appropriate error
      await homePage.page.waitForTimeout(3000)

      // Clear route
      await page.unroute('**/api/student/profile')
    })
  })

  test.describe('Security Features', () => {
    test('should protect against XSS in displayed data', async () => {
      // Verify that user data is properly escaped
      const studentInfo = await homePage.getStudentInfo()

      // Should not contain script tags or other dangerous content
      expect(studentInfo.name).not.toContain('<script>')
      expect(studentInfo.uniqueCode).not.toContain('<script>')
    })

    test('should include proper security headers', async ({ page }) => {
      const response = await page.goto(homePage.url)
      const headers = response?.headers()

      // Check for security headers
      expect(headers).toHaveProperty('x-frame-options')
      expect(headers).toHaveProperty('x-content-type-options')
    })

    test('should validate session properly', async ({ page }) => {
      // Clear cookies to simulate session expiry
      await page.context().clearCookies()

      await homePage.goto()

      // Should redirect to login or show appropriate error
      await expect(homePage.page).toHaveURL(/.*\/student$/, { timeout: 10000 })
    })
  })

  test.describe('Data Integrity', () => {
    test('should maintain consistent student data across page loads', async () => {
      const initialInfo = await homePage.getStudentInfo()

      await homePage.page.reload()
      await homePage.waitForLoad()

      const reloadedInfo = await homePage.getStudentInfo()

      expect(reloadedInfo.name).toBe(initialInfo.name)
      expect(reloadedInfo.uniqueCode).toBe(initialInfo.uniqueCode)
    })

    test('should validate attendance data format', async () => {
      const attendanceStatus = await homePage.getAttendanceStatus()

      // Verify data structure
      Object.entries(attendanceStatus).forEach(([type, data]) => {
        expect(['zuhur', 'asr', 'pulang']).toContain(type)
        expect(typeof data.status).toBe('boolean')

        if (data.time) {
          expect(data.time).toMatch(/\d{2}:\d{2}/)
        }
      })
    })
  })
})
