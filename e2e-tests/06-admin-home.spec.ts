import { test, expect } from '@playwright/test'
import { AdminHomePage } from './page-objects/admin/admin-home-page'
import { AdminLoginPage } from './page-objects/admin/admin-login-page'
import { testUsers } from './test-data/users'
import { AttendanceType } from './test-data/attendance'

test.describe('Admin Home Page - QR Scanner', () => {
  let adminHomePage: AdminHomePage
  let adminLoginPage: AdminLoginPage

  test.beforeEach(async ({ page }) => {
    adminHomePage = new AdminHomePage(page)
    adminLoginPage = new AdminLoginPage(page)
  })

  test.describe('Super Admin Role Tests', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsSuperAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should display all attendance types for Super Admin', async () => {
      const availableTypes = await adminHomePage.getAvailableAttendanceTypes()

      // Super Admin should see all attendance types
      const expectedTypes = [
        'Zuhr',
        'Asr',
        'Pulang',
        'Ijin',
        'Entry',
        'Late Entry',
        'Excused Absence',
        'Temporary Leave',
        'Return from Leave',
        'Sick',
      ]

      for (const type of expectedTypes) {
        expect(availableTypes.some(option => option.includes(type))).toBe(true)
      }
    })

    test('should have manual entry access for Super Admin', async () => {
      await adminHomePage.validateManualEntryAccess(true)
    })

    test('should allow scanning for all attendance types', async () => {
      const attendanceTypes = [AttendanceType.ZUHR, AttendanceType.ASR, AttendanceType.PULANG]

      for (const type of attendanceTypes) {
        await adminHomePage.selectAttendanceType(type)
        await adminHomePage.startScanning()

        // Verify scanner is active
        await expect(adminHomePage.cameraView).toBeVisible()

        // Simulate QR scan
        await adminHomePage.simulateQRScan('STUDENT001')
        await adminHomePage.waitForScanResult()

        const result = await adminHomePage.getScanResultMessage()
        expect(['success', 'duplicate']).toContain(result.type)

        await adminHomePage.closeScanResultDialog()
      }
    })
  })

  test.describe('Admin Role Tests', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should display only prayer-related attendance types for Admin', async () => {
      const availableTypes = await adminHomePage.getAvailableAttendanceTypes()

      // Admin should only see prayer-related types
      const expectedTypes = ['Zuhr', 'Asr', 'Pulang', 'Ijin']
      const restrictedTypes = [
        'Entry',
        'Late Entry',
        'Excused Absence',
        'Temporary Leave',
        'Return from Leave',
        'Sick',
      ]

      for (const type of expectedTypes) {
        expect(availableTypes.some(option => option.includes(type))).toBe(true)
      }

      for (const type of restrictedTypes) {
        expect(availableTypes.some(option => option.includes(type))).toBe(false)
      }
    })

    test('should not have manual entry access for Admin', async () => {
      await adminHomePage.validateManualEntryAccess(false)
    })

    test('should handle Ijin confirmation dialog', async () => {
      await adminHomePage.selectAttendanceType(AttendanceType.IJIN)
      await adminHomePage.startScanning()

      // Simulate Ijin QR scan
      await adminHomePage.simulateQRScan('STUDENT001')

      // Should show Ijin confirmation dialog
      await expect(adminHomePage.ijinConfirmDialog).toBeVisible()

      // Test confirming Ijin
      await adminHomePage.confirmIjinRequest(true)
      await adminHomePage.waitForScanResult()

      const result = await adminHomePage.getScanResultMessage()
      expect(result.type).toBe('success')
      expect(result.message).toContain('Ijin berhasil')
    })

    test('should handle Ijin rejection', async () => {
      await adminHomePage.selectAttendanceType(AttendanceType.IJIN)
      await adminHomePage.startScanning()

      // Simulate Ijin QR scan
      await adminHomePage.simulateQRScan('STUDENT002')

      // Should show Ijin confirmation dialog
      await expect(adminHomePage.ijinConfirmDialog).toBeVisible()

      // Test rejecting Ijin
      await adminHomePage.confirmIjinRequest(false)

      // Should resume scanning without recording attendance
      await expect(adminHomePage.cameraView).toBeVisible()
    })
  })

  test.describe('Teacher Role Tests', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsTeacher()
      await adminHomePage.waitForLoad()
    })

    test('should display only Entry attendance type for Teacher', async () => {
      const availableTypes = await adminHomePage.getAvailableAttendanceTypes()

      // Teacher should only see Entry type
      expect(availableTypes.some(option => option.includes('Entry'))).toBe(true)

      // Should not see prayer or other types
      const restrictedTypes = ['Zuhr', 'Asr', 'Pulang', 'Ijin', 'Late Entry', 'Excused Absence']
      for (const type of restrictedTypes) {
        expect(availableTypes.some(option => option.includes(type))).toBe(false)
      }
    })

    test('should not have manual entry access for Teacher', async () => {
      await adminHomePage.validateManualEntryAccess(false)
    })

    test('should allow scanning for Entry attendance', async () => {
      // Entry should be pre-selected for Teacher
      await adminHomePage.startScanning()

      // Simulate QR scan for school entry
      await adminHomePage.simulateQRScan('STUDENT003')
      await adminHomePage.waitForScanResult()

      const result = await adminHomePage.getScanResultMessage()
      expect(['success', 'duplicate']).toContain(result.type)
    })
  })

  test.describe('Receptionist Role Tests', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsReceptionist()
      await adminHomePage.waitForLoad()
    })

    test('should display absence-related attendance types for Receptionist', async () => {
      const availableTypes = await adminHomePage.getAvailableAttendanceTypes()

      // Receptionist should see absence-related types
      const expectedTypes = [
        'Late Entry',
        'Excused Absence',
        'Temporary Leave',
        'Return from Leave',
        'Sick',
      ]

      for (const type of expectedTypes) {
        expect(availableTypes.some(option => option.includes(type))).toBe(true)
      }

      // Should not see prayer or regular entry types
      const restrictedTypes = ['Zuhr', 'Asr', 'Pulang', 'Ijin', 'Entry']
      for (const type of restrictedTypes) {
        expect(availableTypes.some(option => option.includes(type))).toBe(false)
      }
    })

    test('should have manual entry access for Receptionist', async () => {
      await adminHomePage.validateManualEntryAccess(true)
    })

    test('should allow manual entry for non-scan attendance types', async () => {
      await adminHomePage.switchToManualEntry()

      // Test manual entry for Excused Absence
      await adminHomePage.submitManualEntry(
        'Ahmad Fauzi',
        AttendanceType.EXCUSED_ABSENCE,
        'Keperluan keluarga'
      )

      const result = await adminHomePage.getScanResultMessage()
      expect(result.type).toBe('success')
      expect(result.message).toContain('berhasil')
    })

    test('should require reason for Excused Absence and Sick entries', async () => {
      await adminHomePage.switchToManualEntry()

      // Try to submit without reason
      await adminHomePage.searchStudent('Ahmad Fauzi')
      await adminHomePage.selectStudentFromSearch('Ahmad Fauzi')
      await adminHomePage.manualAttendanceTypeSelect.selectOption(AttendanceType.EXCUSED_ABSENCE)
      await adminHomePage.submitManualButton.click()

      // Should show validation error
      await expect(adminHomePage.validationError).toBeVisible()
    })
  })

  test.describe('Scanner Functionality', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should initialize scanner correctly', async () => {
      await adminHomePage.selectAttendanceType(AttendanceType.ZUHR)
      await adminHomePage.startScanning()

      // Verify scanner interface
      await expect(adminHomePage.cameraView).toBeVisible()
      await expect(adminHomePage.scannerStatus).toContainText('Aktif')
    })

    test('should pause and resume scanning', async () => {
      await adminHomePage.startScanning()

      // Pause scanner
      await adminHomePage.pauseScanning()
      await expect(adminHomePage.scannerStatus).toContainText('Jeda')

      // Resume scanner
      await adminHomePage.startScanning()
      await expect(adminHomePage.scannerStatus).toContainText('Aktif')
    })

    test('should handle duplicate attendance', async () => {
      await adminHomePage.selectAttendanceType(AttendanceType.ZUHR)
      await adminHomePage.startScanning()

      // Scan same student twice
      await adminHomePage.simulateQRScan('STUDENT001')
      await adminHomePage.waitForScanResult()
      await adminHomePage.closeScanResultDialog()

      // Start scanning again
      await adminHomePage.startScanning()
      await adminHomePage.simulateQRScan('STUDENT001')
      await adminHomePage.waitForScanResult()

      const result = await adminHomePage.getScanResultMessage()
      expect(result.type).toBe('duplicate')
      expect(result.message).toContain('sudah tercatat')
    })

    test('should handle invalid QR codes', async () => {
      await adminHomePage.startScanning()

      // Scan invalid QR code
      await adminHomePage.simulateQRScan('INVALID_CODE')
      await adminHomePage.waitForScanResult()

      const result = await adminHomePage.getScanResultMessage()
      expect(result.type).toBe('error')
      expect(result.message).toContain('tidak valid')
    })

    test('should display current date and time', async () => {
      const dateTime = await adminHomePage.getCurrentDateTime()

      expect(dateTime.date).toBeTruthy()
      expect(dateTime.time).toBeTruthy()
      expect(dateTime.time).toContain('WITA')
    })
  })

  test.describe('UI/UX Features', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should toggle theme correctly', async () => {
      await adminHomePage.toggleTheme()
      // Theme should change
    })

    test('should show loading states during operations', async () => {
      await adminHomePage.startScanning()
      await adminHomePage.simulateQRScan('STUDENT001')

      // Should show loading spinner during processing
      await expect(adminHomePage.loadingSpinner).toBeVisible()
    })

    test('should display success and error messages', async () => {
      await adminHomePage.startScanning()
      await adminHomePage.simulateQRScan('STUDENT001')
      await adminHomePage.waitForScanResult()

      // Should show appropriate message
      const result = await adminHomePage.getScanResultMessage()
      expect(['success', 'error', 'duplicate']).toContain(result.type)
    })

    test('should handle responsive design', async () => {
      await adminHomePage.verifyResponsiveDesign()
    })
  })

  test.describe('Navigation and Integration', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should navigate to reports page', async () => {
      await adminHomePage.navigateToReports()
    })

    test('should navigate to profile page', async () => {
      await adminHomePage.navigateToProfile()
    })

    test('should logout successfully', async () => {
      await adminHomePage.logout()
    })

    test('should maintain state across navigation', async () => {
      // Select attendance type
      await adminHomePage.selectAttendanceType(AttendanceType.ASR)

      // Navigate away and back
      await adminHomePage.navigateToProfile()
      await adminHomePage.page.goBack()

      // State should be maintained (or reset appropriately)
      await adminHomePage.waitForLoad()
    })
  })

  test.describe('Security and Error Handling', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should handle network errors gracefully', async () => {
      await adminHomePage.handleNetworkError()

      await adminHomePage.startScanning()
      await adminHomePage.simulateQRScan('STUDENT001')
      await adminHomePage.waitForScanResult()

      const result = await adminHomePage.getScanResultMessage()
      expect(result.type).toBe('error')
      expect(result.message).toContain('Terjadi kesalahan')

      await adminHomePage.clearNetworkMocks()
    })

    test('should validate attendance type selection', async () => {
      // Test with restricted attendance type for current role
      // This would typically be handled by the UI not showing restricted options
      const availableTypes = await adminHomePage.getAvailableAttendanceTypes()
      expect(availableTypes.length).toBeGreaterThan(0)
    })

    test('should prevent unauthorized access to manual entry', async () => {
      // Admin role should not have manual entry access
      await adminHomePage.validateManualEntryAccess(false)
    })
  })

  test.describe('Accessibility', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should meet accessibility standards', async () => {
      await adminHomePage.verifyAccessibility()
    })

    test('should support keyboard navigation', async () => {
      // Test keyboard navigation through scanner controls
      await adminHomePage.attendanceTypeSelect.press('Tab')
      await expect(adminHomePage.startScanButton).toBeFocused()

      await adminHomePage.startScanButton.press('Enter')
      await expect(adminHomePage.cameraView).toBeVisible()
    })

    test('should provide proper ARIA labels and roles', async () => {
      // Check ARIA attributes
      await expect(adminHomePage.attendanceTypeSelect).toHaveAttribute('aria-label')
      await expect(adminHomePage.startScanButton).toHaveAttribute('aria-label')
      await expect(adminHomePage.scannerCard).toHaveAttribute('role')
    })
  })

  test.describe('Performance', () => {
    test.beforeEach(async () => {
      await adminLoginPage.goto()
      await adminLoginPage.loginAsAdmin()
      await adminHomePage.waitForLoad()
    })

    test('should load page quickly', async () => {
      const loadTime = await adminHomePage.page.evaluate(() => {
        return performance.timing.loadEventEnd - performance.timing.navigationStart
      })

      // Page should load within reasonable time
      expect(loadTime).toBeLessThan(5000)
    })

    test('should handle rapid scanning operations', async () => {
      await adminHomePage.startScanning()

      // Simulate rapid scanning
      const scanCodes = ['STUDENT001', 'STUDENT002', 'STUDENT003']

      for (const code of scanCodes) {
        await adminHomePage.simulateQRScan(code)
        await adminHomePage.waitForScanResult()
        await adminHomePage.closeScanResultDialog()

        // Small delay between scans
        await adminHomePage.page.waitForTimeout(500)
      }
    })
  })
})
