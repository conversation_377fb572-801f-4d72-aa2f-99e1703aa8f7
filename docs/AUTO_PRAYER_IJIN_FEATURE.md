# Auto Prayer Ijin Feature

## Overview

Fitur otomatis menambahkan data shalat ijin ketika siswa melakukan scan atau manual entry untuk attendance type `SICK` (Sakit) atau `EXCUSED_ABSENCE` (Ijin Tidak Hadir Sekolah).

## Business Logic

Ketika siswa sakit atau ijin tidak hadir sekolah, mereka secara otomatis tidak dapat melakukan shalat di sekolah. <PERSON><PERSON> karena itu, sistem akan otomatis menambahkan record `IJIN` (Ijin Tidak Shalat) dengan timestamp yang sama untuk memastikan siswa tidak dianggap tidak shalat.

## Technical Implementation

### 1. Modified Files

- `lib/domain/usecases/absence.ts` - Main implementation
- `docs/AUTO_PRAYER_IJIN_FEATURE.md` - Documentation

### 2. Key Components

#### Helper Methods

1. **`shouldAutoCreatePrayerIjin(type: AttendanceType): boolean`**
   - Determines if auto prayer ijin should be created
   - Returns `true` for `SICK` and `EXCUSED_ABSENCE` types

2. **`recordSingleAttendance(...): Promise<Absence>`**
   - Records a single attendance record
   - Handles duplicate detection and force updates
   - Extracted from main `recordAbsence` method for reusability

3. **`autoCreatePrayerIjin(...): Promise<void>`**
   - Creates or updates IJIN record automatically
   - Uses same timestamp as the school attendance
   - Handles duplicate IJIN records gracefully
   - Includes auto-generated reason: "Auto-generated: Tidak hadir sekolah"

#### Transaction Safety

- Uses Drizzle database transaction for atomicity
- If auto IJIN creation fails, the entire operation rolls back
- Ensures data consistency between school and prayer attendance

#### Cache Management

- Invalidates both school and prayer report caches
- Updates unified cache strategy for real-time data
- Handles cache operations for both attendance types

### 3. Flow Diagram

```
Student Scan/Manual Entry (SICK/EXCUSED_ABSENCE)
                    ↓
            Check Student Exists
                    ↓
         Start Database Transaction
                    ↓
        Record School Attendance (SICK/EXCUSED_ABSENCE)
                    ↓
        Check if Auto Prayer Ijin Needed
                    ↓
         Auto-Create Prayer IJIN Record
                    ↓
           Commit Transaction
                    ↓
        Invalidate Related Caches
                    ↓
              Complete
```

## Usage Examples

### Scenario 1: Student Sick
1. Receptionist scans student QR code with type `SICK`
2. System records SICK attendance
3. System automatically creates IJIN prayer record
4. Both records have same timestamp
5. Cache invalidated for both school and prayer reports

### Scenario 2: Student Excused Absence
1. Receptionist manually enters `EXCUSED_ABSENCE` for student
2. System records EXCUSED_ABSENCE attendance
3. System automatically creates IJIN prayer record
4. Both records have same timestamp and reason

### Scenario 3: Duplicate Handling
1. Student already has IJIN record for today
2. Receptionist records SICK attendance
3. If `force=false`: IJIN creation skipped (logged)
4. If `force=true`: IJIN record updated with new timestamp

## Error Handling

### Transaction Rollback
- If IJIN creation fails, entire transaction rolls back
- Original school attendance is not recorded
- Error is propagated to the API layer

### Duplicate Prevention
- Checks for existing IJIN records before creation
- Respects `force` parameter for overwrite behavior
- Logs all auto-creation activities for debugging

### Cache Failure Resilience
- Cache invalidation failures don't affect data recording
- Errors are logged but don't stop the process
- Ensures data consistency over cache consistency

## Logging

The feature includes comprehensive logging:

```
🔄 AUTO PRAYER IJIN: Skipping creation for {uniqueCode} - IJIN already exists
✅ AUTO PRAYER IJIN: Created new IJIN record for {uniqueCode}
✅ AUTO PRAYER IJIN: Updated existing IJIN record for {uniqueCode}
❌ AUTO PRAYER IJIN: Failed to create IJIN record for {uniqueCode}
✅ AUTO PRAYER IJIN CACHE: Invalidated cache for {uniqueCode} - IJIN
```

## Testing Scenarios

### 1. Normal Flow
- Record SICK → Auto IJIN created
- Record EXCUSED_ABSENCE → Auto IJIN created
- Verify both records have same timestamp

### 2. Duplicate Handling
- Create manual IJIN first
- Record SICK with force=false → IJIN creation skipped
- Record SICK with force=true → IJIN updated

### 3. Error Scenarios
- Database failure during IJIN creation → Transaction rollback
- Cache failure → Data still recorded, error logged

### 4. Performance
- Test with high-frequency scanning (2 admins, 3000 students)
- Verify transaction performance impact
- Monitor cache invalidation efficiency

## Security Considerations

- Only authorized roles can record SICK/EXCUSED_ABSENCE
- Auto-generated IJIN records have clear audit trail
- Transaction ensures no partial data states
- All operations logged for accountability

## Future Enhancements

1. **Configurable Auto-Reason**: Allow customization of auto-generated reason
2. **Notification System**: Alert admins when auto IJIN is created
3. **Batch Operations**: Support for bulk auto IJIN creation
4. **Analytics**: Track auto-creation statistics and patterns
