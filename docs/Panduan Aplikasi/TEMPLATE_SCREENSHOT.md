# 📸 Template untuk Menambahkan Screenshot

## Panduan Mengambil Screenshot untuk Dokumentasi

### Screenshot yang <PERSON>

#### 1. Landing Page
- **File**: `landing-page.png`
- **Deskripsi**: <PERSON>aman utama aplikasi
- **Ukuran**: 1200x800px
- **Lokasi dalam dokumen**: Setelah "Pengenalan Aplikasi"

#### 2. Login Pages
- **Student Login**: `student-login.png`
- **Admin Login**: `admin-login.png`
- **Ukuran**: 800x600px
- **Lokasi**: <PERSON> bagian "Akses dan Login"

#### 3. Student App
- **Home/QR Code**: `student-home-qr.png`
- **Profile Page**: `student-profile.png`
- **Ukuran**: 400x700px (mobile view)
- **Lokasi**: <PERSON> bagian "Panduan untuk Siswa"

#### 4. Admin Interfaces
- **Scanner Interface**: `admin-scanner.png`
- **Manual Entry**: `admin-manual-entry.png`
- **Reports Page**: `admin-reports.png`
- **Ukuran**: 1000x700px
- **Lokasi**: Di bagian masing-masing role

#### 5. Super Admin Features
- **User Management**: `super-admin-users.png`
- **Class Management**: `super-admin-classes.png`
- **Prayer Exemption**: `super-admin-exemption.png`
- **Ukuran**: 1200x800px
- **Lokasi**: Di bagian "Panduan untuk Super Admin"

#### 6. Reports & Export
- **Prayer Reports**: `reports-prayer.png`
- **School Reports**: `reports-school.png`
- **Export Dialog**: `export-dialog.png`
- **Excel Output**: `excel-sample.png`
- **Ukuran**: 1000x600px
- **Lokasi**: Di bagian "Laporan dan Export"

### Format Penambahan Screenshot dalam Markdown

```markdown
### Contoh: Login Siswa

![Login Siswa](images/student-login.png)
*Gambar 1: Halaman login untuk siswa*

1. Masukkan username yang diberikan sekolah
2. Masukkan password
3. Klik tombol "Masuk"

---

### Contoh: QR Code Siswa

![QR Code Siswa](images/student-home-qr.png)
*Gambar 2: QR Code unik setiap siswa*

QR Code ini berisi:
- Kode unik siswa
- Informasi enkripsi
- Timestamp validity
```

### Standar Screenshot

#### Kualitas:
- **Resolusi**: Minimal 1080p
- **Format**: PNG (untuk UI) atau JPG (untuk foto)
- **Kompresi**: Optimal untuk web (< 500KB per gambar)

#### Konten:
- **Bersih**: Tidak ada data pribadi real
- **Fokus**: Highlight fitur yang dijelaskan
- **Konsisten**: Gunakan theme yang sama (light/dark)

#### Annotation:
- **Arrow**: Untuk menunjuk elemen penting
- **Numbering**: Untuk step-by-step guide
- **Highlight**: Box merah untuk area fokus

### Tools untuk Screenshot & Editing

#### Windows:
- **Snipping Tool** (built-in)
- **Greenshot** (free, dengan annotation)
- **LightShot** (online sharing)

#### macOS:
- **Screenshot** (Cmd+Shift+4)
- **CleanShot X** (premium, dengan annotation)
- **Skitch** (free, basic editing)

#### Cross-platform:
- **Flameshot** (open source)
- **ShareX** (Windows, advanced)
- **Ksnip** (Linux/Windows)

### Struktur Folder Images

```
docs/
├── images/
│   ├── landing/
│   │   └── landing-page.png
│   ├── auth/
│   │   ├── student-login.png
│   │   └── admin-login.png
│   ├── student/
│   │   ├── home-qr.png
│   │   └── profile.png
│   ├── admin/
│   │   ├── scanner.png
│   │   ├── manual-entry.png
│   │   └── reports.png
│   ├── super-admin/
│   │   ├── user-management.png
│   │   ├── class-management.png
│   │   └── prayer-exemption.png
│   └── reports/
│       ├── prayer-reports.png
│       ├── school-reports.png
│       ├── export-dialog.png
│       └── excel-sample.png
└── PANDUAN_PENGGUNA_SHALAT_YUK.md
```

### Cara Menambahkan ke Dokumentasi

1. **Ambil screenshot** sesuai panduan di atas
2. **Edit dan annotate** jika diperlukan
3. **Simpan** di folder `docs/images/` dengan nama yang sesuai
4. **Update** file `PANDUAN_PENGGUNA_SHALAT_YUK.md`
5. **Test** konversi ke PDF untuk memastikan gambar muncul

### Contoh Implementasi dalam Dokumentasi

```markdown
## 👨‍🎓 Panduan untuk Siswa

### Login Siswa

![Login Siswa](images/auth/student-login.png)
*Halaman login aplikasi siswa*

Langkah-langkah login:

1. **Buka aplikasi** ShalatYuk di browser
2. **Pilih "Masuk ke Akun Siswa"** 
3. **Masukkan username** yang diberikan sekolah
4. **Masukkan password** 
5. **Klik "Masuk"**

> 💡 **Tips**: Simpan bookmark aplikasi di browser untuk akses cepat

### Halaman Home - QR Code Pribadi

![QR Code Siswa](images/student/home-qr.png)
*QR Code unik untuk absensi*

Setelah login berhasil, siswa akan melihat:

- ✅ **QR Code unik** untuk absensi
- ℹ️ **Informasi profil** (nama, kelas, NIS)  
- 📊 **Status absensi hari ini**

#### Cara Menggunakan QR Code:

![Cara Scan QR](images/student/qr-usage.png)
*Proses scanning QR Code*

1. **Tunjukkan QR Code** ke scanner admin/guru
2. **Tunggu bunyi konfirmasi** 
3. **Absensi berhasil** tercatat otomatis

---

### Halaman Profil

![Profil Siswa](images/student/profile.png)
*Halaman profil siswa*

Di halaman profil, siswa dapat:

- ✏️ **Edit informasi pribadi**
- 📱 **Verifikasi nomor WhatsApp** (dengan OTP)
- 📧 **Update email**
- 🔒 **Ganti password**
```

### Checklist Sebelum Publikasi

- [ ] Semua screenshot diambil dengan kualitas tinggi
- [ ] Tidak ada data pribadi yang terekspos
- [ ] Gambar sudah di-compress untuk web
- [ ] Annotation jelas dan mudah dipahami
- [ ] Konsistensi theme (light/dark mode)
- [ ] File naming convention diikuti
- [ ] Folder structure terorganisir
- [ ] Test konversi PDF berhasil
- [ ] Gambar tidak blur saat di-print
- [ ] Alt text tersedia untuk accessibility

### Tips Tambahan

1. **Gunakan data dummy** untuk screenshot, bukan data real
2. **Blur sensitive information** jika terpaksa menggunakan data real
3. **Consistent branding** - pastikan logo dan warna sekolah muncul
4. **Mobile responsive** - ambil screenshot mobile dan desktop
5. **Error states** - sertakan screenshot error message untuk troubleshooting
6. **Loading states** - tunjukkan bagaimana loading indicator terlihat

Dengan mengikuti template ini, dokumentasi akan menjadi lebih visual, mudah dipahami, dan profesional untuk distribusi ke seluruh pengguna aplikasi ShalatYuk.
