# 📖 Buku Panduan Pengguna ShalatYuk

**Sistem Absensi Shalat dan Pulang Sekolah**  
**SMK Negeri 3 Banjarmasin**

---

## 📋 Daftar Isi

1. [Pengenalan Aplikasi](#pengenalan-aplikasi)
2. [<PERSON><PERSON><PERSON> dan <PERSON>](#akses-dan-login)
3. [Panduan untuk Siswa](#panduan-untuk-siswa)
4. [Panduan untuk Super Admin](#panduan-untuk-super-admin)
5. [Panduan untuk Admin](#panduan-untuk-admin)
6. [Panduan untuk Guru](#panduan-untuk-guru)
7. [Panduan untuk Resepsionis](#panduan-untuk-resepsionis)
8. [Sistem Absensi](#sistem-absensi)
9. [Laporan dan Export](#laporan-dan-export)
10. [Troubleshooting](#troubleshooting)
11. [FAQ](#faq)

---

## 🎯 Pengenalan Aplikasi

### Apa itu ShalatYuk?

ShalatYuk adalah aplikasi web modern untuk mengelola absensi shalat dan kehadiran sekolah di SMK Negeri 3 Banjarmasin. Aplikasi ini menggunakan teknologi QR Code untuk memudahkan proses absensi dan menyediakan laporan real-time.

### Fitur Utama

✅ **QR Code Scanning** - Absensi cepat dan akurat  
✅ **Multi-Role Access** - 5 tingkat akses pengguna  
✅ **Real-time Reports** - Laporan langsung dan export Excel  
✅ **Prayer Tracking** - Monitoring shalat Zuhur dan Ashar  
✅ **School Attendance** - Absensi masuk, pulang, dan izin  
✅ **Mobile Responsive** - Dapat diakses dari smartphone dan desktop  

### Teknologi yang Digunakan

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: PostgreSQL, Redis, JWT Authentication
- **Security**: Single device login, session management
- **Time Zone**: WITA (Waktu Indonesia Tengah)

---

## 🔐 Akses dan Login

### URL Aplikasi
- **Website Sekolah**: https://smkn3banjarmasin.sch.id/
- **Aplikasi ShalatYuk**: [URL akan disesuaikan dengan deployment]

### 5 Tingkat Akses Pengguna

| Role | Akses | Fungsi Utama |
|------|-------|--------------|
| **Student** | Aplikasi Siswa | QR Code pribadi, profil |
| **Super Admin** | Akses penuh | Manajemen user, kelas, semua fitur |
| **Admin** | Scanner + Laporan | Scan QR shalat, laporan |
| **Teacher** | Scanner + Laporan | Scan QR masuk sekolah, laporan |
| **Receptionist** | Scanner + Laporan | Scan QR terlambat, izin, sakit |

### Cara Login

1. **Siswa**: Buka `/student` → Masukkan username dan password
2. **Admin/Staff**: Buka `/admin` → Masukkan username dan password sesuai role

> ⚠️ **Penting**: Sistem menerapkan "One Device One Login" - login di perangkat baru akan otomatis logout dari perangkat lama.

---

## 👨‍🎓 Panduan untuk Siswa

### Login Siswa

1. Buka aplikasi ShalatYuk
2. Pilih "Masuk ke Akun Siswa"
3. Masukkan **username** dan **password** yang diberikan sekolah
4. Klik "Masuk"

### Halaman Home - QR Code Pribadi

Setelah login, siswa akan melihat:
- **QR Code unik** untuk absensi
- **Informasi profil** (nama, kelas, NIS)
- **Status absensi hari ini**

#### Cara Menggunakan QR Code:
1. Tunjukkan QR Code ke scanner admin/guru
2. Tunggu bunyi konfirmasi
3. Absensi berhasil tercatat

### Halaman Profil

Di halaman profil, siswa dapat:
- ✏️ **Edit informasi pribadi**
- 📱 **Verifikasi nomor WhatsApp** (dengan OTP)
- 📧 **Update email**
- 🔒 **Ganti password**

#### Cara Ganti Password:
1. Masuk ke halaman Profil
2. Klik "Ganti Password"
3. Masukkan password lama
4. Masukkan password baru
5. Konfirmasi password baru
6. Klik "Simpan"

### Lupa Password

1. Di halaman login, klik "Lupa Password"
2. Masukkan username
3. Sistem akan mengirim kode OTP ke WhatsApp terdaftar
4. Masukkan kode OTP
5. Buat password baru
6. Login dengan password baru

---

## 👑 Panduan untuk Super Admin

Super Admin memiliki akses penuh ke semua fitur sistem.

### Dashboard Super Admin

Setelah login, Super Admin dapat mengakses:
- 🏠 **Home** - Scanner QR Code
- 📊 **Reports** - Laporan lengkap
- 👥 **User Management** - Kelola pengguna
- 🏫 **Class Management** - Kelola kelas
- 👤 **Profile** - Pengaturan akun

### User Management

#### Menambah Pengguna Baru:
1. Masuk ke menu "User Management"
2. Klik "Tambah Pengguna"
3. Isi form:
   - **Nama lengkap**
   - **Username** (unik)
   - **Password**
   - **Role** (Student/Admin/Teacher/Receptionist)
   - **Kelas** (untuk siswa)
   - **NIS** (opsional)
4. Klik "Simpan"

#### Bulk Operations:
- **Import Excel** - Upload file Excel dengan format template
- **Export Data** - Download data pengguna
- **Reset Password** - Reset password massal
- **Force Logout** - Paksa logout pengguna tertentu

### Class Management

#### Mengelola Kelas:
1. Masuk ke menu "Class Management"
2. **Tambah Kelas Baru**:
   - Klik "Tambah Kelas"
   - Masukkan nama kelas (contoh: "XII RPL 1")
   - Klik "Simpan"
3. **Edit Kelas**:
   - Klik ikon edit pada kelas
   - Ubah nama kelas
   - Klik "Simpan"
4. **Hapus Kelas**:
   - Klik ikon hapus
   - Konfirmasi penghapusan

### Pengecualian Shalat (Prayer Exemption)

Super Admin dapat mengatur pengecualian shalat untuk situasi khusus:

#### Kapan Menggunakan:
✅ Ujian pagi (pulang sebelum Zuhur)  
✅ Pulang cepat karena kegiatan sekolah  
✅ Kondisi darurat atau cuaca ekstrem  
✅ Field trip atau kunjungan industri  

#### Cara Mengatur:
1. Masuk ke menu "Prayer Exemption"
2. Pilih tanggal
3. Pilih jenis pengecualian:
   - **Zuhr Only** - Hanya Zuhur dikecualikan
   - **Asr Only** - Hanya Ashar dikecualikan
   - **Both** - Kedua shalat dikecualikan
4. Masukkan alasan
5. Klik "Simpan"

---

## 🔧 Panduan untuk Admin

Admin memiliki akses ke Scanner dan Reports untuk shalat.

### Scanner QR Code

#### Jenis Absensi yang Dapat Diproses:
- ✅ **Shalat Zuhur** (12:00-13:00 WITA)
- ✅ **Shalat Ashar** (16:00-17:00 WITA)
- ✅ **Pulang Sekolah** (17:00-18:00 WITA)
- ✅ **Ijin Tidak Shalat** (dengan alasan)

#### Cara Menggunakan Scanner:
1. Masuk ke halaman "Home"
2. Kamera akan aktif otomatis
3. Arahkan kamera ke QR Code siswa
4. Sistem akan memproses otomatis
5. Dengarkan bunyi konfirmasi:
   - **Berhasil**: Bunyi sukses
   - **Gagal**: Bunyi error + pesan kesalahan

#### Manual Input (Jika QR Code Tidak Terbaca):
1. Klik tab "Manual Entry"
2. Masukkan kode unik siswa
3. Pilih jenis absensi
4. Masukkan alasan (jika diperlukan)
5. Klik "Submit"

### Aturan Khusus Pulang Sekolah

⚠️ **Penting**: Siswa harus menyelesaikan shalat Zuhur DAN Ashar sebelum bisa absen pulang.

**Pengecualian**: Jika Super Admin telah mengatur prayer exemption untuk hari tersebut.

---

## 👨‍🏫 Panduan untuk Guru

Guru memiliki akses ke Scanner untuk absensi masuk sekolah dan Reports.

### Scanner untuk Guru

#### Jenis Absensi:
- ✅ **Masuk Sekolah** (06:00-07:00 WITA)

#### Cara Menggunakan:
1. Masuk ke halaman "Home"
2. Scan QR Code siswa yang masuk
3. Sistem otomatis mencatat waktu masuk
4. Bunyi konfirmasi akan berbunyi

### Waktu Operasional

| Jenis Absensi | Waktu | Keterangan |
|---------------|-------|------------|
| Masuk Normal | 06:00-07:00 | Masuk tepat waktu |
| Terlambat | 08:00-09:00 | Ditangani Resepsionis |

---

## 👩‍💼 Panduan untuk Resepsionis

Resepsionis menangani absensi khusus dan situasi non-normal.

### Jenis Absensi Resepsionis

- ✅ **Masuk Terlambat** (08:00-09:00 WITA)
- ✅ **Izin** (06:00-09:00 WITA)
- ✅ **Izin Sementara**
- ✅ **Kembali dari Izin**
- ✅ **Sakit** (06:00-09:00 WITA)

### Manual Entry untuk Resepsionis

Resepsionis memiliki akses khusus ke tab "Manual Entry" untuk absensi yang tidak menggunakan QR Code:

#### Cara Menggunakan:
1. Klik tab "Manual Entry"
2. Masukkan kode unik atau cari nama siswa
3. Pilih jenis absensi
4. **Wajib masukkan alasan** untuk:
   - Izin
   - Sakit
   - Izin Sementara
5. Klik "Submit"

### Contoh Penggunaan

**Skenario**: Siswa sakit dan orang tua menelepon sekolah
1. Buka Manual Entry
2. Cari nama siswa
3. Pilih "Sakit"
4. Masukkan alasan: "Demam tinggi, dikonfirmasi orang tua via telepon"
5. Submit

---

## ⏰ Sistem Absensi

### Jadwal Absensi

| Waktu | Jenis | PIC | Lokasi Scanner |
|-------|-------|-----|----------------|
| 06:00-07:00 | Masuk Sekolah | Guru | Gerbang Masuk |
| 08:00-09:00 | Terlambat/Izin/Sakit | Resepsionis | Ruang TU |
| 12:00-13:00 | Shalat Zuhur | Admin | Mushola |
| 16:00-17:00 | Shalat Ashar | Admin | Mushola |
| 17:00-18:00 | Pulang Sekolah | Admin | Gerbang Keluar |

### Aturan Sistem

1. **One Device One Login** - Satu akun hanya bisa login di satu perangkat
2. **Prayer Requirement** - Harus shalat Zuhur + Ashar sebelum pulang
3. **Time-based Access** - Setiap absensi memiliki waktu yang ditentukan
4. **Reason Required** - Alasan wajib untuk izin, sakit, dan izin sementara

### Status Absensi

| Status | Kode | Keterangan |
|--------|------|------------|
| Hadir | H | Masuk normal |
| Terlambat | T | Masuk setelah jam 07:00 |
| Izin | I | Tidak masuk dengan izin |
| Sakit | S | Tidak masuk karena sakit |
| Alpha | A | Tidak masuk tanpa keterangan |
| Zuhur | Z | Shalat Zuhur |
| Ashar | As | Shalat Ashar |
| Pulang | P | Pulang sekolah |

---

## 📊 Laporan dan Export

### Jenis Laporan

#### 1. Prayer Reports (Laporan Shalat)
- **Akses**: Super Admin, Admin
- **Isi**: Data shalat Zuhur, Ashar, dan pulang
- **Filter**: Hari ini, Kemarin, 7 hari, Bulan ini, Custom

#### 2. School Reports (Laporan Sekolah)
- **Akses**: Super Admin, Teacher, Receptionist
- **Isi**: Data masuk, terlambat, izin, sakit
- **Filter**: Hari ini, Kemarin, 7 hari, Bulan ini, Custom

### Cara Mengakses Laporan

1. Login sesuai role
2. Masuk ke menu "Reports"
3. Pilih jenis laporan (Prayer/School)
4. Pilih filter waktu
5. Klik "Tampilkan Data"

### Export ke Excel

#### Format Export Tersedia:
- **Daily List** - Daftar harian per tanggal
- **Weekly Summary** - Ringkasan mingguan
- **Monthly Matrix** - Matrix bulanan per kelas
- **Yearly Matrix** - Matrix tahunan (per kelas)

#### Cara Export:
1. Di halaman laporan, klik "Export"
2. Pilih format export
3. Pilih periode
4. Pilih kelas (untuk matrix)
5. Klik "Download Excel"

### Matrix Export Format

**Monthly Matrix** menampilkan:
- Kolom: Tanggal (1, 2, 3, ..., 31)
- Baris: Nama siswa
- Cell: Status kode (H, Z, A, I, S, T)
- Tab: Per kelas

**Yearly Matrix** menampilkan:
- Tab per bulan (Jan, Feb, Mar, ...)
- Format sama dengan monthly matrix
- Hanya untuk satu kelas terpilih

---

## 🔧 Troubleshooting

### Masalah Umum dan Solusi

#### 1. Tidak Bisa Login
**Gejala**: Error saat login
**Solusi**:
- Pastikan username dan password benar
- Cek koneksi internet
- Hapus cache browser
- Hubungi admin jika masih bermasalah

#### 2. QR Code Tidak Terbaca
**Gejala**: Scanner tidak mendeteksi QR Code
**Solusi**:
- Pastikan QR Code tidak buram atau rusak
- Cek pencahayaan ruangan
- Bersihkan kamera
- Gunakan Manual Entry sebagai alternatif

#### 3. Tidak Bisa Pulang (Prayer Requirement)
**Gejala**: Error "Harus shalat Zuhur dan Ashar dulu"
**Solusi**:
- Pastikan sudah absen shalat Zuhur dan Ashar
- Cek dengan admin apakah ada prayer exemption
- Hubungi Super Admin jika ada masalah sistem

#### 4. Data Laporan Tidak Muncul
**Gejala**: Laporan kosong atau tidak update
**Solusi**:
- Refresh halaman (F5)
- Cek filter tanggal
- Tunggu beberapa detik (sistem menggunakan cache)
- Hubungi admin jika data masih tidak muncul

#### 5. Lupa Password
**Solusi**:
- Gunakan fitur "Lupa Password" di halaman login
- Pastikan nomor WhatsApp terdaftar dan aktif
- Hubungi Super Admin jika tidak bisa reset

### Error Messages dan Artinya

| Error Message | Arti | Solusi |
|---------------|------|--------|
| "Student not found" | Kode siswa tidak ditemukan | Cek kode unik atau hubungi admin |
| "Already recorded today" | Sudah absen hari ini | Normal, tidak perlu absen lagi |
| "Outside time range" | Di luar jam absensi | Tunggu waktu yang tepat |
| "Prayer requirement not met" | Belum shalat lengkap | Lengkapi shalat Zuhur dan Ashar |
| "Session expired" | Sesi login habis | Login ulang |

---

## ❓ FAQ (Frequently Asked Questions)

### Umum

**Q: Apakah bisa login di beberapa perangkat sekaligus?**
A: Tidak. Sistem menerapkan "One Device One Login". Login di perangkat baru akan otomatis logout dari perangkat lama.

**Q: Bagaimana jika lupa password?**
A: Gunakan fitur "Lupa Password" di halaman login. Sistem akan mengirim OTP ke WhatsApp terdaftar.

**Q: Apakah data tersimpan secara real-time?**
A: Ya, semua absensi tersimpan langsung ke database dan muncul di laporan secara real-time.

### Untuk Siswa

**Q: QR Code saya tidak bisa dibaca, bagaimana?**
A: Hubungi admin/guru yang bertugas. Mereka bisa input manual menggunakan kode unik Anda.

**Q: Bisakah ganti foto profil?**
A: Saat ini belum tersedia fitur upload foto. Fokus pada data absensi.

**Q: Bagaimana cara cek absensi saya hari ini?**
A: Di halaman Home aplikasi siswa, akan tampil status absensi hari ini.

### Untuk Admin/Staff

**Q: Bagaimana jika kamera tidak berfungsi?**
A: Gunakan tab "Manual Entry" untuk input kode unik siswa secara manual.

**Q: Bisakah edit data absensi yang salah?**
A: Hanya Super Admin yang bisa edit/hapus data absensi. Hubungi Super Admin jika ada kesalahan.

**Q: Laporan tidak muncul, kenapa?**
A: Coba refresh halaman atau tunggu beberapa detik. Sistem menggunakan cache untuk performa.

### Untuk Super Admin

**Q: Bagaimana cara backup data?**
A: Export semua data ke Excel secara berkala. Koordinasi dengan tim IT untuk backup database.

**Q: Bisakah import data siswa dari Excel?**
A: Ya, gunakan fitur "Bulk Import" di User Management dengan template yang disediakan.

**Q: Bagaimana mengatasi siswa yang tidak bisa pulang karena belum shalat?**
A: Gunakan fitur "Prayer Exemption" untuk situasi khusus seperti ujian atau pulang cepat.

---

## 📞 Kontak dan Dukungan

### Tim Support ShalatYuk
- **Email**: [<EMAIL>]
- **WhatsApp**: +62 812-3456-7890
- **Website**: https://smkn3banjarmasin.sch.id/

### Jam Operasional Support
- **Senin - Jumat**: 07:00 - 16:00 WITA
- **Sabtu**: 07:00 - 12:00 WITA
- **Minggu**: Libur

### Escalation Path
1. **Level 1**: Admin sekolah
2. **Level 2**: Super Admin sistem
3. **Level 3**: Tim IT sekolah
4. **Level 4**: Developer (untuk bug critical)

---

**© 2025 SMK Negeri 3 Banjarmasin - ShalatYuk Application**  
*Dokumentasi ini dibuat untuk memudahkan penggunaan aplikasi ShalatYuk*
