# 📄 Cara Mengkonversi Dokumentasi ke PDF

## Metode 1: Menggunakan Pandoc (Recommended)

### Instalasi Pandoc

**Windows:**
```bash
# Menggunakan Chocolatey
choco install pandoc

# Atau download dari https://pandoc.org/installing.html
```

**macOS:**
```bash
# Menggunakan Homebrew
brew install pandoc

# Untuk LaTeX engine (opsional, untuk styling yang lebih baik)
brew install --cask mactex
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get install pandoc
sudo apt-get install texlive-latex-base texlive-fonts-recommended texlive-latex-extra
```

### Konversi ke PDF

```bash
# Konversi dasar
pandoc docs/PANDUAN_PENGGUNA_SHALAT_YUK.md -o PANDUAN_SHALAT_YUK.pdf

# Konversi dengan styling yang lebih baik
pandoc docs/PANDUAN_PENGGUNA_SHALAT_YUK.md \
  --pdf-engine=xelatex \
  --variable mainfont="Arial" \
  --variable fontsize=11pt \
  --variable geometry:margin=2cm \
  --toc \
  --toc-depth=3 \
  --highlight-style=github \
  -o PANDUAN_SHALAT_YUK.pdf
```

### Opsi Styling Lanjutan

```bash
# Dengan cover page dan header/footer
pandoc docs/PANDUAN_PENGGUNA_SHALAT_YUK.md \
  --pdf-engine=xelatex \
  --variable mainfont="Arial" \
  --variable fontsize=11pt \
  --variable geometry:margin=2.5cm \
  --variable documentclass=report \
  --variable classoption=oneside \
  --variable title="Buku Panduan ShalatYuk" \
  --variable author="SMK Negeri 3 Banjarmasin" \
  --variable date="2025" \
  --toc \
  --toc-depth=3 \
  --number-sections \
  --highlight-style=github \
  --include-in-header=header.tex \
  -o PANDUAN_SHALAT_YUK.pdf
```

## Metode 2: Menggunakan Markdown to PDF Online

### Layanan Online Gratis:
1. **Markdown to PDF** - https://md-to-pdf.fly.dev/
2. **Pandoc Try** - https://pandoc.org/try/
3. **Dillinger** - https://dillinger.io/

### Cara Menggunakan:
1. Buka salah satu layanan di atas
2. Copy-paste isi file `PANDUAN_PENGGUNA_SHALAT_YUK.md`
3. Pilih output format "PDF"
4. Download hasil konversi

## Metode 3: Menggunakan VS Code Extension

### Extension yang Direkomendasikan:
- **Markdown PDF** by yzane
- **Markdown All in One** by Yu Zhang

### Cara Menggunakan:
1. Install extension "Markdown PDF"
2. Buka file `PANDUAN_PENGGUNA_SHALAT_YUK.md` di VS Code
3. Tekan `Ctrl+Shift+P` (Windows/Linux) atau `Cmd+Shift+P` (Mac)
4. Ketik "Markdown PDF: Export (pdf)"
5. Pilih lokasi penyimpanan

## Metode 4: Menggunakan Chrome/Browser

### Langkah-langkah:
1. Install extension "Markdown Viewer" di Chrome
2. Buka file markdown di browser
3. Tekan `Ctrl+P` untuk print
4. Pilih "Save as PDF" sebagai destination
5. Atur margin dan layout sesuai kebutuhan
6. Klik "Save"

## Kustomisasi Styling

### Membuat File CSS Custom (opsional)

Buat file `style.css`:

```css
/* style.css */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    margin-top: 30px;
    margin-bottom: 15px;
}

h1 {
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

h2 {
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 8px;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin: 20px 0;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

blockquote {
    border-left: 4px solid #3498db;
    margin: 20px 0;
    padding: 10px 20px;
    background-color: #f8f9fa;
}

.emoji {
    font-size: 1.2em;
}

@media print {
    body {
        font-size: 12pt;
    }
    
    h1 {
        page-break-before: always;
    }
    
    table {
        page-break-inside: avoid;
    }
}
```

### Menggunakan CSS dengan Pandoc:

```bash
pandoc docs/PANDUAN_PENGGUNA_SHALAT_YUK.md \
  --css=style.css \
  --pdf-engine=wkhtmltopdf \
  -o PANDUAN_SHALAT_YUK.pdf
```

## Tips untuk Hasil PDF yang Optimal

### 1. Persiapan Konten
- Pastikan semua emoji dan karakter khusus kompatibel
- Periksa format tabel agar tidak terpotong
- Atur heading hierarchy dengan benar

### 2. Pengaturan Layout
- Gunakan margin yang cukup (2-2.5cm)
- Pilih font yang mudah dibaca (Arial, Calibri, Times New Roman)
- Atur line spacing yang nyaman (1.5 atau 1.6)

### 3. Table of Contents
- Aktifkan TOC untuk navigasi yang mudah
- Atur depth TOC maksimal 3 level
- Gunakan page numbers

### 4. Kualitas Gambar
- Jika menambahkan screenshot, gunakan resolusi tinggi
- Format PNG atau JPG dengan kompresi optimal
- Pastikan gambar tidak blur saat di-print

## Troubleshooting

### Masalah Umum:

**1. Font tidak muncul dengan benar**
```bash
# Pastikan font terinstall di sistem
# Untuk Windows, copy font ke C:\Windows\Fonts\
# Untuk macOS, copy ke /Library/Fonts/
```

**2. Tabel terpotong**
```bash
# Gunakan opsi landscape untuk tabel lebar
pandoc --variable geometry:landscape ...
```

**3. Emoji tidak muncul**
```bash
# Gunakan font yang support emoji
--variable mainfont="Segoe UI Emoji"
```

**4. File terlalu besar**
```bash
# Kompres gambar sebelum konversi
# Atau gunakan opsi kompresi PDF
```

## Hasil Akhir

Setelah konversi berhasil, Anda akan mendapatkan:
- **File PDF** dengan format profesional
- **Table of Contents** yang dapat diklik
- **Styling** yang konsisten dan mudah dibaca
- **Page numbers** dan header/footer
- **Print-ready** format untuk distribusi

## Rekomendasi Final

Untuk hasil terbaik, gunakan **Metode 1 (Pandoc)** dengan perintah styling lanjutan. Ini memberikan kontrol penuh atas format dan menghasilkan PDF berkualitas profesional yang siap untuk distribusi ke seluruh pengguna aplikasi ShalatYuk.
