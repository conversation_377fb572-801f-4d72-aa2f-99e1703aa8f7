# 📖 Panduan Penggunaan Pengecualian Shalat

## 🎯 Apa itu Pengecualian Shalat?

Pengecualian Shalat adalah fitur yang memungkinkan Super Admin untuk mengatur situasi khusus dimana siswa tidak perlu melakukan shalat Zuhur atau Ashar sebelum absen pulang.

### Kapan Menggunakan Fitur Ini?

✅ **Situasi yang Tepat:**
- <PERSON><PERSON><PERSON> pagi (siswa pulang sebelum waktu Zuhur)
- Pulang cepat karena kegiatan sekolah (sebelum waktu Ashar)
- Kondisi darurat atau cuaca ekstrem
- Kegiatan field trip atau kunjungan industri
- Hari libur khusus atau peringatan

❌ **Jangan Digunakan Untuk:**
- Siswa yang malas shalat
- Permintaan individual tanpa alasan jelas
- Situasi normal sehari-hari

## 🔐 Akses dan Keamanan

### Siapa yang Bisa Menggunakan?
- **Hanya Super Admin** yang dapat mengelola pengecualian shalat
- <PERSON><PERSON> biasa **tidak memiliki akses** ke fitur ini
- <PERSON><PERSON><PERSON> per<PERSON> **tercatat** dengan timestamp dan pembuat

### Cara Mengakses
1. Login sebagai Super Admin
2. Buka halaman Scanner Admin
3. Klik tombol **"Pengecualian Shalat"** di header (ikon ⚙️)
4. Halaman akan terbuka di tab baru

## 📱 Cara Menggunakan

### 1. Membuat Pengecualian Baru

#### Langkah-langkah:
1. Klik tombol **"Tambah Pengecualian"**
2. Isi form dengan data berikut:

**Jenis Pengecualian:**
- **Semua Siswa**: Berlaku untuk seluruh siswa di sekolah
- **Kelas Tertentu**: Hanya berlaku untuk kelas yang dipilih

**Kelas (jika memilih "Kelas Tertentu"):**
- Pilih kelas dari dropdown yang tersedia

**Tanggal:**
- Pilih tanggal kapan pengecualian berlaku
- Hanya bisa memilih tanggal hari ini atau masa depan

**Jenis Shalat:**
- **Shalat Zuhur**: Hanya Zuhur yang dikecualikan
- **Shalat Ashar**: Hanya Ashar yang dikecualikan  
- **Zuhur & Ashar**: Kedua shalat dikecualikan

**Alasan:**
- Tulis alasan yang jelas (minimal 10 karakter)
- Contoh: "Ujian nasional - semua siswa pulang pukul 11:00"

3. Klik **"Simpan"**

### 2. Melihat Daftar Pengecualian

Halaman utama menampilkan semua pengecualian yang telah dibuat dengan informasi:
- Jenis pengecualian (Semua Siswa / Kelas Tertentu)
- Tanggal berlaku
- Jenis shalat yang dikecualikan
- Alasan pengecualian
- Tanggal pembuatan

### 3. Menghapus Pengecualian

1. Cari pengecualian yang ingin dihapus
2. Klik tombol **🗑️ (Trash)** di sebelah kanan
3. Konfirmasi penghapusan
4. Pengecualian akan dihapus permanen

## 📋 Contoh Penggunaan

### Contoh 1: Ujian Nasional
```
Jenis Pengecualian: Semua Siswa
Tanggal: 15 Maret 2024
Jenis Shalat: Zuhur & Ashar
Alasan: Ujian Nasional - semua siswa pulang pukul 10:30
```

### Contoh 2: Field Trip Kelas
```
Jenis Pengecualian: Kelas Tertentu
Kelas: XII IPA 1
Tanggal: 20 Maret 2024
Jenis Shalat: Shalat Ashar
Alasan: Kunjungan industri ke PT. ABC - pulang pukul 15:30
```

### Contoh 3: Cuaca Ekstrem
```
Jenis Pengecualian: Semua Siswa
Tanggal: 25 Maret 2024
Jenis Shalat: Shalat Ashar
Alasan: Hujan deras dan banjir - siswa dipulangkan lebih awal
```

## ⚡ Cara Kerja Sistem

### Validasi Otomatis
1. Ketika siswa scan QR untuk absen pulang
2. Sistem mengecek apakah siswa sudah shalat Zuhur dan Ashar
3. **Jika ada pengecualian yang berlaku:**
   - Siswa diizinkan pulang meski belum shalat
   - Sistem mencatat absen pulang normal
4. **Jika tidak ada pengecualian:**
   - Siswa harus shalat dulu sebelum bisa pulang
   - Muncul pesan error jika belum shalat

### Prioritas Pengecualian
1. **Ijin Manual** (prioritas tertinggi)
2. **Pengecualian Shalat** (fitur baru)
3. **Validasi Normal** (harus shalat dulu)

## 🚨 Hal Penting yang Perlu Diperhatikan

### ⚠️ Peringatan
- **Gunakan dengan bijak** - fitur ini untuk situasi khusus saja
- **Dokumentasi lengkap** - selalu tulis alasan yang jelas
- **Tidak bisa diubah** - pengecualian yang sudah dibuat tidak bisa diedit
- **Tanggal masa lalu** - tidak bisa membuat pengecualian untuk hari yang sudah lewat

### 📊 Monitoring
- Semua pengecualian tercatat dalam sistem
- Super Admin dapat melihat riwayat penggunaan
- Data dapat digunakan untuk evaluasi kebijakan

### 🔄 Backup Plan
Jika sistem pengecualian bermasalah:
1. Gunakan fitur **"Entry Manual"** dengan jenis "Ijin"
2. Hubungi tim IT untuk perbaikan sistem
3. Catat manual untuk dokumentasi

## 🆘 Troubleshooting

### Masalah Umum

**Q: Tombol "Pengecualian Shalat" tidak muncul**
A: Pastikan Anda login sebagai Super Admin, bukan Admin biasa

**Q: Tidak bisa memilih tanggal kemarin**
A: Sistem tidak mengizinkan pengecualian untuk tanggal yang sudah lewat

**Q: Error saat menyimpan pengecualian**
A: Periksa koneksi internet dan pastikan semua field terisi dengan benar

**Q: Siswa masih tidak bisa pulang meski ada pengecualian**
A: Periksa apakah tanggal, kelas, dan jenis shalat sudah sesuai

### Kontak Support
Jika mengalami masalah teknis:
- Hubungi Tim IT Sekolah
- Sertakan screenshot error (jika ada)
- Jelaskan langkah yang sudah dilakukan

## 📈 Tips Penggunaan Efektif

### 🎯 Best Practices
1. **Buat pengecualian sehari sebelumnya** untuk persiapan yang baik
2. **Gunakan alasan yang spesifik** untuk dokumentasi yang jelas
3. **Koordinasi dengan guru** sebelum membuat pengecualian kelas
4. **Monitor penggunaan** secara berkala untuk evaluasi

### 📝 Template Alasan
- "Ujian [mata pelajaran] - siswa pulang pukul [waktu]"
- "Kegiatan [nama kegiatan] - [kelas] pulang lebih awal"
- "Kondisi cuaca ekstrem - sekolah ditutup lebih awal"
- "Peringatan [nama hari] - libur khusus"

---

**💡 Ingat:** Fitur ini dibuat untuk membantu dalam situasi khusus. Gunakan dengan bijak dan selalu prioritaskan pendidikan karakter siswa dalam beribadah.
