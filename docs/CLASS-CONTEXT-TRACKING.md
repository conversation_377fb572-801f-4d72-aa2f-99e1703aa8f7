# 📚 Class Context Tracking Feature Documentation

## 🎯 Overview

The Class Context Tracking feature preserves historical class information in attendance records, preventing data loss when students change classes (e.g., grade progression from X → XI → XII). This ensures reports display accurate class information at the time attendance was recorded.

## 🚨 Problem Solved

### Before Implementation
```
Student: Budi
- 2023: Class X IPA 1 (attendance recorded)
- 2024: Class XI IPA 1 (student promoted)
- 2025: Class XII IPA 1 (student promoted again)

Problem: 2023 attendance reports show "XII IPA 1" instead of "X IPA 1"
```

### After Implementation
```
Student: Budi
- 2023 attendance: Shows "X IPA 1" ✅ (preserved in attendance record)
- 2024 attendance: Shows "XI IPA 1" ✅ (preserved in attendance record)  
- 2025 attendance: Shows "XII IPA 1" ✅ (current class)

Result: Historical accuracy maintained across all reports
```

## 🏗️ Architecture

### Database Schema Changes

#### New Columns in `absences` Table
```sql
-- Historical class tracking columns
class_id INTEGER REFERENCES classes(id) ON DELETE SET NULL,
class_name VARCHAR(10) -- Denormalized for performance
```

#### New Indexes for Performance
```sql
-- Class-based filtering
CREATE INDEX idx_absences_class_id ON absences(class_id);
CREATE INDEX idx_absences_class_name ON absences(class_name);

-- Composite indexes for reports
CREATE INDEX idx_absences_class_recorded_at ON absences(class_id, recorded_at);
CREATE INDEX idx_absences_class_name_type ON absences(class_name, type);
```

### Application Layer Changes

#### Domain Entity Updates
```typescript
interface Absence {
  // Existing fields...
  
  // ✅ New historical tracking fields
  classId?: number    // Foreign key to classes table
  className?: string  // Class name at time of attendance
}
```

#### Repository Layer
```typescript
class AbsenceRepository {
  async create(data: CreateAbsenceDTO): Promise<Absence> {
    // Now includes classId and className in insert
  }
  
  async update(id: number, data: UpdateAbsenceDTO): Promise<Absence> {
    // Now supports updating class context
  }
}
```

#### Use Cases Layer
```typescript
class AbsenceUseCases {
  async recordAbsence(uniqueCode: string, type: AttendanceType): Promise<Absence> {
    // Automatically fetches and includes student's current class information
    const student = await this.studentRepo.findByUniqueCode(uniqueCode)
    
    // Records attendance with class context
    return this.absenceRepo.create({
      uniqueCode,
      type,
      recordedAt: getCurrentWITATime(),
      classId: student.classId,      // ✅ Historical tracking
      className: student.className,  // ✅ Historical tracking
    })
  }
}
```

## 📊 Data Migration Strategy

### Safe Backfill for 2025 Data
Since all existing data is from 2025 (application launched in 2025), we can safely backfill all records with current class assignments:

```sql
-- Safe backfill: All data is from current year
UPDATE absences
SET 
  class_id = u.class_id,
  class_name = c.name
FROM users u
INNER JOIN classes c ON u.class_id = c.id
WHERE absences.unique_code = u.unique_code
  AND absences.class_id IS NULL;
```

### Future-Proof Design
For future years when students change classes, the system will:
1. ✅ Preserve historical class information automatically
2. ✅ Show accurate class names in all reports
3. ✅ Support class progression analytics

## 🔍 Usage Examples

### Recording Attendance (Automatic Class Context)
```typescript
// Before: Only attendance data recorded
const absence = await absenceUseCases.recordAbsence('STUDENT001', AttendanceType.ZUHR)

// After: Attendance + class context recorded automatically
const absence = await absenceUseCases.recordAbsence('STUDENT001', AttendanceType.ZUHR)
console.log(absence.className) // "X IPA 1" (preserved for history)
```

### API Response (Enhanced with Class Context)
```json
{
  "id": 123,
  "uniqueCode": "STUDENT001",
  "type": "Zuhr",
  "recordedAt": "2025-01-31T12:30:00Z",
  "classId": 1,
  "className": "X IPA 1"
}
```

### Reports (Historical Accuracy)
```typescript
// Daily report shows class at time of attendance
const dailyReport = await getAttendanceSummary('2025-01-31')
// Result: Shows "X IPA 1" for students who were in that class on that date

// Monthly report shows progression if student changed classes
const monthlyReport = await getAttendanceSummary('2025-01')
// Result: Shows different class names if student changed classes during month
```

## 📈 Performance Benefits

### Before (with JOINs)
```sql
-- Slow: Requires JOIN with classes table
SELECT u.name, c.name as class_name, COUNT(*) 
FROM absences a
JOIN users u ON a.unique_code = u.unique_code
JOIN classes c ON u.class_id = c.id
WHERE a.recorded_at >= '2025-01-01'
GROUP BY u.name, c.name;
```

### After (Direct Access)
```sql
-- Fast: Direct access to class_name
SELECT u.name, a.class_name, COUNT(*) 
FROM absences a
JOIN users u ON a.unique_code = u.unique_code
WHERE a.recorded_at >= '2025-01-01'
  AND a.class_name IS NOT NULL
GROUP BY u.name, a.class_name;
```

### Performance Improvements
- ✅ **Reduced JOINs**: Fewer table joins in report queries
- ✅ **Better Indexing**: Direct indexes on class_name for filtering
- ✅ **Faster Aggregation**: Group by class_name without JOIN overhead
- ✅ **Cached Class Names**: No need to resolve class IDs to names

## 🛡️ Data Integrity

### Foreign Key Constraints
```sql
-- Ensures referential integrity
ALTER TABLE absences ADD CONSTRAINT absences_class_id_classes_id_fk 
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL;
```

### Consistency Checks
```sql
-- Verify data consistency
SELECT COUNT(*) as inconsistent_records
FROM absences a
LEFT JOIN classes c ON a.class_id = c.id
WHERE a.class_id IS NOT NULL 
  AND (a.class_name != c.name OR c.name IS NULL);
-- Should return 0
```

### Backward Compatibility
- ✅ **Nullable Fields**: New columns are nullable for backward compatibility
- ✅ **Graceful Degradation**: System works with mixed data (old + new)
- ✅ **No Breaking Changes**: Existing functionality remains intact

## 🧪 Testing Strategy

### Unit Tests
```typescript
describe('Class Context Tracking', () => {
  it('should record attendance with class context', async () => {
    const absence = await absenceUseCases.recordAbsence('STUDENT001', AttendanceType.ZUHR)
    expect(absence.classId).toBeDefined()
    expect(absence.className).toBeDefined()
  })
})
```

### Integration Tests
- ✅ Repository layer with database
- ✅ Use cases with real student data
- ✅ API endpoints with class context
- ✅ Report generation with historical data

### Performance Tests
- ✅ Query performance with indexes
- ✅ Bulk operations with class context
- ✅ Report generation speed
- ✅ Memory usage optimization

## 📋 Monitoring & Maintenance

### Key Metrics
```sql
-- Monitor backfill progress
SELECT 
  COUNT(*) as total_records,
  COUNT(class_id) as records_with_class_id,
  ROUND(COUNT(class_id) * 100.0 / COUNT(*), 2) as completion_percentage
FROM absences;
```

### Health Checks
```sql
-- Daily consistency check
SELECT 
  DATE(recorded_at) as date,
  COUNT(*) as total_records,
  COUNT(class_name) as records_with_class_name
FROM absences 
WHERE recorded_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(recorded_at)
ORDER BY date DESC;
```

### Maintenance Tasks
- 🔄 **Weekly**: Verify data consistency
- 🔄 **Monthly**: Analyze query performance
- 🔄 **Quarterly**: Review index usage
- 🔄 **Yearly**: Plan for class progression scenarios

## 🚀 Future Enhancements

### Phase 2: Advanced Analytics
- 📊 Student progression tracking (X → XI → XII)
- 📈 Class performance comparisons over time
- 📋 Historical attendance pattern analysis

### Phase 3: Automated Class Updates
- 🔄 Bulk class promotion at year-end
- 📅 Academic year management
- 🎓 Graduation tracking

### Phase 4: Enhanced Reporting
- 📊 Multi-year progression reports
- 📈 Class-based trend analysis
- 📋 Historical accuracy validation

## 📞 Support & Troubleshooting

### Common Issues

#### Issue: Backfill not completing
```sql
-- Check for orphaned records
SELECT COUNT(*) FROM absences a
LEFT JOIN users u ON a.unique_code = u.unique_code
WHERE u.unique_code IS NULL;
```

#### Issue: Performance degradation
```sql
-- Check index usage
EXPLAIN (ANALYZE, BUFFERS) 
SELECT class_name, COUNT(*) 
FROM absences 
WHERE class_name = 'X IPA 1' 
GROUP BY class_name;
```

#### Issue: Data inconsistency
```sql
-- Fix inconsistent records
UPDATE absences 
SET class_name = c.name
FROM classes c
WHERE absences.class_id = c.id
  AND absences.class_name != c.name;
```

---

**Feature Version**: 1.0  
**Documentation Updated**: 2025-01-31  
**Next Review**: After first class progression (2026)
