# Format Export Excel – Sistem Kehadiran **ShalatYuk**

_Dokumentasi resmi format berkas ekspor Excel untuk seluruh jenis laporan pada sistem kehadiran **SMK Negeri 3 Banjarmasin**._

---

## 🎯 **Konsep Export yang Disederhanakan**

Sistem export hanya mendukung **2 filter periode** dan **1 format laporan** untuk menghindari kompleksitas berlebihan:

### **Filter Periode yang Tersedia:**

- 📅 **Bulanan** - Data dalam 1 bulan tertentu
- 📅 **Tahunan** - Data dalam 1 tahun tertentu

### **Format Laporan yang Tersedia:**

- 📋 **Matrix Report** - Data kehadiran dalam format matrix dengan hover details (tersedia untuk semua periode)

### **🗂️ Struktur Tab Excel:**

**Semua format export menggunakan tab per kelas untuk organisasi yang lebih baik:**

- **Tab per Kelas**: X-TKJ-1, X-TKJ-2, X-MM-1, XI-TKJ-1, dst.
- **Organisasi Data**: Setiap tab berisi data siswa dalam kelas tersebut saja

---

## 1. Laporan Shalat

### 1.1 Format Ringkasan/Rekap

#### 🗂️ Struktur Tab Excel:

- **Tab per Kelas**: X-TKJ-1, X-TKJ-2, X-MM-1, XI-TKJ-1, dst.

#### Header Excel (per tab kelas):

```excel
Tahun : Tahun
Bulan : Bulan
No,NIS,Nama,Jenis_Kelamin,Total_Zuhr,Total_Asr,Total_Ijin
```

#### Contoh Data Tab "X-TKJ-1":

```excel
Tahun : 2024
Bulan : Januari
1,2024001,Ahmad Fauzi,Laki-laki,20,19,1
2,2024002,Siti Aminah,Perempuan,18,17,2
3,2024003,Budi Santoso,Laki-laki,22,21,0

Bulan : Februari
1,2024001,Ahmad Fauzi,Laki-laki,21,20,0
2,2024002,Siti Aminah,Perempuan,19,18,1
3,2024003,Budi Santoso,Laki-laki,23,22,0
```

#### Contoh Data Tab "X-TKJ-2":

```excel
Bulan : Januari 2024
1,2024004,Dewi Sari,Perempuan,19,18,1
2,2024005,Eko Prasetyo,Laki-laki,21,20,0

Bulan : Februari 2024
1,2024004,Dewi Sari,Perempuan,20,19,1
2,2024005,Eko Prasetyo,Laki-laki,22,21,0
```

#### Keterangan:

- **Total** = jumlah hari siswa melakukan aktivitas dalam periode
- **Data diurutkan** per Nama (ascending) dalam setiap tab kelas
- **Dikelompokkan per bulan** dalam tab kelas jika periode lintas bulan

### 1.2 Format Matrix/Pivot Style

_Tersedia untuk: Bulanan, Tahunan, Custom_

#### 🗂️ Struktur Tab Excel:

- **Tab per Kelas**: X-TKJ-1, X-TKJ-2, X-MM-1, XI-TKJ-1, dst.
- **2 Sheet per Tab Kelas**:
  - **Sheet "Matriks"**: Format pivot dengan siswa sebagai baris, tanggal sebagai kolom

#### Format Matrix Sheet (per tab kelas):

**Untuk Laporan Bulanan:**

```excel
Laporan Shalat Kelas X-TKJ-1 - Januari 2024

NIS      | Nama         | L/P | 01 | 02 | 03 | 04 | 05 | ... | 31 | Total H | Total Z | Total A | Total I
---------|--------------|-----|----|----|----|----|----|----|----|----|---------|---------|---------|--------
2024001  | Ahmad Fauzi  | L   | H  | H  | Z  | H  | I  | ... | H  |   25    |   2     |   1     |   2
2024002  | Siti Aminah  | P   | H  | A  | H  | -  | H  | ... | H  |   23    |   1     |   2     |   1
2024003  | Budi Santoso | L   | Z  | H  | H  | H  | H  | ... | A  |   26    |   2     |   1     |   0
```

**Untuk Laporan Tahunan (Tab per Bulan):**

```excel
Laporan Shalat Kelas X-TKJ-1 - Tahun 2024

=== JANUARI 2024 ===
NIS      | Nama         | L/P | 01 | 02 | 03 | 04 | 05 | ... | 31 | Total H | Total Z | Total A | Total I
---------|--------------|-----|----|----|----|----|----|----|----|----|---------|---------|---------|--------
2024001  | Ahmad Fauzi  | L   | H  | H  | Z  | H  | I  | ... | H  |   25    |   2     |   1     |   2
2024002  | Siti Aminah  | P   | H  | A  | H  | -  | H  | ... | H  |   23    |   1     |   2     |   1
2024003  | Budi Santoso | L   | Z  | H  | H  | H  | H  | ... | A  |   26    |   2     |   1     |   0

Keterangan Status:
H = Zuhr + Asr | Z = Zuhr Only | A = Asr Only | I = Ijin | - = Absen
```

#### Keunggulan Format Matrix:

- **Efisiensi Ruang**: 1 baris per siswa per bulan (vs 30+ baris format lama)
- **Visual Pattern**: Mudah melihat pola kehadiran siswa dalam sebulan
- **Quick Analysis**: Langsung terlihat siswa yang bermasalah
- **Conditional Formatting**: Warna-warni untuk identifikasi cepat

---

## 2. Laporan Sekolah

### 2.1 Format Matrix Report

#### 🗂️ Struktur Tab Excel:

- **Tab per Kelas**: X-TKJ-1, X-TKJ-2, X-MM-1, XI-TKJ-1, dst.
- **Format Matrix**: Setiap tanggal sebagai kolom dengan status code
- **Hover Details**: Informasi detail dengan timestamp dan status

#### Header Excel (per tab kelas):

```excel
Tahun : Tahun
Bulan : Bulan
No,NIS,Nama,Jenis_Kelamin,1,2,3,4,5,...,31
```

#### Contoh Data Tab "X-TKJ-1":

```excel
Tahun : 2024
Bulan : Januari
1,2024001,Ahmad Fauzi,Laki-laki,20,3,1,0,0,1
2,2024002,Siti Aminah,Perempuan,18,2,2,1,0,0

Bulan : Februari
1,2024001,Ahmad Fauzi,Laki-laki,21,2,1,0,0,1
2,2024002,Siti Aminah,Perempuan,19,1,2,1,0,0
```

#### Contoh Data Tab "X-TKJ-2":

```excel
Tahun : 2024
Bulan : Januari
1,2024004,Dewi Sari,Perempuan,19,1,1,0,0,1
2,2024005,Eko Prasetyo,Laki-laki,21,2,2,1,0,0

Bulan : Februari
1,2024004,Dewi Sari,Perempuan,20,1,1,0,0,1
2,2024005,Eko Prasetyo,Laki-laki,22,2,2,1,0,0
```

#### Keterangan:

- **Total_Hadir** = jumlah hari siswa hadir dalam periode
- **Data diurutkan** per Nama (ascending) dalam setiap tab kelas
- **Dikelompokkan per bulan** dalam tab kelas jika periode lintas bulan

### 2.2 Format Matrix/Pivot Style

#### 🗂️ Struktur Tab Excel:

- **2 Sheet per Tab Kelas**:
  - **Sheet "Matriks"**: Format pivot dengan siswa sebagai baris, tanggal sebagai kolom
  - **Sheet "Detail"**: Data lengkap tradisional untuk referensi

#### Format Matrix Sheet (per tab kelas):

**Untuk Laporan Bulanan:**

- **Tab per Kelas**: X-TKJ-1, X-TKJ-2, X-MM-1, XI-TKJ-1, dst.

```excel
Laporan Sekolah Kelas X-TKJ-1 - Januari 2024

NIS      | Nama         | L/P | 01 | 02 | 03 | 04 | 05 | ... | 31 | H | M | T | S | I | IS | K | P
---------|--------------|-----|----|----|----|----|----|----|----|----|---|---|---|---|---|----|----|---
2024001  | Ahmad Fauzi  | L   | H  | H  | T  | H  | I  | ... | H  |25 | 1 | 2 | 1 | 2 | 0  | 0  | 0
2024002  | Siti Aminah  | P   | H  | M  | H  | S  | H  | ... | H  |26 | 2 | 1 | 1 | 1 | 0  | 0  | 1
2024003  | Budi Santoso | L   | T  | H  | H  | IS | H  | ... | H  |27 | 0 | 1 | 0 | 0 | 1  | 1  | 0
```

**Untuk Laporan Tahunan (per tab Bulan):**

- **Tab per Bulan**: Januari, Februari, Maret, dst.

```excel
Laporan Sekolah Kelas X-TKJ-1 - Tahun 2024

=== JANUARI 2024 ===
NIS      | Nama         | L/P | 01 | 02 | 03 | 04 | 05 | ... | 31 | H | M | T | S | I | IS | K | P
---------|--------------|-----|----|----|----|----|----|----|----|----|---|---|---|---|---|----|----|---
2024001  | Ahmad Fauzi  | L   | H  | H  | T  | H  | I  | ... | H  |25 | 1 | 2 | 1 | 2 | 0  | 0  | 0
2024002  | Siti Aminah  | P   | H  | M  | H  | S  | H  | ... | H  |26 | 2 | 1 | 1 | 1 | 0  | 0  | 1
2024003  | Budi Santoso | L   | T  | H  | H  | IS | H  | ... | H  |27 | 0 | 1 | 0 | 0 | 1  | 1  | 0

Keterangan Status:
H = Hadir | M = Masuk Only | T = Terlambat | S = Sakit | I = Ijin | IS = Ijin Sementara | K = Kembali | P = Pulang Only | - = Absen
```

#### Keunggulan Format Matrix:

- **Efisiensi Ruang**: 1 baris per siswa per bulan (vs 30+ baris format lama)
- **Visual Pattern**: Mudah melihat pola kehadiran siswa dalam sebulan
- **Quick Analysis**: Langsung terlihat siswa yang bermasalah
- **Conditional Formatting**: Warna-warni untuk identifikasi cepat

---

## 3. Metadata Excel

Setiap file Excel dimulai dengan metadata di baris pertama:

```excel
# LAPORAN KEHADIRAN SHALAT - SMK NEGERI 3 BANJARMASIN
# Tanggal Export: 02 Juli 2024 14:30:25 WITA
# Periode: Juli 2024 (Bulanan) / 2024 (Tahunan) / 01-15 Juli 2024 (Custom)
# Format: Ringkasan Semua Siswa / Detail Per Tanggal
# Total Record: 150
# Filter Kelas: Semua Kelas / X-TKJ-1
# Zona Waktu: WITA (Asia/Makassar)
# Diekspor oleh: Admin User
#
```

---

## 4. Aturan Formatting

| Tipe              | Format                    | Contoh                        |
| ----------------- | ------------------------- | ----------------------------- |
| **Waktu**         | `HH:mm` (24 jam, WITA)    | `07:15`, `15:30`              |
| **Tanggal**       | `DD-MM-YYYY`              | `02-07-2024`                  |
| **Angka**         | Bilangan bulat            | `20`, `15`, `0`               |
| **Jenis Kelamin** | `Laki-laki` / `Perempuan` |                               |
| **Alasan**        | Text bebas                | `Sakit`, `Keperluan keluarga` |
| **Kosong**        | `-` (dash)                | Untuk data yang tidak ada     |

---

## 5. Matrix Filter dan Format

| Filter Periode | Ringkasan/Rekap | Matrix Report | Keterangan                                                         |
| -------------- | --------------- | ------------- | ------------------------------------------------------------------ |
| **Bulanan**    | ✅ Tersedia     | ✅ Tersedia   | tab per kelaskelas                                                 |
| **Tahunan**    | ✅ Tersedia     | ✅ Tersedia   | hanya matrix tahunan per bulan (hanya 1 kelas)                     |
| **Custom**     | ✅ Tersedia     | ✅ Tersedia   | hanya matrix custom per bulan (maksimal 1 bulan dan hanya 1 kelas) |

### 🗂️ **Struktur Tab untuk Semua Format:**

- **Semua format menggunakan tab per kelas kecuali matrix tahunan tab per bulan**
- **Format Ringkasan**: Tab per kelas dengan statistik keseluruhan

---

## 6. Penamaan File

### Format Nama File:

```
laporan-{jenis}-{format}-{periode}-{timestamp}.xlsx
```

### Contoh:

- `laporan-shalat-ringkasan-bulanan-2024-07.xlsx`
- `laporan-sekolah-detail-custom-2024-07-01-to-2024-07-15.xlsx`
- `laporan-shalat-ringkasan-tahunan-2024.xlsx`

---

## 7. UI Dialog Export

### 7.1 Layout Dialog

```
┌─────────────────────────────────────────┐
│ 📊 Export Laporan Excel                │
├─────────────────────────────────────────┤
│ Jenis Laporan:                          │
│ ○ Laporan Shalat    ○ Laporan Sekolah   │
│                                         │
│ Filter Periode:                         │
│ ○ Bulanan    ○ Tahunan    ○ Custom      │
│                                         │
│ [Jika Bulanan]                          │
│ Bulan: [Juli ▼]  Tahun: [2024 ▼]       │
│                                         │
│ [Jika Tahunan]                          │
│ Tahun: [2024 ▼]                         │
│                                         │
│ [Jika Custom]                           │
│ Dari: [📅 01/07/2024] Sampai: [📅 31/07/2024] │
│                                         │
│ Format Laporan:                         │
│ ○ Ringkasan/Rekap                       │
│ ○ Detail Report                         │
│                                         │
│ Filter Kelas: [Semua Kelas ▼]          │
│                                         │
│ 🗂️ Semua export menggunakan tab per kelas │
│                                         │
│ [❌ Batal]              [📥 Export Excel] │
└─────────────────────────────────────────┘
```

### 7.2 Validasi UI

- **Ringkasan/Rekap** tersedia untuk semua periode (Bulanan, Tahunan, Custom)
- **Matrix Report Tahunan** hanya tersedia jika tepat 1 kelas dipilih (untuk performa optimal)
- **Validasi tanggal** tidak boleh masa depan
- **Semua export** otomatis menggunakan struktur tab per kelas kecuali matrix tahunan tab per bulan

---
