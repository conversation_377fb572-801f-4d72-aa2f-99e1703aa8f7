# Redis Cache Strategy Guide - ShalatYuk Project

## 🎯 **OVERVIEW: UNIFIED CACHE STRATEGY**

ShalatYuk menggunakan **Unified Cache Strategy** yang terinspirasi dari **Netflix EVCache Pattern** dengan implementasi **Write-Through Cache tanpa TTL**. Strategy ini dirancang untuk memberikan **real-time performance** dengan **event-driven invalidation**.

---

## 🏗️ **ARCHITECTURE OVERVIEW**

### **Core Components**

```
┌─────────────────────────────────────────────────────────────┐
│                    UNIFIED CACHE STRATEGY                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Redis Cache   │  │  Cache Events   │  │ Cache Keys   │ │
│  │   (No TTL)      │  │  (CRUD Ops)     │  │ (Hierarchy)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Write-Through   │  │ Event-Driven    │  │ Cache        │ │
│  │ Updates         │  │ Invalidation    │  │ Warming      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 🚀 **KEY FEATURES**

### **✅ 1. Write-Through Strategy (No TTL)**

- **Data Persistence**: Cache persists until explicitly invalidated
- **Immediate Updates**: New data written to cache instantly
- **Memory Efficient**: No TTL expiration overhead
- **Predictable Behavior**: Cache state is deterministic

### **✅ 2. Event-Driven Invalidation**

- **Smart Invalidation**: Cache cleared only when data changes
- **Comprehensive Coverage**: All related cache patterns invalidated
- **Real-time Updates**: Immediate visibility of new data
- **Efficient Operations**: Parallel invalidation with Promise.allSettled

### **✅ 3. Hierarchical Cache Keys**

- **Predictable Patterns**: Consistent key naming convention
- **Easy Management**: Clear cache key hierarchy
- **Efficient Queries**: Fast cache lookups
- **Scalable Design**: Supports multiple report types and filters

---

## 📊 **CACHE KEY PATTERNS**

### **Daily Reports**

```typescript
// Pattern: absence:reports:{reportType}:{date}:{class}:day
absence:reports:school:2025-01-22:all:day
absence:reports:prayer:2025-01-22:X-TKJ-1:day
```

### **Monthly Reports**

```typescript
// Pattern: reports:monthly:{reportType}:{year-month}:{class}
reports:monthly:school:2025-01:all
reports:monthly:prayer:2025-01:X-TKJ-1
```

### **Yearly Reports**

```typescript
// Pattern: reports:yearly:{reportType}:{year}:{class}
reports:yearly:school:2025:all
reports:yearly:prayer:2025:X-TKJ-1
```

### **Aggregated Cache**

```typescript
// Pattern: aggregated:{startDate}:{endDate}:{class}:{reportType}
aggregated:2025-01-01:2025-01-31:all:school
aggregated:2025-01-01:2025-12-31:X-TKJ-1:prayer
```

---

## 🔧 **IMPLEMENTATION GUIDE**

### **1. Core Service Usage**

```typescript
import { unifiedCacheStrategy } from '@/lib/services/unified-cache-strategy'

// Write-through cache set (no TTL)
await unifiedCacheStrategy.set(cacheKey, data)

// Cache get
const cachedData = await unifiedCacheStrategy.get(cacheKey)

// Cache delete
await unifiedCacheStrategy.del(cacheKey)

// Health check
const health = await unifiedCacheStrategy.healthCheck()
```

### **2. Event-Driven Cache Updates**

```typescript
import { createCacheEvent, unifiedCacheStrategy } from '@/lib/services/unified-cache-strategy'

// Create cache event
const cacheEvent = createCacheEvent(
  uniqueCode, // Student unique code
  attendanceType, // ZUHR, ASR, ENTRY, etc.
  'create', // Operation: create, update, delete
  className // Optional: class name
)

// Update cache immediately
await unifiedCacheStrategy.updateCacheOnAttendance(cacheEvent)

// Invalidate related cache
await unifiedCacheStrategy.invalidateRelatedCache(cacheEvent)
```

### **3. Service Factory Pattern**

```typescript
// ✅ UNIFIED: No cache parameter needed
export function createAbsenceUseCases(): AbsenceUseCases {
  const absenceRepo = new AbsenceRepository()
  const studentRepo = new StudentRepository() // No cache parameter
  return new AbsenceUseCases(absenceRepo, studentRepo)
}
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **Cache Warming Strategy**

```typescript
import { CacheWarmingService } from '@/lib/services/cache-warming'

const cacheWarming = new CacheWarmingService()

// Warm today's cache (priority data)
await cacheWarming.warmTodayCache()

// Warm yesterday's cache
await cacheWarming.warmYesterdayCache()

// Warm weekly cache
await cacheWarming.warmWeeklyCache()

// Comprehensive warming
await cacheWarming.warmAllCache()
```

### **Scheduled Warming**

```typescript
// Time-based cache warming
async scheduleWarmingTasks(): Promise<void> {
  const now = getCurrentWITATime()
  const hour = now.getHours()

  // School hours: 6 AM - 6 PM WITA
  if (hour >= 6 && hour <= 18) {
    await this.warmTodayCache() // Every 5 minutes
  }

  // Hourly warming
  if (now.getMinutes() === 0) {
    await this.warmYesterdayCache()
  }

  // Daily warming at 1 AM
  if (hour === 1 && now.getMinutes() === 0) {
    await this.warmWeeklyCache()
  }
}
```

---

## 🔄 **CACHE INVALIDATION FLOW**

### **Comprehensive Invalidation**

```typescript
// When attendance is recorded:
// 1. Create cache event
const event = createCacheEvent(uniqueCode, type, 'create', className)

// 2. Write-through update
await unifiedCacheStrategy.updateCacheOnAttendance(event)

// 3. Invalidate related cache patterns
await unifiedCacheStrategy.invalidateRelatedCache(event)

// Cache keys invalidated:
// - Daily reports (today, yesterday)
// - Monthly reports (current month)
// - Yearly reports (current year)
// - Aggregated cache (daily, monthly, yearly ranges)
// - Class-specific cache (if applicable)
```

### **Critical Fix: Aggregated Cache**

```typescript
// CRITICAL FIX: Monthly aggregated cache invalidation
const monthStart = new Date(year, month - 1, 1)
const monthEnd = new Date(year, month, 0)
cacheKeysToInvalidate.push(`aggregated:${monthStartKey}:${monthEndKey}:all:${reportType}`)

// CRITICAL FIX: Yearly aggregated cache invalidation
const yearStart = new Date(year, 0, 1)
const yearEnd = new Date(year, 11, 31)
cacheKeysToInvalidate.push(`aggregated:${yearStartKey}:${yearEndKey}:all:${reportType}`)
```

---

## 🛡️ **ERROR HANDLING & FALLBACKS**

### **Graceful Degradation**

```typescript
// Redis connection failure handling
async get(key: string): Promise<string | null> {
  if (this.client && isConnected) {
    return await this.client.get(key)
  } else {
    // Fallback to in-memory cache
    const item = this.inMemoryCache.get(key)
    if (item && item.expiry > Date.now()) {
      return item.value
    }
    return null
  }
}
```

### **Health Monitoring**

```typescript
// Comprehensive health check
async healthCheck(): Promise<HealthCheckResult> {
  try {
    const testKey = `health_check_${Date.now()}`
    await this.set(testKey, { test: 'data' })
    const testData = await this.get(testKey)
    await this.del(testKey)

    const isHealthy = testData?.test === 'data'

    return {
      status: isHealthy ? 'healthy' : 'degraded',
      redis: isHealthy,
      strategy: 'Write-Through (No TTL)',
      recommendations: isHealthy
        ? ['Cache is operating normally']
        : ['Check Redis connection', 'Verify configuration']
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      redis: false,
      strategy: 'Write-Through (No TTL)',
      recommendations: ['Redis connection failed', 'Check server status']
    }
  }
}
```

---

## 🎯 **BEST PRACTICES**

### **✅ DO's**

1. **Use Unified Cache Strategy**: Always use `unifiedCacheStrategy` for consistency
2. **Event-Driven Updates**: Create cache events for all data changes
3. **Hierarchical Keys**: Follow established cache key patterns
4. **Health Monitoring**: Implement health checks for production
5. **Graceful Degradation**: Handle Redis failures gracefully
6. **Parallel Operations**: Use Promise.allSettled for batch operations

### **❌ DON'Ts**

1. **Don't Use TTL**: Avoid TTL-based cache expiration
2. **Don't Manual Cache**: Avoid manual cache management
3. **Don't Skip Events**: Always create cache events for data changes
4. **Don't Ignore Errors**: Handle cache errors properly
5. **Don't Mix Strategies**: Use unified strategy consistently
6. **Don't Hardcode Keys**: Use key generation functions

---

## 🚀 **EXTENDING FOR NEW FEATURES**

### **Step 1: Define Cache Keys**

```typescript
// Add new cache key patterns
const newFeatureCacheKey = `feature:reports:${featureType}:${date}:${filter}`
```

### **Step 2: Implement Cache Events**

```typescript
// Create cache events for new feature
const cacheEvent = createCacheEvent(entityId, operationType, 'create', additionalContext)
```

### **Step 3: Add Invalidation Logic**

```typescript
// Extend invalidateRelatedCache method
if (event.featureType === 'newFeature') {
  cacheKeysToInvalidate.push(`feature:reports:${event.featureType}:${dateKey}:all`)
}
```

### **Step 4: Update Use Cases**

```typescript
// Use unified cache strategy in new use cases
export class NewFeatureUseCases {
  async getFeatureData(params: any): Promise<any> {
    const cacheKey = `feature:data:${params.id}`

    // Check cache first
    const cachedData = await unifiedCacheStrategy.get(cacheKey)
    if (cachedData) return cachedData

    // Fetch from database
    const data = await this.repository.getData(params)

    // Write-through cache
    await unifiedCacheStrategy.set(cacheKey, data)

    return data
  }
}
```

---

## 🎉 **CONCLUSION**

**Unified Cache Strategy** memberikan:

- **Real-time Performance** dengan write-through updates
- **Memory Efficiency** tanpa TTL overhead
- **Event-Driven Architecture** untuk optimal invalidation
- **Production-Ready** untuk 3000+ concurrent users
- **Extensible Design** untuk fitur-fitur baru

**Strategy ini siap digunakan untuk semua fitur baru dengan pattern yang konsisten! 🚀**

---

## 📋 **API ENDPOINTS FOR CACHE MANAGEMENT**

### **Cache Health Check**

```typescript
// GET /api/admin/cache/write-through
// Returns cache strategy status and health information

{
  "status": "success",
  "data": {
    "strategy": "Unified Write-Through Cache (No TTL)",
    "description": "Updates cache immediately when new attendance is recorded, persists until invalidated",
    "healthCheck": {
      "status": "healthy",
      "redis": true,
      "strategy": "Write-Through (No TTL)",
      "recommendations": ["Cache is operating normally"]
    },
    "features": {
      "immediateUpdates": "Reports show new data instantly after attendance",
      "cacheConsistency": "Cache always reflects latest database state",
      "fallbackStrategy": "Auto-delete cache on update failure",
      "preemptiveWarming": "Cache warmed before requests arrive"
    }
  }
}
```

### **Cache Testing**

```typescript
// POST /api/admin/cache/write-through
// Body: { "action": "test" }

{
  "status": "success",
  "message": "Unified cache strategy test completed",
  "testEvent": {
    "uniqueCode": "TEST_USER_001",
    "attendanceType": "ZUHR",
    "className": "XII RPL 1",
    "operation": "create",
    "timestamp": "2025-01-22T10:30:00.000Z"
  },
  "result": "Cache updated and invalidated successfully with test data"
}
```

---

## 🔍 **MONITORING & DEBUGGING**

### **Cache Key Inspection**

```typescript
// Debug cache keys for specific patterns
const debugCacheKeys = async (pattern: string) => {
  const keys = await redis.keys(pattern)
  console.log(`Cache keys matching ${pattern}:`, keys)

  for (const key of keys) {
    const value = await redis.get(key)
    const ttl = await redis.ttl(key)
    console.log(`${key}: TTL=${ttl}, Size=${value?.length || 0}`)
  }
}

// Usage examples:
await debugCacheKeys('absence:reports:school:*')
await debugCacheKeys('reports:monthly:*')
await debugCacheKeys('aggregated:*')
```

### **Performance Metrics**

```typescript
// Cache performance monitoring
export class CacheMetrics {
  private static hitCount = 0
  private static missCount = 0
  private static errorCount = 0

  static recordHit() {
    this.hitCount++
  }
  static recordMiss() {
    this.missCount++
  }
  static recordError() {
    this.errorCount++
  }

  static getStats() {
    const total = this.hitCount + this.missCount
    const hitRate = total > 0 ? (this.hitCount / total) * 100 : 0

    return {
      hitCount: this.hitCount,
      missCount: this.missCount,
      errorCount: this.errorCount,
      hitRate: `${hitRate.toFixed(2)}%`,
      total,
    }
  }
}
```

---

## 🛠️ **TROUBLESHOOTING GUIDE**

### **Common Issues & Solutions**

#### **1. Cache Not Updating**

```typescript
// Problem: New attendance not showing in reports
// Solution: Check cache event creation and invalidation

// Debug steps:
console.log('Cache event:', cacheEvent)
console.log('Invalidated keys:', cacheKeysToInvalidate)

// Verify cache invalidation
const cacheKey = 'absence:reports:school:2025-01-22:all:day'
const cachedData = await unifiedCacheStrategy.get(cacheKey)
console.log('Cache data after invalidation:', cachedData)
```

#### **2. Redis Connection Issues**

```typescript
// Problem: Redis connection failures
// Solution: Check fallback to in-memory cache

// Health check
const health = await unifiedCacheStrategy.healthCheck()
if (health.status !== 'healthy') {
  console.log('Redis issues detected:', health.recommendations)
}
```

#### **3. Memory Usage Issues**

```typescript
// Problem: High Redis memory usage
// Solution: Monitor cache key patterns and implement cleanup

// Check memory usage
const memoryInfo = await redis.memory('usage')
console.log('Redis memory usage:', memoryInfo)

// Clean up old cache keys if needed
const oldKeys = await redis.keys('*:2024-*') // Old year data
if (oldKeys.length > 0) {
  await redis.del(...oldKeys)
}
```

### **4. Performance Issues**

```typescript
// Problem: Slow cache operations
// Solution: Optimize cache key patterns and batch operations

// Use parallel operations
const cacheOperations = keys.map(key => unifiedCacheStrategy.del(key))
await Promise.allSettled(cacheOperations)

// Monitor operation timing
const startTime = Date.now()
await unifiedCacheStrategy.set(key, data)
const duration = Date.now() - startTime
console.log(`Cache set took ${duration}ms`)
```

---

## 📚 **MIGRATION GUIDE**

### **From Old Cache Strategy to Unified**

#### **Before (Old Pattern)**

```typescript
// ❌ OLD: Using cache parameter
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

// ❌ OLD: Manual TTL management
await cache.set(key, data, 300) // 5 minutes TTL
```

#### **After (Unified Pattern)**

```typescript
// ✅ NEW: No cache parameter needed
const studentRepo = new StudentRepository()
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo)

// ✅ NEW: Write-through without TTL
await unifiedCacheStrategy.set(key, data) // No TTL, persists until invalidated
```

### **Migration Steps**

1. **Update Service Factories**

   ```typescript
   // Remove cache parameters from constructors
   // Use unified cache strategy internally
   ```

2. **Update Use Cases**

   ```typescript
   // Replace manual cache operations with unified strategy
   // Add cache event creation for data changes
   ```

3. **Update API Endpoints**

   ```typescript
   // Use unified initialization patterns
   // Remove cache-specific headers and TTL logic
   ```

4. **Test Migration**
   ```typescript
   // Verify cache operations work correctly
   // Check performance and memory usage
   // Validate real-time updates
   ```

---

## 🎯 **PRODUCTION DEPLOYMENT CHECKLIST**

### **Pre-Deployment**

- [ ] Redis server configured and running
- [ ] Connection string and credentials set
- [ ] Health check endpoints tested
- [ ] Cache warming scheduled
- [ ] Monitoring and alerting configured

### **Deployment**

- [ ] Deploy unified cache strategy
- [ ] Update service factories
- [ ] Migrate API endpoints
- [ ] Test cache operations
- [ ] Verify real-time updates

### **Post-Deployment**

- [ ] Monitor cache hit rates
- [ ] Check Redis memory usage
- [ ] Verify performance metrics
- [ ] Test fallback mechanisms
- [ ] Document any issues

---

## 🚀 **FUTURE ENHANCEMENTS**

### **Planned Improvements**

- [ ] **Cache Compression**: Implement data compression for large cache values
- [ ] **Distributed Caching**: Support for Redis Cluster
- [ ] **Cache Analytics**: Advanced metrics and reporting
- [ ] **Auto-Scaling**: Dynamic cache warming based on usage patterns
- [ ] **Cache Versioning**: Support for cache schema migrations

### **Advanced Features**

- [ ] **Predictive Caching**: ML-based cache warming
- [ ] **Geographic Distribution**: Multi-region cache strategy
- [ ] **Real-time Sync**: WebSocket-based cache updates
- [ ] **Cache Optimization**: Automatic key pattern optimization

**Unified Cache Strategy adalah foundation yang solid untuk semua enhancement ini! 🎉**

---

## 🔄 **CACHE STRATEGY SCOPE & BOUNDARIES**

### **✅ UNIFIED CACHE STRATEGY SCOPE**

**Currently Applied To**:

- **✅ Prayer Reports**: All prayer-related attendance reports
- **✅ School Reports**: All school-related attendance reports
- **✅ Absence Use Cases**: Attendance recording and reporting
- **✅ Reports Use Cases**: Monthly and yearly report generation
- **✅ Cache Warming**: Background cache warming services

**Implementation Files**:

```typescript
// ✅ UNIFIED CACHE STRATEGY FILES
lib / services / unified - cache - strategy.ts
lib / services / absence - service - factory.ts
lib / services / cache - warming.ts
lib / domain / usecases / absence.ts
lib / domain / usecases / reports.ts
app / api / absence / reports / route.ts
app / api / reports / monthly / route.ts
app / api / reports / yearly / route.ts
app / api / absence / reports / range / route.ts
```

### **✅ TTL CACHE STRATEGY SCOPE**

**Intentionally Different Pattern**:

- **✅ User Management**: User CRUD operations (students, admins)
- **✅ Authentication**: Login, session management
- **✅ Analytics**: Dashboard analytics and metrics

**Why Different?**:

```typescript
// ✅ USER MANAGEMENT: Uses TTL cache (5 minutes)
// Different caching requirements than real-time attendance reports
export class UserUseCases {
  constructor(
    private studentRepo: StudentRepository,
    private adminRepo: AdminRepository,
    private classRepo: ClassRepository,
    private cache: CacheService, // ✅ INTENTIONAL: TTL cache interface
    private absenceRepo: AbsenceRepository
  ) {}

  async getAllUsers(): Promise<(Student | Admin)[]> {
    const cacheKey = 'users:all'
    const cachedUsers = await this.cache.get(cacheKey)
    if (cachedUsers) return JSON.parse(cachedUsers)

    const users = await this.fetchUsersFromDatabase()
    // ✅ INTENTIONAL: TTL cache (5 minutes)
    await this.cache.set(cacheKey, JSON.stringify(users), 5 * 60)
    return users
  }
}
```

**Implementation Files**:

```typescript
// ✅ TTL CACHE STRATEGY FILES
lib / domain / usecases / user.ts
lib / domain / usecases / analytics.ts
app / api / users / route.ts
app / api / analytics / dashboard / route.ts
```

### **✅ RATIONALE FOR DIFFERENT STRATEGIES**

#### **Unified Cache Strategy (No TTL)**

**Use Case**: Real-time attendance reports
**Requirements**:

- **Immediate Updates**: New attendance must show instantly
- **Event-Driven**: Cache invalidated only when data changes
- **High Frequency**: 3000+ students, 2 admins scanning simultaneously
- **Real-time Monitoring**: Teachers need immediate visibility

#### **TTL Cache Strategy (5 minutes)**

**Use Case**: User management and analytics
**Requirements**:

- **Less Frequent Updates**: User data changes infrequently
- **Acceptable Delay**: 5-minute delay is acceptable for user lists
- **Lower Frequency**: Admin operations, not real-time critical
- **Memory Efficiency**: Automatic expiration prevents memory buildup

### **✅ IMPLEMENTATION GUIDELINES**

#### **When to Use Unified Cache Strategy**

```typescript
// ✅ USE UNIFIED CACHE FOR:
// - Real-time attendance data
// - Reports that need immediate updates
// - High-frequency data changes
// - Event-driven invalidation needs

// Example implementation:
export function createAbsenceUseCases(): AbsenceUseCases {
  const absenceRepo = new AbsenceRepository()
  const studentRepo = new StudentRepository() // No cache parameter
  return new AbsenceUseCases(absenceRepo, studentRepo) // Uses unified cache internally
}
```

#### **When to Use TTL Cache Strategy**

```typescript
// ✅ USE TTL CACHE FOR:
// - User management operations
// - Analytics and dashboard data
// - Configuration data
// - Less frequently changing data

// Example implementation:
const cache = getRedisCache() // TTL cache interface
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)
```

### **✅ MIGRATION CONSIDERATIONS**

**Future Migration Path**:

- **Phase 1**: ✅ **COMPLETE** - Unified cache for attendance reports
- **Phase 2**: Consider unified cache for user management (if needed)
- **Phase 3**: Consider unified cache for analytics (if needed)

**Decision Criteria for Migration**:

- **Real-time Requirements**: Does the feature need immediate updates?
- **Update Frequency**: How often does the data change?
- **User Experience**: Is delay acceptable for this feature?
- **System Load**: What's the impact on Redis memory usage?

---

## 🎯 **BEST PRACTICES BY STRATEGY**

### **✅ Unified Cache Strategy Best Practices**

1. **Always Create Cache Events**: For every data change
2. **Comprehensive Invalidation**: Clear all related cache patterns
3. **Write-Through Updates**: Update cache immediately
4. **No TTL Dependencies**: Let events drive invalidation
5. **Health Monitoring**: Implement health checks

### **✅ TTL Cache Strategy Best Practices**

1. **Appropriate TTL**: 5 minutes for user data, adjust as needed
2. **Cache Key Consistency**: Use predictable key patterns
3. **Graceful Degradation**: Handle cache misses properly
4. **Memory Management**: Let TTL handle automatic cleanup
5. **Performance Monitoring**: Track hit rates and performance

**Both strategies coexist perfectly in the ShalatYuk system! 🚀**
