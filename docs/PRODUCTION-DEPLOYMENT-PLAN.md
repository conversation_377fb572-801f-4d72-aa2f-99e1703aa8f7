# 🚀 PRODUCTION DEPLOYMENT PLAN: Class Context Tracking

## 📋 Overview

This document outlines the step-by-step production deployment plan for the Class Context Tracking feature. This feature adds historical class tracking to attendance records, preventing data loss when students change classes.

## ⚠️ CRITICAL SAFETY MEASURES

### 🔒 Pre-Deployment Requirements
- [ ] **Full database backup completed**
- [ ] **Staging environment tested successfully**
- [ ] **All tests passing (unit, integration, e2e)**
- [ ] **Maintenance window scheduled**
- [ ] **Rollback plan prepared**
- [ ] **Team notification sent**

### 🎯 Success Criteria
- [ ] Schema migration completes without errors
- [ ] Data backfill achieves >95% success rate
- [ ] All existing functionality remains intact
- [ ] New attendance records include class context
- [ ] Performance impact <10% on critical queries
- [ ] Zero data loss or corruption

## 📅 DEPLOYMENT TIMELINE

### Phase 1: Pre-Deployment (30 minutes)
**Time: T-30 to T-0**

#### Step 1.1: Environment Preparation (10 minutes)
```bash
# 1. Create backup directory
mkdir -p /backups/class-tracking-$(date +%Y%m%d_%H%M%S)

# 2. Verify database connectivity
psql -d $DB_NAME -U $DB_USER -c "SELECT version();"

# 3. Check current system load
top -n 1 | head -20
```

#### Step 1.2: Full Database Backup (15 minutes)
```bash
# Create comprehensive backup
pg_dump -d $DB_NAME -U $DB_USER \
  --verbose --no-owner --no-privileges \
  --file=/backups/class-tracking-$(date +%Y%m%d_%H%M%S)/full_backup.sql

# Verify backup integrity
pg_restore --list /backups/class-tracking-$(date +%Y%m%d_%H%M%S)/full_backup.sql
```

#### Step 1.3: Final Verification (5 minutes)
```bash
# Run pre-deployment tests
npm run test:pre-deployment

# Verify application health
curl -f http://localhost:3000/api/health
```

### Phase 2: Database Migration (20 minutes)
**Time: T-0 to T+20**

#### Step 2.1: Schema Migration (10 minutes)
```bash
# Run schema migration
psql -d $DB_NAME -U $DB_USER -f drizzle/migrations/0015_add_class_context_to_absences.sql

# Verify schema changes
psql -d $DB_NAME -U $DB_USER -f scripts/test-class-tracking.sql
```

#### Step 2.2: Data Backfill (10 minutes)
```bash
# Run safe backfill script
psql -d $DB_NAME -U $DB_USER -f scripts/backfill-class-context.sql

# Verify backfill results
psql -d $DB_NAME -U $DB_USER -c "
SELECT 
  COUNT(*) as total_records,
  COUNT(class_id) as records_with_class_id,
  ROUND(COUNT(class_id) * 100.0 / COUNT(*), 2) as success_percentage
FROM absences;"
```

### Phase 3: Application Deployment (15 minutes)
**Time: T+20 to T+35**

#### Step 3.1: Code Deployment (10 minutes)
```bash
# Deploy updated application code
git pull origin main
npm ci --production
npm run build

# Update environment variables if needed
# (No changes required for this feature)
```

#### Step 3.2: Application Restart (5 minutes)
```bash
# Graceful application restart
pm2 reload shalatYuk --wait-ready

# Verify application startup
pm2 status
curl -f http://localhost:3000/api/health
```

### Phase 4: Verification & Testing (25 minutes)
**Time: T+35 to T+60**

#### Step 4.1: Smoke Tests (10 minutes)
```bash
# Run comprehensive test suite
npm run test:class-tracking

# Test critical user flows
curl -X POST http://localhost:3000/api/absence/record \
  -H "Content-Type: application/json" \
  -d '{"uniqueCode":"TEST001","type":"Zuhr"}'
```

#### Step 4.2: Performance Verification (10 minutes)
```bash
# Test query performance
psql -d $DB_NAME -U $DB_USER -c "
EXPLAIN (ANALYZE, BUFFERS) 
SELECT class_name, COUNT(*) 
FROM absences 
WHERE class_name = 'X IPA 1' 
  AND recorded_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY class_name;"
```

#### Step 4.3: Data Integrity Check (5 minutes)
```bash
# Verify data consistency
psql -d $DB_NAME -U $DB_USER -c "
SELECT 
  'CONSISTENCY CHECK' as status,
  COUNT(*) as inconsistent_records
FROM absences a
LEFT JOIN classes c ON a.class_id = c.id
WHERE a.class_id IS NOT NULL 
  AND (a.class_name != c.name OR c.name IS NULL);"
```

## 🔄 ROLLBACK PROCEDURES

### Immediate Rollback (if issues detected within 1 hour)

#### Option A: Database Rollback
```bash
# Stop application
pm2 stop shalatYuk

# Restore database from backup
psql -d $DB_NAME -U $DB_USER -f /backups/class-tracking-TIMESTAMP/full_backup.sql

# Deploy previous application version
git checkout HEAD~1
npm ci --production
npm run build
pm2 start shalatYuk
```

#### Option B: Column Removal (if schema issues)
```bash
# Remove new columns only
psql -d $DB_NAME -U $DB_USER -c "
ALTER TABLE absences DROP COLUMN IF EXISTS class_id;
ALTER TABLE absences DROP COLUMN IF EXISTS class_name;
DROP INDEX IF EXISTS idx_absences_class_id;
DROP INDEX IF EXISTS idx_absences_class_name;
DROP INDEX IF EXISTS idx_absences_class_recorded_at;
DROP INDEX IF EXISTS idx_absences_class_name_type;"
```

### Delayed Rollback (if issues detected after 1 hour)
- Assess impact and data changes since deployment
- Create new backup before rollback
- Plan rollback during next maintenance window
- Consider partial rollback (disable feature without data loss)

## 📊 MONITORING & ALERTS

### Key Metrics to Monitor
- **Database Performance**: Query execution times
- **Application Response Times**: API endpoint latency
- **Error Rates**: Application and database errors
- **Memory Usage**: Application memory consumption
- **Disk Space**: Database storage usage

### Alert Thresholds
- API response time > 2 seconds
- Database query time > 5 seconds
- Error rate > 1%
- Memory usage > 80%
- Disk usage > 85%

## 🧪 POST-DEPLOYMENT VALIDATION

### Day 1: Immediate Validation
- [ ] All critical user flows working
- [ ] New attendance records include class context
- [ ] Reports display correctly
- [ ] Performance within acceptable limits
- [ ] No error spikes in logs

### Week 1: Extended Validation
- [ ] Data consistency maintained
- [ ] No memory leaks detected
- [ ] User feedback collected
- [ ] Performance trends analyzed
- [ ] Backup procedures tested

### Month 1: Long-term Validation
- [ ] Historical data accuracy verified
- [ ] Class progression scenarios tested
- [ ] Export functionality validated
- [ ] System stability confirmed
- [ ] Documentation updated

## 👥 TEAM RESPONSIBILITIES

### Database Administrator
- [ ] Execute database migration
- [ ] Monitor database performance
- [ ] Manage backups and rollback procedures

### Backend Developer
- [ ] Deploy application code
- [ ] Monitor API performance
- [ ] Handle application-level issues

### DevOps Engineer
- [ ] Coordinate deployment timeline
- [ ] Monitor system resources
- [ ] Manage infrastructure alerts

### QA Engineer
- [ ] Execute test suites
- [ ] Validate user flows
- [ ] Report any issues found

## 📞 EMERGENCY CONTACTS

- **Database Issues**: DBA Team - [contact info]
- **Application Issues**: Backend Team - [contact info]
- **Infrastructure Issues**: DevOps Team - [contact info]
- **Business Impact**: Product Team - [contact info]

## 📝 DEPLOYMENT CHECKLIST

### Pre-Deployment
- [ ] Backup completed and verified
- [ ] Staging tests passed
- [ ] Team notified
- [ ] Maintenance window active
- [ ] Rollback plan ready

### During Deployment
- [ ] Schema migration successful
- [ ] Data backfill completed
- [ ] Application deployed
- [ ] Services restarted
- [ ] Smoke tests passed

### Post-Deployment
- [ ] Performance verified
- [ ] Data integrity confirmed
- [ ] User flows tested
- [ ] Monitoring active
- [ ] Team notified of completion

## 🎉 SUCCESS CONFIRMATION

Deployment is considered successful when:
1. ✅ All checklist items completed
2. ✅ Performance metrics within thresholds
3. ✅ No critical errors in logs
4. ✅ User flows functioning correctly
5. ✅ Data integrity maintained
6. ✅ Team sign-off received

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-31  
**Next Review**: After deployment completion
