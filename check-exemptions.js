/**
 * Simple script to check prayer exemptions in database
 */

const { db } = require('./lib/data/drizzle/db.js')
const { prayerExemptions } = require('./lib/data/drizzle/schema.js')
const { getCurrentWITATime } = require('./lib/utils/date.js')

async function checkExemptions() {
  try {
    console.log('🔍 Checking prayer exemptions in database...\n')
    
    // Get all exemptions
    const allExemptions = await db.select().from(prayerExemptions)
    console.log(`Total exemptions in database: ${allExemptions.length}`)
    
    if (allExemptions.length > 0) {
      console.log('\nAll exemptions:')
      allExemptions.forEach(exemption => {
        console.log(`- ID: ${exemption.id}`)
        console.log(`  Type: ${exemption.exemptionType}`)
        console.log(`  Prayer: ${exemption.prayerType}`)
        console.log(`  Pattern: ${exemption.recurrencePattern}`)
        console.log(`  Date: ${exemption.exemptionDate}`)
        console.log(`  Start: ${exemption.startDate}`)
        console.log(`  End: ${exemption.endDate}`)
        console.log(`  Days: ${exemption.daysOfWeek}`)
        console.log(`  Active: ${exemption.isActive}`)
        console.log(`  Reason: ${exemption.reason}`)
        console.log(`  Created: ${exemption.createdAt}`)
        console.log('---')
      })
    }
    
    // Check active exemptions for today
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)
    const todayStr = today.toISOString().split('T')[0]
    
    console.log(`\nChecking active exemptions for today (${todayStr})...`)
    
    const activeToday = allExemptions.filter(exemption => {
      if (!exemption.isActive) return false
      
      if (exemption.recurrencePattern === 'once') {
        return exemption.exemptionDate === todayStr
      } else if (exemption.recurrencePattern === 'weekly') {
        const dayOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][today.getDay()]
        const startDate = exemption.startDate
        const endDate = exemption.endDate
        const daysOfWeek = exemption.daysOfWeek
        
        // Check if today is within the date range
        if (startDate && todayStr < startDate) return false
        if (endDate && todayStr > endDate) return false
        
        // Check if today's day of week is included
        if (daysOfWeek && daysOfWeek.includes(dayOfWeek)) return true
      }
      
      return false
    })
    
    console.log(`Active exemptions for today: ${activeToday.length}`)
    
    if (activeToday.length > 0) {
      console.log('\nActive exemptions today:')
      activeToday.forEach(exemption => {
        console.log(`- ID: ${exemption.id}, Prayer: ${exemption.prayerType}, Reason: ${exemption.reason}`)
      })
    }
    
    console.log('\n✅ Check completed!')
    
  } catch (error) {
    console.error('❌ Error checking exemptions:', error)
  } finally {
    process.exit(0)
  }
}

checkExemptions()
