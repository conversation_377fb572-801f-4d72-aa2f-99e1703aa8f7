--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 15.13

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: drizzle; Type: SCHEMA; Schema: -; Owner: shalatdev
--

CREATE SCHEMA drizzle;


ALTER SCHEMA drizzle OWNER TO shalatdev;

--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA public;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: attendance_type; Type: TYPE; Schema: public; Owner: shalatdev
--

CREATE TYPE public.attendance_type AS ENUM (
    'Zuhr',
    'Asr',
    'Pulang',
    'Ijin'
);


ALTER TYPE public.attendance_type OWNER TO shalatdev;

--
-- Name: user_role; Type: TYPE; Schema: public; Owner: shalatdev
--

CREATE TYPE public.user_role AS ENUM (
    'student',
    'admin',
    'super_admin'
);


ALTER TYPE public.user_role OWNER TO shalatdev;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: __drizzle_migrations; Type: TABLE; Schema: drizzle; Owner: shalatdev
--

CREATE TABLE drizzle.__drizzle_migrations (
    id integer NOT NULL,
    hash text NOT NULL,
    created_at bigint
);


ALTER TABLE drizzle.__drizzle_migrations OWNER TO shalatdev;

--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE; Schema: drizzle; Owner: shalatdev
--

CREATE SEQUENCE drizzle.__drizzle_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE drizzle.__drizzle_migrations_id_seq OWNER TO shalatdev;

--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: drizzle; Owner: shalatdev
--

ALTER SEQUENCE drizzle.__drizzle_migrations_id_seq OWNED BY drizzle.__drizzle_migrations.id;


--
-- Name: absences; Type: TABLE; Schema: public; Owner: shalatdev
--

CREATE TABLE public.absences (
    id integer NOT NULL,
    unique_code character varying(36) NOT NULL,
    type public.attendance_type NOT NULL,
    recorded_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.absences OWNER TO shalatdev;

--
-- Name: absences_id_seq; Type: SEQUENCE; Schema: public; Owner: shalatdev
--

CREATE SEQUENCE public.absences_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.absences_id_seq OWNER TO shalatdev;

--
-- Name: absences_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: shalatdev
--

ALTER SEQUENCE public.absences_id_seq OWNED BY public.absences.id;


--
-- Name: classes; Type: TABLE; Schema: public; Owner: shalatdev
--

CREATE TABLE public.classes (
    id integer NOT NULL,
    name character varying(10) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.classes OWNER TO shalatdev;

--
-- Name: users; Type: TABLE; Schema: public; Owner: shalatdev
--

CREATE TABLE public.users (
    id integer NOT NULL,
    role public.user_role NOT NULL,
    unique_code character varying(36),
    google_email character varying(255),
    nis character varying(10),
    username character varying(50),
    name character varying(100) NOT NULL,
    whatsapp character varying(15),
    class_id integer NOT NULL,
    password_hash character varying(255),
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone,
    CONSTRAINT chk_nis_format CHECK (((nis IS NULL) OR ((nis)::text ~ '^[A-Za-z0-9]{1,10}$'::text))),
    CONSTRAINT chk_role_data CHECK ((((role = 'student'::public.user_role) AND (google_email IS NOT NULL) AND (unique_code IS NOT NULL) AND (username IS NULL) AND (password_hash IS NULL)) OR ((role = 'admin'::public.user_role) AND (google_email IS NULL) AND (unique_code IS NULL) AND (username IS NOT NULL) AND (password_hash IS NOT NULL) AND (class_id IS NULL))))
);


ALTER TABLE public.users OWNER TO shalatdev;

--
-- Name: attendance_summary; Type: MATERIALIZED VIEW; Schema: public; Owner: shalatdev
--

CREATE MATERIALIZED VIEW public.attendance_summary AS
 WITH pivoted AS (
         SELECT date(absences.recorded_at) AS summary_date,
            absences.unique_code,
            bool_or(
                CASE
                    WHEN (absences.type = 'Zuhr'::public.attendance_type) THEN true
                    ELSE false
                END) AS zuhr,
            bool_or(
                CASE
                    WHEN (absences.type = 'Asr'::public.attendance_type) THEN true
                    ELSE false
                END) AS asr,
            bool_or(
                CASE
                    WHEN (absences.type = 'Pulang'::public.attendance_type) THEN true
                    ELSE false
                END) AS pulang,
            bool_or(
                CASE
                    WHEN (absences.type = 'Ijin'::public.attendance_type) THEN true
                    ELSE false
                END) AS ijin,
            max(absences.recorded_at) AS last_updated
           FROM public.absences
          GROUP BY (date(absences.recorded_at)), absences.unique_code
        )
 SELECT p.summary_date,
    u.unique_code,
    u.name,
    c.name AS class_name,
    p.zuhr,
    p.asr,
    p.pulang,
    p.ijin,
    p.last_updated AS updated_at
   FROM ((pivoted p
     JOIN public.users u ON (((p.unique_code)::text = (u.unique_code)::text)))
     JOIN public.classes c ON ((u.class_id = c.id)))
  WITH NO DATA;


ALTER TABLE public.attendance_summary OWNER TO shalatdev;

--
-- Name: classes_id_seq; Type: SEQUENCE; Schema: public; Owner: shalatdev
--

CREATE SEQUENCE public.classes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.classes_id_seq OWNER TO shalatdev;

--
-- Name: classes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: shalatdev
--

ALTER SEQUENCE public.classes_id_seq OWNED BY public.classes.id;


--
-- Name: users_class_id_seq; Type: SEQUENCE; Schema: public; Owner: shalatdev
--

CREATE SEQUENCE public.users_class_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_class_id_seq OWNER TO shalatdev;

--
-- Name: users_class_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: shalatdev
--

ALTER SEQUENCE public.users_class_id_seq OWNED BY public.users.class_id;


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: shalatdev
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO shalatdev;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: shalatdev
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: __drizzle_migrations id; Type: DEFAULT; Schema: drizzle; Owner: shalatdev
--

ALTER TABLE ONLY drizzle.__drizzle_migrations ALTER COLUMN id SET DEFAULT nextval('drizzle.__drizzle_migrations_id_seq'::regclass);


--
-- Name: absences id; Type: DEFAULT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.absences ALTER COLUMN id SET DEFAULT nextval('public.absences_id_seq'::regclass);


--
-- Name: classes id; Type: DEFAULT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.classes ALTER COLUMN id SET DEFAULT nextval('public.classes_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: users class_id; Type: DEFAULT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.users ALTER COLUMN class_id SET DEFAULT nextval('public.users_class_id_seq'::regclass);


--
-- Data for Name: __drizzle_migrations; Type: TABLE DATA; Schema: drizzle; Owner: shalatdev
--

COPY drizzle.__drizzle_migrations (id, hash, created_at) FROM stdin;
1	b7ca13fc5e01b0d3975712153aedfc6498ea554b9cc88ce30e43d029a946456e	1718175000000
2	61fa8b153a7b0e632a0111b455342f54110ce643cc23bfc35ab0c11a25ef8f85	1718175100000
3	cf464425d6e94095ba8e469c8dc78e5baf113690d2179cc78ec8de6984bde841	1746528837466
4	cbfcf0686ed53e23048cfa6f39b08a2b16aac07679750e92719816200145bd63	1746529199849
5	47d384ba4018487aed6074f3948beae6adf78f829a9151ad5f6e179723a8cc28	1746843355037
6	6f750a83528666c0e7bb71c772b762c14ee86a63d86ae854ba63444588a461d5	1747799342429
7	a9ece8aeb6eeab0891f5497cd2e106ac56ade07e296d633ed23778ee35fbfcdf	1747981219364
\.


--
-- Data for Name: absences; Type: TABLE DATA; Schema: public; Owner: shalatdev
--

COPY public.absences (id, unique_code, type, recorded_at, created_at) FROM stdin;
\.


--
-- Data for Name: classes; Type: TABLE DATA; Schema: public; Owner: shalatdev
--

COPY public.classes (id, name, created_at) FROM stdin;
1	X IPA 1	2025-06-17 07:41:55.73873
2	X IPA 2	2025-06-17 07:41:55.73873
3	X IPA 3	2025-06-17 07:41:55.73873
4	X IPS 1	2025-06-17 07:41:55.73873
5	X IPS 2	2025-06-17 07:41:55.73873
6	XI IPA 1	2025-06-17 07:41:55.73873
7	XI IPA 2	2025-06-17 07:41:55.73873
8	XI IPA 3	2025-06-17 07:41:55.73873
9	XI IPS 1	2025-06-17 07:41:55.73873
10	XI IPS 2	2025-06-17 07:41:55.73873
11	XII IPA 1	2025-06-17 07:41:55.73873
12	XII IPA 2	2025-06-17 07:41:55.73873
13	XII IPA 3	2025-06-17 07:41:55.73873
14	XII IPS 1	2025-06-17 07:41:55.73873
15	XII IPS 2	2025-06-17 07:41:55.73873
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: shalatdev
--

COPY public.users (id, role, unique_code, google_email, nis, username, name, whatsapp, class_id, password_hash, created_at, updated_at) FROM stdin;
\.


--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE SET; Schema: drizzle; Owner: shalatdev
--

SELECT pg_catalog.setval('drizzle.__drizzle_migrations_id_seq', 7, true);


--
-- Name: absences_id_seq; Type: SEQUENCE SET; Schema: public; Owner: shalatdev
--

SELECT pg_catalog.setval('public.absences_id_seq', 1, false);


--
-- Name: classes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: shalatdev
--

SELECT pg_catalog.setval('public.classes_id_seq', 15, true);


--
-- Name: users_class_id_seq; Type: SEQUENCE SET; Schema: public; Owner: shalatdev
--

SELECT pg_catalog.setval('public.users_class_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: shalatdev
--

SELECT pg_catalog.setval('public.users_id_seq', 1, true);


--
-- Name: __drizzle_migrations __drizzle_migrations_pkey; Type: CONSTRAINT; Schema: drizzle; Owner: shalatdev
--

ALTER TABLE ONLY drizzle.__drizzle_migrations
    ADD CONSTRAINT __drizzle_migrations_pkey PRIMARY KEY (id);


--
-- Name: absences absences_pkey; Type: CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.absences
    ADD CONSTRAINT absences_pkey PRIMARY KEY (id);


--
-- Name: classes classes_name_unique; Type: CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.classes
    ADD CONSTRAINT classes_name_unique UNIQUE (name);


--
-- Name: classes classes_pkey; Type: CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.classes
    ADD CONSTRAINT classes_pkey PRIMARY KEY (id);


--
-- Name: users users_google_email_unique; Type: CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_google_email_unique UNIQUE (google_email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_unique_code_unique; Type: CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_unique_code_unique UNIQUE (unique_code);


--
-- Name: users users_username_unique; Type: CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_unique UNIQUE (username);


--
-- Name: idx_absences_unique_code_recorded_at; Type: INDEX; Schema: public; Owner: shalatdev
--

CREATE INDEX idx_absences_unique_code_recorded_at ON public.absences USING btree (unique_code, recorded_at);


--
-- Name: idx_attendance_summary_date; Type: INDEX; Schema: public; Owner: shalatdev
--

CREATE INDEX idx_attendance_summary_date ON public.attendance_summary USING btree (summary_date);


--
-- Name: idx_users_class_id; Type: INDEX; Schema: public; Owner: shalatdev
--

CREATE INDEX idx_users_class_id ON public.users USING btree (class_id);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: shalatdev
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: idx_users_unique_code; Type: INDEX; Schema: public; Owner: shalatdev
--

CREATE INDEX idx_users_unique_code ON public.users USING btree (unique_code);


--
-- Name: absences absences_unique_code_users_unique_code_fk; Type: FK CONSTRAINT; Schema: public; Owner: shalatdev
--

ALTER TABLE ONLY public.absences
    ADD CONSTRAINT absences_unique_code_users_unique_code_fk FOREIGN KEY (unique_code) REFERENCES public.users(unique_code);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO shalatdev;


--
-- Name: attendance_summary; Type: MATERIALIZED VIEW DATA; Schema: public; Owner: shalatdev
--

REFRESH MATERIALIZED VIEW public.attendance_summary;


--
-- PostgreSQL database dump complete
--

