#!/bin/bash

# 🚀 Production Migration Script - Add Reason Column
# This script safely migrates the production database to add reason column

set -e  # Exit on any error

echo "🚀 Starting Production Migration - Add Reason Column"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL environment variable is not set!"
    echo "Please set it with: export DATABASE_URL='your_production_database_url'"
    exit 1
fi

print_status "Database URL configured: ${DATABASE_URL:0:20}..."

# Step 1: Create backup
print_status "Creating database backup..."
BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
if pg_dump "$DATABASE_URL" > "$BACKUP_FILE"; then
    print_success "Backup created: $BACKUP_FILE"
else
    print_error "Failed to create backup!"
    exit 1
fi

# Step 2: Check current schema
print_status "Checking current database schema..."
if psql "$DATABASE_URL" -c "\d+ absences" > /dev/null 2>&1; then
    print_success "Database connection successful"
else
    print_error "Failed to connect to database!"
    exit 1
fi

# Step 3: Check if reason column already exists
print_status "Checking if reason column already exists..."
REASON_EXISTS=$(psql "$DATABASE_URL" -t -c "
SELECT COUNT(*) FROM information_schema.columns 
WHERE table_name = 'absences' AND column_name = 'reason';
" | tr -d ' ')

if [ "$REASON_EXISTS" -gt 0 ]; then
    print_warning "Reason column already exists! Skipping migration."
    print_status "Verifying column structure..."
    psql "$DATABASE_URL" -c "
    SELECT column_name, data_type, character_maximum_length, is_nullable 
    FROM information_schema.columns 
    WHERE table_name = 'absences' AND column_name = 'reason';
    "
    print_success "Migration verification complete"
    exit 0
fi

# Step 4: Run Drizzle migration
print_status "Running Drizzle migration..."
if npx drizzle-kit migrate; then
    print_success "Migration completed successfully!"
else
    print_error "Migration failed!"
    print_warning "You can restore from backup: psql \$DATABASE_URL < $BACKUP_FILE"
    exit 1
fi

# Step 5: Verify migration
print_status "Verifying migration..."
REASON_EXISTS_AFTER=$(psql "$DATABASE_URL" -t -c "
SELECT COUNT(*) FROM information_schema.columns 
WHERE table_name = 'absences' AND column_name = 'reason';
" | tr -d ' ')

if [ "$REASON_EXISTS_AFTER" -gt 0 ]; then
    print_success "Reason column successfully added!"
    
    # Show the new schema
    print_status "Updated absences table structure:"
    psql "$DATABASE_URL" -c "\d+ absences"
    
    # Test the migration
    print_status "Testing migration with sample data..."
    TEST_CODE="migration-test-$(date +%s)"
    
    # Insert test record
    psql "$DATABASE_URL" -c "
    INSERT INTO absences (unique_code, type, recorded_at, reason) 
    VALUES ('$TEST_CODE', 'Sick', NOW(), 'Migration test - can be deleted');
    " > /dev/null
    
    # Verify test record
    TEST_RESULT=$(psql "$DATABASE_URL" -t -c "
    SELECT reason FROM absences WHERE unique_code = '$TEST_CODE';
    " | tr -d ' ')
    
    if [ "$TEST_RESULT" = "Migration test - can be deleted" ]; then
        print_success "Migration test passed!"
        
        # Clean up test record
        psql "$DATABASE_URL" -c "DELETE FROM absences WHERE unique_code = '$TEST_CODE';" > /dev/null
        print_status "Test data cleaned up"
    else
        print_error "Migration test failed!"
        exit 1
    fi
    
else
    print_error "Migration verification failed - reason column not found!"
    exit 1
fi

# Step 6: Final summary
echo ""
echo "🎉 Migration Summary"
echo "==================="
print_success "✅ Database backup created: $BACKUP_FILE"
print_success "✅ Reason column added to absences table"
print_success "✅ Migration verified with test data"
print_success "✅ Production database ready for reason functionality"

echo ""
print_status "Next steps:"
echo "1. Deploy the updated application code"
echo "2. Test manual entry with attendance types requiring reason"
echo "3. Monitor application logs for any issues"
echo "4. Keep the backup file safe: $BACKUP_FILE"

echo ""
print_success "🚀 Production migration completed successfully!"
