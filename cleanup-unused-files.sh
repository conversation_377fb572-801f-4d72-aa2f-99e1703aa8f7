#!/bin/bash

# Cleanup Unused Files and Code
# This script removes files and code that are no longer needed after SSOT timezone fix

echo "🧹 Cleaning up unused files and code..."
echo ""

# Configuration
BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"

echo "📦 Creating backup directory: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# 1. Remove test scripts that are no longer needed
echo ""
echo "1️⃣ Removing temporary test scripts..."

TEST_SCRIPTS=(
    "test-cache-fix.js"
    "test-timezone-fix.js" 
    "test-monthly-yearly-fix.js"
    "test-ssot-timezone-fix.js"
    "debug-cache-issue.js"
    "clear-cache-manual.js"
    "fix-missing-asr.sh"
    "fix-monthly-yearly.sh"
)

for script in "${TEST_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "   🗑️  Moving $script to backup"
        mv "$script" "$BACKUP_DIR/"
    fi
done

# 2. Clean up TODO files that are now completed
echo ""
echo "2️⃣ Cleaning up completed TODO files..."

COMPLETED_TODOS=(
    "TODO/CACHE-STRATEGY-FIX-SUMMARY.md"
    "TODO/TIMEZONE-BUG-FIX-SUMMARY.md"
    "TODO/MONTHLY-YEARLY-TIMEZONE-FIX.md"
    "TODO/SSOT-TIMEZONE-FIX-COMPLETE.md"
    "TODO/WRITE-THROUGH-CACHE-SOLUTION.md"
)

for todo in "${COMPLETED_TODOS[@]}"; do
    if [ -f "$todo" ]; then
        echo "   📝 Moving $todo to backup"
        mv "$todo" "$BACKUP_DIR/"
    fi
done

# 3. Remove unused imports and functions (this would need manual review)
echo ""
echo "3️⃣ Files that may need manual cleanup review:"

echo "   📁 lib/utils/date.ts - Check for unused timezone functions"
echo "   📁 app/api/absence/reports/route.ts - Remove old timezone comments"
echo "   📁 lib/data/repositories/absence.ts - Clean up old timezone logic"

# 4. Check for unused dependencies in package.json
echo ""
echo "4️⃣ Checking for potentially unused dependencies..."

# Common dependencies that might be unused after cleanup
POTENTIAL_UNUSED=(
    "moment"
    "date-fns"
    "luxon"
)

echo "   📦 Checking package.json for timezone libraries that might be unused:"
for dep in "${POTENTIAL_UNUSED[@]}"; do
    if grep -q "\"$dep\"" package.json 2>/dev/null; then
        echo "   ⚠️  Found $dep - verify if still needed"
    fi
done

# 5. List files that should be reviewed for cleanup
echo ""
echo "5️⃣ Files that should be manually reviewed for cleanup:"

REVIEW_FILES=(
    "lib/utils/date.ts"
    "app/api/absence/reports/route.ts"
    "app/api/absence/reports/range/route.ts"
    "app/api/debug/absence/route.ts"
    "lib/data/repositories/absence.ts"
)

for file in "${REVIEW_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "   📄 $file"
        echo "      - Remove old timezone comments"
        echo "      - Clean up unused variables"
        echo "      - Remove debug console.logs if not needed"
    fi
done

# 6. Check git status
echo ""
echo "6️⃣ Current git status:"
git status --porcelain | head -20

# 7. Suggest next steps
echo ""
echo "📋 Cleanup Summary:"
echo "✅ Moved test scripts to backup: $BACKUP_DIR"
echo "✅ Moved completed TODO files to backup: $BACKUP_DIR"
echo ""
echo "🔍 Manual cleanup needed:"
echo "1. Review files listed above for unused code"
echo "2. Remove old timezone comments and debug logs"
echo "3. Check package.json for unused dependencies"
echo "4. Run tests to ensure nothing is broken"
echo ""
echo "📝 Recommended next steps:"
echo "1. git add . && git commit -m 'SSOT timezone fix implementation'"
echo "2. Review and clean up the files listed above"
echo "3. Remove backup directory if everything works: rm -rf $BACKUP_DIR"
echo "4. Run npm run build to ensure no build errors"

echo ""
echo "🎯 Files moved to backup:"
ls -la "$BACKUP_DIR/" 2>/dev/null || echo "   (No files moved)"
