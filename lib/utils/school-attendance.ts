/**
 * School Attendance Utilities
 *
 * Utility functions for processing and managing school attendance data
 * following clean code principles and best practices.
 */

import { AttendanceType, getAttendanceTypeLabel } from '@/lib/domain/entities/absence'
import {
  SchoolAttendanceStatus,
  SchoolAttendanceItem,
  SchoolAttendanceSection,
  SchoolAttendanceDisplayConfig,
  SchoolAttendanceSummary,
  SchoolAttendanceValidationResult,
} from '@/lib/types/student-attendance'

/**
 * School Attendance Icons Mapping
 * Maps attendance types to their corresponding Lucide React icons
 */
export const SCHOOL_ATTENDANCE_ICONS: Record<AttendanceType, string> = {
  [AttendanceType.ENTRY]: 'LogIn',
  [AttendanceType.LATE_ENTRY]: 'Clock',
  [AttendanceType.EXCUSED_ABSENCE]: 'FileText',
  [AttendanceType.SICK]: 'Heart',
  [AttendanceType.TEMPORARY_LEAVE]: 'ArrowRight',
  [AttendanceType.RETURN_FROM_LEAVE]: 'ArrowLeft',
  // Prayer attendance icons (for completeness)
  [AttendanceType.ZUHR]: 'Sun',
  [AttendanceType.ASR]: 'Sunset',
  [AttendanceType.IJIN]: 'FileText',
  [AttendanceType.IJIN_ZUHR]: 'FileText',
  [AttendanceType.IJIN_ASR]: 'FileText',
  [AttendanceType.DISMISSAL]: 'Home',
}

/**
 * School Attendance Variants Mapping
 * Maps attendance types to their UI variants for styling
 */
export const SCHOOL_ATTENDANCE_VARIANTS: Record<
  AttendanceType,
  'success' | 'warning' | 'info' | 'neutral'
> = {
  [AttendanceType.ENTRY]: 'success',
  [AttendanceType.LATE_ENTRY]: 'warning',
  [AttendanceType.EXCUSED_ABSENCE]: 'info',
  [AttendanceType.SICK]: 'info',
  [AttendanceType.TEMPORARY_LEAVE]: 'info',
  [AttendanceType.RETURN_FROM_LEAVE]: 'success',
  // Prayer attendance variants (for completeness)
  [AttendanceType.ZUHR]: 'success',
  [AttendanceType.ASR]: 'success',
  [AttendanceType.IJIN]: 'info',
  [AttendanceType.IJIN_ZUHR]: 'info',
  [AttendanceType.IJIN_ASR]: 'info',
  [AttendanceType.DISMISSAL]: 'success',
}

/**
 * School Attendance Priority Mapping
 * Defines the display priority for attendance items (lower number = higher priority)
 * UPDATED: Consistent with Excel export logic - Sick > Excused Absence > Entry > Others
 */
export const SCHOOL_ATTENDANCE_PRIORITIES: Record<AttendanceType, number> = {
  [AttendanceType.SICK]: 1, // Highest priority - Main status
  [AttendanceType.EXCUSED_ABSENCE]: 2, // Second priority - Main status
  [AttendanceType.ENTRY]: 3, // Third priority - Main status (Hadir)
  [AttendanceType.LATE_ENTRY]: 4, // Fourth priority - Main status (Terlambat)
  [AttendanceType.TEMPORARY_LEAVE]: 5, // Lower priority - Additional info
  [AttendanceType.RETURN_FROM_LEAVE]: 6, // Lowest priority - Additional info
  // Prayer attendance priorities (for completeness)
  [AttendanceType.ZUHR]: 1,
  [AttendanceType.ASR]: 2,
  [AttendanceType.IJIN]: 3,
  [AttendanceType.IJIN_ZUHR]: 4,
  [AttendanceType.IJIN_ASR]: 5,
  [AttendanceType.DISMISSAL]: 6,
}

/**
 * Converts school attendance status to display items
 * @param status - School attendance status data
 * @returns Array of school attendance items for UI display
 */
export function convertSchoolAttendanceToItems(
  status: SchoolAttendanceStatus
): SchoolAttendanceItem[] {
  const items: SchoolAttendanceItem[] = []

  // Entry attendance
  items.push({
    id: 'entry',
    type: AttendanceType.ENTRY,
    label: getAttendanceTypeLabel(AttendanceType.ENTRY),
    icon: SCHOOL_ATTENDANCE_ICONS[AttendanceType.ENTRY],
    status: status.entry,
    time: status.entryTime,
    variant: status.entry ? SCHOOL_ATTENDANCE_VARIANTS[AttendanceType.ENTRY] : 'neutral',
    priority: SCHOOL_ATTENDANCE_PRIORITIES[AttendanceType.ENTRY],
  })

  // Late entry attendance
  items.push({
    id: 'lateEntry',
    type: AttendanceType.LATE_ENTRY,
    label: getAttendanceTypeLabel(AttendanceType.LATE_ENTRY),
    icon: SCHOOL_ATTENDANCE_ICONS[AttendanceType.LATE_ENTRY],
    status: status.lateEntry,
    time: status.lateEntryTime,
    variant: status.lateEntry ? SCHOOL_ATTENDANCE_VARIANTS[AttendanceType.LATE_ENTRY] : 'neutral',
    priority: SCHOOL_ATTENDANCE_PRIORITIES[AttendanceType.LATE_ENTRY],
  })

  // Excused absence
  items.push({
    id: 'excusedAbsence',
    type: AttendanceType.EXCUSED_ABSENCE,
    label: getAttendanceTypeLabel(AttendanceType.EXCUSED_ABSENCE),
    icon: SCHOOL_ATTENDANCE_ICONS[AttendanceType.EXCUSED_ABSENCE],
    status: status.excusedAbsence,
    time: status.excusedAbsenceTime,
    reason: status.excusedAbsenceReason,
    variant: status.excusedAbsence
      ? SCHOOL_ATTENDANCE_VARIANTS[AttendanceType.EXCUSED_ABSENCE]
      : 'neutral',
    priority: SCHOOL_ATTENDANCE_PRIORITIES[AttendanceType.EXCUSED_ABSENCE],
  })

  // Sick
  items.push({
    id: 'sick',
    type: AttendanceType.SICK,
    label: getAttendanceTypeLabel(AttendanceType.SICK),
    icon: SCHOOL_ATTENDANCE_ICONS[AttendanceType.SICK],
    status: status.sick,
    time: status.sickTime,
    reason: status.sickReason,
    variant: status.sick ? SCHOOL_ATTENDANCE_VARIANTS[AttendanceType.SICK] : 'neutral',
    priority: SCHOOL_ATTENDANCE_PRIORITIES[AttendanceType.SICK],
  })

  // Temporary leave
  items.push({
    id: 'temporaryLeave',
    type: AttendanceType.TEMPORARY_LEAVE,
    label: getAttendanceTypeLabel(AttendanceType.TEMPORARY_LEAVE),
    icon: SCHOOL_ATTENDANCE_ICONS[AttendanceType.TEMPORARY_LEAVE],
    status: status.temporaryLeave,
    time: status.temporaryLeaveTime,
    reason: status.temporaryLeaveReason,
    variant: status.temporaryLeave
      ? SCHOOL_ATTENDANCE_VARIANTS[AttendanceType.TEMPORARY_LEAVE]
      : 'neutral',
    priority: SCHOOL_ATTENDANCE_PRIORITIES[AttendanceType.TEMPORARY_LEAVE],
  })

  // Return from leave
  items.push({
    id: 'returnFromLeave',
    type: AttendanceType.RETURN_FROM_LEAVE,
    label: getAttendanceTypeLabel(AttendanceType.RETURN_FROM_LEAVE),
    icon: SCHOOL_ATTENDANCE_ICONS[AttendanceType.RETURN_FROM_LEAVE],
    status: status.returnFromLeave,
    time: status.returnFromLeaveTime,
    variant: status.returnFromLeave
      ? SCHOOL_ATTENDANCE_VARIANTS[AttendanceType.RETURN_FROM_LEAVE]
      : 'neutral',
    priority: SCHOOL_ATTENDANCE_PRIORITIES[AttendanceType.RETURN_FROM_LEAVE],
  })

  return items.sort((a, b) => a.priority - b.priority)
}

/**
 * Groups school attendance items into logical sections
 * @param items - Array of school attendance items
 * @returns Array of grouped sections
 */
export function groupSchoolAttendanceItems(
  items: SchoolAttendanceItem[]
): SchoolAttendanceSection[] {
  const sections: SchoolAttendanceSection[] = [
    {
      id: 'presence',
      title: 'Kehadiran',
      items: items.filter(
        item => item.type === AttendanceType.ENTRY || item.type === AttendanceType.LATE_ENTRY
      ),
      priority: 1,
    },
    {
      id: 'absence',
      title: 'Izin & Kesehatan',
      items: items.filter(
        item => item.type === AttendanceType.EXCUSED_ABSENCE || item.type === AttendanceType.SICK
      ),
      priority: 2,
    },
    {
      id: 'leave',
      title: 'Izin Keluar',
      items: items.filter(
        item =>
          item.type === AttendanceType.TEMPORARY_LEAVE ||
          item.type === AttendanceType.RETURN_FROM_LEAVE
      ),
      priority: 3,
    },
  ]

  return sections.filter(section => section.items.length > 0)
}

/**
 * Creates display configuration for school attendance
 * @param compactMode - Whether to use compact display mode
 * @param showReasons - Whether to show reason text
 * @param showTimeTooltips - Whether to show time tooltips
 * @returns Display configuration object
 */
export function createSchoolAttendanceDisplayConfig(
  compactMode: boolean = false,
  showReasons: boolean = true,
  showTimeTooltips: boolean = true
): SchoolAttendanceDisplayConfig {
  return {
    sections: [],
    showReasons,
    showTimeTooltips,
    compactMode,
  }
}

/**
 * Calculates school attendance summary statistics
 * @param status - School attendance status data
 * @returns Summary statistics object
 */
export function calculateSchoolAttendanceSummary(
  status: SchoolAttendanceStatus
): SchoolAttendanceSummary {
  const items = convertSchoolAttendanceToItems(status)
  const completedItems = items.filter(item => item.status).length
  const totalItems = items.length
  const pendingItems = totalItems - completedItems
  const completionRate = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0

  // Find the latest activity time
  const times = items
    .filter(item => item.time)
    .map(item => new Date(item.time!))
    .filter(date => !isNaN(date.getTime()))

  const lastActivity = times.length > 0 ? new Date(Math.max(...times.map(d => d.getTime()))) : null

  return {
    totalItems,
    completedItems,
    pendingItems,
    completionRate,
    lastActivity,
  }
}

/**
 * Validates school attendance data
 * @param status - School attendance status data
 * @returns Validation result with errors and warnings
 */
export function validateSchoolAttendanceData(
  status: SchoolAttendanceStatus
): SchoolAttendanceValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Business rule: Student cannot have both entry and late entry
  if (status.entry && status.lateEntry) {
    errors.push('Siswa tidak dapat memiliki status masuk normal dan terlambat secara bersamaan')
  }

  // Business rule: Excused absence and sick are alternative statuses
  if (status.excusedAbsence && status.sick) {
    warnings.push('Siswa memiliki status izin dan sakit secara bersamaan')
  }

  // Business rule: Temporary leave should have corresponding return
  if (status.temporaryLeave && !status.returnFromLeave) {
    warnings.push('Siswa memiliki izin keluar sementara tetapi belum kembali')
  }

  // Validation: Required reasons for certain attendance types
  if (status.excusedAbsence && !status.excusedAbsenceReason?.trim()) {
    warnings.push('Status izin sebaiknya disertai dengan alasan')
  }

  if (status.sick && !status.sickReason?.trim()) {
    warnings.push('Status sakit sebaiknya disertai dengan alasan')
  }

  if (status.temporaryLeave && !status.temporaryLeaveReason?.trim()) {
    warnings.push('Izin keluar sementara sebaiknya disertai dengan alasan')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  }
}

/**
 * Checks if school attendance data is empty
 * @param status - School attendance status data
 * @returns True if no attendance data is present
 */
export function isSchoolAttendanceEmpty(status: SchoolAttendanceStatus): boolean {
  return (
    !status.entry &&
    !status.lateEntry &&
    !status.excusedAbsence &&
    !status.sick &&
    !status.temporaryLeave &&
    !status.returnFromLeave
  )
}

/**
 * Gets the primary attendance status for a student
 * @param status - School attendance status data
 * @returns Primary attendance type or null if none
 */
export function getPrimarySchoolAttendanceStatus(
  status: SchoolAttendanceStatus
): AttendanceType | null {
  if (status.entry) return AttendanceType.ENTRY
  if (status.lateEntry) return AttendanceType.LATE_ENTRY
  if (status.excusedAbsence) return AttendanceType.EXCUSED_ABSENCE
  if (status.sick) return AttendanceType.SICK
  if (status.temporaryLeave) return AttendanceType.TEMPORARY_LEAVE
  if (status.returnFromLeave) return AttendanceType.RETURN_FROM_LEAVE
  return null
}
