/**
 * Analytics Export Utilities
 * Clean Architecture - Infrastructure Layer
 */

import {
  DashboardKPIs,
  AnalyticsSummary,
  ClassAnalytics,
  StudentAnalytics,
  AnalyticsExportConfig,
} from '../domain/entities/analytics'
import { TIMEZONE_CONFIG } from '../config'

/**
 * Helper function to escape CSV values
 */
const escapeCsvValue = (value: string | number | null | undefined): string => {
  if (value === null || value === undefined) return '""'
  const stringValue = String(value)
  if (stringValue.includes('"') || stringValue.includes(',') || stringValue.includes('\n')) {
    return `"${stringValue.replace(/"/g, '""')}"`
  }
  return stringValue
}

/**
 * Get current time in configured timezone
 */
const getCurrentWITATime = () => {
  return new Date(new Date().toLocaleString('en-US', { timeZone: TIMEZONE_CONFIG.TIMEZONE }))
}

/**
 * Generate comprehensive analytics CSV export
 */
export function generateAnalyticsCSV(
  kpis: DashboardKPIs,
  summary: AnalyticsSummary,
  config: Partial<AnalyticsExportConfig> = {}
): string {
  const currentTime = getCurrentWITATime()
  const exportDate =
    currentTime.toLocaleDateString(TIMEZONE_CONFIG.LOCALE, {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
      timeZone: TIMEZONE_CONFIG.TIMEZONE,
    }) +
    ' ' +
    currentTime.toLocaleTimeString(TIMEZONE_CONFIG.LOCALE, {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: TIMEZONE_CONFIG.TIMEZONE,
    }) +
    ` ${TIMEZONE_CONFIG.TIMEZONE_NAMES[TIMEZONE_CONFIG.TIMEZONE as keyof typeof TIMEZONE_CONFIG.TIMEZONE_NAMES]}`

  // Create metadata section
  const metadata = [
    ['=== LAPORAN ANALYTICS KEHADIRAN SHALAT ==='],
    ['SMK Negeri 3 Banjarmasin'],
    ['https://smkn3banjarmasin.sch.id/'],
    [''],
    ['INFORMASI LAPORAN'],
    [`Periode: ${summary.period}`],
    [`Total Siswa: ${summary.totalStudents} siswa`],
    [`Tingkat Kehadiran Keseluruhan: ${summary.overallAttendanceRate.toFixed(1)}%`],
    [`Tingkat Kepatuhan Shalat: ${summary.prayerComplianceRate.toFixed(1)}%`],
    [`Tanggal Ekspor: ${exportDate}`],
    [''],
    ['RINGKASAN KPI HARI INI'],
    [`Total Siswa Aktif: ${kpis.todayAttendance.total} siswa`],
    [`Siswa Hadir: ${kpis.todayAttendance.present} siswa (${kpis.todayAttendance.percentage}%)`],
    [`Siswa Tidak Hadir: ${kpis.todayAttendance.absent} siswa`],
    [
      `Perubahan dari Kemarin: ${kpis.todayAttendance.change > 0 ? '+' : ''}${kpis.todayAttendance.change.toFixed(1)}%`,
    ],
    [''],
    ['METRIK SHALAT HARI INI'],
    [`Kehadiran Shalat Zuhur: ${kpis.prayerMetrics.zuhrAttendance} siswa`],
    [`Kehadiran Shalat Asr: ${kpis.prayerMetrics.asrAttendance} siswa`],
    [`Tingkat Kepatuhan Shalat: ${kpis.prayerMetrics.overallCompliance.toFixed(1)}%`],
    [
      `Perubahan Kepatuhan: ${kpis.prayerMetrics.change > 0 ? '+' : ''}${kpis.prayerMetrics.change.toFixed(1)}%`,
    ],
    [''],
    ['TREN MINGGUAN'],
    [`Minggu Ini: ${kpis.weeklyTrend.currentWeek}%`],
    [`Minggu Lalu: ${kpis.weeklyTrend.previousWeek}%`],
    [`Perubahan: ${kpis.weeklyTrend.change > 0 ? '+' : ''}${kpis.weeklyTrend.change.toFixed(1)}%`],
    [
      `Tren: ${kpis.weeklyTrend.trend === 'up' ? 'Meningkat' : kpis.weeklyTrend.trend === 'down' ? 'Menurun' : 'Stabil'}`,
    ],
    [''],
  ]

  // Add insights section
  if (config.includeSummary !== false && summary.insights.length > 0) {
    metadata.push(['WAWASAN UTAMA'])
    summary.insights.forEach(insight => {
      metadata.push([`${insight.description}: ${insight.value}`])
    })
    metadata.push([''])
  }

  // Add recommendations section
  if (config.includeRecommendations !== false && summary.recommendations.length > 0) {
    metadata.push(['REKOMENDASI TINDAKAN'])
    summary.recommendations.forEach((rec, index) => {
      metadata.push([`${index + 1}. ${rec.title} (Prioritas: ${rec.priority.toUpperCase()})`])
      metadata.push([`   ${rec.description}`])
      rec.actionItems.forEach(action => {
        metadata.push([`   • ${action}`])
      })
      metadata.push([''])
    })
  }

  // Class performance section
  metadata.push(['=== ANALISIS PERFORMA KELAS ==='])
  metadata.push([''])

  const classHeaders = [
    'No',
    'Nama_Kelas',
    'Total_Siswa',
    'Siswa_Hadir',
    'Siswa_Tidak_Hadir',
    'Tingkat_Kehadiran_Persen',
    'Tingkat_Kepatuhan_Shalat_Persen',
    'Tingkat_Shalat_Zuhur_Persen',
    'Tingkat_Shalat_Asr_Persen',
    'Tingkat_Pulang_Persen',
    'Tingkat_Ijin_Persen',
    'Tren_Performa',
    'Level_Risiko',
    'Status_Kelas',
    'Rekomendasi_Tindakan',
  ]

  const classRows: string[][] = []
  summary.classPerformance.forEach((classData, index) => {
    const status =
      classData.attendanceRate >= 90
        ? 'SANGAT_BAIK'
        : classData.attendanceRate >= 80
          ? 'BAIK'
          : classData.attendanceRate >= 70
            ? 'PERLU_PERHATIAN'
            : 'KRITIS'

    const recommendations = []
    if (classData.riskLevel === 'high') {
      recommendations.push('Intervensi segera diperlukan')
      recommendations.push('Rapat dengan wali kelas')
      recommendations.push('Program pembinaan khusus')
    } else if (classData.riskLevel === 'medium') {
      recommendations.push('Monitoring ketat')
      recommendations.push('Dukungan tambahan')
    } else {
      recommendations.push('Pertahankan performa')
      recommendations.push('Jadikan contoh untuk kelas lain')
    }

    classRows.push([
      escapeCsvValue((index + 1).toString()),
      escapeCsvValue(classData.className),
      escapeCsvValue(classData.totalStudents.toString()),
      escapeCsvValue(classData.presentStudents.toString()),
      escapeCsvValue(classData.absentStudents.toString()),
      escapeCsvValue(classData.attendanceRate.toFixed(1)),
      escapeCsvValue(classData.prayerComplianceRate.toFixed(1)),
      escapeCsvValue(classData.zuhrRate.toFixed(1)),
      escapeCsvValue(classData.asrRate.toFixed(1)),
      escapeCsvValue(classData.dismissalRate.toFixed(1)),
      escapeCsvValue(classData.ijinRate.toFixed(1)),
      escapeCsvValue(classData.trend.toUpperCase()),
      escapeCsvValue(classData.riskLevel.toUpperCase()),
      escapeCsvValue(status),
      escapeCsvValue(recommendations.join('; ')),
    ])
  })

  // Top performers section
  metadata.push([''])
  metadata.push(['=== SISWA BERPRESTASI TERBAIK ==='])
  metadata.push([''])

  const topPerformersHeaders = [
    'No',
    'Kode_Siswa',
    'Nama_Siswa',
    'Kelas',
    'Tingkat_Kehadiran_Persen',
    'Tingkat_Kepatuhan_Shalat_Persen',
    'Skor_Konsistensi',
    'Skor_Risiko',
    'Tren_Performa',
    'Hari_Berturut_Turut',
    'Total_Shalat_Dihadiri',
    'Kehadiran_Terakhir',
    'Status_Siswa',
  ]

  const topPerformersRows: string[][] = []
  summary.topPerformers.slice(0, 10).forEach((student, index) => {
    const status =
      student.attendanceRate >= 95
        ? 'TELADAN'
        : student.attendanceRate >= 90
          ? 'SANGAT_BAIK'
          : 'BAIK'

    topPerformersRows.push([
      escapeCsvValue((index + 1).toString()),
      escapeCsvValue(student.uniqueCode),
      escapeCsvValue(student.name),
      escapeCsvValue(student.className),
      escapeCsvValue(student.attendanceRate.toFixed(1)),
      escapeCsvValue(student.prayerComplianceRate.toFixed(1)),
      escapeCsvValue(student.consistencyScore.toString()),
      escapeCsvValue(student.riskScore.toString()),
      escapeCsvValue(student.trend.toUpperCase()),
      escapeCsvValue(student.streakDays.toString()),
      escapeCsvValue(student.totalPrayersAttended.toString()),
      escapeCsvValue(student.lastAttendance || 'Tidak ada data'),
      escapeCsvValue(status),
    ])
  })

  // At-risk students section
  metadata.push([''])
  metadata.push(['=== SISWA BERISIKO TINGGI ==='])
  metadata.push([''])

  const atRiskHeaders = [
    'No',
    'Kode_Siswa',
    'Nama_Siswa',
    'Kelas',
    'Tingkat_Kehadiran_Persen',
    'Tingkat_Kepatuhan_Shalat_Persen',
    'Skor_Risiko',
    'Tren_Performa',
    'Shalat_Terlewat',
    'Kehadiran_Terakhir',
    'Level_Prioritas',
    'Rekomendasi_Intervensi',
  ]

  const atRiskRows: string[][] = []
  summary.atRiskStudents.slice(0, 20).forEach((student, index) => {
    const priority =
      student.riskScore >= 90 ? 'KRITIS' : student.riskScore >= 80 ? 'TINGGI' : 'SEDANG'

    atRiskRows.push([
      escapeCsvValue((index + 1).toString()),
      escapeCsvValue(student.uniqueCode),
      escapeCsvValue(student.name),
      escapeCsvValue(student.className),
      escapeCsvValue(student.attendanceRate.toFixed(1)),
      escapeCsvValue(student.prayerComplianceRate.toFixed(1)),
      escapeCsvValue(student.riskScore.toString()),
      escapeCsvValue(student.trend.toUpperCase()),
      escapeCsvValue(student.totalPrayersMissed.toString()),
      escapeCsvValue(student.lastAttendance || 'Tidak ada data'),
      escapeCsvValue(priority),
      escapeCsvValue(student.recommendations.join('; ')),
    ])
  })

  // Create comprehensive footer
  const footer = [
    [''],
    ['=== PANDUAN INTERPRETASI DATA ==='],
    [''],
    ['1. TINGKAT KEHADIRAN:'],
    ['   - SANGAT_BAIK: ≥90% - Performa luar biasa'],
    ['   - BAIK: 80-89% - Performa memuaskan'],
    ['   - PERLU_PERHATIAN: 70-79% - Memerlukan monitoring'],
    ['   - KRITIS: <70% - Intervensi segera diperlukan'],
    [''],
    ['2. LEVEL RISIKO:'],
    ['   - LOW: Siswa/kelas berkinerja baik, risiko rendah'],
    ['   - MEDIUM: Memerlukan perhatian dan monitoring'],
    ['   - HIGH: Risiko tinggi, intervensi segera diperlukan'],
    [''],
    ['3. TREN PERFORMA:'],
    ['   - IMPROVING: Menunjukkan perbaikan'],
    ['   - STABLE: Performa stabil'],
    ['   - DECLINING: Menunjukkan penurunan'],
    [''],
    ['4. SKOR KONSISTENSI:'],
    ['   - Mengukur keteraturan kehadiran (0-100)'],
    ['   - Skor tinggi = kehadiran yang konsisten'],
    [''],
    ['5. SKOR RISIKO:'],
    ['   - Mengukur risiko masalah kehadiran (0-100)'],
    ['   - Skor tinggi = risiko tinggi'],
    [''],
    ['=== TINDAK LANJUT YANG DISARANKAN ==='],
    [''],
    ['Untuk Kelas/Siswa Berisiko Tinggi:'],
    ['1. Konseling individual atau kelompok'],
    ['2. Koordinasi dengan wali kelas dan orang tua'],
    ['3. Program pembinaan khusus'],
    ['4. Monitoring harian'],
    ['5. Evaluasi mingguan'],
    [''],
    ['Untuk Kelas/Siswa Berprestasi:'],
    ['1. Berikan apresiasi dan penghargaan'],
    ['2. Jadikan mentor untuk yang lain'],
    ['3. Libatkan dalam program kepemimpinan'],
    [''],
    [`Diekspor pada: ${exportDate}`],
    [`Sistem Analytics Kehadiran Shalat - SMK Negeri 3 Banjarmasin`],
    [`Website: https://smkn3banjarmasin.sch.id/`],
  ]

  // Combine all content
  return [
    ...metadata.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
    classHeaders.map(header => escapeCsvValue(header)).join(','),
    ...classRows.map(row => row.join(',')),
    [''],
    topPerformersHeaders.map(header => escapeCsvValue(header)).join(','),
    ...topPerformersRows.map(row => row.join(',')),
    [''],
    atRiskHeaders.map(header => escapeCsvValue(header)).join(','),
    ...atRiskRows.map(row => row.join(',')),
    ...footer.map(row => row.map(cell => escapeCsvValue(cell)).join(',')),
  ].join('\n')
}

/**
 * Generate filename for analytics export
 */
export function generateAnalyticsFilename(period: string): string {
  const currentTime = getCurrentWITATime()
  const dateStr = currentTime.toISOString().split('T')[0]
  return `analytics-kehadiran-shalat-${period}-${dateStr}.csv`
}

/**
 * Download CSV file
 */
export function downloadCSV(content: string, filename: string): void {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
