/**
 * Excel Matrix Yearly Export
 * Clean Architecture - Infrastructure Layer
 *
 * Single responsibility: Generate yearly matrix Excel reports
 * Handles both prayer and school reports for yearly periods
 * Supports both single-sheet and month-tabs formats
 */

import ExcelJS from 'exceljs'
import { format, eachDayOfInterval } from 'date-fns'
import { id } from 'date-fns/locale'
import type { MatrixStudentData, ReportType } from './excel-shared-constants'
import {
  EXCEL_FONTS as FONTS,
  EXCEL_COLORS as COLORS,
  EXCEL_COLUMN_WIDTHS as WIDTHS,
  STATUS_LEGENDS,
} from './excel-shared-constants'
import { generateHoverDetails, generateFallbackHover } from './excel-hover-generator'

/**
 * Create yearly matrix with tabs per month (for single class)
 */
export async function createYearlyMatrixWithMonthTabs(
  workbook: ExcelJS.Workbook,
  className: string,
  students: MatrixStudentData[],
  startDate: Date,
  endDate: Date,
  reportType: ReportType,
  enableFormatting: boolean = true,
  setProgress?: (progress: number) => void
): Promise<void> {
  console.log(`📅 Creating yearly matrix with month tabs for class ${className}`)

  const dateRange = eachDayOfInterval({ start: startDate, end: endDate })
  const datesByMonth = groupDatesByMonth(dateRange)
  const months = Array.from(datesByMonth.keys()).sort()

  for (let i = 0; i < months.length; i++) {
    const monthKey = months[i]
    const monthDates = datesByMonth.get(monthKey)!
    const monthLabel = format(monthDates[0], 'MMMM yyyy', { locale: id })

    const worksheet = workbook.addWorksheet(monthLabel)
    await createMonthlyMatrixWorksheetWithCustomSummary(
      worksheet,
      className,
      students,
      monthDates,
      reportType,
      enableFormatting
    )

    const progress = 50 + ((i + 1) / months.length) * 50
    setProgress?.(Math.round(progress))
  }

  console.log(`✅ Yearly matrix with month tabs completed for class ${className}`)
}

/**
 * Create monthly matrix worksheet with custom summary calculation for yearly tabs
 */
async function createMonthlyMatrixWorksheetWithCustomSummary(
  worksheet: ExcelJS.Worksheet,
  className: string,
  students: MatrixStudentData[],
  monthDates: Date[],
  reportType: ReportType,
  enableFormatting: boolean = true
): Promise<void> {
  console.log(
    `📊 Creating monthly matrix with custom summary for class ${className} with ${students.length} students`
  )

  // Create title
  const reportTypeLabel = reportType === 'prayer' ? 'Shalat' : 'Sekolah'
  const periodLabel = format(monthDates[0], 'MMMM yyyy', { locale: id })
  const titleText = `Laporan ${reportTypeLabel} Kelas ${className} - ${periodLabel}`

  const titleRow = worksheet.addRow([titleText])
  if (enableFormatting) {
    titleRow.font = FONTS.TITLE
    titleRow.alignment = { horizontal: 'center' }
    worksheet.mergeCells(1, 1, 1, 10)
  }

  // Add empty row
  worksheet.addRow([])

  // Create headers with numbered date columns for compact matrix format
  const headers = ['No', 'NIS', 'Nama', 'L/P']

  // Add numbered date columns (1, 2, 3, 4, 5, etc.) instead of actual dates
  // This creates a more compact table while maintaining readability
  // The actual dates are implied from the report period
  for (let i = 1; i <= monthDates.length; i++) {
    headers.push(i.toString())
  }

  // Add summary headers based on report type
  if (reportType === 'prayer') {
    headers.push('Total H', 'Total Z', 'Total A', 'Total I')
  } else {
    headers.push('H', 'T', 'S', 'I', 'IS', 'K') // RESTORED: Keep 'T' column for totals
  }

  const headerRow = worksheet.addRow(headers)
  if (enableFormatting) {
    headerRow.font = FONTS.HEADER
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: COLORS.COLUMN_HEADER },
    }
    headerRow.alignment = { horizontal: 'center' }
  }

  // Add student data with MONTHLY SUMMARY (not yearly)
  const firstStudentRow = 4 // After title, empty row, and headers
  for (let i = 0; i < students.length; i++) {
    const student = students[i]
    const row = [i + 1, student.nis, student.name, student.gender]

    // Add daily status for each date in this month
    for (const date of monthDates) {
      const dateKey = format(date, 'yyyy-MM-dd')
      const status = student.dailyStatus[dateKey] || '-'
      // FIXED: Display 'T' (Terlambat) as 'H' in Excel cell
      const displayStatus = status === 'T' ? 'H' : status
      row.push(displayStatus)
    }

    // Calculate and add MONTHLY summary (not yearly summary)
    const monthlySummary = calculateMonthlySummary(student, monthDates, reportType)
    addMonthlySummaryToRow(row, monthlySummary, reportType)

    worksheet.addRow(row)
  }

  // Add hover details with accurate positioning
  if (enableFormatting) {
    addHoverDetailsToYearlyMatrix(worksheet, students, monthDates, firstStudentRow, reportType)
  }

  // Add legend and format columns
  addLegendAndFormatting(worksheet, students, reportType)

  console.log(`✅ Monthly matrix with custom summary completed for class ${className}`)
}

/**
 * Create yearly matrix in single sheet (for multiple classes or single sheet preference)
 */
export async function createYearlyMatrixSingleSheet(
  worksheet: ExcelJS.Worksheet,
  className: string,
  students: MatrixStudentData[],
  startDate: Date,
  endDate: Date,
  reportType: ReportType,
  enableFormatting: boolean = true
): Promise<void> {
  console.log(`📅 Creating yearly matrix single sheet for class ${className}`)

  // Create title
  const reportTypeLabel = reportType === 'prayer' ? 'Shalat' : 'Sekolah'
  const yearLabel = startDate.getFullYear().toString()
  const titleText = `Laporan ${reportTypeLabel} Kelas ${className} - Tahun ${yearLabel}`

  const titleRow = worksheet.addRow([titleText])
  if (enableFormatting) {
    titleRow.font = FONTS.TITLE
    titleRow.alignment = { horizontal: 'center' }
    worksheet.mergeCells(1, 1, 1, 10)
  }

  // Add empty row
  worksheet.addRow([])

  // Group dates by month
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate })
  const datesByMonth = groupDatesByMonth(dateRange)
  const months = Array.from(datesByMonth.keys()).sort()

  let currentRow = 3

  // Process each month
  for (const monthKey of months) {
    const monthDates = datesByMonth.get(monthKey)!
    const monthLabel = format(monthDates[0], 'MMMM yyyy', { locale: id }).toUpperCase()

    // Add month section header
    const monthHeaderRow = worksheet.addRow([`=== ${monthLabel} ===`])
    if (enableFormatting) {
      monthHeaderRow.font = { bold: true, size: 12 }
      monthHeaderRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: COLORS.MONTH_HEADER },
      }
      worksheet.mergeCells(currentRow, 1, currentRow, 10)
    }
    currentRow++

    // Create headers for this month with numbered date columns for compact matrix format
    const headers = ['No', 'NIS', 'Nama', 'L/P']

    // Add numbered date columns (1, 2, 3, 4, 5, etc.) instead of actual dates
    // This creates a more compact table while maintaining readability
    // The actual dates are implied from the report period
    for (let i = 1; i <= monthDates.length; i++) {
      headers.push(i.toString())
    }

    // Add summary headers
    if (reportType === 'prayer') {
      headers.push('Total H', 'Total Z', 'Total A', 'Total I')
    } else {
      headers.push('H', 'T', 'S', 'I', 'IS', 'K') // RESTORED: Keep 'T' column for totals
    }

    const headerRow = worksheet.addRow(headers)
    if (enableFormatting) {
      headerRow.font = FONTS.HEADER
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: COLORS.COLUMN_HEADER },
      }
      headerRow.alignment = { horizontal: 'center' }
    }
    currentRow++

    // Add student data for this month
    const firstStudentRowForMonth = currentRow
    for (let i = 0; i < students.length; i++) {
      const student = students[i]
      const row = [i + 1, student.nis, student.name, student.gender]

      // Add daily status for this month
      for (const date of monthDates) {
        const dateKey = format(date, 'yyyy-MM-dd')
        const status = student.dailyStatus[dateKey] || '-'
        // FIXED: Display 'T' (Terlambat) as 'H' in Excel cell
        const displayStatus = status === 'T' ? 'H' : status
        row.push(displayStatus)
      }

      // Add monthly summary
      const monthlySummary = calculateMonthlySummary(student, monthDates, reportType)
      addMonthlySummaryToRow(row, monthlySummary, reportType)

      worksheet.addRow(row)
      currentRow++
    }

    // Add hover details for this month with EXACT positioning
    if (enableFormatting) {
      addHoverDetailsToYearlyMatrix(
        worksheet,
        students,
        monthDates,
        firstStudentRowForMonth,
        reportType
      )
    }

    // Add empty row between months
    worksheet.addRow([])
    currentRow++
  }

  // Add legend and formatting
  addLegendAndFormatting(worksheet, students, reportType)

  console.log(`✅ Yearly matrix single sheet completed for class ${className}`)
}

/**
 * Group dates by month
 */
function groupDatesByMonth(dates: Date[]): Map<string, Date[]> {
  const grouped = new Map<string, Date[]>()

  for (const date of dates) {
    const monthKey = format(date, 'yyyy-MM')
    if (!grouped.has(monthKey)) {
      grouped.set(monthKey, [])
    }
    grouped.get(monthKey)!.push(date)
  }

  return grouped
}

/**
 * Calculate monthly summary for a student
 */
function calculateMonthlySummary(
  student: MatrixStudentData,
  monthDates: Date[],
  reportType: ReportType
): any {
  const summary: any = {}

  if (reportType === 'prayer') {
    summary.totalH = 0
    summary.totalZ = 0
    summary.totalA = 0
    summary.totalI = 0

    monthDates.forEach(date => {
      const dateKey = format(date, 'yyyy-MM-dd')
      const activities = student.dailyActivities[dateKey] || []

      // UPDATED: Count all activities, not just primary status
      activities.forEach(activityCode => {
        if (activityCode === 'H') summary.totalH++
        else if (activityCode === 'Z') summary.totalZ++
        else if (activityCode === 'A') summary.totalA++
        else if (activityCode === 'I') summary.totalI++
      })
    })
  } else {
    summary.totalHadir = 0
    summary.totalTerlambat = 0 // RESTORED: Keep separate count for late entry
    summary.totalSakit = 0
    summary.totalIjinTidakHadir = 0
    summary.totalIjinSementara = 0
    summary.totalKembali = 0

    monthDates.forEach(date => {
      const dateKey = format(date, 'yyyy-MM-dd')
      const activities = student.dailyActivities[dateKey] || []

      // UPDATED: Count all activities, not just primary status
      activities.forEach(activityCode => {
        if (activityCode === 'H')
          summary.totalHadir++ // includes former M (Masuk Only)
        else if (activityCode === 'T') {
          summary.totalHadir++ // FIXED: Terlambat counts as Hadir
          summary.totalTerlambat++ // Also count separately for detail
        } else if (activityCode === 'S') summary.totalSakit++
        else if (activityCode === 'I') summary.totalIjinTidakHadir++
        else if (activityCode === 'IS') summary.totalIjinSementara++
        else if (activityCode === 'K') summary.totalKembali++
      })
      // Note: M (Masuk Only) now becomes H (Hadir)
      // Note: P (Pulang Only) now becomes - (Absen) - not counted
      // Note: '-' (absent) is not counted in any category
    })
  }

  return summary
}

/**
 * Add monthly summary to row
 */
function addMonthlySummaryToRow(row: any[], summary: any, reportType: ReportType): void {
  if (reportType === 'prayer') {
    row.push(
      summary.totalH.toString(),
      summary.totalZ.toString(),
      summary.totalA.toString(),
      summary.totalI.toString()
    )
  } else {
    row.push(
      summary.totalHadir.toString(),
      summary.totalTerlambat.toString(), // RESTORED: Keep separate count
      summary.totalSakit.toString(),
      summary.totalIjinTidakHadir.toString(),
      summary.totalIjinSementara.toString(),
      summary.totalKembali.toString()
    )
  }
}

/**
 * Add hover details to yearly matrix with EXACT positioning
 */
function addHoverDetailsToYearlyMatrix(
  worksheet: ExcelJS.Worksheet,
  students: MatrixStudentData[],
  monthDates: Date[],
  firstStudentRowForMonth: number,
  reportType: ReportType
): void {
  console.log(
    `🔍 Adding hover details to yearly matrix starting from row ${firstStudentRowForMonth}`
  )

  students.forEach((student, studentIndex) => {
    const studentRow = firstStudentRowForMonth + studentIndex

    monthDates.forEach((date, dateIndex) => {
      const dateKey = format(date, 'yyyy-MM-dd')
      const status = student.dailyStatus[dateKey] || '-'

      // Only add hover for cells with actual status codes (not absent)
      if (status && status !== '-') {
        const cellCol = 5 + dateIndex // After No, NIS, Nama, L/P
        const cell = worksheet.getCell(studentRow, cellCol)

        // Get detailed information for hover
        const studentDetail = student.details[dateKey]

        let hoverText: string
        if (studentDetail && studentDetail.times && Object.keys(studentDetail.times).length > 0) {
          hoverText = generateHoverDetails(studentDetail, status, date, reportType)
        } else {
          hoverText = generateFallbackHover(status, date, reportType)
        }

        cell.note = {
          texts: [
            {
              text: hoverText,
              font: FONTS.COMMENT,
            },
          ],
        }
      }
    })
  })
}

/**
 * Add legend and apply formatting to worksheet
 */
function addLegendAndFormatting(
  worksheet: ExcelJS.Worksheet,
  students: MatrixStudentData[],
  reportType: ReportType
): void {
  // Add legend
  worksheet.addRow([])
  const legendRow = worksheet.addRow(['Keterangan Status:'])
  legendRow.font = FONTS.HEADER

  const statusCodes = STATUS_LEGENDS[reportType]
  worksheet.addRow([statusCodes])

  // Auto-fit columns
  worksheet.columns.forEach((column, index) => {
    if (index === 0) {
      column.width = WIDTHS.NO
    } else if (index === 1) {
      column.width = WIDTHS.NIS
    } else if (index === 2) {
      // Nama column - dynamic width based on content
      const maxNameLength = Math.max(
        ...students.map(student => (student.name || '').length),
        'Nama'.length
      )
      column.width = Math.min(Math.max(maxNameLength + 2, WIDTHS.NAME_MIN), WIDTHS.NAME_MAX)
    } else if (index === 3) {
      column.width = WIDTHS.GENDER
    } else {
      column.width = WIDTHS.DATE
    }
  })
}
