/**
 * 🎯 SIMPLIFIED SESSION PERFORMANCE MONITOR
 *
 * Root Cause Fix: Drastically simplified monitoring
 * - Only essential metrics, no complex warnings
 * - Minimal overhead, maximum clarity
 * - Simple implementation, easy to understand
 */

// ✅ SIMPLIFIED: Only essential metrics
interface SessionMetrics {
  totalRequests: number
  averageResponseTime: number
  lastReset: number
}

class SessionPerformanceMonitor {
  private static instance: SessionPerformanceMonitor
  private metrics: SessionMetrics = {
    totalRequests: 0,
    averageResponseTime: 0,
    lastReset: Date.now(),
  }
  private responseTimes: number[] = []
  private readonly MAX_RESPONSE_TIMES = 50 // ✅ SIMPLIFIED: Keep last 50 only

  static getInstance(): SessionPerformanceMonitor {
    if (!SessionPerformanceMonitor.instance) {
      SessionPerformanceMonitor.instance = new SessionPerformanceMonitor()
    }
    return SessionPerformanceMonitor.instance
  }

  /**
   * ✅ SIMPLIFIED: Record any request with response time
   */
  recordRequest(responseTime: number) {
    this.metrics.totalRequests++
    this.recordResponseTime(responseTime)
  }

  /**
   * ✅ LEGACY COMPATIBILITY: Map old methods to new simplified method
   */
  recordSessionCheck(responseTime: number) {
    this.recordRequest(responseTime)
  }

  recordRedisOp() {
    // ✅ SIMPLIFIED: No separate tracking, just increment total
    this.metrics.totalRequests++
  }

  recordApiCall(responseTime: number) {
    this.recordRequest(responseTime)
  }

  // ✅ REMOVED: No more concurrent tracking (unnecessary complexity)
  incrementConcurrentChecks() {
    // No-op for backward compatibility
  }

  decrementConcurrentChecks() {
    // No-op for backward compatibility
  }

  /**
   * Record response time and calculate average
   */
  private recordResponseTime(responseTime: number) {
    this.responseTimes.push(responseTime)

    // Keep only the last MAX_RESPONSE_TIMES entries
    if (this.responseTimes.length > this.MAX_RESPONSE_TIMES) {
      this.responseTimes.shift()
    }

    // Calculate average
    this.metrics.averageResponseTime =
      this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length
  }

  /**
   * ✅ SIMPLIFIED: No more excessive logging - keep it simple
   */
  private checkMetrics() {
    const now = Date.now()
    const timeSinceReset = now - this.metrics.lastReset

    // ✅ SIMPLE: Reset metrics every 10 minutes (no complex warnings)
    if (timeSinceReset > 600000) {
      this.resetMetrics()
    }
  }

  /**
   * ✅ SIMPLIFIED: Get basic metrics only
   */
  getMetrics(): SessionMetrics & { requestsPerMinute: number } {
    const now = Date.now()
    const timeSinceReset = now - this.metrics.lastReset
    const requestsPerMinute = (this.metrics.totalRequests / timeSinceReset) * 60000

    return {
      ...this.metrics,
      requestsPerMinute: isNaN(requestsPerMinute) ? 0 : requestsPerMinute,
    }
  }

  /**
   * ✅ SIMPLIFIED: Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      totalRequests: 0,
      averageResponseTime: 0,
      lastReset: Date.now(),
    }
    this.responseTimes = []
  }

  /**
   * ✅ SIMPLIFIED: Basic status logging
   */
  logPerformanceStatus() {
    const metrics = this.getMetrics()
    console.log('📊 Performance Status:', {
      'Requests/min': metrics.requestsPerMinute.toFixed(1),
      'Avg Response': `${metrics.averageResponseTime.toFixed(0)}ms`,
      'Total Requests': metrics.totalRequests,
    })
  }
}

/**
 * Utility functions for performance monitoring
 */
export const SessionPerformance = {
  /**
   * Get the singleton instance
   */
  getInstance: () => SessionPerformanceMonitor.getInstance(),

  /**
   * ✅ PROPER ARCHITECTURE: Monitor actual session checks (not every validation)
   * Only count deliberate session monitoring calls, not authentication validations
   */
  async monitorSessionCheck<T>(operation: () => Promise<T>): Promise<T> {
    const monitor = SessionPerformanceMonitor.getInstance()
    const startTime = Date.now()

    monitor.incrementConcurrentChecks()

    try {
      const result = await operation()
      const responseTime = Date.now() - startTime
      // ✅ PROPER: Only count actual session monitoring calls
      monitor.recordSessionCheck(responseTime)
      return result
    } finally {
      monitor.decrementConcurrentChecks()
    }
  },

  /**
   * Wrap an API call with performance monitoring
   */
  async monitorApiCall<T>(operation: () => Promise<T>): Promise<T> {
    const monitor = SessionPerformanceMonitor.getInstance()
    const startTime = Date.now()

    try {
      const result = await operation()
      const responseTime = Date.now() - startTime
      monitor.recordApiCall(responseTime)
      return result
    } catch (error) {
      const responseTime = Date.now() - startTime
      monitor.recordApiCall(responseTime)
      throw error
    }
  },

  /**
   * ✅ SIMPLIFIED: Record a Redis operation
   */
  recordRedisOp: () => {
    SessionPerformanceMonitor.getInstance().recordRedisOp()
  },

  /**
   * Get current metrics
   */
  getMetrics: () => {
    return SessionPerformanceMonitor.getInstance().getMetrics()
  },

  /**
   * Log performance status
   */
  logStatus: () => {
    SessionPerformanceMonitor.getInstance().logPerformanceStatus()
  },
}

export default SessionPerformance
