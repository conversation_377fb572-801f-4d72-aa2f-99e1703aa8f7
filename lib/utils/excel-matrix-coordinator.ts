/**
 * Excel Matrix Coordinator
 * Clean Architecture - Application Layer
 *
 * Single responsibility: Coordinate matrix Excel generation
 * Orchestrates data processing and Excel creation
 */

import ExcelJS from 'exceljs'
import { format, eachDayOfInterval } from 'date-fns'
import { id } from 'date-fns/locale'
import type { MatrixStudentData, ExcelExportConfig, ReportType } from './excel-shared-constants'
import { processAttendanceToMatrix } from './excel-data-processor'
import { createMonthlyMatrixWorksheet } from './excel-matrix-monthly'
import {
  createYearlyMatrixWithMonthTabs,
  createYearlyMatrixSingleSheet,
} from './excel-matrix-yearly'

/**
 * Generate matrix Excel workbook with proper architecture
 */
export async function generateMatrixExcel(
  workbook: ExcelJS.Workbook,
  config: ExcelExportConfig,
  setProgress?: (progress: number) => void
): Promise<void> {
  console.log('📊 Starting matrix Excel generation with clean architecture')
  setProgress?.(10)

  // Step 1: Process attendance data into matrix format
  const matrixData = processAttendanceToMatrix(
    config.attendanceData,
    config.reportType,
    config.startDate,
    config.endDate
  )

  setProgress?.(40)

  // Step 2: Group students by class
  const studentsByClass = groupStudentsByClass(matrixData)
  const classes = Array.from(studentsByClass.keys()).sort()

  if (classes.length === 0) {
    const worksheet = workbook.addWorksheet('Tidak Ada Data')
    worksheet.addRow(['Tidak ada data untuk periode yang dipilih'])
    return
  }

  setProgress?.(50)

  // Step 3: Determine report format based on date range and class count
  const dateRange = eachDayOfInterval({ start: config.startDate, end: config.endDate })
  const isYearlyReport = isYearlyReportRange(config.startDate, config.endDate, dateRange.length)
  const hasExactlyOneClass = classes.length === 1

  // Step 4: Generate appropriate Excel format
  if (isYearlyReport && hasExactlyOneClass) {
    // Yearly report with single class: Create tabs per month
    await createYearlyMatrixWithMonthTabs(
      workbook,
      classes[0],
      studentsByClass.get(classes[0])!,
      config.startDate,
      config.endDate,
      config.reportType,
      config.enableFormatting,
      setProgress
    )
  } else if (isYearlyReport) {
    // Yearly report with multiple classes: Single sheet per class
    await createYearlyMatrixForMultipleClasses(
      workbook,
      classes,
      studentsByClass,
      config,
      setProgress
    )
  } else {
    // Monthly/weekly report: Regular matrix format
    await createMonthlyMatrixForMultipleClasses(
      workbook,
      classes,
      studentsByClass,
      config,
      dateRange,
      setProgress
    )
  }

  setProgress?.(100)
  console.log('✅ Matrix Excel generation completed successfully')
}

/**
 * Check if date range represents a yearly report
 */
function isYearlyReportRange(startDate: Date, endDate: Date, dayCount: number): boolean {
  const startYear = startDate.getFullYear()
  const endYear = endDate.getFullYear()
  const startMonth = startDate.getMonth()
  const endMonth = endDate.getMonth()

  return startYear === endYear && (endMonth - startMonth >= 11 || dayCount > 93)
}

/**
 * Group students by class name
 */
function groupStudentsByClass(students: MatrixStudentData[]): Map<string, MatrixStudentData[]> {
  const grouped = new Map<string, MatrixStudentData[]>()

  for (const student of students) {
    const className = student.className || 'Tidak Ada Kelas'
    if (!grouped.has(className)) {
      grouped.set(className, [])
    }
    grouped.get(className)!.push(student)
  }

  return grouped
}

/**
 * Create yearly matrix for multiple classes
 */
async function createYearlyMatrixForMultipleClasses(
  workbook: ExcelJS.Workbook,
  classes: string[],
  studentsByClass: Map<string, MatrixStudentData[]>,
  config: ExcelExportConfig,
  setProgress?: (progress: number) => void
): Promise<void> {
  console.log('📅 Creating yearly matrix for multiple classes')

  let currentProgress = 50
  const progressPerClass = 40 / classes.length

  for (const className of classes) {
    const classStudents = studentsByClass.get(className)!
    const worksheet = workbook.addWorksheet(className)

    await createYearlyMatrixSingleSheet(
      worksheet,
      className,
      classStudents,
      config.startDate,
      config.endDate,
      config.reportType,
      config.enableFormatting
    )

    currentProgress += progressPerClass
    setProgress?.(Math.round(currentProgress))
  }
}

/**
 * Create monthly matrix for multiple classes
 */
async function createMonthlyMatrixForMultipleClasses(
  workbook: ExcelJS.Workbook,
  classes: string[],
  studentsByClass: Map<string, MatrixStudentData[]>,
  config: ExcelExportConfig,
  dateRange: Date[],
  setProgress?: (progress: number) => void
): Promise<void> {
  console.log('📊 Creating monthly matrix for multiple classes')

  let currentProgress = 50
  const progressPerClass = 40 / classes.length

  for (const className of classes) {
    const classStudents = studentsByClass.get(className)!
    const worksheet = workbook.addWorksheet(className)

    await createMonthlyMatrixWorksheet(
      worksheet,
      className,
      classStudents,
      dateRange,
      config.reportType,
      config.enableFormatting
    )

    currentProgress += progressPerClass
    setProgress?.(Math.round(currentProgress))
  }
}

/**
 * Generate filename for matrix Excel export according to excel-export-formats.md
 * Format: laporan-{jenis}-{format}-{periode}-{timestamp}.xlsx
 */
export function generateMatrixExcelFilename(config: ExcelExportConfig): string {
  const now = new Date()
  const timestamp = format(now, 'yyyy-MM-dd', { locale: id })
  const typeStr = config.reportType === 'prayer' ? 'shalat' : 'sekolah'
  const formatStr = 'matrix' // Matrix format

  let periodStr = ''
  if (config.startDate && config.endDate) {
    // Determine period type
    const daysDiff = Math.ceil(
      (config.endDate.getTime() - config.startDate.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (daysDiff > 93) {
      // Yearly report
      periodStr = `tahunan-${config.startDate.getFullYear()}`
    } else if (daysDiff > 31) {
      // Custom range report
      const startStr = format(config.startDate, 'yyyy-MM-dd', { locale: id })
      const endStr = format(config.endDate, 'yyyy-MM-dd', { locale: id })
      periodStr = `custom-${startStr}-to-${endStr}`
    } else {
      // Monthly report
      periodStr = `bulanan-${format(config.startDate, 'yyyy-MM', { locale: id })}`
    }
  } else {
    periodStr = 'custom'
  }

  return `laporan-${typeStr}-${formatStr}-${periodStr}-${timestamp}.xlsx`
}
