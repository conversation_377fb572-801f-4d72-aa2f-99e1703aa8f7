/**
 * ⚠️ DEPRECATED: Legacy session management utilities
 * Use hooks/use-session-monitor.ts instead for better performance
 */

/**
 * @deprecated Use hooks/use-session-monitor.ts instead
 * Check if current session is valid
 */
export async function checkSession(): Promise<{
  valid: boolean
  user?: { id: number; role: string; sessionId: string }
  error?: string
}> {
  console.warn(
    '⚠️ DEPRECATED: checkSession() is deprecated. Use hooks/use-session-monitor.ts instead'
  )

  // ✅ FIXED: Return early to prevent unnecessary API calls
  return { valid: false, error: 'Deprecated function - use session monitor hooks instead' }
}

/**
 * Check session for specific role
 */
export async function checkSessionForRole(_role: 'admin' | 'student'): Promise<{
  valid: boolean
  user?: { id: number; role: string; sessionId: string }
  error?: string
}> {
  console.warn(
    '⚠️ DEPRECATED: checkSessionForRole() is deprecated. Use hooks/use-session-monitor.ts instead'
  )

  // ✅ FIXED: Return early to prevent unnecessary API calls
  return { valid: false, error: 'Deprecated function - use session monitor hooks instead' }
}

/**
 * Force logout - clear cookies when session is invalid
 */
export async function forceLogout(role: 'admin' | 'student'): Promise<void> {
  try {
    await fetch('/api/auth/force-logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ role }),
      credentials: 'include',
    })
  } catch (error) {
    console.error('Force logout failed:', error)
  }
}

/**
 * Handle session invalid - redirect to login
 */
export function handleSessionInvalid(role: 'admin' | 'student'): void {
  // Clear cookies first
  forceLogout(role)

  // Redirect to appropriate login page
  const loginPath = role === 'admin' ? '/admin' : '/student'

  // Add a small delay to ensure cookies are cleared
  setTimeout(() => {
    window.location.href = loginPath + '?error=session_expired'
  }, 100)
}

/**
 * Setup session monitoring for automatic logout on session expiry
 */
export function setupSessionMonitoring(
  role: 'admin' | 'student',
  intervalMs: number = 60000 // Check every minute
): () => void {
  const interval = setInterval(async () => {
    const sessionCheck = await checkSessionForRole(role)

    if (!sessionCheck.valid) {
      console.log('Session expired, redirecting to login...')
      clearInterval(interval)
      handleSessionInvalid(role)
    }
  }, intervalMs)

  // Return cleanup function
  return () => clearInterval(interval)
}

/**
 * Check if user should be redirected due to session expiry
 */
export function checkForSessionExpiry(): void {
  const urlParams = new URLSearchParams(window.location.search)
  const error = urlParams.get('error')

  if (error === 'session_expired') {
    // Show notification about session expiry
    if (typeof window !== 'undefined' && 'localStorage' in window) {
      localStorage.setItem(
        'session_expired_message',
        'Your session has expired. Please login again.'
      )
    }

    // Clean up URL
    const url = new URL(window.location.href)
    url.searchParams.delete('error')
    window.history.replaceState({}, document.title, url.toString())
  }
}
