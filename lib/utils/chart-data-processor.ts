import { MonthlyReportData, YearlyReportData } from '@/lib/domain/usecases/reports'

/**
 * Chart Data Structures
 */
export interface ChartDataPoint {
  date: string
  zuhr?: number
  asr?: number
  ijin?: number
  entry?: number
  lateEntry?: number
  excusedAbsence?: number
  sick?: number
  temporaryLeave?: number
  returnFromLeave?: number
  pulang?: number
}

export interface ChartProcessorOptions {
  reportType: 'prayer' | 'school'
  aggregateBy: 'week' | 'month'
}

/**
 * Chart Data Processor - Clean Architecture Implementation
 * Processes monthly and yearly report data into chart-ready format
 */
export class ChartDataProcessor {
  /**
   * Process Monthly Report Data into Weekly Chart Data
   */
  static processMonthlyReportForChart(
    monthlyReport: MonthlyReportData,
    reportType: 'prayer' | 'school' = 'prayer'
  ): ChartDataPoint[] {
    console.log(`📊 Processing Monthly Report for ${reportType} Chart`)

    const chartData: ChartDataPoint[] = []

    // Process each week in the monthly report
    monthlyReport.weeks.forEach((week, index) => {
      const weekLabel = `Minggu ${index + 1}`

      // Initialize chart data point
      const dataPoint: ChartDataPoint = {
        date: weekLabel,
      }

      // Aggregate attendance data for this week
      const weekAttendance = this.aggregateAttendanceData(week.data, reportType)

      // Add attendance data based on report type
      if (reportType === 'prayer') {
        dataPoint.zuhr = weekAttendance.zuhr || 0
        dataPoint.asr = weekAttendance.asr || 0
        dataPoint.ijin = weekAttendance.ijin || 0
        dataPoint.pulang = weekAttendance.dismissal || 0
      } else if (reportType === 'school') {
        dataPoint.entry = weekAttendance.entry || 0
        dataPoint.lateEntry = weekAttendance.lateEntry || 0
        dataPoint.excusedAbsence = weekAttendance.excusedAbsence || 0
        dataPoint.sick = weekAttendance.sick || 0
        dataPoint.temporaryLeave = weekAttendance.temporaryLeave || 0
        dataPoint.returnFromLeave = weekAttendance.returnFromLeave || 0
      }

      chartData.push(dataPoint)
    })

    console.log(`📊 Monthly Chart Data Generated: ${chartData.length} weeks`)
    return chartData
  }

  /**
   * Process Yearly Report Data into Monthly Chart Data
   */
  static processYearlyReportForChart(
    yearlyReport: YearlyReportData,
    reportType: 'prayer' | 'school' = 'prayer'
  ): ChartDataPoint[] {
    console.log(`📊 Processing Yearly Report for ${reportType} Chart`)

    const chartData: ChartDataPoint[] = []

    // Process each month in the yearly report
    yearlyReport.months.forEach(month => {
      // Initialize chart data point
      const dataPoint: ChartDataPoint = {
        date: month.monthName,
      }

      // Debug: Log month data details
      console.log(
        `📊 Processing ${month.monthName}: ${month.data.length} students, attendanceCount: ${month.attendanceCount}`
      )

      if (month.monthName === 'Jul' && month.data.length > 0) {
        const studentsWithAttendance = month.data.filter(student => {
          const counts = (student as any).aggregatedCounts || {}
          return counts.zuhr > 0 || counts.asr > 0 || counts.ijin > 0
        })
        console.log(`📊 July students with actual attendance: ${studentsWithAttendance.length}`)
        if (studentsWithAttendance.length > 0) {
          console.log(
            `📊 Sample July student:`,
            studentsWithAttendance[0].uniqueCode,
            (studentsWithAttendance[0] as any).aggregatedCounts
          )
        }
      }

      // Aggregate attendance data for this month
      const monthAttendance = this.aggregateAttendanceData(month.data, reportType)

      console.log(`📊 ${month.monthName} aggregated:`, monthAttendance)

      // Add attendance data based on report type
      if (reportType === 'prayer') {
        dataPoint.zuhr = monthAttendance.zuhr || 0
        dataPoint.asr = monthAttendance.asr || 0
        dataPoint.ijin = monthAttendance.ijin || 0
        dataPoint.pulang = monthAttendance.dismissal || 0
      } else if (reportType === 'school') {
        dataPoint.entry = monthAttendance.entry || 0
        dataPoint.lateEntry = monthAttendance.lateEntry || 0
        dataPoint.excusedAbsence = monthAttendance.excusedAbsence || 0
        dataPoint.sick = monthAttendance.sick || 0
        dataPoint.temporaryLeave = monthAttendance.temporaryLeave || 0
        dataPoint.returnFromLeave = monthAttendance.returnFromLeave || 0
      }

      chartData.push(dataPoint)
    })

    console.log(`📊 Yearly Chart Data Generated: ${chartData.length} months`)
    return chartData
  }

  /**
   * Aggregate attendance data from summary records
   * @private
   */
  private static aggregateAttendanceData(
    summaryData: any[],
    reportType: 'prayer' | 'school'
  ): Record<string, number> {
    const aggregated: Record<string, number> = {}

    summaryData.forEach(student => {
      const counts = student.aggregatedCounts || {}

      // Aggregate based on report type
      if (reportType === 'prayer') {
        aggregated.zuhr = (aggregated.zuhr || 0) + (counts.zuhr || 0)
        aggregated.asr = (aggregated.asr || 0) + (counts.asr || 0)
        aggregated.ijin = (aggregated.ijin || 0) + (counts.ijin || 0)
        aggregated.dismissal = (aggregated.dismissal || 0) + (counts.dismissal || 0)
      } else if (reportType === 'school') {
        aggregated.entry = (aggregated.entry || 0) + (counts.entry || 0)
        aggregated.lateEntry = (aggregated.lateEntry || 0) + (counts.lateEntry || 0)
        aggregated.excusedAbsence = (aggregated.excusedAbsence || 0) + (counts.excusedAbsence || 0)
        aggregated.sick = (aggregated.sick || 0) + (counts.sick || 0)
        aggregated.temporaryLeave = (aggregated.temporaryLeave || 0) + (counts.temporaryLeave || 0)
        aggregated.returnFromLeave =
          (aggregated.returnFromLeave || 0) + (counts.returnFromLeave || 0)
      }
    })

    return aggregated
  }

  /**
   * Generate Empty Chart Data for fallback
   */
  static generateEmptyChartData(
    type: 'monthly' | 'yearly',
    reportType: 'prayer' | 'school' = 'prayer'
  ): ChartDataPoint[] {
    const chartData: ChartDataPoint[] = []

    if (type === 'monthly') {
      // Generate 5 weeks
      for (let i = 1; i <= 5; i++) {
        const dataPoint: ChartDataPoint = {
          date: `Minggu ${i}`,
        }

        if (reportType === 'prayer') {
          dataPoint.zuhr = 0
          dataPoint.asr = 0
          dataPoint.ijin = 0
          dataPoint.pulang = 0
        } else {
          dataPoint.entry = 0
          dataPoint.lateEntry = 0
          dataPoint.excusedAbsence = 0
          dataPoint.sick = 0
          dataPoint.temporaryLeave = 0
          dataPoint.returnFromLeave = 0
        }

        chartData.push(dataPoint)
      }
    } else if (type === 'yearly') {
      // Generate 12 months
      const monthNames = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'Mei',
        'Jun',
        'Jul',
        'Ags',
        'Sep',
        'Okt',
        'Nov',
        'Des',
      ]

      monthNames.forEach(monthName => {
        const dataPoint: ChartDataPoint = {
          date: monthName,
        }

        if (reportType === 'prayer') {
          dataPoint.zuhr = 0
          dataPoint.asr = 0
          dataPoint.ijin = 0
          dataPoint.pulang = 0
        } else {
          dataPoint.entry = 0
          dataPoint.lateEntry = 0
          dataPoint.excusedAbsence = 0
          dataPoint.sick = 0
          dataPoint.temporaryLeave = 0
          dataPoint.returnFromLeave = 0
        }

        chartData.push(dataPoint)
      })
    }

    return chartData
  }

  /**
   * Validate Chart Data
   */
  static validateChartData(chartData: ChartDataPoint[]): boolean {
    if (!Array.isArray(chartData) || chartData.length === 0) {
      return false
    }

    // Check if all data points have required fields
    return chartData.every(point => point.date && typeof point.date === 'string')
  }

  /**
   * Get Chart Data Summary
   */
  static getChartDataSummary(chartData: ChartDataPoint[]): {
    totalDataPoints: number
    totalAttendance: number
    hasData: boolean
  } {
    const totalDataPoints = chartData.length

    const totalAttendance = chartData.reduce((sum, point) => {
      const values = Object.values(point).filter(val => typeof val === 'number')
      return sum + values.reduce((a, b) => a + b, 0)
    }, 0)

    const hasData = totalAttendance > 0

    return {
      totalDataPoints,
      totalAttendance,
      hasData,
    }
  }
}
