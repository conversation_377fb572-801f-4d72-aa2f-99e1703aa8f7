/**
 * Excel Matrix Monthly Export
 * Clean Architecture - Infrastructure Layer
 *
 * Single responsibility: Generate monthly matrix Excel reports
 * Handles both prayer and school reports for monthly periods
 */

import ExcelJS from 'exceljs'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import type { MatrixStudentData, ReportType } from './excel-shared-constants'
import {
  EXCEL_FONTS as FONTS,
  EXCEL_COLORS as COLORS,
  EXCEL_COLUMN_WIDTHS as WIDTHS,
  STATUS_LEGENDS,
} from './excel-shared-constants'
import { generateHoverDetails, generateFallbackHover } from './excel-hover-generator'

/**
 * Create monthly matrix Excel worksheet
 */
export async function createMonthlyMatrixWorksheet(
  worksheet: ExcelJS.Worksheet,
  className: string,
  students: MatrixStudentData[],
  dateRange: Date[],
  reportType: ReportType,
  enableFormatting: boolean = true
): Promise<void> {
  console.log(`📊 Creating monthly matrix for class ${className} with ${students.length} students`)

  // Create title
  const reportTypeLabel = reportType === 'prayer' ? 'Shalat' : 'Sekolah'
  const periodLabel = format(dateRange[0], 'MMMM yyyy', { locale: id })
  const titleText = `Laporan ${reportTypeLabel} Kelas ${className} - ${periodLabel}`

  const titleRow = worksheet.addRow([titleText])
  if (enableFormatting) {
    titleRow.font = FONTS.TITLE
    titleRow.alignment = { horizontal: 'center' }
    worksheet.mergeCells(1, 1, 1, 10)
  }

  // Add empty row
  worksheet.addRow([])

  // Create headers with numbered date columns for compact matrix format
  const headers = ['No', 'NIS', 'Nama', 'L/P']

  // Add numbered date columns (1, 2, 3, 4, 5, etc.) instead of actual dates
  // This creates a more compact table while maintaining readability
  // The actual dates are implied from the report period
  for (let i = 1; i <= dateRange.length; i++) {
    headers.push(i.toString())
  }

  // Add summary headers based on report type
  if (reportType === 'prayer') {
    headers.push('Total H', 'Total Z', 'Total A', 'Total I')
  } else {
    headers.push('H', 'T', 'S', 'I', 'IS', 'K') // RESTORED: Keep 'T' column for totals
  }

  const headerRow = worksheet.addRow(headers)
  if (enableFormatting) {
    headerRow.font = FONTS.HEADER
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: COLORS.COLUMN_HEADER },
    }
    headerRow.alignment = { horizontal: 'center' }
  }

  // Add student data
  const firstStudentRow = 4 // After title, empty row, and headers
  for (let i = 0; i < students.length; i++) {
    const student = students[i]
    const row = [i + 1, student.nis, student.name, student.gender]

    // Add daily status for each date
    for (const date of dateRange) {
      const dateKey = format(date, 'yyyy-MM-dd')
      const status = student.dailyStatus[dateKey] || '-'
      // FIXED: Display 'T' (Terlambat) as 'H' in Excel cell
      const displayStatus = status === 'T' ? 'H' : status
      row.push(displayStatus)
    }

    // Add summary data based on report type
    addSummaryToRow(row, student, reportType)
    worksheet.addRow(row)
  }

  // Add hover details with accurate positioning
  if (enableFormatting) {
    addHoverDetailsToMonthlyMatrix(worksheet, students, dateRange, firstStudentRow, reportType)
  }

  // Add legend and format columns
  addLegendAndFormatting(worksheet, students, reportType)

  console.log(`✅ Monthly matrix completed for class ${className}`)
}

/**
 * Add summary data to student row - calculated from dailyActivities (UPDATED)
 */
function addSummaryToRow(row: any[], student: MatrixStudentData, reportType: ReportType): void {
  const summary = calculateSummaryFromDailyActivities(student.dailyActivities, reportType)

  if (reportType === 'prayer') {
    row.push(
      summary.totalH.toString(),
      summary.totalZ.toString(),
      summary.totalA.toString(),
      summary.totalI.toString()
    )
  } else {
    row.push(
      summary.totalHadir.toString(),
      summary.totalTerlambat.toString(), // RESTORED: Keep separate count
      summary.totalSakit.toString(),
      summary.totalIjinTidakHadir.toString(),
      summary.totalIjinSementara.toString(),
      summary.totalKembali.toString()
    )
  }
}

/**
 * Add hover details to monthly matrix with accurate positioning
 */
function addHoverDetailsToMonthlyMatrix(
  worksheet: ExcelJS.Worksheet,
  students: MatrixStudentData[],
  dateRange: Date[],
  firstStudentRow: number,
  reportType: ReportType
): void {
  console.log(`🔍 Adding hover details to monthly matrix starting from row ${firstStudentRow}`)

  students.forEach((student, studentIndex) => {
    const studentRow = firstStudentRow + studentIndex

    dateRange.forEach((date, dateIndex) => {
      const dateKey = format(date, 'yyyy-MM-dd')
      const status = student.dailyStatus[dateKey] || '-'

      // Only add hover for cells with actual status codes (not absent)
      if (status && status !== '-') {
        const cellCol = 5 + dateIndex // After No, NIS, Nama, L/P
        const cell = worksheet.getCell(studentRow, cellCol)

        // Get detailed information for hover
        const studentDetail = student.details[dateKey]

        let hoverText: string
        if (studentDetail && studentDetail.times && Object.keys(studentDetail.times).length > 0) {
          hoverText = generateHoverDetails(studentDetail, status, date, reportType)
        } else {
          hoverText = generateFallbackHover(status, date, reportType)
        }

        cell.note = {
          texts: [
            {
              text: hoverText,
              font: FONTS.COMMENT,
            },
          ],
        }
      }
    })
  })
}

/**
 * Add legend and apply formatting to worksheet
 */
function addLegendAndFormatting(
  worksheet: ExcelJS.Worksheet,
  students: MatrixStudentData[],
  reportType: ReportType
): void {
  // Add legend
  worksheet.addRow([])
  const legendRow = worksheet.addRow(['Keterangan Status:'])
  legendRow.font = FONTS.HEADER

  const statusCodes = STATUS_LEGENDS[reportType]
  worksheet.addRow([statusCodes])

  // Auto-fit columns
  worksheet.columns.forEach((column, index) => {
    if (index === 0) {
      column.width = WIDTHS.NO
    } else if (index === 1) {
      column.width = WIDTHS.NIS
    } else if (index === 2) {
      // Nama column - dynamic width based on content
      const maxNameLength = Math.max(
        ...students.map(student => (student.name || '').length),
        'Nama'.length
      )
      column.width = Math.min(Math.max(maxNameLength + 2, WIDTHS.NAME_MIN), WIDTHS.NAME_MAX)
    } else if (index === 3) {
      column.width = WIDTHS.GENDER
    } else {
      column.width = WIDTHS.DATE
    }
  })
}

/**
 * UPDATED: Calculate summary statistics from daily activities (counts all activities)
 */
function calculateSummaryFromDailyActivities(
  dailyActivities: Record<string, string[]>,
  reportType: ReportType
): any {
  if (reportType === 'prayer') {
    const summary = {
      totalH: 0,
      totalZ: 0,
      totalA: 0,
      totalI: 0,
    }

    // Count all activities across all days
    Object.values(dailyActivities).forEach(dayActivities => {
      dayActivities.forEach(activityCode => {
        if (activityCode === 'H') summary.totalH++
        else if (activityCode === 'Z') summary.totalZ++
        else if (activityCode === 'A') summary.totalA++
        else if (activityCode === 'I') summary.totalI++
      })
    })

    return summary
  } else {
    const summary = {
      totalHadir: 0,
      totalTerlambat: 0, // RESTORED: Keep separate count for late entry
      totalSakit: 0,
      totalIjinTidakHadir: 0,
      totalIjinSementara: 0,
      totalKembali: 0,
    }

    // Count all activities across all days
    Object.values(dailyActivities).forEach(dayActivities => {
      dayActivities.forEach(activityCode => {
        if (activityCode === 'H') summary.totalHadir++
        else if (activityCode === 'T') {
          summary.totalHadir++ // FIXED: Terlambat counts as Hadir
          summary.totalTerlambat++ // Also count separately for detail
        } else if (activityCode === 'S') summary.totalSakit++
        else if (activityCode === 'I') summary.totalIjinTidakHadir++
        else if (activityCode === 'IS') summary.totalIjinSementara++
        else if (activityCode === 'K') summary.totalKembali++
      })
    })

    return summary
  }
}

/**
 * LEGACY: Calculate summary statistics from daily status (kept for compatibility)
 */
function calculateSummaryFromDailyStatus(
  dailyStatus: Record<string, string>,
  reportType: ReportType
): any {
  if (reportType === 'prayer') {
    const summary = { totalH: 0, totalZ: 0, totalA: 0, totalI: 0 }

    Object.values(dailyStatus).forEach(status => {
      switch (status) {
        case 'H': // HADIR
          summary.totalH++
          break
        case 'Z': // ZUHR_ONLY
          summary.totalZ++
          break
        case 'A': // ASR_ONLY
          summary.totalA++
          break
        case 'I': // IJIN
        case 'IZ': // IJIN_ZUHR
        case 'IA': // IJIN_ASR
          summary.totalI++
          break
      }
    })

    return summary
  } else {
    const summary = {
      totalHadir: 0,
      totalTerlambat: 0,
      totalSakit: 0,
      totalIjinTidakHadir: 0,
      totalIjinSementara: 0,
      totalKembali: 0,
    }

    Object.values(dailyStatus).forEach(status => {
      switch (status) {
        case 'H': // HADIR (includes former M - Masuk Only)
          summary.totalHadir++
          break
        case 'T': // TERLAMBAT
          summary.totalTerlambat++
          break
        case 'S': // SAKIT
          summary.totalSakit++
          break
        case 'I': // IJIN
          summary.totalIjinTidakHadir++
          break
        case 'IS': // IJIN_SEMENTARA
          summary.totalIjinSementara++
          break
        case 'K': // KEMBALI
          summary.totalKembali++
          break
        // Note: M (Masuk Only) now becomes H (Hadir)
        // Note: P (Pulang Only) now becomes - (Absen) - not counted in summary
      }
    })

    return summary
  }
}
