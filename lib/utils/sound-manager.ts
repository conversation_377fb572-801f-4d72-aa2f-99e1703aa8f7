/**
 * Sound Manager Utility
 * Handles audio feedback for the application with proper error handling and performance
 */

export enum SoundType {
  SUCCESS = 'success',
  ALERT = 'alert',
  ERROR = 'error',
}

interface SoundConfig {
  [SoundType.SUCCESS]: string
  [SoundType.ALERT]: string
  [SoundType.ERROR]: string
}

class SoundManager {
  private static instance: SoundManager
  private audioCache: Map<SoundType, HTMLAudioElement> = new Map()
  private isEnabled: boolean = true
  private isInitialized: boolean = false

  private readonly soundConfig: SoundConfig = {
    [SoundType.SUCCESS]: '/beep.mp3',
    [SoundType.ALERT]: '/alert.mp3',
    [SoundType.ERROR]: '/error.mp3',
  }

  private constructor() {
    // Private constructor for singleton pattern
  }

  public static getInstance(): SoundManager {
    if (!SoundManager.instance) {
      SoundManager.instance = new SoundManager()
    }
    return SoundManager.instance
  }

  /**
   * Initialize the sound manager (called once)
   */
  private async initialize(): Promise<void> {
    if (this.isInitialized || typeof window === 'undefined') return

    try {
      // Preload audio files for better performance
      for (const [soundType, audioPath] of Object.entries(this.soundConfig)) {
        const audio = new Audio(audioPath)
        audio.preload = 'auto'
        audio.volume = 0.7 // Set reasonable default volume
        this.audioCache.set(soundType as SoundType, audio)
      }

      this.isInitialized = true
    } catch (error) {
      console.warn('Failed to initialize sound manager:', error)
      this.isEnabled = false
    }
  }

  /**
   * Play a sound with the specified type
   */
  public async play(soundType: SoundType): Promise<void> {
    // Only run in browser environment
    if (typeof window === 'undefined' || !this.isEnabled) return

    try {
      // Initialize if not already done
      if (!this.isInitialized) {
        await this.initialize()
      }

      const audio = this.audioCache.get(soundType)
      if (!audio) {
        console.warn(`Sound type ${soundType} not found`)
        return
      }

      // Reset audio to start if it was playing
      audio.currentTime = 0

      // Attempt to play the sound
      const playPromise = audio.play()

      if (playPromise !== undefined) {
        await playPromise
      }
    } catch (error) {
      // Handle autoplay restrictions gracefully
      if (error instanceof Error && error.name === 'NotAllowedError') {
        // Setup click handler for autoplay restrictions
        this.setupClickToPlay(soundType)
      } else {
        console.warn(`Failed to play ${soundType} sound:`, error)
      }
    }
  }

  /**
   * Setup click-to-play for browsers that block autoplay
   */
  private setupClickToPlay(soundType: SoundType): void {
    const audio = this.audioCache.get(soundType)
    if (!audio) return

    const playOnClick = () => {
      audio.play().catch(() => {
        // Ignore errors on fallback attempt
      })
      document.removeEventListener('click', playOnClick)
    }

    document.addEventListener('click', playOnClick, { once: true })
  }

  /**
   * Enable or disable sound globally
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * Check if sound is enabled
   */
  public getEnabled(): boolean {
    return this.isEnabled
  }

  /**
   * Set volume for all sounds (0.0 to 1.0)
   */
  public setVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume))

    for (const audio of this.audioCache.values()) {
      audio.volume = clampedVolume
    }
  }
}

// Export the singleton instance and convenience functions
export const soundManager = SoundManager.getInstance()

/**
 * Convenience functions for common sound operations
 */
export const playSuccessSound = () => soundManager.play(SoundType.SUCCESS)
export const playAlertSound = () => soundManager.play(SoundType.ALERT)
export const playErrorSound = () => soundManager.play(SoundType.ERROR)

/**
 * Legacy function for backward compatibility
 * @deprecated Use playAlertSound() instead
 */
export const playAlert = playAlertSound
