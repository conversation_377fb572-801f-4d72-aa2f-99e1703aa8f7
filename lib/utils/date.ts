/**
 * Utility functions for date and time handling
 * All functions ensure proper timezone handling using centralized configuration
 */

import { TIMEZONE_CONFIG } from '../config'

/**
 * Format a date to a time string using configured timezone
 * @param date The date to format
 * @returns Formatted time string in HH:MM format with configured timezone
 */
export function formatTime(date: Date): string {
  try {
    // Use centralized timezone configuration
    return new Intl.DateTimeFormat(TIMEZONE_CONFIG.LOCALE, {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: TIMEZONE_CONFIG.TIMEZONE,
    }).format(date)
  } catch (error) {
    console.error('Error formatting time:', error)
    return '00:00' // Fallback
  }
}

/**
 * SINGLE SOURCE OF TRUTH: Format any Date object for time display
 * This function should be used consistently across the entire application
 * @param date - Date object to format
 * @param options - Formatting options
 * @returns Formatted time string using configured timezone and locale
 */
export function formatTimeWITA(
  date: Date,
  options: {
    includeSeconds?: boolean
    includeDate?: boolean
    hour12?: boolean
  } = {}
): string {
  const { includeSeconds = false, includeDate = false, hour12 = false } = options

  const formatOptions: Intl.DateTimeFormatOptions = {
    timeZone: TIMEZONE_CONFIG.TIMEZONE,
    hour: '2-digit',
    minute: '2-digit',
    hour12,
  }

  if (includeSeconds) {
    formatOptions.second = '2-digit'
  }

  if (includeDate) {
    formatOptions.year = 'numeric'
    formatOptions.month = '2-digit'
    formatOptions.day = '2-digit'
  }

  // Use configured locale for consistency
  return date.toLocaleTimeString(TIMEZONE_CONFIG.LOCALE, formatOptions)
}

/**
 * Format a date to a date string using configured timezone
 * @param date The date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  try {
    // Use centralized timezone configuration
    return new Intl.DateTimeFormat(TIMEZONE_CONFIG.LOCALE, {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      timeZone: TIMEZONE_CONFIG.TIMEZONE,
    }).format(date)
  } catch (error) {
    console.error('Error formatting date:', error)
    return '' // Fallback
  }
}

/**
 * Get the current date and time in WITA timezone
 * @returns Object with formatted date and time strings
 */
export function getCurrentDateTime(): { date: string; time: string } {
  const now = new Date()
  return {
    date: formatDate(now),
    time: formatTime(now),
  }
}

/**
 * Create a Date object for the start of the day in WITA timezone
 * @param date Optional date to use (defaults to current WITA time)
 * @returns Date object set to the start of the day in WITA
 */
export function getStartOfDay(date?: Date): Date {
  // Use current WITA time if no date provided
  const baseDate = date || getCurrentWITATime()

  // Create a new date to avoid modifying the original
  const newDate = new Date(baseDate)
  newDate.setHours(0, 0, 0, 0)
  return newDate
}

/**
 * Create a Date object for the end of the day in WITA timezone
 * @param date Optional date to use (defaults to current WITA time)
 * @returns Date object set to the end of the day in WITA
 */
export function getEndOfDay(date?: Date): Date {
  // Use current WITA time if no date provided
  const baseDate = date || getCurrentWITATime()

  // Create a new date to avoid modifying the original
  const newDate = new Date(baseDate)
  newDate.setHours(23, 59, 59, 999)
  return newDate
}

/**
 * SINGLE SOURCE OF TRUTH: Format any Date object for WITA date display
 * @param date - Date object to format
 * @param options - Formatting options
 * @returns Formatted date string in WITA timezone with Indonesian locale
 */
export function formatDateWITA(
  date: Date,
  options: {
    includeWeekday?: boolean
    includeTime?: boolean
    shortFormat?: boolean
  } = {}
): string {
  const { includeWeekday = false, includeTime = false, shortFormat = false } = options

  try {
    const formatOptions: Intl.DateTimeFormatOptions = {
      timeZone: TIMEZONE_CONFIG.TIMEZONE,
      year: 'numeric',
      month: shortFormat ? '2-digit' : 'long',
      day: '2-digit',
    }

    if (includeWeekday) {
      formatOptions.weekday = shortFormat ? 'short' : 'long'
    }

    if (includeTime) {
      formatOptions.hour = '2-digit'
      formatOptions.minute = '2-digit'
      formatOptions.hour12 = false
    }

    // Use configured locale for consistency
    return date.toLocaleDateString(TIMEZONE_CONFIG.LOCALE, formatOptions)
  } catch (error) {
    console.error('Error formatting date:', error)
    return 'Invalid Date'
  }
}

/**
 * Create a date for a specific day with offset
 * @param baseDate - Base date (defaults to current WITA time)
 * @param dayOffset - Number of days to offset (negative for past, positive for future)
 * @returns Date object for the specified day
 */
export function createDateForDay(baseDate?: Date, dayOffset: number = 0): Date {
  const date = baseDate || getCurrentWITATime()
  const newDate = new Date(date)

  if (dayOffset !== 0) {
    newDate.setDate(newDate.getDate() + dayOffset)
  }

  newDate.setHours(0, 0, 0, 0)
  return newDate
}

/**
 * Format a Date object to YYYY-MM-DD string without timezone conversion
 * This ensures consistent date formatting across the application
 * @param date - Date object to format
 * @returns Date string in YYYY-MM-DD format
 */
export function formatDateToString(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * Get the current date and time in configured timezone
 * This function ensures we get the actual current time in the configured timezone
 * @returns Date object representing current time (stored as UTC for consistency)
 */
export function getCurrentWITATime(): Date {
  // FIXED: Use proper SSOT timezone handling
  // Simply return current time - the database will store as UTC
  // Display functions will handle timezone conversion using SSOT
  return new Date()
}

/**
 * Convert UTC date to WITA timezone for display
 * @param utcDate - UTC date from database
 * @returns Date object adjusted to WITA timezone
 */
export function convertUTCToWITA(utcDate: Date): Date {
  // WITA is UTC+8
  const witaOffset = 8 * 60 * 60 * 1000 // 8 hours in milliseconds
  return new Date(utcDate.getTime() + witaOffset)
}

/**
 * Get WITA date components for date operations
 * @param date - Date object (can be UTC or local)
 * @returns Object with WITA date components
 */
export function getWITADateComponents(date: Date = new Date()) {
  const timeParts = new Intl.DateTimeFormat('en-CA', {
    timeZone: TIMEZONE_CONFIG.TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).formatToParts(date)

  return {
    year: parseInt(timeParts.find(part => part.type === 'year')?.value || '0'),
    month: parseInt(timeParts.find(part => part.type === 'month')?.value || '1'), // FIXED: Don't subtract 1, formatToParts gives 1-based month
    day: parseInt(timeParts.find(part => part.type === 'day')?.value || '1'),
    hour: parseInt(timeParts.find(part => part.type === 'hour')?.value || '0'),
    minute: parseInt(timeParts.find(part => part.type === 'minute')?.value || '0'),
    second: parseInt(timeParts.find(part => part.type === 'second')?.value || '0'),
  }
}

/**
 * Get today's date in WITA timezone (for date comparisons)
 * @returns Date object set to 00:00:00 in WITA timezone
 */
export function getTodayWITA(): Date {
  const components = getWITADateComponents()
  // FIXED: Create date at midnight WITA using correct month (subtract 1 for Date constructor)
  const witaDate = new Date(components.year, components.month - 1, components.day, 0, 0, 0, 0)
  return witaDate
}

/**
 * SINGLE SOURCE OF TRUTH: Generate WITA-aware date key for cache keys
 * This ensures cache keys use consistent WITA timezone regardless of server timezone
 * @param date - Date object to format (defaults to current date)
 * @returns Date string in YYYY-MM-DD format using WITA timezone
 */
export function getWITADateKey(date: Date = new Date()): string {
  const components = getWITADateComponents(date)
  return `${components.year}-${components.month.toString().padStart(2, '0')}-${components.day.toString().padStart(2, '0')}`
}

/**
 * Parse date string to Date object using SSOT timezone
 * This ensures consistent date parsing across the application
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Date object representing the date in WITA timezone
 */
export function parseDateStringWITA(dateString: string): Date {
  // Parse YYYY-MM-DD format
  const [year, month, day] = dateString.split('-').map(Number)

  if (!year || !month || !day) {
    throw new Error(`Invalid date string format: ${dateString}. Expected YYYY-MM-DD`)
  }

  // Create date at midnight WITA (month is 1-based in string, 0-based in Date constructor)
  return new Date(year, month - 1, day, 0, 0, 0, 0)
}

/**
 * Create a Date object that represents a specific time in WITA timezone
 * @param year Year
 * @param month Month (1-12, not 0-indexed)
 * @param day Day of month
 * @param hour Hour (0-23)
 * @param minute Minute (0-59)
 * @param second Second (0-59)
 * @returns Date object representing the specified time in WITA
 */
export function createWITADate(
  year: number,
  month: number,
  day: number,
  hour: number = 0,
  minute: number = 0,
  second: number = 0
): Date {
  // Create date in local time (month is 0-indexed in Date constructor)
  const date = new Date(year, month - 1, day, hour, minute, second)

  // Adjust for WITA timezone offset if needed
  // This ensures the date represents the correct WITA time
  return date
}

/**
 * SINGLE SOURCE OF TRUTH: Generate array of dates between start and end date (inclusive)
 * This function ensures consistent WITA timezone handling for date ranges
 *
 * @param startDate Start date (inclusive)
 * @param endDate End date (inclusive)
 * @param skipWeekends Whether to skip weekends (Saturday & Sunday - school holidays) - default false for backward compatibility
 * @returns Array of Date objects for each day in the range (excluding weekends if specified)
 */
export function generateDateRange(
  startDate: Date,
  endDate: Date,
  skipWeekends: boolean = false
): Date[] {
  const dates: Date[] = []

  // Validate inputs
  if (!startDate || !endDate || isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    console.error('Invalid dates provided to generateDateRange:', { startDate, endDate })
    return []
  }

  if (startDate > endDate) {
    console.error('Start date cannot be after end date:', { startDate, endDate })
    return []
  }

  // Create normalized dates at midnight WITA for consistent comparison
  const start = new Date(startDate)
  start.setHours(0, 0, 0, 0)

  const end = new Date(endDate)
  end.setHours(0, 0, 0, 0)

  // Generate dates from start to end (inclusive)
  const current = new Date(start)
  while (current <= end) {
    // Business Rule: Skip weekends for school attendance (Saturday = 6, Sunday = 0)
    const dayOfWeek = current.getDay()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6 // Sunday or Saturday

    if (!skipWeekends || !isWeekend) {
      dates.push(new Date(current))
    }

    current.setDate(current.getDate() + 1)
  }

  return dates
}

/**
 * SINGLE SOURCE OF TRUTH: Calculate working days (excluding weekends) between two dates
 * Used for UI display to show accurate count of school days
 *
 * @param startDate Start date (inclusive)
 * @param endDate End date (inclusive)
 * @returns Number of working days (excluding Saturday & Sunday)
 */
export function calculateWorkingDays(startDate: Date, endDate: Date): number {
  // Use generateDateRange with skipWeekends to get accurate count
  const workingDates = generateDateRange(startDate, endDate, true)
  return workingDates.length
}

/**
 * SINGLE SOURCE OF TRUTH: Validate date range for manual attendance entries
 * Ensures business rules are enforced consistently
 *
 * @param startDate Start date
 * @param endDate End date
 * @param allowFuture Whether to allow future dates (default: true for manual entries)
 * @returns Validation result with success flag and error message
 */
export function validateDateRange(
  startDate: Date,
  endDate: Date,
  allowFuture: boolean = true
): { isValid: boolean; error?: string } {
  // Check for valid dates
  if (!startDate || !endDate || isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    return { isValid: false, error: 'Tanggal tidak valid' }
  }

  // Check if start date is after end date
  if (startDate > endDate) {
    return { isValid: false, error: 'Tanggal mulai tidak boleh setelah tanggal akhir' }
  }

  // Check future dates only if not allowed
  if (!allowFuture) {
    const today = getCurrentWITATime()
    today.setHours(23, 59, 59, 999) // End of today

    if (startDate > today || endDate > today) {
      return { isValid: false, error: 'Tanggal tidak boleh di masa depan' }
    }
  } else {
    // For manual entries, allow future dates for administrative planning
    // No restriction on future dates for manual administrative entries
    // This allows for advance planning of student absences, family events, etc.
  }

  // Check if dates are too far in the past (1 year limit)
  const today = getCurrentWITATime()
  const oneYearAgo = new Date(today)
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)

  if (startDate < oneYearAgo || endDate < oneYearAgo) {
    return { isValid: false, error: 'Tanggal tidak boleh lebih dari 1 tahun yang lalu' }
  }

  // No maximum range limit - allow full flexibility for administrative needs

  return { isValid: true }
}

/**
 * Convert any Date object to configured timezone representation
 * @param date The date to convert
 * @returns Date object adjusted to represent configured timezone time
 */
export function toWITATime(date: Date): Date {
  // Get the time in configured timezone
  const timeParts = new Intl.DateTimeFormat('en-CA', {
    timeZone: TIMEZONE_CONFIG.TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).formatToParts(date)

  // Extract parts and create a proper Date object
  const year = parseInt(timeParts.find(part => part.type === 'year')?.value || '0')
  const month = parseInt(timeParts.find(part => part.type === 'month')?.value || '1') - 1
  const day = parseInt(timeParts.find(part => part.type === 'day')?.value || '1')
  const hour = parseInt(timeParts.find(part => part.type === 'hour')?.value || '0')
  const minute = parseInt(timeParts.find(part => part.type === 'minute')?.value || '0')
  const second = parseInt(timeParts.find(part => part.type === 'second')?.value || '0')

  return new Date(year, month, day, hour, minute, second)
}

/**
 * SINGLE SOURCE OF TRUTH: Create WITA date range for database queries
 * This function ensures all date range creation uses consistent WITA timezone conversion
 *
 * @param year Year
 * @param month Month (1-12, not 0-indexed)
 * @param day Day of month
 * @returns Object with startDate and endDate in UTC for database queries
 */
export function createWITADateRange(
  year: number,
  month: number,
  day: number
): {
  startDate: Date
  endDate: Date
} {
  // ✅ SSOT: Create date range correctly for WITA timezone
  // WITA is UTC+8, so we need to create UTC dates that represent WITA day boundaries

  // Create start of day in WITA (00:00:00 WITA = 16:00:00 previous day UTC)
  const startDate = new Date(Date.UTC(year, month - 1, day - 1, 16, 0, 0, 0))

  // Create end of day in WITA (23:59:59 WITA = 15:59:59 current day UTC)
  const endDate = new Date(Date.UTC(year, month - 1, day, 15, 59, 59, 999))

  return { startDate, endDate }
}

/**
 * SINGLE SOURCE OF TRUTH: Create WITA month range for database queries
 *
 * @param year Year
 * @param month Month (1-12, not 0-indexed)
 * @returns Object with startDate and endDate in UTC for database queries
 */
export function createWITAMonthRange(
  year: number,
  month: number
): {
  startDate: Date
  endDate: Date
} {
  // Get first and last day of month
  const firstDay = new Date(year, month - 1, 1)
  const lastDay = new Date(year, month, 0)

  // Create WITA range using SSOT logic
  const startDate = new Date(
    Date.UTC(firstDay.getFullYear(), firstDay.getMonth(), firstDay.getDate() - 1, 16, 0, 0, 0)
  )

  const endDate = new Date(
    Date.UTC(lastDay.getFullYear(), lastDay.getMonth(), lastDay.getDate(), 15, 59, 59, 999)
  )

  return { startDate, endDate }
}

/**
 * SINGLE SOURCE OF TRUTH: Create WITA year range for database queries
 *
 * @param year Year
 * @returns Object with startDate and endDate in UTC for database queries
 */
export function createWITAYearRange(year: number): {
  startDate: Date
  endDate: Date
} {
  // January 1st 00:00:00 WITA = December 31st 16:00:00 UTC (previous year)
  // December 31st 23:59:59 WITA = December 31st 15:59:59 UTC (same year)
  const startDate = new Date(Date.UTC(year - 1, 11, 31, 16, 0, 0, 0))
  const endDate = new Date(Date.UTC(year, 11, 31, 15, 59, 59, 999))

  return { startDate, endDate }
}

/**
 * SINGLE SOURCE OF TRUTH: Create WITA custom date range for database queries
 *
 * @param startYear Start year
 * @param startMonth Start month (1-12)
 * @param startDay Start day
 * @param endYear End year
 * @param endMonth End month (1-12)
 * @param endDay End day
 * @returns Object with startDate and endDate in UTC for database queries
 */
export function createWITACustomRange(
  startYear: number,
  startMonth: number,
  startDay: number,
  endYear: number,
  endMonth: number,
  endDay: number
): {
  startDate: Date
  endDate: Date
} {
  // Start of first day in WITA
  const startDate = new Date(Date.UTC(startYear, startMonth - 1, startDay - 1, 16, 0, 0, 0))

  // End of last day in WITA
  const endDate = new Date(Date.UTC(endYear, endMonth - 1, endDay, 15, 59, 59, 999))

  return { startDate, endDate }
}
