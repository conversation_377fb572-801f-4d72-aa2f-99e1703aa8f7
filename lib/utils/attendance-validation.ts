import {
  AttendanceType,
  PRAYER_ATTENDANCE_TYPES,
  SCHOOL_ATTENDANCE_TYPES,
  ALL_ATTENDANCE_TYPES,
  getAttendanceType<PERSON>abel as getCentralizedLabel,
} from '@/lib/domain/entities/absence'
import {
  canHandleAttendanceType,
  getAllowedAttendanceTypes as getCentralizedAllowedTypes,
  type UserRole,
} from '@/lib/config/role-permissions'

/**
 * SECURE: Validate if a user role can handle a specific attendance type
 * Used in API routes to prevent unauthorized attendance recording
 *
 * Special case: Receptionist can use IJIN_ZUHR and IJIN_ASR only in context of temporary leave workflow
 */
export function validateAttendanceTypeAccess(
  userRole: UserRole,
  attendanceType: AttendanceType | string,
  isTemporaryLeaveWorkflow: boolean = false
): boolean {
  // Special case: Allow receptionist to use prayer-specific ijin types during temporary leave workflow
  if (userRole === 'receptionist' && isTemporaryLeaveWorkflow) {
    if (attendanceType === AttendanceType.IJIN_ZUHR || attendanceType === AttendanceType.IJIN_ASR) {
      return true
    }
  }

  return canHandleAttendanceType(userRole, attendanceType)
}

/**
 * SECURE: Get allowed attendance types for a role
 * Used in UI components to show only relevant options
 * Now reads from centralized role-permissions.ts config
 */
export function getAllowedAttendanceTypes(userRole: UserRole): AttendanceType[] | string[] {
  return getCentralizedAllowedTypes(userRole)
}

/**
 * SINGLE SOURCE OF TRUTH: Get attendance type labels for UI display
 * Uses centralized function from domain entities
 */
export function getAttendanceTypeLabel(attendanceType: AttendanceType | string): string {
  return getCentralizedLabel(attendanceType)
}

/**
 * SECURE: Validate attendance type exists and is valid
 */
export function isValidAttendanceType(type: string): type is AttendanceType {
  return Object.values(AttendanceType).includes(type as AttendanceType)
}
