/**
 * Excel Hover Information Generator
 * Clean Architecture - Infrastructure Layer
 *
 * Generates detailed hover information with proper time data
 * Single responsibility: Create hover text for Excel comments
 */

import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import type { StudentDayDetail, ReportType } from './excel-shared-constants'

/**
 * Generate comprehensive hover details with time information
 */
export function generateHoverDetails(
  studentDetail: StudentDayDetail,
  statusCode: string,
  date: Date,
  reportType: ReportType
): string {
  const dateStr = format(date, 'dd MMMM yyyy', { locale: id })
  const details: string[] = []

  // Header with date and status
  details.push(`📅 ${dateStr}`)
  details.push(`📊 Status: ${statusCode}`)
  details.push('')

  if (reportType === 'prayer') {
    generatePrayerHoverDetails(details, studentDetail, statusCode)
  } else {
    generateSchoolHoverDetails(details, studentDetail, statusCode)
  }

  return details.join('\n')
}

/**
 * Generate prayer-specific hover details with times
 */
function generatePrayerHoverDetails(
  details: string[],
  studentDetail: StudentDayDetail,
  statusCode: string
): void {
  switch (statusCode) {
    case 'H':
      details.push('✅ Hadir Lengkap (Zuhr + Asr)')
      if (studentDetail.times.zuhr) {
        details.push(`   🕐 Zuhr: ${studentDetail.times.zuhr}`)
      }
      if (studentDetail.times.asr) {
        details.push(`   🕐 Asr: ${studentDetail.times.asr}`)
      }
      break

    case 'Z':
      details.push('🟡 Zuhr Saja')
      if (studentDetail.times.zuhr) {
        details.push(`   🕐 Zuhr: ${studentDetail.times.zuhr}`)
      }
      details.push('   ❌ Asr: Tidak hadir')
      break

    case 'A':
      details.push('🟡 Asr Saja')
      details.push('   ❌ Zuhr: Tidak hadir')
      if (studentDetail.times.asr) {
        details.push(`   🕐 Asr: ${studentDetail.times.asr}`)
      }
      break

    case 'I':
      details.push('🔵 Ijin')
      if (studentDetail.times.ijin) {
        details.push(`   🕐 Waktu: ${studentDetail.times.ijin}`)
      }
      if (studentDetail.reasons.ijin) {
        details.push(`   📝 Alasan: ${studentDetail.reasons.ijin}`)
      }
      break

    case '-':
      details.push('❌ Tidak Hadir')
      details.push('   • Tidak ada catatan kehadiran')
      break

    default:
      details.push(`⚠️ Status tidak dikenal: ${statusCode}`)
  }
}

/**
 * Generate school-specific hover details with times
 */
function generateSchoolHoverDetails(
  details: string[],
  studentDetail: StudentDayDetail,
  statusCode: string
): void {
  switch (statusCode) {
    case 'H':
      details.push('✅ Hadir')
      if (studentDetail.times.entry) {
        details.push(`   🕐 Masuk: ${studentDetail.times.entry}`)
      }
      // FIXED: Add temporary leave and return from leave info for Hadir status
      if (studentDetail.times.temporaryLeave) {
        details.push(`   🚪 Ijin Keluar: ${studentDetail.times.temporaryLeave}`)
      }
      if (studentDetail.times.returnFromLeave) {
        details.push(`   🔙 Kembali: ${studentDetail.times.returnFromLeave}`)
      }
      if (studentDetail.times.dismissal) {
        details.push(`   🕐 Pulang: ${studentDetail.times.dismissal}`)
      }
      // Add reasons if available
      if (studentDetail.reasons.temporaryLeave) {
        details.push(`   📝 Alasan Keluar: ${studentDetail.reasons.temporaryLeave}`)
      }
      break

    case 'M':
      details.push('🟡 Masuk Saja')
      if (studentDetail.times.entry) {
        details.push(`   🕐 Masuk: ${studentDetail.times.entry}`)
      }
      details.push('   ❌ Pulang: Tidak tercatat')
      break

    case 'P':
      details.push('🟠 Pulang Saja')
      details.push('   ❌ Masuk: Tidak tercatat')
      if (studentDetail.times.dismissal) {
        details.push(`   🕐 Pulang: ${studentDetail.times.dismissal}`)
      }
      break

    case 'T':
      details.push('✅ Hadir')
      if (studentDetail.times.lateEntry) {
        details.push(`   🟠 Terlambat: ${studentDetail.times.lateEntry}`)
      }
      // FIXED: Add temporary leave and return from leave info for Terlambat status
      if (studentDetail.times.temporaryLeave) {
        details.push(`   🚪 Ijin Keluar: ${studentDetail.times.temporaryLeave}`)
      }
      if (studentDetail.times.returnFromLeave) {
        details.push(`   🔙 Kembali: ${studentDetail.times.returnFromLeave}`)
      }
      if (studentDetail.times.dismissal) {
        details.push(`   🕐 Pulang: ${studentDetail.times.dismissal}`)
      }
      // Add reasons if available
      if (studentDetail.reasons.temporaryLeave) {
        details.push(`   📝 Alasan Keluar: ${studentDetail.reasons.temporaryLeave}`)
      }
      break

    case 'S':
      details.push('🔴 Sakit')
      if (studentDetail.times.sick) {
        details.push(`   🕐 Waktu Lapor: ${studentDetail.times.sick}`)
      }
      if (studentDetail.reasons.sick) {
        details.push(`   📝 Keterangan: ${studentDetail.reasons.sick}`)
      }
      // FIXED: Show other times if available (edge case)
      if (studentDetail.times.entry) {
        details.push(`   🕐 Masuk: ${studentDetail.times.entry}`)
      }
      if (studentDetail.times.dismissal) {
        details.push(`   🕐 Pulang: ${studentDetail.times.dismissal}`)
      }
      break

    case 'I':
      details.push('🔵 Ijin Tidak Hadir')
      if (studentDetail.times.excusedAbsence) {
        details.push(`   🕐 Waktu Lapor: ${studentDetail.times.excusedAbsence}`)
      }
      if (studentDetail.reasons.excusedAbsence) {
        details.push(`   📝 Alasan: ${studentDetail.reasons.excusedAbsence}`)
      }
      // FIXED: Show other times if available (edge case)
      if (studentDetail.times.entry) {
        details.push(`   🕐 Masuk: ${studentDetail.times.entry}`)
      }
      if (studentDetail.times.dismissal) {
        details.push(`   🕐 Pulang: ${studentDetail.times.dismissal}`)
      }
      break

    case 'IS':
      details.push('🟣 Ijin Sementara')
      // FIXED: Show all available times, not just temporaryLeave
      if (studentDetail.times.entry) {
        details.push(`   🕐 Masuk: ${studentDetail.times.entry}`)
      }
      if (studentDetail.times.lateEntry) {
        details.push(`   🕐 Masuk Terlambat: ${studentDetail.times.lateEntry}`)
      }
      if (studentDetail.times.temporaryLeave) {
        details.push(`   🚪 Ijin Keluar: ${studentDetail.times.temporaryLeave}`)
      }
      if (studentDetail.times.returnFromLeave) {
        details.push(`   🔙 Kembali: ${studentDetail.times.returnFromLeave}`)
      }
      if (studentDetail.times.dismissal) {
        details.push(`   🕐 Pulang: ${studentDetail.times.dismissal}`)
      }
      if (studentDetail.reasons.temporaryLeave) {
        details.push(`   📝 Alasan Keluar: ${studentDetail.reasons.temporaryLeave}`)
      }
      break

    case 'K':
      details.push('🟢 Kembali dari Ijin')
      // FIXED: Show all available times, not just returnFromLeave
      if (studentDetail.times.entry) {
        details.push(`   🕐 Masuk: ${studentDetail.times.entry}`)
      }
      if (studentDetail.times.lateEntry) {
        details.push(`   🕐 Masuk Terlambat: ${studentDetail.times.lateEntry}`)
      }
      if (studentDetail.times.temporaryLeave) {
        details.push(`   🚪 Ijin Keluar: ${studentDetail.times.temporaryLeave}`)
      }
      if (studentDetail.times.returnFromLeave) {
        details.push(`   🔙 Kembali: ${studentDetail.times.returnFromLeave}`)
      }
      if (studentDetail.times.dismissal) {
        details.push(`   🕐 Pulang: ${studentDetail.times.dismissal}`)
      }
      if (studentDetail.reasons.temporaryLeave) {
        details.push(`   📝 Alasan Keluar: ${studentDetail.reasons.temporaryLeave}`)
      }
      break

    case '-':
      details.push('❌ Tidak Hadir')
      details.push('   • Tidak ada catatan kehadiran')
      break

    default:
      details.push(`⚠️ Status tidak dikenal: ${statusCode}`)
  }
}

/**
 * Generate fallback hover text when detailed data is not available
 */
export function generateFallbackHover(
  statusCode: string,
  date: Date,
  reportType: ReportType
): string {
  const dateStr = format(date, 'dd MMMM yyyy', { locale: id })

  return [
    `📅 ${dateStr}`,
    `📊 Status: ${statusCode}`,
    '',
    '⚠️ Detail waktu tidak tersedia',
    '• Data diambil dari status kehadiran',
    reportType === 'prayer'
      ? '• Untuk detail waktu shalat, hubungi admin'
      : '• Untuk detail waktu kehadiran, hubungi admin',
  ].join('\n')
}
