/**
 * Excel Data Processor - Matrix Format Only
 * Clean Architecture - Application Layer
 *
 * Processes attendance data into matrix format for Excel export
 * Optimized for performance with O(n) complexity
 */

import { format, eachDayOfInterval } from 'date-fns'
import type { MatrixStudentData, StudentDayDetail, ReportType } from './excel-shared-constants'
import {
  PRAYER_STATUS_CODES as PRAYER_CODES,
  SCHOOL_STATUS_CODES as SCHOOL_CODES,
} from './excel-shared-constants'
import type { DetailedAttendanceRecord } from '@/lib/data/repositories/real-matrix-export-repository'

/**
 * Process attendance data into matrix format with O(n) complexity
 */
export function processAttendanceToMatrix(
  attendanceData: DetailedAttendanceRecord[],
  reportType: ReportType,
  startDate: Date,
  endDate: Date
): MatrixStudentData[] {
  const startTime = Date.now()

  // Generate all dates in range - O(d) where d = days
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate })
  const dateKeys = dateRange.map(date => format(date, 'yyyy-MM-dd'))

  // Use Map for O(1) student lookup
  const studentMap = new Map<string, MatrixStudentData>()

  // Process each attendance record - O(n) where n = records
  for (const record of attendanceData) {
    const studentKey = record.nis
    const dateKey = record.attendanceDate

    // Initialize student if not exists
    if (!studentMap.has(studentKey)) {
      studentMap.set(studentKey, {
        nis: record.nis,
        name: record.name,
        gender: record.gender,
        className: record.className,
        dailyStatus: {},
        dailyActivities: {},
        details: {},
      })
    }

    const student = studentMap.get(studentKey)!

    // UPDATED: Generate multiple activities per day
    if (reportType === 'prayer') {
      const statusResult = determinePrayerStatus(record)
      student.dailyStatus[dateKey] = statusResult.code
      student.dailyActivities[dateKey] = [statusResult.code] // Prayer is single activity
      student.details[dateKey] = createPrayerDetail(record, statusResult, dateKey)
    } else {
      const { primaryStatus, allActivities } = determineSchoolActivities(record)
      student.dailyStatus[dateKey] = primaryStatus.code // Primary status for display
      student.dailyActivities[dateKey] = allActivities.map(a => a.code) // All activities for totals
      student.details[dateKey] = createSchoolDetail(record, primaryStatus, dateKey)
    }
  }

  // Fill missing dates with absent status - batch processing
  const absentCode = reportType === 'prayer' ? PRAYER_CODES.ABSEN : SCHOOL_CODES.ABSEN
  for (const student of studentMap.values()) {
    for (const dateKey of dateKeys) {
      if (!student.dailyStatus[dateKey]) {
        student.dailyStatus[dateKey] = absentCode
        student.details[dateKey] = createAbsentDetail(dateKey, absentCode)
      }
    }
  }

  // Sort students by class name, then by name
  const result = Array.from(studentMap.values()).sort((a, b) => {
    const classCompare = a.className.localeCompare(b.className)
    return classCompare !== 0 ? classCompare : a.name.localeCompare(b.name)
  })

  const processingTime = Date.now() - startTime
  if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Processed ${result.length} students in ${processingTime}ms`)
  }

  return result
}

/**
 * Determine prayer status from attendance record
 */
function determinePrayerStatus(record: DetailedAttendanceRecord): {
  code: string
  description: string
} {
  const hasZuhr = !!record.zuhrTime
  const hasAsr = !!record.asrTime
  const hasIjin = !!record.ijinTime
  const hasIjinZuhr = !!record.ijinZuhrTime
  const hasIjinAsr = !!record.ijinAsrTime

  // Check for specific prayer permissions first
  if (hasIjinZuhr && hasIjinAsr) {
    return {
      code: PRAYER_CODES.IJIN,
      description: `Izin Zuhur: ${record.ijinZuhrTime}, Izin Asr: ${record.ijinAsrTime}`,
    }
  }

  if (hasIjinZuhr && !hasIjinAsr) {
    if (hasAsr) {
      return {
        code: PRAYER_CODES.ASR_ONLY,
        description: `Izin Zuhur: ${record.ijinZuhrTime}, Asr: ${record.asrTime}`,
      }
    } else {
      return {
        code: PRAYER_CODES.IJIN_ZUHR,
        description: `Izin Zuhur: ${record.ijinZuhrTime}, Asr: Tidak hadir`,
      }
    }
  }

  if (hasIjinAsr && !hasIjinZuhr) {
    if (hasZuhr) {
      return {
        code: PRAYER_CODES.ZUHR_ONLY,
        description: `Zuhr: ${record.zuhrTime}, Izin Asr: ${record.ijinAsrTime}`,
      }
    } else {
      return {
        code: PRAYER_CODES.IJIN_ASR,
        description: `Zuhr: Tidak hadir, Izin Asr: ${record.ijinAsrTime}`,
      }
    }
  }

  // Check for general prayer permission
  if (hasIjin) {
    return {
      code: PRAYER_CODES.IJIN,
      description: `Ijin${record.ijinReason ? `: ${record.ijinReason}` : ''}`,
    }
  }

  // Check for regular prayer attendance
  if (hasZuhr && hasAsr) {
    return {
      code: PRAYER_CODES.HADIR,
      description: `Zuhr: ${record.zuhrTime}, Asr: ${record.asrTime}`,
    }
  }

  if (hasZuhr && !hasAsr) {
    return {
      code: PRAYER_CODES.ZUHR_ONLY,
      description: `Zuhr: ${record.zuhrTime}, Asr: Tidak hadir`,
    }
  }

  if (!hasZuhr && hasAsr) {
    return {
      code: PRAYER_CODES.ASR_ONLY,
      description: `Zuhr: Tidak hadir, Asr: ${record.asrTime}`,
    }
  }

  return {
    code: PRAYER_CODES.ABSEN,
    description: 'Tidak ada kehadiran shalat',
  }
}

/**
 * UPDATED: Determine school activities - returns primary status + all activities
 */
function determineSchoolActivities(record: DetailedAttendanceRecord): {
  primaryStatus: { code: string; description: string }
  allActivities: { code: string; description: string }[]
} {
  const activities: { code: string; description: string }[] = []

  // Check all possible activities
  const hasEntry = !!record.entryTime
  const hasLateEntry = !!record.lateEntryTime
  const hasDismissal = !!record.dismissalTime
  const hasSick = !!record.sickTime
  const hasExcusedAbsence = !!record.excusedAbsenceTime
  const hasTemporaryLeave =
    !!record.temporaryLeaveTime || (record.temporaryLeaveTimes?.length ?? 0) > 0
  const hasReturnFromLeave =
    !!record.returnFromLeaveTime || (record.returnFromLeaveTimes?.length ?? 0) > 0

  // 1. Absolute absence conditions (highest priority for primary status)
  if (hasSick) {
    const activity = {
      code: SCHOOL_CODES.SAKIT,
      description: `Sakit${record.sickReason ? `: ${record.sickReason}` : ''}`,
    }
    activities.push(activity)
    return { primaryStatus: activity, allActivities: activities }
  }

  if (hasExcusedAbsence) {
    const activity = {
      code: SCHOOL_CODES.IJIN,
      description: `Ijin${record.excusedAbsenceReason ? `: ${record.excusedAbsenceReason}` : ''}`,
    }
    activities.push(activity)
    return { primaryStatus: activity, allActivities: activities }
  }

  // 2. Entry-based activities (primary status)
  let primaryStatus: { code: string; description: string }

  if (hasEntry || hasLateEntry) {
    // CORRECTED: Keep separate codes but display symbol will be 'H' for both
    if (hasLateEntry) {
      primaryStatus = {
        code: SCHOOL_CODES.TERLAMBAT, // Keep 'T' code for counting
        description: `Terlambat: ${record.lateEntryTime}`,
      }
    } else {
      primaryStatus = {
        code: SCHOOL_CODES.HADIR,
        description: `Hadir: ${record.entryTime}`,
      }
    }
    activities.push(primaryStatus)
  }

  // 3. Add all other activities - FIXED: Handle multiple instances
  if (hasTemporaryLeave) {
    // Handle multiple temporary leaves
    const tempLeaveTimes =
      record.temporaryLeaveTimes?.length > 0
        ? record.temporaryLeaveTimes
        : record.temporaryLeaveTime
          ? [record.temporaryLeaveTime]
          : []

    const tempLeaveReasons =
      record.temporaryLeaveReasons?.length > 0
        ? record.temporaryLeaveReasons
        : record.temporaryLeaveReason
          ? [record.temporaryLeaveReason]
          : ['']

    // Add one activity for each temporary leave
    tempLeaveTimes.forEach((time, index) => {
      const reason = tempLeaveReasons[index] || ''
      activities.push({
        code: SCHOOL_CODES.IJIN_SEMENTARA,
        description: `Ijin Keluar: ${time}${reason ? ` (${reason})` : ''}`,
      })
    })
  }

  if (hasReturnFromLeave) {
    // Handle multiple returns
    const returnTimes =
      record.returnFromLeaveTimes?.length > 0
        ? record.returnFromLeaveTimes
        : record.returnFromLeaveTime
          ? [record.returnFromLeaveTime]
          : []

    // Add one activity for each return
    returnTimes.forEach(time => {
      activities.push({
        code: SCHOOL_CODES.KEMBALI,
        description: `Kembali: ${time}`,
      })
    })
  }

  if (hasDismissal) {
    activities.push({
      code: SCHOOL_CODES.HADIR, // Dismissal is part of Hadir, not separate
      description: `Pulang: ${record.dismissalTime}`,
    })
  }

  // 4. Handle standalone activities (no entry)
  if (!hasEntry && !hasLateEntry) {
    if (hasTemporaryLeave) {
      primaryStatus = {
        code: SCHOOL_CODES.IJIN_SEMENTARA,
        description: `Ijin Keluar: ${record.temporaryLeaveTime}${record.temporaryLeaveReason ? ` (${record.temporaryLeaveReason})` : ''}`,
      }
    } else if (hasReturnFromLeave) {
      primaryStatus = {
        code: SCHOOL_CODES.KEMBALI,
        description: `Kembali: ${record.returnFromLeaveTime}`,
      }
    } else if (hasDismissal) {
      primaryStatus = {
        code: SCHOOL_CODES.ABSEN,
        description: `Tidak hadir, hanya pulang: ${record.dismissalTime}`,
      }
    } else {
      primaryStatus = {
        code: SCHOOL_CODES.ABSEN,
        description: 'Tidak ada kehadiran',
      }
    }
  }

  return {
    primaryStatus: primaryStatus!,
    allActivities: activities.length > 0 ? activities : [primaryStatus!],
  }
}

/**
 * LEGACY: Determine school status from attendance record (kept for compatibility)
 */
function determineSchoolStatus(record: DetailedAttendanceRecord): {
  code: string
  description: string
} {
  const hasEntry = !!record.entryTime
  const hasLateEntry = !!record.lateEntryTime
  const hasDismissal = !!record.dismissalTime
  const hasSick = !!record.sickTime
  const hasExcusedAbsence = !!record.excusedAbsenceTime
  const hasTemporaryLeave = !!record.temporaryLeaveTime
  const hasReturnFromLeave = !!record.returnFromLeaveTime

  // CORRECT Priority order: Sick > Excused Absence > Entry = Hadir > Standalone Leave/Return

  // 1. Absolute absence conditions (highest priority)
  if (hasSick) {
    return {
      code: SCHOOL_CODES.SAKIT,
      description: `Sakit${record.sickReason ? `: ${record.sickReason}` : ''}`,
    }
  }

  if (hasExcusedAbsence) {
    return {
      code: SCHOOL_CODES.IJIN,
      description: `Ijin${record.excusedAbsenceReason ? `: ${record.excusedAbsenceReason}` : ''}`,
    }
  }

  // 2. Entry = Hadir (regardless of other activities)
  if (hasEntry || hasLateEntry) {
    // Build description with all activities
    let description = ''

    if (hasLateEntry) {
      description = `Terlambat: ${record.lateEntryTime}`
    } else {
      description = `Hadir: ${record.entryTime}`
    }

    // Add temporary leave info if exists
    if (hasTemporaryLeave) {
      description += `, Ijin Keluar: ${record.temporaryLeaveTime}`
    }

    // Add return from leave info if exists
    if (hasReturnFromLeave) {
      description += `, Kembali: ${record.returnFromLeaveTime}`
    }

    // Add dismissal info if exists
    if (hasDismissal) {
      description += `, Pulang: ${record.dismissalTime}`
    }

    return {
      code: hasLateEntry ? SCHOOL_CODES.TERLAMBAT : SCHOOL_CODES.HADIR, // RESTORED: Keep separate codes
      description,
    }
  }

  // 3. Standalone temporary leave (without entry)
  if (hasTemporaryLeave) {
    return {
      code: SCHOOL_CODES.IJIN_SEMENTARA,
      description: `Ijin Keluar: ${record.temporaryLeaveTime}${record.temporaryLeaveReason ? ` (${record.temporaryLeaveReason})` : ''}`,
    }
  }

  // 4. Standalone return from leave (without entry)
  if (hasReturnFromLeave) {
    return {
      code: SCHOOL_CODES.KEMBALI,
      description: `Kembali: ${record.returnFromLeaveTime}`,
    }
  }

  // Entry logic already handled above - these are duplicate and removed

  if (!hasEntry && hasDismissal) {
    // Pulang saja dianggap Absen (-) tapi info pulang tetap di hover
    return {
      code: SCHOOL_CODES.ABSEN,
      description: `Tidak hadir, hanya pulang: ${record.dismissalTime}`,
    }
  }

  return {
    code: SCHOOL_CODES.ABSEN,
    description: 'Tidak ada kehadiran',
  }
}

/**
 * Create prayer detail object
 */
function createPrayerDetail(
  record: DetailedAttendanceRecord,
  statusResult: { code: string; description: string },
  dateKey: string
): StudentDayDetail {
  return {
    date: dateKey,
    times: {
      zuhr: record.zuhrTime || undefined,
      asr: record.asrTime || undefined,
      ijin: record.ijinTime || undefined,
      ijinZuhr: record.ijinZuhrTime || undefined,
      ijinAsr: record.ijinAsrTime || undefined,
    },
    reasons: {
      ijin: record.ijinReason || undefined,
    },
    statusCode: statusResult.code,
    statusDescription: statusResult.description,
  }
}

/**
 * Create school detail object
 */
function createSchoolDetail(
  record: DetailedAttendanceRecord,
  statusResult: { code: string; description: string },
  dateKey: string
): StudentDayDetail {
  return {
    date: dateKey,
    times: {
      entry: record.entryTime || undefined,
      lateEntry: record.lateEntryTime || undefined,
      sick: record.sickTime || undefined,
      excusedAbsence: record.excusedAbsenceTime || undefined,
      temporaryLeave: record.temporaryLeaveTime || undefined, // DEPRECATED
      returnFromLeave: record.returnFromLeaveTime || undefined, // DEPRECATED
      // NEW: Support multiple instances
      temporaryLeaveTimes:
        record.temporaryLeaveTimes?.length > 0 ? record.temporaryLeaveTimes : undefined,
      returnFromLeaveTimes:
        record.returnFromLeaveTimes?.length > 0 ? record.returnFromLeaveTimes : undefined,
      dismissal: record.dismissalTime || undefined,
    },
    reasons: {
      sick: record.sickReason || undefined,
      excusedAbsence: record.excusedAbsenceReason || undefined,
      temporaryLeave: record.temporaryLeaveReason || undefined, // DEPRECATED
      // NEW: Support multiple instances
      temporaryLeaveReasons:
        record.temporaryLeaveReasons?.length > 0 ? record.temporaryLeaveReasons : undefined,
    },
    statusCode: statusResult.code,
    statusDescription: statusResult.description,
  }
}

/**
 * Create absent detail object
 */
function createAbsentDetail(dateKey: string, absentCode: string): StudentDayDetail {
  return {
    date: dateKey,
    times: {},
    reasons: {},
    statusCode: absentCode,
    statusDescription: 'Tidak ada kehadiran',
  }
}

// Matrix format processing complete - no summary calculations needed
