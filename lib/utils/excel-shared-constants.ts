/**
 * Excel Export Shared Constants
 * Clean Architecture - Domain Layer
 *
 * Single source of truth for all Excel export constants
 * Following DRY principle
 */

/**
 * Status codes for Prayer Reports
 */
export const PRAYER_STATUS_CODES = {
  HADIR: 'H', // Zuhr + Asr
  ZUHR_ONLY: 'Z', // Zuhr only
  ASR_ONLY: 'A', // Asr only
  IJIN: 'I', // General prayer permission (Ijin)
  IJIN_ZUHR: 'IZ', // Specific Zuhr prayer permission
  IJIN_ASR: 'IA', // Specific Asr prayer permission
  ABSEN: '-', // No attendance
} as const

/**
 * Status codes for School Reports
 * Note: M (Masuk Only) now becomes H (Hadir)
 * Note: P (Pulang Only) now becomes - (<PERSON><PERSON><PERSON>) with hover info
 */
export const SCHOOL_STATUS_CODES = {
  HADIR: 'H', // Entry (with or without Pulang)
  MASUK_ONLY: 'M', // Legacy - now becomes H
  TERLAMBAT: 'T', // Late Entry
  SAKIT: 'S', // Sick
  IJIN: 'I', // Excused Absence
  IJIN_SEMENTARA: 'IS', // Temporary Leave
  KEMBALI: 'K', // Return from Leave
  PULANG_ONLY: 'P', // Legacy - now becomes - with hover
  ABSEN: '-', // No attendance
} as const

/**
 * Professional fonts for Excel formatting
 */
export const EXCEL_FONTS = {
  TITLE: { name: 'Calibri', size: 16, bold: true },
  HEADER: { name: 'Calibri', size: 11, bold: true },
  BODY: { name: 'Calibri', size: 10 },
  COMMENT: { name: 'Calibri', size: 9 },
} as const

/**
 * Excel color scheme
 */
export const EXCEL_COLORS = {
  MONTH_HEADER: 'FFD9EDF7',
  COLUMN_HEADER: 'FFE6F3FF',
  BORDER: 'FF000000',
} as const

/**
 * Column widths for Excel sheets
 * Optimized for compact, readable matrix format
 */
export const EXCEL_COLUMN_WIDTHS = {
  NO: 4, // Compact numbering column
  NIS: 10, // Typical NIS length (8-10 digits)
  NAME_MIN: 12, // Minimum name width
  NAME_MAX: 20, // Maximum name width (reduced for compactness)
  GENDER: 4, // Just "L" or "P"
  DATE: 4, // Numbered columns (1, 2, 3, etc.) or status codes
  SUMMARY: 4, // Summary status counts
} as const

/**
 * Status code explanations for legends
 */
export const STATUS_LEGENDS = {
  prayer: 'H = Zuhr + Asr | Z = Zuhr Only | A = Asr Only | I = Ijin | - = Absen',
  school:
    'H = Hadir | T = Terlambat | S = Sakit | I = Ijin | IS = Ijin Keluar | K = Kembali | - = Absen',
} as const

/**
 * Report type enumeration
 */
export type ReportType = 'prayer' | 'school'

/**
 * Export format enumeration
 */
export type ExportFormat = 'matrix' | 'summary'

/**
 * Matrix student data structure - Matrix format only
 * UPDATED: Support multiple activities per day
 */
export interface MatrixStudentData {
  nis: string
  name: string
  gender: string
  className: string
  dailyStatus: Record<string, string> // date -> primary status code (for display)
  dailyActivities: Record<string, string[]> // date -> all activity codes (for totals)
  details: Record<string, StudentDayDetail> // date -> detailed info
}

/**
 * Detailed information for a student's day
 */
export interface StudentDayDetail {
  date: string
  times: {
    // Prayer times
    zuhr?: string
    asr?: string
    ijin?: string
    ijinZuhr?: string
    ijinAsr?: string
    // School times
    entry?: string
    lateEntry?: string
    sick?: string
    excusedAbsence?: string
    temporaryLeave?: string // DEPRECATED: Use temporaryLeaveTimes array
    returnFromLeave?: string // DEPRECATED: Use returnFromLeaveTimes array
    // NEW: Support multiple instances
    temporaryLeaveTimes?: string[]
    returnFromLeaveTimes?: string[]
    dismissal?: string
  }
  reasons: {
    ijin?: string
    sick?: string
    excusedAbsence?: string
    temporaryLeave?: string // DEPRECATED: Use temporaryLeaveReasons array
    // NEW: Support multiple instances
    temporaryLeaveReasons?: string[]
  }
  statusCode: string
  statusDescription: string
}

/**
 * Excel export configuration - Matrix format only
 */
export interface ExcelExportConfig {
  reportType: ReportType
  exportFormat: 'matrix' // Matrix format only - summary export removed
  startDate: Date
  endDate: Date
  attendanceData: any[]
  enableFormatting: boolean
  selectedClasses?: string[]
  availableClasses?: Array<{ id: string; name: string }>
}

/**
 * Detailed attendance record from database
 */
export interface DetailedAttendanceRecord {
  nis: string
  name: string
  gender: string
  className: string
  attendanceDate: string
  // Prayer times
  zuhrTime?: string
  asrTime?: string
  ijinTime?: string
  ijinReason?: string
  // School times
  entryTime?: string
  lateEntryTime?: string
  sickTime?: string
  sickReason?: string
  excusedAbsenceTime?: string
  excusedAbsenceReason?: string
  temporaryLeaveTime?: string
  temporaryLeaveReason?: string
  returnFromLeaveTime?: string
  dismissalTime?: string
}
