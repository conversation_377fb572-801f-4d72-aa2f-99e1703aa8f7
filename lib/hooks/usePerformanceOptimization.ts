/**
 * Performance Optimization Hook
 * 
 * Provides utilities for optimizing performance in production environments
 * with clean code principles and best practices.
 */

import { useEffect, useRef, useCallback } from 'react'

interface PerformanceOptions {
  enableLogging?: boolean
  pollingInterval?: number
  maxConcurrentRequests?: number
}

interface PerformanceMetrics {
  requestCount: number
  averageResponseTime: number
  cacheHitRate: number
  lastRequestTime: number
}

/**
 * Hook for performance optimization with clean architecture
 */
export function usePerformanceOptimization(options: PerformanceOptions = {}) {
  const {
    enableLogging = process.env.NODE_ENV === 'development',
    pollingInterval = 30000, // 30 seconds default
    maxConcurrentRequests = 3
  } = options

  const metricsRef = useRef<PerformanceMetrics>({
    requestCount: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    lastRequestTime: 0
  })

  const activeRequestsRef = useRef<Set<string>>(new Set())

  /**
   * Track API request performance
   */
  const trackRequest = useCallback((requestId: string, startTime: number, endTime: number) => {
    const responseTime = endTime - startTime
    const metrics = metricsRef.current

    metrics.requestCount++
    metrics.averageResponseTime = 
      (metrics.averageResponseTime * (metrics.requestCount - 1) + responseTime) / metrics.requestCount
    metrics.lastRequestTime = endTime

    if (enableLogging) {
      console.log(`📊 PERFORMANCE: Request ${requestId} completed in ${responseTime}ms`)
    }

    activeRequestsRef.current.delete(requestId)
  }, [enableLogging])

  /**
   * Check if request should be throttled
   */
  const shouldThrottleRequest = useCallback((requestId: string): boolean => {
    if (activeRequestsRef.current.has(requestId)) {
      if (enableLogging) {
        console.log(`🚫 THROTTLED: Duplicate request ${requestId}`)
      }
      return true
    }

    if (activeRequestsRef.current.size >= maxConcurrentRequests) {
      if (enableLogging) {
        console.log(`🚫 THROTTLED: Max concurrent requests reached (${maxConcurrentRequests})`)
      }
      return true
    }

    activeRequestsRef.current.add(requestId)
    return false
  }, [maxConcurrentRequests, enableLogging])

  /**
   * Optimized fetch with performance tracking
   */
  const performantFetch = useCallback(async (
    url: string, 
    options: RequestInit = {}
  ): Promise<Response> => {
    const requestId = `${url}-${Date.now()}`
    const startTime = performance.now()

    if (shouldThrottleRequest(requestId)) {
      throw new Error('Request throttled for performance')
    }

    try {
      const response = await fetch(url, options)
      const endTime = performance.now()
      
      trackRequest(requestId, startTime, endTime)
      
      return response
    } catch (error) {
      activeRequestsRef.current.delete(requestId)
      throw error
    }
  }, [shouldThrottleRequest, trackRequest])

  /**
   * Smart polling with visibility API
   */
  const useSmartPolling = useCallback((
    callback: () => void | Promise<void>,
    interval: number = pollingInterval
  ) => {
    useEffect(() => {
      let intervalId: NodeJS.Timeout | null = null
      
      const startPolling = () => {
        if (intervalId) clearInterval(intervalId)
        intervalId = setInterval(callback, interval)
      }
      
      const stopPolling = () => {
        if (intervalId) {
          clearInterval(intervalId)
          intervalId = null
        }
      }

      const handleVisibilityChange = () => {
        if (document.hidden) {
          stopPolling()
          if (enableLogging) {
            console.log('🔇 POLLING PAUSED: Page not visible')
          }
        } else {
          callback() // Execute immediately when visible
          startPolling()
          if (enableLogging) {
            console.log('🔊 POLLING RESUMED: Page visible')
          }
        }
      }

      // Start initial polling
      startPolling()
      
      // Add visibility listener
      document.addEventListener('visibilitychange', handleVisibilityChange)

      return () => {
        stopPolling()
        document.removeEventListener('visibilitychange', handleVisibilityChange)
      }
    }, [callback, interval])
  }, [pollingInterval, enableLogging])

  /**
   * Get current performance metrics
   */
  const getMetrics = useCallback((): PerformanceMetrics => {
    return { ...metricsRef.current }
  }, [])

  /**
   * Reset performance metrics
   */
  const resetMetrics = useCallback(() => {
    metricsRef.current = {
      requestCount: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      lastRequestTime: 0
    }
    
    if (enableLogging) {
      console.log('📊 PERFORMANCE: Metrics reset')
    }
  }, [enableLogging])

  return {
    performantFetch,
    useSmartPolling,
    trackRequest,
    shouldThrottleRequest,
    getMetrics,
    resetMetrics,
    activeRequests: activeRequestsRef.current.size
  }
}

/**
 * Performance monitoring component for development
 */
export function PerformanceMonitor() {
  const { getMetrics } = usePerformanceOptimization()

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(() => {
        const metrics = getMetrics()
        console.log('📊 PERFORMANCE METRICS:', metrics)
      }, 60000) // Log every minute

      return () => clearInterval(interval)
    }
  }, [getMetrics])

  return null // This is a monitoring component, no UI
}

export default usePerformanceOptimization
