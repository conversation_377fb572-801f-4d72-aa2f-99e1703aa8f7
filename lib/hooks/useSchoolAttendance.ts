/**
 * School Attendance Hook
 *
 * Custom React hook for managing school attendance state with proper
 * loading, error handling, and real-time updates following clean architecture.
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import {
  SchoolAttendanceStatus,
  SchoolAttendanceState,
  UseSchoolAttendanceOptions,
  SchoolAttendanceApiResponse,
} from '@/lib/types/student-attendance'

/**
 * Default options for the school attendance hook
 * Optimized interval for performance vs real-time balance
 */
const DEFAULT_OPTIONS: Partial<UseSchoolAttendanceOptions> = {
  autoRefresh: true,
  refreshInterval: 30000, // 30 seconds - optimized for performance
}

/**
 * Custom hook for managing school attendance data
 * @param options - Configuration options for the hook
 * @returns School attendance state and control functions
 */
export function useSchoolAttendance(options: UseSchoolAttendanceOptions) {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options }
  const { uniqueCode, autoRefresh, refreshInterval, onSuccess, onError } = mergedOptions

  // State management
  const [state, setState] = useState<SchoolAttendanceState>({
    data: null,
    loading: true,
    error: null,
    lastUpdated: null,
  })

  // Refs for cleanup and preventing memory leaks
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)
  const isMountedRef = useRef(true)
  const retryCountRef = useRef(0)
  const maxRetries = 3

  /**
   * Fetches school attendance data from the API
   * @param showLoading - Whether to show loading state
   */
  const fetchSchoolAttendance = useCallback(
    async (showLoading: boolean = true) => {
      if (!uniqueCode) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Kode unik siswa tidak tersedia',
        }))
        return
      }

      try {
        // Cancel previous request if still pending
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
        }

        // Create new abort controller for this request
        abortControllerRef.current = new AbortController()

        // Set loading state if requested
        if (showLoading && isMountedRef.current) {
          setState(prev => ({
            ...prev,
            loading: true,
            error: null,
          }))
        }

        // Make API request with cache-busting timestamp for real-time updates
        const timestamp = Date.now()
        const response = await fetch(
          `/api/absence/check?uniqueCode=${uniqueCode}&_t=${timestamp}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
            },
            credentials: 'include',
            signal: abortControllerRef.current.signal,
          }
        )

        // Check if component is still mounted
        if (!isMountedRef.current) return

        // Handle HTTP errors
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        // Parse response data
        const apiData: SchoolAttendanceApiResponse = await response.json()

        // Extract school attendance data
        const schoolAttendanceData: SchoolAttendanceStatus = {
          entry: apiData.entry || false,
          entryTime: apiData.entryTime || null,
          lateEntry: apiData.lateEntry || false,
          lateEntryTime: apiData.lateEntryTime || null,
          excusedAbsence: apiData.excusedAbsence || false,
          excusedAbsenceTime: apiData.excusedAbsenceTime || null,
          excusedAbsenceReason: apiData.excusedAbsenceReason || null,
          sick: apiData.sick || false,
          sickTime: apiData.sickTime || null,
          sickReason: apiData.sickReason || null,
          temporaryLeave: apiData.temporaryLeave || false,
          temporaryLeaveTime: apiData.temporaryLeaveTime || null,
          temporaryLeaveReason: apiData.temporaryLeaveReason || null,
          returnFromLeave: apiData.returnFromLeave || false,
          returnFromLeaveTime: apiData.returnFromLeaveTime || null,
        }

        // Update state with successful data
        if (isMountedRef.current) {
          setState({
            data: schoolAttendanceData,
            loading: false,
            error: null,
            lastUpdated: new Date(),
          })

          // Reset retry count on success
          retryCountRef.current = 0

          // Call success callback
          onSuccess?.(schoolAttendanceData)
        }
      } catch (error) {
        // Handle aborted requests gracefully
        if (error instanceof Error && error.name === 'AbortError') {
          return
        }

        console.error('Error fetching school attendance:', error)

        // Implement exponential backoff retry logic
        if (retryCountRef.current < maxRetries && !showLoading) {
          retryCountRef.current++
          const retryDelay = Math.pow(2, retryCountRef.current) * 1000 // 2s, 4s, 8s

          console.log(
            `Retrying in ${retryDelay}ms (attempt ${retryCountRef.current}/${maxRetries})`
          )

          setTimeout(() => {
            if (isMountedRef.current) {
              fetchSchoolAttendance(false)
            }
          }, retryDelay)

          return
        }

        // Update state with error after max retries
        if (isMountedRef.current) {
          const errorMessage =
            error instanceof Error ? error.message : 'Gagal memuat status absensi sekolah'

          setState(prev => ({
            ...prev,
            loading: false,
            error: errorMessage,
          }))

          // Call error callback
          onError?.(error instanceof Error ? error : new Error(errorMessage))
        }
      }
    },
    [uniqueCode, onSuccess, onError]
  )

  /**
   * Manually refresh school attendance data
   */
  const refresh = useCallback(() => {
    fetchSchoolAttendance(true)
  }, [fetchSchoolAttendance])

  /**
   * Reset the state to initial values
   */
  const reset = useCallback(() => {
    setState({
      data: null,
      loading: true,
      error: null,
      lastUpdated: null,
    })
  }, [])

  /**
   * Clear any existing interval
   */
  const clearRefreshInterval = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])

  /**
   * Set up auto-refresh interval
   */
  const setupRefreshInterval = useCallback(() => {
    if (autoRefresh && refreshInterval && refreshInterval > 0) {
      clearRefreshInterval()
      intervalRef.current = setInterval(() => {
        fetchSchoolAttendance(false) // Don't show loading for auto-refresh
      }, refreshInterval)
    }
  }, [autoRefresh, refreshInterval, fetchSchoolAttendance, clearRefreshInterval])

  // Initial data fetch and setup
  useEffect(() => {
    isMountedRef.current = true
    fetchSchoolAttendance(true)
    setupRefreshInterval()

    return () => {
      isMountedRef.current = false
      clearRefreshInterval()
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchSchoolAttendance, setupRefreshInterval, clearRefreshInterval])

  // Update interval when options change
  useEffect(() => {
    setupRefreshInterval()
    return clearRefreshInterval
  }, [setupRefreshInterval, clearRefreshInterval])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      clearRefreshInterval()
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [clearRefreshInterval])

  return {
    // State
    data: state.data,
    loading: state.loading,
    error: state.error,
    lastUpdated: state.lastUpdated,

    // Actions
    refresh,
    reset,

    // Computed values
    hasData: state.data !== null,
    isEmpty: state.data
      ? Object.values(state.data).every(
          value => value === false || value === null || value === undefined
        )
      : true,

    // Status helpers
    isRefreshing: state.loading && state.data !== null,
    isInitialLoading: state.loading && state.data === null,
    hasError: state.error !== null,
  }
}

/**
 * Simplified hook for basic school attendance data fetching
 * @param uniqueCode - Student unique code
 * @returns Basic school attendance state
 */
export function useBasicSchoolAttendance(uniqueCode: string) {
  return useSchoolAttendance({
    uniqueCode,
    autoRefresh: true,
    refreshInterval: 10000, // 10 seconds for real-time updates
  })
}

/**
 * Hook for school attendance with custom refresh interval
 * @param uniqueCode - Student unique code
 * @param refreshInterval - Custom refresh interval in milliseconds
 * @returns School attendance state with custom refresh
 */
export function useSchoolAttendanceWithInterval(uniqueCode: string, refreshInterval: number) {
  return useSchoolAttendance({
    uniqueCode,
    autoRefresh: true,
    refreshInterval,
  })
}

/**
 * Hook for one-time school attendance data fetching (no auto-refresh)
 * @param uniqueCode - Student unique code
 * @returns School attendance state without auto-refresh
 */
export function useSchoolAttendanceOnce(uniqueCode: string) {
  return useSchoolAttendance({
    uniqueCode,
    autoRefresh: false,
  })
}
