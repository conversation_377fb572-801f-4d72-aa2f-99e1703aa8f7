import { useState, useEffect, useCallback } from 'react'
import { MonthlyReportData, YearlyReportData } from '@/lib/domain/usecases/reports'
import { ChartDataPoint, ChartDataProcessor } from '@/lib/utils/chart-data-processor'

/**
 * Hook Options
 */
interface UseReportsOptions {
  reportType: 'prayer' | 'school' | 'all'
  className?: string
  autoFetch?: boolean
  onError?: (error: Error) => void
  onSuccess?: (data: any) => void
}

/**
 * Monthly Report Hook State
 */
interface UseMonthlyReportState {
  data: MonthlyReportData | null
  chartData: ChartDataPoint[]
  loading: boolean
  error: string | null
  lastFetched: Date | null
}

/**
 * Yearly Report Hook State
 */
interface UseYearlyReportState {
  data: YearlyReportData | null
  chartData: ChartDataPoint[]
  loading: boolean
  error: string | null
  lastFetched: Date | null
}

/**
 * Custom Hook for Monthly Reports
 * Follows clean architecture principles with proper error handling and caching
 */
export function useMonthlyReport(
  month: number,
  year: number,
  options: UseReportsOptions = { reportType: 'all' }
) {
  const [state, setState] = useState<UseMonthlyReportState>({
    data: null,
    chartData: [],
    loading: false,
    error: null,
    lastFetched: null
  })

  const fetchMonthlyReport = useCallback(async (forceFresh: boolean = false) => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      console.log(`📊 Fetching Monthly Report: ${month}/${year} (${options.reportType})`)

      const params = new URLSearchParams({
        month: month.toString(),
        year: year.toString(),
        reportType: options.reportType,
        forceFresh: forceFresh.toString(),
        ...(options.className && { class: options.className })
      })

      const response = await fetch(`/api/reports/monthly?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to fetch monthly report')
      }

      const result = await response.json()
      const monthlyData = result.data as MonthlyReportData

      // Process data for charts
      const chartData = ChartDataProcessor.processMonthlyReportForChart(
        monthlyData,
        options.reportType === 'all' ? 'prayer' : options.reportType as 'prayer' | 'school'
      )

      setState({
        data: monthlyData,
        chartData,
        loading: false,
        error: null,
        lastFetched: new Date()
      })

      options.onSuccess?.(monthlyData)
      console.log(`📊 Monthly Report Loaded: ${chartData.length} weeks`)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error('Monthly report fetch error:', error)

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        chartData: ChartDataProcessor.generateEmptyChartData(
          'monthly',
          options.reportType === 'all' ? 'prayer' : options.reportType as 'prayer' | 'school'
        )
      }))

      options.onError?.(error instanceof Error ? error : new Error(errorMessage))
    }
  }, [month, year, options.reportType, options.className])

  const invalidateCache = useCallback(async () => {
    try {
      await fetch('/api/reports/monthly', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          month,
          year,
          class: options.className
        })
      })
      console.log('📊 Monthly Report Cache Invalidated')
    } catch (error) {
      console.error('Failed to invalidate monthly report cache:', error)
    }
  }, [month, year, options.className])

  // Auto-fetch on mount and dependency changes
  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchMonthlyReport()
    }
  }, [fetchMonthlyReport, options.autoFetch])

  return {
    ...state,
    refetch: fetchMonthlyReport,
    invalidateCache,
    refresh: () => fetchMonthlyReport(true)
  }
}

/**
 * Custom Hook for Yearly Reports
 * Follows clean architecture principles with proper error handling and caching
 */
export function useYearlyReport(
  year: number,
  options: UseReportsOptions = { reportType: 'all' }
) {
  const [state, setState] = useState<UseYearlyReportState>({
    data: null,
    chartData: [],
    loading: false,
    error: null,
    lastFetched: null
  })

  const fetchYearlyReport = useCallback(async (forceFresh: boolean = false) => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      console.log(`📊 Fetching Yearly Report: ${year} (${options.reportType})`)

      const params = new URLSearchParams({
        year: year.toString(),
        reportType: options.reportType,
        forceFresh: forceFresh.toString(),
        ...(options.className && { class: options.className })
      })

      const response = await fetch(`/api/reports/yearly?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to fetch yearly report')
      }

      const result = await response.json()
      const yearlyData = result.data as YearlyReportData

      // Process data for charts
      const chartData = ChartDataProcessor.processYearlyReportForChart(
        yearlyData,
        options.reportType === 'all' ? 'prayer' : options.reportType as 'prayer' | 'school'
      )

      setState({
        data: yearlyData,
        chartData,
        loading: false,
        error: null,
        lastFetched: new Date()
      })

      options.onSuccess?.(yearlyData)
      console.log(`📊 Yearly Report Loaded: ${chartData.length} months`)

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error('Yearly report fetch error:', error)

      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        chartData: ChartDataProcessor.generateEmptyChartData(
          'yearly',
          options.reportType === 'all' ? 'prayer' : options.reportType as 'prayer' | 'school'
        )
      }))

      options.onError?.(error instanceof Error ? error : new Error(errorMessage))
    }
  }, [year, options.reportType, options.className])

  const invalidateCache = useCallback(async () => {
    try {
      await fetch('/api/reports/yearly', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          year,
          class: options.className
        })
      })
      console.log('📊 Yearly Report Cache Invalidated')
    } catch (error) {
      console.error('Failed to invalidate yearly report cache:', error)
    }
  }, [year, options.className])

  // Auto-fetch on mount and dependency changes
  useEffect(() => {
    if (options.autoFetch !== false) {
      fetchYearlyReport()
    }
  }, [fetchYearlyReport, options.autoFetch])

  return {
    ...state,
    refetch: fetchYearlyReport,
    invalidateCache,
    refresh: () => fetchYearlyReport(true)
  }
}

/**
 * Combined Reports Hook for easier usage
 */
export function useReports(
  type: 'monthly' | 'yearly',
  params: { month?: number; year: number },
  options: UseReportsOptions = { reportType: 'all' }
) {
  const monthlyReport = useMonthlyReport(
    params.month || new Date().getMonth() + 1,
    params.year,
    { ...options, autoFetch: type === 'monthly' }
  )

  const yearlyReport = useYearlyReport(
    params.year,
    { ...options, autoFetch: type === 'yearly' }
  )

  if (type === 'monthly') {
    return monthlyReport
  } else {
    return yearlyReport
  }
}
