const { Pool } = require('pg')
const path = require('path')

// Load environment variables from .env.local
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') })

async function testDatabaseConnection() {
    const databaseUrl = process.env.DATABASE_URL

    if (!databaseUrl) {
        console.error('❌ DATABASE_URL not found in environment variables')
        console.log('💡 Make sure .env.local file exists and contains DATABASE_URL')
        return false
    }

    console.log('🔍 Using DATABASE_URL:', databaseUrl.replace(/:[^:@]+@/, ':****@'))

    const pool = new Pool({
        connectionString: databaseUrl,
    })

    try {
        console.log('🔄 Testing database connection...')

        const client = await pool.connect()
        const result = await client.query('SELECT NOW() as current_time')

        console.log('✅ Database connection successful!')
        console.log('📅 Current time from database:', result.rows[0].current_time)

        // Test basic tables
        try {
            const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `)

            if (tablesResult.rows.length > 0) {
                console.log('📋 Available tables:')
                tablesResult.rows.forEach(row => {
                    console.log(`   - ${row.table_name}`)
                })
            } else {
                console.log('📋 No tables found (database might be empty)')
            }
        } catch (tableError) {
            console.log('⚠️  Could not list tables:', tableError.message)
        }

        client.release()

        return true
    } catch (error) {
        console.error('❌ Database connection failed:', error.message)

        if (error.code === 'ECONNREFUSED') {
            console.log('�� Possible solutions:')
            console.log('   - Check if PostgreSQL is running')
            console.log('   - Run: docker compose -f docker-compose.dev.yml up -d postgres-dev')
            console.log('   - Verify DATABASE_URL in .env.local')
        }

        return false
    } finally {
        await pool.end()
    }
}

// Run the test
if (require.main === module) {
    testDatabaseConnection()
        .then(success => {
            if (success) {
                console.log('🎉 Database test completed successfully!')
            } else {
                console.log('❌ Database test failed')
            }
            process.exit(success ? 0 : 1)
        })
        .catch(error => {
            console.error('❌ Unexpected error:', error)
            process.exit(1)
        })
}

module.exports = { testDatabaseConnection } 