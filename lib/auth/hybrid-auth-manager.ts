/**
 * 🎯 HYBRID JWT + REDIS AUTHENTICATION MANAGER
 * 
 * Best Practice Implementation:
 * - JWT for stateless authentication (short-lived)
 * - Redis for session control & device management
 * - One device per user policy
 * - Immediate logout capability
 * 
 * Based on industry best practices research:
 * - Hybrid approach combines JWT benefits with server-side control
 * - Short-lived JWT (15-30 min) with Redis session validation
 * - <PERSON><PERSON> fingerprinting for security
 * - Real-time session invalidation
 */

import { generateToken, verifyToken, JWTPayload } from '@/lib/utils/auth'
import { RedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config/server'
import { v4 as uuidv4 } from 'uuid'

interface DeviceInfo {
  deviceId: string
  userAgent: string
  ipAddress: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  browser: string
}

interface SessionData {
  sessionId: string
  userId: number
  role: string
  deviceInfo: DeviceInfo
  createdAt: Date
  lastAccessedAt: Date
  expiresAt: Date
  isActive: boolean
}

interface AuthResult {
  accessToken: string
  refreshToken: string
  sessionId: string
  expiresIn: number
}

export class HybridAuthManager {
  private cache: RedisCache
  private readonly SESSION_PREFIX = 'session:'
  private readonly USER_SESSION_PREFIX = 'user_session:'
  private readonly DEVICE_SESSION_PREFIX = 'device_session:'
  private readonly REFRESH_TOKEN_PREFIX = 'refresh:'
  
  // ✅ BEST PRACTICE: Short-lived access tokens
  private readonly ACCESS_TOKEN_EXPIRY = 30 * 60 // 30 minutes
  private readonly REFRESH_TOKEN_EXPIRY = 7 * 24 * 60 * 60 // 7 days
  private readonly SESSION_EXPIRY = 24 * 60 * 60 // 24 hours

  constructor() {
    this.cache = new RedisCache()
  }

  /**
   * ✅ HYBRID LOGIN: JWT + Redis Session with One Device Policy
   */
  async login(
    userId: number,
    role: string,
    deviceInfo: DeviceInfo
  ): Promise<AuthResult> {
    const sessionId = uuidv4()
    const now = new Date()
    const sessionExpiresAt = new Date(now.getTime() + this.SESSION_EXPIRY * 1000)
    
    // ✅ STEP 1: Check and invalidate existing sessions (One Device Policy)
    await this.enforceOneDevicePolicy(userId, deviceInfo.deviceId)
    
    // ✅ STEP 2: Create session data in Redis
    const sessionData: SessionData = {
      sessionId,
      userId,
      role,
      deviceInfo,
      createdAt: now,
      lastAccessedAt: now,
      expiresAt: sessionExpiresAt,
      isActive: true
    }
    
    // Store session with TTL
    await this.cache.set(
      `${this.SESSION_PREFIX}${sessionId}`,
      JSON.stringify(sessionData),
      this.SESSION_EXPIRY
    )
    
    // ✅ STEP 3: Create user -> session mapping
    await this.cache.set(
      `${this.USER_SESSION_PREFIX}${userId}`,
      sessionId,
      this.SESSION_EXPIRY
    )
    
    // ✅ STEP 4: Create device -> session mapping
    await this.cache.set(
      `${this.DEVICE_SESSION_PREFIX}${userId}:${deviceInfo.deviceId}`,
      sessionId,
      this.SESSION_EXPIRY
    )
    
    // ✅ STEP 5: Generate JWT tokens
    const accessToken = this.generateAccessToken(userId, role, sessionId)
    const refreshToken = this.generateRefreshToken(userId, sessionId)
    
    // Store refresh token
    await this.cache.set(
      `${this.REFRESH_TOKEN_PREFIX}${sessionId}`,
      refreshToken,
      this.REFRESH_TOKEN_EXPIRY
    )
    
    console.log(`✅ HYBRID LOGIN: User ${userId} logged in with session ${sessionId}`)
    
    return {
      accessToken,
      refreshToken,
      sessionId,
      expiresIn: this.ACCESS_TOKEN_EXPIRY
    }
  }

  /**
   * ✅ HYBRID VALIDATION: Fast JWT + Redis Session Check
   */
  async validateAuth(accessToken: string): Promise<{
    isValid: boolean
    userId?: number
    role?: string
    sessionId?: string
    needsRefresh?: boolean
  }> {
    try {
      // ✅ STEP 1: Verify JWT (fast, stateless)
      const decoded = verifyToken(accessToken, serverConfig.auth.jwtSecret || '')
      
      if (!decoded.sessionId) {
        return { isValid: false }
      }
      
      // ✅ STEP 2: Quick Redis session check (only if JWT is valid)
      const sessionData = await this.getSession(decoded.sessionId)
      
      if (!sessionData || !sessionData.isActive) {
        return { isValid: false }
      }
      
      // ✅ STEP 3: Update last accessed (async, don't wait)
      this.updateLastAccessed(decoded.sessionId).catch(console.error)
      
      // ✅ STEP 4: Check if token needs refresh (5 minutes before expiry)
      const needsRefresh = this.shouldRefreshToken(decoded)
      
      return {
        isValid: true,
        userId: decoded.id,
        role: decoded.role,
        sessionId: decoded.sessionId,
        needsRefresh
      }
      
    } catch (error) {
      console.error('Auth validation error:', error)
      return { isValid: false }
    }
  }

  /**
   * ✅ ONE DEVICE POLICY: Invalidate other sessions
   */
  private async enforceOneDevicePolicy(userId: number, newDeviceId: string): Promise<void> {
    try {
      // Get existing session for this user
      const existingSessionId = await this.cache.get(`${this.USER_SESSION_PREFIX}${userId}`)
      
      if (existingSessionId) {
        const existingSession = await this.getSession(existingSessionId)
        
        if (existingSession && existingSession.deviceInfo.deviceId !== newDeviceId) {
          console.log(`🔒 ONE DEVICE POLICY: Invalidating session ${existingSessionId} for user ${userId}`)
          await this.invalidateSession(existingSessionId)
        }
      }
    } catch (error) {
      console.error('Error enforcing one device policy:', error)
    }
  }

  /**
   * ✅ LOGOUT: Immediate session invalidation
   */
  async logout(sessionId: string): Promise<void> {
    await this.invalidateSession(sessionId)
    console.log(`✅ LOGOUT: Session ${sessionId} invalidated`)
  }

  /**
   * ✅ REFRESH TOKEN: Generate new access token
   */
  async refreshAccessToken(refreshToken: string): Promise<AuthResult | null> {
    try {
      const decoded = verifyToken(refreshToken, serverConfig.auth.jwtSecret || '')
      
      if (!decoded.sessionId) {
        return null
      }
      
      // Verify refresh token exists in Redis
      const storedRefreshToken = await this.cache.get(`${this.REFRESH_TOKEN_PREFIX}${decoded.sessionId}`)
      
      if (storedRefreshToken !== refreshToken) {
        return null
      }
      
      // Get session data
      const sessionData = await this.getSession(decoded.sessionId)
      
      if (!sessionData || !sessionData.isActive) {
        return null
      }
      
      // Generate new access token
      const newAccessToken = this.generateAccessToken(
        sessionData.userId,
        sessionData.role,
        decoded.sessionId
      )
      
      return {
        accessToken: newAccessToken,
        refreshToken, // Keep same refresh token
        sessionId: decoded.sessionId,
        expiresIn: this.ACCESS_TOKEN_EXPIRY
      }
      
    } catch (error) {
      console.error('Token refresh error:', error)
      return null
    }
  }

  // ✅ HELPER METHODS
  
  private generateAccessToken(userId: number, role: string, sessionId: string): string {
    const payload: JWTPayload = {
      id: userId,
      role,
      sessionId,
      type: 'access'
    }
    
    return generateToken(payload, serverConfig.auth.jwtSecret || '', `${this.ACCESS_TOKEN_EXPIRY}s`)
  }
  
  private generateRefreshToken(userId: number, sessionId: string): string {
    const payload = {
      id: userId,
      sessionId,
      type: 'refresh'
    }
    
    return generateToken(payload, serverConfig.auth.jwtSecret || '', `${this.REFRESH_TOKEN_EXPIRY}s`)
  }
  
  private async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionJson = await this.cache.get(`${this.SESSION_PREFIX}${sessionId}`)
      return sessionJson ? JSON.parse(sessionJson) : null
    } catch (error) {
      console.error('Error getting session:', error)
      return null
    }
  }
  
  private async invalidateSession(sessionId: string): Promise<void> {
    try {
      const sessionData = await this.getSession(sessionId)
      
      if (sessionData) {
        // Remove all related keys
        await Promise.all([
          this.cache.del(`${this.SESSION_PREFIX}${sessionId}`),
          this.cache.del(`${this.USER_SESSION_PREFIX}${sessionData.userId}`),
          this.cache.del(`${this.DEVICE_SESSION_PREFIX}${sessionData.userId}:${sessionData.deviceInfo.deviceId}`),
          this.cache.del(`${this.REFRESH_TOKEN_PREFIX}${sessionId}`)
        ])
      }
    } catch (error) {
      console.error('Error invalidating session:', error)
    }
  }
  
  private async updateLastAccessed(sessionId: string): Promise<void> {
    try {
      const sessionData = await this.getSession(sessionId)
      
      if (sessionData) {
        sessionData.lastAccessedAt = new Date()
        await this.cache.set(
          `${this.SESSION_PREFIX}${sessionId}`,
          JSON.stringify(sessionData),
          this.SESSION_EXPIRY
        )
      }
    } catch (error) {
      console.error('Error updating last accessed:', error)
    }
  }
  
  private shouldRefreshToken(decoded: JWTPayload): boolean {
    if (!decoded.exp) return false
    
    const now = Math.floor(Date.now() / 1000)
    const timeUntilExpiry = decoded.exp - now
    
    // Refresh if less than 5 minutes remaining
    return timeUntilExpiry < 300
  }
}

// ✅ SINGLETON INSTANCE
export const hybridAuth = new HybridAuthManager()
