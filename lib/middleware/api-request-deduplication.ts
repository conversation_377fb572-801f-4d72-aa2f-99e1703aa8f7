/**
 * 🚀 API Request Deduplication Middleware
 * Prevents multiple identical API requests from being processed simultaneously
 *
 * This middleware is especially useful for:
 * - Session validation endpoints
 * - Data fetching endpoints that are called multiple times
 * - Heavy database operations
 */

import { NextRequest, NextResponse } from 'next/server'

// Store pending requests by their unique key
const pendingApiRequests = new Map<string, Promise<NextResponse>>()

// Cache for recent responses (short-term caching)
const responseCache = new Map<
  string,
  {
    response: NextResponse
    timestamp: number
    expiresAt: number
  }
>()

// Default cache TTL: 30 seconds for API responses
const API_RESPONSE_CACHE_TTL = 30 * 1000

/**
 * Generate a unique key for the request
 */
function generateRequestKey(req: NextRequest): string {
  const url = req.url
  const method = req.method
  const headers = req.headers.get('authorization') || ''

  // Include relevant headers and query params for uniqueness
  return `${method}:${url}:${headers.substring(0, 50)}`
}

/**
 * Get cached response if still valid (FIXED: Avoid ReadableStream errors)
 */
function getCachedResponse(key: string): NextResponse | null {
  const cached = responseCache.get(key)
  if (!cached) return null

  const now = Date.now()
  if (now > cached.expiresAt) {
    responseCache.delete(key)
    return null
  }

  console.log(`✅ API RESPONSE CACHE HIT: ${key.split(':')[1]}`)
  // 🚀 FIX: Return the cached response directly (no clone needed for JSON responses)
  return cached.response
}

/**
 * Cache API response (FIXED: Avoid ReadableStream errors)
 */
async function setCachedResponse(key: string, response: NextResponse): Promise<void> {
  // Only cache successful responses
  if (response.status >= 200 && response.status < 300) {
    try {
      // 🚀 FIX: Convert response to JSON to avoid ReadableStream issues
      const responseData = await response.json()
      const now = Date.now()

      // Create a new response from the data
      const cachedResponse = NextResponse.json(responseData, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      })

      responseCache.set(key, {
        response: cachedResponse,
        timestamp: now,
        expiresAt: now + API_RESPONSE_CACHE_TTL,
      })

      console.log(
        `🔄 API RESPONSE CACHED: ${key.split(':')[1]} (TTL: ${API_RESPONSE_CACHE_TTL / 1000}s)`
      )

      // Cleanup expired entries periodically
      if (responseCache.size % 20 === 0) {
        cleanupExpiredResponses()
      }
    } catch (error) {
      console.error(`❌ Failed to cache response for ${key}:`, error)
    }
  }
}

/**
 * Clean up expired response cache entries
 */
function cleanupExpiredResponses(): void {
  const now = Date.now()
  let cleanedCount = 0

  for (const [key, cached] of responseCache.entries()) {
    if (now > cached.expiresAt) {
      responseCache.delete(key)
      cleanedCount++
    }
  }

  if (cleanedCount > 0) {
    console.log(`🧹 API CACHE CLEANUP: Removed ${cleanedCount} expired entries`)
  }
}

/**
 * Request deduplication wrapper for API endpoints
 * 🚨 TEMPORARY FIX: Disabled response caching to prevent ReadableStream errors
 */
export function withApiRequestDeduplication(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const requestKey = generateRequestKey(req)

    // 🚨 DISABLED: Response caching temporarily disabled due to ReadableStream issues
    // TODO: Implement proper response caching without consuming the stream

    // Check if the same request is already in progress
    const existingRequest = pendingApiRequests.get(requestKey)
    if (existingRequest) {
      console.log(`🔄 API REQUEST DEDUPLICATION: Waiting for ${req.method} ${req.url}`)
      return existingRequest
    }

    // Start new request and store the promise
    const requestPromise = handler(req)
    pendingApiRequests.set(requestKey, requestPromise)

    try {
      const response = await requestPromise

      console.log(`✅ API REQUEST COMPLETED: ${req.method} ${req.url}`)
      return response
    } finally {
      // Always clean up the pending request
      pendingApiRequests.delete(requestKey)
    }
  }
}

/**
 * Get current cache statistics
 */
export function getApiCacheStats() {
  return {
    pendingRequests: pendingApiRequests.size,
    cachedResponses: responseCache.size,
    cacheHitRate: 0, // TODO: Implement hit rate tracking
  }
}
