import { NextRequest, NextResponse } from 'next/server'
import { verifyToken, JWTPayload } from '@/lib/utils/auth'
import { getClientIpAddress } from '@/lib/utils/session'
import { SessionUseCases } from '@/lib/domain/usecases/session'
import { RedisSessionRepository } from '@/lib/data/repositories/redis-session-repository'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'

// Initialize dependencies
const cache = getRedisCache()
const sessionRepo = new RedisSessionRepository(cache)
const studentRepo = new StudentRepository()
const adminRepo = new AdminRepository(cache)
const sessionUseCases = new SessionUseCases(sessionRepo, studentRepo, adminRepo, cache)

// ✅ PROPER ARCHITECTURE: Redis-based session validation cache
// Persistent cache that survives server restarts and works across instances
const SESSION_VALIDATION_CACHE_PREFIX = 'session_validation:'

// 🚀 SESSION VALIDATION DEDUPLICATION: Prevent concurrent validations
const pendingSessionValidations = new Map<string, Promise<any>>()

// ✅ ROOT CAUSE FIX: Much longer cache TTL to reduce validation frequency
// Cache TTL: 15 minutes for session validation results (was 2 minutes)
const SESSION_VALIDATION_CACHE_TTL = 15 * 60 * 1000

/**
 * ✅ PROPER ARCHITECTURE: Redis-based session validation cache
 * Get cached session validation result if still valid
 */
async function getCachedSessionValidation(sessionId: string): Promise<any | null> {
  try {
    const cacheKey = `${SESSION_VALIDATION_CACHE_PREFIX}${sessionId}`
    const cachedJson = await cache.get(cacheKey)

    console.log(
      `🔍 SESSION CACHE CHECK: ${sessionId} - ${cachedJson ? 'FOUND' : 'NOT_FOUND'} (Redis-based)`
    )

    if (!cachedJson) return null

    const cached = JSON.parse(cachedJson)
    const now = Date.now()

    if (now > cached.expiresAt) {
      console.log(
        `⏰ SESSION CACHE EXPIRED: ${sessionId} (expired ${(now - cached.expiresAt) / 1000}s ago)`
      )
      await cache.del(cacheKey)
      return null
    }

    console.log(`✅ SESSION CACHE HIT: ${sessionId} (age: ${(now - cached.timestamp) / 1000}s)`)
    return cached.result
  } catch (error) {
    console.error('Session cache get error:', error)
    return null
  }
}

/**
 * ✅ PROPER ARCHITECTURE: Redis-based session validation cache
 * Cache session validation result with proper TTL
 */
async function setCachedSessionValidation(sessionId: string, result: any): Promise<void> {
  try {
    const now = Date.now()
    const cacheKey = `${SESSION_VALIDATION_CACHE_PREFIX}${sessionId}`
    const cacheData = {
      result,
      timestamp: now,
      expiresAt: now + SESSION_VALIDATION_CACHE_TTL,
    }

    // Set with TTL in Redis (convert ms to seconds)
    const ttlSeconds = Math.floor(SESSION_VALIDATION_CACHE_TTL / 1000)
    await cache.set(cacheKey, JSON.stringify(cacheData), ttlSeconds)

    console.log(`🔄 SESSION CACHE SET: ${sessionId} (TTL: ${ttlSeconds}s, Redis-based)`)
  } catch (error) {
    console.error('Session cache set error:', error)
  }
}

// ✅ CLEANUP NOT NEEDED: Redis handles TTL automatically

/**
 * ✅ ROOT CAUSE FIX: Determine if session validation should be skipped
 * Skip validation for non-sensitive requests to reduce frequency
 */
function shouldSkipValidation(req: NextRequest): boolean {
  const pathname = req.nextUrl.pathname

  // ✅ SKIP validation for these non-sensitive endpoints:
  const skipPaths = [
    '/api/classes', // Class data (relatively static)
    '/api/users', // User listing (non-sensitive)
    '/api/admins', // Admin listing (non-sensitive)
    '/api/prayer-exemptions', // Prayer exemptions (non-sensitive)
  ]

  // ✅ SKIP validation for GET requests to non-sensitive endpoints
  if (req.method === 'GET' && skipPaths.some(path => pathname.startsWith(path))) {
    return true
  }

  // ✅ ALWAYS validate for sensitive operations
  const sensitivePaths = [
    '/api/auth/', // Authentication endpoints
    '/api/admin/sessions', // Session management
    '/api/absence/reports', // Sensitive reports
  ]

  if (sensitivePaths.some(path => pathname.startsWith(path))) {
    return false
  }

  // ✅ SKIP validation for static/frequent requests
  if (pathname.includes('/_next/') || pathname.includes('/favicon')) {
    return true
  }

  // ✅ DEFAULT: Validate session (conservative approach)
  return false
}

/**
 * Enhanced authentication result
 */
export interface AuthenticationResult {
  id: number
  role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'
  sessionId: string
  deviceId: string
  newToken?: string // If token was refreshed
}

/**
 * Enhanced middleware to authenticate requests with session validation
 * @param req The incoming request
 * @param role The expected role ('admin' or 'student')
 * @returns The authenticated user info and session data
 */
export async function authenticateWithSession(
  req: NextRequest,
  role?: 'admin' | 'student'
): Promise<AuthenticationResult> {
  // Get the appropriate auth token
  let authToken: string | undefined = undefined

  if (role === 'admin') {
    authToken = req.cookies.get('admin_auth_token')?.value
  } else if (role === 'student') {
    authToken = req.cookies.get('student_auth_token')?.value
  } else {
    // If no specific role, try to find any auth token
    authToken =
      req.cookies.get('admin_auth_token')?.value || req.cookies.get('student_auth_token')?.value
  }

  if (!authToken) {
    console.log(`Authentication failed: No auth token found for role: ${role || 'any'}`)
    throw new Error('Authentication required')
  }

  let decoded: JWTPayload
  try {
    decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

    if (!decoded || !decoded.id) {
      console.error('Token verification failed: Missing ID in token payload')
      throw new Error('Invalid token')
    }

    // Validate role if specified
    if (
      role &&
      decoded.role !== role &&
      !(role === 'admin' && ['super_admin', 'teacher', 'receptionist'].includes(decoded.role))
    ) {
      console.error(`Role validation failed: Expected ${role}, got ${decoded.role}`)
      throw new Error('Insufficient permissions')
    }

    console.log(
      `Token verified successfully for user ${decoded.id}, role: ${decoded.role}, sessionId: ${decoded.sessionId || 'none'}`
    )
  } catch (error) {
    console.error('Token verification error:', error)
    throw new Error('Invalid or expired token')
  }

  // ✅ ROOT CAUSE FIX: Skip session validation for non-sensitive requests
  const shouldSkipSessionValidation = shouldSkipValidation(req)

  // If token has session information, validate the session (only when necessary)
  if (decoded.sessionId && decoded.sessionId !== 'legacy' && !shouldSkipSessionValidation) {
    try {
      // 🚀 CHECK CACHE FIRST: Avoid repeated validation of the same session
      const cachedValidation = await getCachedSessionValidation(decoded.sessionId)
      if (cachedValidation) {
        console.log(`✅ CACHED SESSION VALIDATION: ${decoded.sessionId} (avoiding database call)`)
        return {
          id: decoded.id,
          role: decoded.role,
          sessionId: decoded.sessionId,
          deviceId: decoded.deviceId || 'unknown',
          newToken:
            cachedValidation.newToken === 'REFRESH_NEEDED' ? undefined : cachedValidation.newToken,
        }
      }

      // 🚀 REQUEST DEDUPLICATION: Check if validation is already in progress
      const existingValidation = pendingSessionValidations.get(decoded.sessionId)
      if (existingValidation) {
        console.log(
          `🔄 SESSION DEDUPLICATION: Waiting for existing validation ${decoded.sessionId}`
        )
        const sessionValidation = await existingValidation
        return {
          id: decoded.id,
          role: decoded.role,
          sessionId: decoded.sessionId,
          deviceId: decoded.deviceId || 'unknown',
          newToken:
            sessionValidation.newToken === 'REFRESH_NEEDED'
              ? undefined
              : sessionValidation.newToken,
        }
      }

      console.log(`Validating session ${decoded.sessionId} for user ${decoded.id}`)

      // Start validation and store promise for deduplication
      const validationPromise = sessionUseCases.validateAndRefreshSession(
        decoded.sessionId,
        decoded.id
      )
      pendingSessionValidations.set(decoded.sessionId, validationPromise)

      try {
        const sessionValidation = await validationPromise

        if (!sessionValidation.isValid) {
          console.error(
            `Session validation failed for session ${decoded.sessionId}:`,
            sessionValidation
          )
          throw new Error('Session is invalid or expired')
        }

        console.log(`Session validation successful for session ${decoded.sessionId}`)

        // 🚀 CACHE THE RESULT: Store successful validation for 2 minutes
        await setCachedSessionValidation(decoded.sessionId, sessionValidation)

        // Return authentication result with optional new token
        return {
          id: decoded.id,
          role: decoded.role,
          sessionId: decoded.sessionId,
          deviceId: decoded.deviceId || 'unknown',
          newToken:
            sessionValidation.newToken === 'REFRESH_NEEDED'
              ? undefined
              : sessionValidation.newToken,
        }
      } catch (error) {
        console.error(`Session validation error for session ${decoded.sessionId}:`, error)
        throw new Error('Session validation failed')
      } finally {
        // Always clean up pending validation
        pendingSessionValidations.delete(decoded.sessionId)
      }
    } catch (error) {
      console.error(`Session validation error for session ${decoded.sessionId}:`, error)
      throw new Error('Session validation failed')
    }
  }

  // For production, we should require session-based authentication
  // Legacy tokens without session info should be rejected for better security
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Legacy tokens not supported in production')
  }

  // Fallback for legacy tokens without session info (development only)
  console.warn('Using legacy token without session - this should not happen in production')
  return {
    id: decoded.id,
    role: decoded.role,
    sessionId: 'legacy',
    deviceId: decoded.deviceId || 'unknown',
  }
}

/**
 * Middleware specifically for super admin access
 */
export async function authenticateSuperAdmin(req: NextRequest): Promise<AuthenticationResult> {
  const result = await authenticateWithSession(req, 'admin')

  if (result.role !== 'super_admin') {
    throw new Error('Super admin access required')
  }

  return result
}

/**
 * Create response with refreshed token if needed
 */
export function createResponseWithRefreshedToken(
  response: NextResponse,
  authResult: AuthenticationResult,
  role: 'admin' | 'student'
): NextResponse {
  if (authResult.newToken) {
    const { getSecureCookieOptions, COOKIE_NAMES } = require('@/lib/utils/cookie-security')
    const cookieName = role === 'admin' ? COOKIE_NAMES.ADMIN_AUTH : COOKIE_NAMES.STUDENT_AUTH

    response.cookies.set(cookieName, authResult.newToken, getSecureCookieOptions(60 * 60)) // 1 hour
  }

  return response
}

/**
 * Validate session from request headers (for API calls)
 */
export async function validateSessionFromHeaders(req: NextRequest): Promise<{
  isValid: boolean
  userId?: number
  role?: string
  sessionId?: string
  error?: string
}> {
  const authHeader = req.headers.get('authorization')

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      isValid: false,
      error: 'Missing or invalid authorization header',
    }
  }

  const token = authHeader.substring(7) // Remove 'Bearer ' prefix

  try {
    const decoded = verifyToken(token, serverConfig.auth.jwtSecret || '')

    if (decoded.sessionId) {
      const sessionValidation = await sessionUseCases.validateSession(decoded.sessionId, true)

      if (!sessionValidation.isValid) {
        return {
          isValid: false,
          error: 'Session is invalid or expired',
        }
      }

      return {
        isValid: true,
        userId: decoded.id,
        role: decoded.role,
        sessionId: decoded.sessionId,
      }
    }

    // Legacy token without session
    return {
      isValid: true,
      userId: decoded.id,
      role: decoded.role,
      sessionId: 'legacy',
    }
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid token',
    }
  }
}

/**
 * Extract device information from request
 */
export function extractDeviceInfo(req: NextRequest): {
  userAgent: string
  ipAddress: string
} {
  const userAgent = req.headers.get('user-agent') || 'Unknown'
  const ipAddress = getClientIpAddress(req.headers)

  return { userAgent, ipAddress }
}

/**
 * Check if request is from same device as session
 */
export async function validateDeviceConsistency(
  req: NextRequest,
  sessionId: string
): Promise<boolean> {
  try {
    const session = await sessionUseCases.getSession(sessionId)

    if (!session) {
      return false
    }

    const { userAgent, ipAddress } = extractDeviceInfo(req)

    // For now, we'll just check if the user agent matches
    // In a production system, you might want more sophisticated device fingerprinting
    return session.userAgent === userAgent
  } catch (error) {
    console.error('Device consistency check failed:', error)
    return false
  }
}

/**
 * Clear auth cookies for a specific role
 */
export function clearAuthCookies(response: NextResponse, role: 'admin' | 'student'): NextResponse {
  const { getClearCookieOptions, getCookieNamesForRole } = require('@/lib/utils/cookie-security')
  const cookieNames = getCookieNamesForRole(role)
  const clearOptions = getClearCookieOptions()

  // Clear all cookies for the role
  cookieNames.forEach((cookieName: string) => {
    response.cookies.set(cookieName, '', clearOptions)
  })

  return response
}

/**
 * Logout helper that clears cookies and invalidates session
 */
export async function logoutUser(
  response: NextResponse,
  sessionId: string,
  userId: number,
  role: 'admin' | 'student'
): Promise<NextResponse> {
  // Invalidate session
  await sessionUseCases.invalidateSession(sessionId)

  // Clear auth cookies
  return clearAuthCookies(response, role)
}
