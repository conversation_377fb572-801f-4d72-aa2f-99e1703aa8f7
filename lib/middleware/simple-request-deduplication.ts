/**
 * 🚀 Data-Level Request Deduplication Middleware
 * Prevents multiple identical API requests from being processed simultaneously
 *
 * ✅ FIXED: Uses data-level deduplication to avoid ReadableStream issues
 * ✅ STRATEGY: Cache response data, not response objects
 */

import { NextRequest, NextResponse } from 'next/server'

// Store pending requests by their unique key (returns response data, not NextResponse)
const pendingRequests = new Map<string, Promise<any>>()

// Cache response data temporarily (not NextResponse objects)
const responseDataCache = new Map<
  string,
  {
    data: any
    status: number
    headers: Record<string, string>
    timestamp: number
    expiresAt: number
  }
>()

// Cache TTL: 30 seconds for response data
const RESPONSE_DATA_CACHE_TTL = 30 * 1000

/**
 * Generate a unique key for the request
 */
function generateRequestKey(req: NextRequest): string {
  const url = req.url
  const method = req.method
  const headers = req.headers.get('authorization') || ''

  // Include relevant headers and query params for uniqueness
  return `${method}:${url}:${headers.substring(0, 50)}`
}

/**
 * Get cached response data if still valid
 */
function getCachedResponseData(key: string): any | null {
  const cached = responseDataCache.get(key)
  if (!cached) return null

  const now = Date.now()
  if (now > cached.expiresAt) {
    responseDataCache.delete(key)
    return null
  }

  console.log(`✅ RESPONSE DATA CACHE HIT: ${key.split(':')[1]}`)
  return cached
}

/**
 * Cache response data
 */
function setCachedResponseData(
  key: string,
  data: any,
  status: number,
  headers: Record<string, string>
): void {
  const now = Date.now()
  responseDataCache.set(key, {
    data,
    status,
    headers,
    timestamp: now,
    expiresAt: now + RESPONSE_DATA_CACHE_TTL,
  })

  console.log(
    `🔄 RESPONSE DATA CACHED: ${key.split(':')[1]} (TTL: ${RESPONSE_DATA_CACHE_TTL / 1000}s)`
  )

  // Cleanup expired entries periodically
  if (responseDataCache.size % 20 === 0) {
    cleanupExpiredResponseData()
  }
}

/**
 * Clean up expired response data cache entries
 */
function cleanupExpiredResponseData(): void {
  const now = Date.now()
  let cleanedCount = 0

  for (const [key, cached] of responseDataCache.entries()) {
    if (now > cached.expiresAt) {
      responseDataCache.delete(key)
      cleanedCount++
    }
  }

  if (cleanedCount > 0) {
    console.log(`🧹 RESPONSE DATA CACHE CLEANUP: Removed ${cleanedCount} expired entries`)
  }
}

/**
 * 🚀 Data-Level Request Deduplication Wrapper
 * FIXED: Uses data-level deduplication to avoid ReadableStream issues
 */
export function withSimpleRequestDeduplication(
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const requestKey = generateRequestKey(req)

    // Check cache first for GET requests
    if (req.method === 'GET') {
      const cachedData = getCachedResponseData(requestKey)
      if (cachedData) {
        // Create new response from cached data (no stream issues)
        return NextResponse.json(cachedData.data, {
          status: cachedData.status,
          headers: cachedData.headers,
        })
      }
    }

    // Check if the same request is already in progress
    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      console.log(`🔄 REQUEST DEDUPLICATION: Waiting for ${req.method} ${req.url}`)

      // Wait for the data, then create new response (no stream sharing)
      const responseData = await existingRequest
      return NextResponse.json(responseData.data, {
        status: responseData.status,
        headers: responseData.headers,
      })
    }

    // Start new request and store the promise that returns data (not NextResponse)
    const requestPromise = (async () => {
      const response = await handler(req)

      // Extract data from response
      const data = await response.json()
      const status = response.status
      const headers: Record<string, string> = {}

      // Convert Headers to plain object
      response.headers.forEach((value, key) => {
        headers[key] = value
      })

      const responseData = { data, status, headers }

      // Cache successful GET responses
      if (req.method === 'GET' && status >= 200 && status < 300) {
        setCachedResponseData(requestKey, data, status, headers)
      }

      return responseData
    })()

    pendingRequests.set(requestKey, requestPromise)

    try {
      const responseData = await requestPromise
      console.log(`✅ REQUEST COMPLETED: ${req.method} ${req.url}`)

      // Create new response from data
      return NextResponse.json(responseData.data, {
        status: responseData.status,
        headers: responseData.headers,
      })
    } finally {
      // Always clean up the pending request
      pendingRequests.delete(requestKey)
    }
  }
}

/**
 * Get current deduplication statistics
 */
export function getDeduplicationStats() {
  return {
    pendingRequests: pendingRequests.size,
    cachedResponses: responseDataCache.size,
  }
}

/**
 * Clear all pending requests (for testing/debugging)
 */
export function clearPendingRequests() {
  pendingRequests.clear()
  responseDataCache.clear()
  console.log('🧹 CLEARED: All pending requests and cached data')
}
