/**
 * 🎯 SERVER CONFIGURATION
 *
 * Centralized server-side configuration for the hybrid authentication system.
 * This file provides type-safe access to environment variables and server settings.
 */

export const serverConfig = {
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key-change-in-production',
    accessTokenExpiry: 30 * 60, // 30 minutes
    refreshTokenExpiry: 7 * 24 * 60 * 60, // 7 days
    sessionExpiry: 24 * 60 * 60, // 24 hours
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD,
    maxRetries: 3,
    retryDelayOnFailover: 100,
  },
  database: {
    url: process.env.DATABASE_URL || '',
  },
  app: {
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000', 10),
    baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  },
  security: {
    cookieSecure: process.env.NODE_ENV === 'production',
    cookieSameSite: 'lax' as const,
    maxLoginAttempts: 5,
    loginLockoutDuration: 15 * 60, // 15 minutes
  },
} as const

// Type-safe environment validation
export function validateServerConfig() {
  const requiredEnvVars = ['JWT_SECRET', 'DATABASE_URL', 'REDIS_URL']

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])

  if (missingVars.length > 0) {
    console.warn('⚠️ Missing environment variables:', missingVars.join(', '))

    // Only throw error in production runtime, not during build
    if (
      process.env.NODE_ENV === 'production' &&
      typeof window === 'undefined' &&
      !process.env.NEXT_PHASE
    ) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`)
    }
  }

  return serverConfig
}

// Export validated config
export default validateServerConfig()
