/**
 * Performance Configuration
 *
 * Centralized configuration for performance optimization
 * following clean code and best practice principles.
 */

export interface PerformanceConfig {
  // Polling intervals (in milliseconds)
  polling: {
    attendance: number
    session: number
    profile: number
  }

  // Cache settings
  cache: {
    enableDebugLogging: boolean
    enableVersioning: boolean
    maxConcurrentRequests: number
  }

  // Request throttling
  throttling: {
    maxRequestsPerMinute: number
    duplicateRequestWindow: number
  }

  // Logging levels
  logging: {
    enableDebug: boolean
    enablePerformanceMetrics: boolean
    enableCacheMetrics: boolean
  }
}

/**
 * Development configuration - Optimized for debugging with reasonable performance
 */
const developmentConfig: PerformanceConfig = {
  polling: {
    attendance: 60000, // 1 minute - reduced from 30s for better performance
    session: 600000, // 10 minutes - optimized for performance and stability
    profile: 300000, // 5 minutes
  },
  cache: {
    enableDebugLogging: true,
    enableVersioning: true,
    maxConcurrentRequests: 5,
  },
  throttling: {
    maxRequestsPerMinute: 60,
    duplicateRequestWindow: 1000, // 1 second
  },
  logging: {
    enableDebug: true,
    enablePerformanceMetrics: true,
    enableCacheMetrics: true,
  },
}

/**
 * Production configuration - Optimized for performance, minimal logging
 */
const productionConfig: PerformanceConfig = {
  polling: {
    attendance: 60000, // 1 minute - reduced frequency
    session: 300000, // 5 minutes
    profile: 600000, // 10 minutes
  },
  cache: {
    enableDebugLogging: false,
    enableVersioning: true,
    maxConcurrentRequests: 3,
  },
  throttling: {
    maxRequestsPerMinute: 30,
    duplicateRequestWindow: 2000, // 2 seconds
  },
  logging: {
    enableDebug: false,
    enablePerformanceMetrics: false,
    enableCacheMetrics: false,
  },
}

/**
 * Test configuration - Fast intervals for testing, minimal logging
 */
const testConfig: PerformanceConfig = {
  polling: {
    attendance: 5000, // 5 seconds
    session: 10000, // 10 seconds
    profile: 30000, // 30 seconds
  },
  cache: {
    enableDebugLogging: false,
    enableVersioning: true,
    maxConcurrentRequests: 10,
  },
  throttling: {
    maxRequestsPerMinute: 120,
    duplicateRequestWindow: 500, // 0.5 seconds
  },
  logging: {
    enableDebug: false,
    enablePerformanceMetrics: true,
    enableCacheMetrics: false,
  },
}

/**
 * Get performance configuration based on environment
 */
export function getPerformanceConfig(): PerformanceConfig {
  const env = process.env.NODE_ENV || 'development'

  switch (env) {
    case 'production':
      return productionConfig
    case 'test':
      return testConfig
    case 'development':
    default:
      return developmentConfig
  }
}

/**
 * Performance utilities
 */
export class PerformanceUtils {
  private static config = getPerformanceConfig()

  /**
   * Check if debug logging is enabled
   */
  static isDebugEnabled(): boolean {
    return this.config.logging.enableDebug
  }

  /**
   * Check if performance metrics are enabled
   */
  static isPerformanceMetricsEnabled(): boolean {
    return this.config.logging.enablePerformanceMetrics
  }

  /**
   * Check if cache metrics are enabled
   */
  static isCacheMetricsEnabled(): boolean {
    return this.config.logging.enableCacheMetrics
  }

  /**
   * Get polling interval for specific feature
   */
  static getPollingInterval(feature: keyof PerformanceConfig['polling']): number {
    return this.config.polling[feature]
  }

  /**
   * Get max concurrent requests
   */
  static getMaxConcurrentRequests(): number {
    return this.config.cache.maxConcurrentRequests
  }

  /**
   * Get throttling configuration
   */
  static getThrottlingConfig() {
    return this.config.throttling
  }

  /**
   * Conditional logging for performance
   */
  static log(message: string, type: 'debug' | 'performance' | 'cache' = 'debug') {
    switch (type) {
      case 'debug':
        if (this.isDebugEnabled()) {
          console.log(message)
        }
        break
      case 'performance':
        if (this.isPerformanceMetricsEnabled()) {
          console.log(message)
        }
        break
      case 'cache':
        if (this.isCacheMetricsEnabled()) {
          console.log(message)
        }
        break
    }
  }

  /**
   * Performance timing utility
   */
  static time<T>(label: string, fn: () => T): T {
    if (!this.isPerformanceMetricsEnabled()) {
      return fn()
    }

    const start = performance.now()
    const result = fn()
    const end = performance.now()

    console.log(`⏱️ PERFORMANCE: ${label} took ${(end - start).toFixed(2)}ms`)

    return result
  }

  /**
   * Async performance timing utility
   */
  static async timeAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    if (!this.isPerformanceMetricsEnabled()) {
      return await fn()
    }

    const start = performance.now()
    const result = await fn()
    const end = performance.now()

    console.log(`⏱️ PERFORMANCE: ${label} took ${(end - start).toFixed(2)}ms`)

    return result
  }
}

export default getPerformanceConfig
