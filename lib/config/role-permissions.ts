import {
  AttendanceType,
  PRAYER_ATTENDANCE_TYPES,
  SCHOOL_ATTENDANCE_TYPES,
  ALL_ATTENDANCE_TYPES,
} from '@/lib/domain/entities/absence'

// Core role configuration based on actual database implementation
export type UserRole = 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist'

// ✅ SECURITY: Single source of truth for admin roles
export const ADMIN_ROLES: UserRole[] = ['admin', 'super_admin', 'teacher', 'receptionist']
export const STUDENT_ROLES: UserRole[] = ['student']

export interface RoleConfig {
  allowedPages: string[] // Page patterns this role can access
  redirectTo: string // Default redirect for this role
  attendanceTypes: AttendanceType[] | ['qr-display'] | ['all'] // Which attendance types they can handle
  navigation: {
    label: string
    path: string
    icon?: string
  }[]
}

export const ROLE_CONFIG: Record<UserRole, RoleConfig> = {
  student: {
    allowedPages: ['/student/home', '/student/profile'],
    redirectTo: '/student/home',
    attendanceTypes: ['qr-display'], // Can only display QR code
    navigation: [
      { label: 'Home', path: '/student/home', icon: 'home' },
      { label: 'Profil', path: '/student/profile', icon: 'user' }, // Indonesian label
    ],
  },

  admin: {
    // FIXED: Admin can access prayer reports only
    allowedPages: ['/admin/home', '/admin/prayer-reports', '/admin/profile'],
    redirectTo: '/admin/home',
    attendanceTypes: [...PRAYER_ATTENDANCE_TYPES], // Use centralized prayer types
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' },
      { label: 'Laporan Shalat', path: '/admin/prayer-reports', icon: 'file-text' }, // Prayer Reports only
      { label: 'Profil', path: '/admin/profile', icon: 'user' }, // Indonesian label
    ],
  },

  super_admin: {
    allowedPages: ['/admin/*'], // Full system access - wildcard should match all admin pages
    redirectTo: '/admin/home',
    attendanceTypes: [...ALL_ATTENDANCE_TYPES], // Use centralized all types
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' },
      { label: 'Laporan Shalat', path: '/admin/prayer-reports', icon: 'file-text' }, // Prayer Reports
      { label: 'Laporan Sekolah', path: '/admin/school-reports', icon: 'graduation-cap' }, // School Reports
      { label: 'Kelas', path: '/admin/classes', icon: 'users' }, // Indonesian label
      { label: 'Siswa', path: '/admin/users', icon: 'user' }, // Indonesian label
      { label: 'Admin', path: '/admin/admins', icon: 'shield' }, // Indonesian label
      { label: 'Sesi', path: '/admin/sessions', icon: 'clock' }, // Indonesian label
      { label: 'Pengecualian Shalat', path: '/admin/prayer-exemptions', icon: 'calendar-x' }, // Prayer Exemptions
      { label: 'Profil', path: '/admin/profile', icon: 'user' }, // Indonesian label
    ],
  },

  teacher: {
    allowedPages: ['/admin/home', '/admin/school-reports', '/admin/profile'],
    redirectTo: '/admin/home',
    attendanceTypes: [AttendanceType.ENTRY], // Teacher only handles entry
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' },
      { label: 'Laporan Sekolah', path: '/admin/school-reports', icon: 'graduation-cap' }, // School Reports only
      { label: 'Profil', path: '/admin/profile', icon: 'user' },
    ],
  },

  receptionist: {
    allowedPages: ['/admin/home', '/admin/school-reports', '/admin/profile'],
    redirectTo: '/admin/home',
    attendanceTypes: [
      // Use specific types for receptionist (not using SCHOOL_ATTENDANCE_TYPES because teacher handles ENTRY)
      AttendanceType.LATE_ENTRY,
      AttendanceType.EXCUSED_ABSENCE,
      AttendanceType.TEMPORARY_LEAVE,
      AttendanceType.RETURN_FROM_LEAVE,
      AttendanceType.SICK,
      AttendanceType.IJIN, // ✅ Added: For "ijin kedua shalat" in temporary leave workflow
      // Note: IJIN_ZUHR and IJIN_ASR are NOT included here
      // They are only used internally during temporary leave workflow
      // Receptionist cannot directly scan for these types
    ],
    navigation: [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' },
      { label: 'Laporan Sekolah', path: '/admin/school-reports', icon: 'graduation-cap' }, // School Reports only
      { label: 'Profil', path: '/admin/profile', icon: 'user' },
    ],
  },
}

// SECURE helper functions with proper validation
export function canAccessPage(role: UserRole, page: string): boolean {
  if (!role || !page) return false

  const config = ROLE_CONFIG[role]
  if (!config) return false

  return config.allowedPages.some(pattern => {
    if (pattern.startsWith('!')) {
      // Exclusion pattern (e.g., '!/admin/users/*')
      const excludePattern = pattern.slice(1)
      return !matchesPattern(page, excludePattern)
    }
    return matchesPattern(page, pattern)
  })
}

export function getNavigationItems(role: UserRole) {
  const config = ROLE_CONFIG[role]
  return config ? config.navigation : []
}

export function canHandleAttendanceType(role: UserRole, type: AttendanceType | string): boolean {
  if (!role || !type) return false

  const config = ROLE_CONFIG[role]
  if (!config) return false

  // Special case for students - they can only display QR
  if (role === 'student') {
    return type === 'qr-display'
  }

  // Special case for super_admin - they can handle all types
  if (role === 'super_admin') {
    return true
  }

  // For admin role, check against specific attendance types
  // Use type-safe approach to handle the union type
  const attendanceTypes = config.attendanceTypes as (AttendanceType | string)[]

  // Check if 'all' is in the array (for super_admin case, but handled above)
  if (attendanceTypes.includes('all')) {
    return true
  }

  // Check if the specific type is in the array
  return attendanceTypes.includes(type)
}

export function getDefaultRedirect(role: UserRole): string {
  const config = ROLE_CONFIG[role]
  return config ? config.redirectTo : '/'
}

/**
 * CENTRALIZED: Get allowed attendance types for a role from the central config
 * This is the SINGLE SOURCE OF TRUTH for attendance type permissions
 */
export function getAllowedAttendanceTypes(role: UserRole): AttendanceType[] | string[] {
  if (!role) return []

  const config = ROLE_CONFIG[role]
  if (!config) return []

  // Handle special cases
  if (role === 'student') {
    return ['qr-display']
  }

  // If attendanceTypes includes 'all', return all available attendance types using centralized constant
  const attendanceTypes = config.attendanceTypes as (AttendanceType | string)[]
  if (attendanceTypes.includes('all')) {
    return [...ALL_ATTENDANCE_TYPES]
  }

  // Return the specific attendance types from config
  return config.attendanceTypes as AttendanceType[]
}

/**
 * SECURE: Get allowed attendance types specifically for manual entry
 * Manual entry is for cases where students cannot be physically present
 * Business rule: Only absence types (izin/sakit) should be available for manual entry
 */
export function getManualEntryAttendanceTypes(role: UserRole): AttendanceType[] | string[] {
  if (!role) return []

  const config = ROLE_CONFIG[role]
  if (!config) return []

  // Handle special cases
  if (role === 'student') {
    return ['qr-display']
  }

  // For receptionist role, limit manual entry to only absence types
  // Business logic: Manual entry should only be used when students cannot be physically present
  if (role === 'receptionist') {
    return [
      AttendanceType.EXCUSED_ABSENCE, // Izin - student absent with permission
      AttendanceType.SICK, // Sakit - student sick and absent
    ]
  }

  // For other roles, return the same as regular attendance types
  return getAllowedAttendanceTypes(role)
}

/**
 * SSOT: Check if user can manage prayer exemptions
 * Only super_admin can manage prayer exemptions
 */
export function canManagePrayerExemptions(role: UserRole): boolean {
  return role === 'super_admin'
}

/**
 * CENTRALIZED: Check if a role has access to manual entry functionality
 * Based on the principle: roles with school attendance types get manual entry
 */
export function hasManualEntryAccess(role: UserRole): boolean {
  if (!role) return false

  // Super admin gets manual entry access
  if (role === 'super_admin') return true

  // Receptionist gets manual entry access (they handle school attendance)
  if (role === 'receptionist') return true

  // Other roles don't get manual entry for now
  // This can be easily configured by changing this logic
  return false
}

// NOTE: Attendance type functions moved to lib/domain/entities/absence.ts
// to follow Single Source of Truth principle and avoid duplication

// SECURE pattern matching with input validation
function matchesPattern(path: string, pattern: string): boolean {
  if (!path || !pattern) return false

  if (pattern.endsWith('/*')) {
    const prefix = pattern.slice(0, -2)
    return path.startsWith(prefix)
  }
  return path === pattern
}
