/**
 * Application configuration
 *
 * This file centralizes all configuration values from environment variables.
 * It ensures that required environment variables are present and provides
 * type-safe access to them throughout the application.
 */

// Environment
export const NODE_ENV = process.env.NODE_ENV || 'development'
export const IS_DEVELOPMENT = NODE_ENV === 'development'
export const IS_PRODUCTION = NODE_ENV === 'production'
export const IS_TEST = NODE_ENV === 'test'

// Domain configuration removed - application works on any domain without configuration

// School Configuration
export const SCHOOL_NAME = process.env.SCHOOL_NAME || 'SMK Negeri 3 Banjarmasin'
export const SCHOOL_ADDRESS =
  process.env.SCHOOL_ADDRESS || 'Jl. A. Yani Km. 6, Banjarmasin, Kalimantan Selatan'
export const SCHOOL_WEBSITE = process.env.SCHOOL_WEBSITE || 'https://smkn3banjarmasin.sch.id/'

// Timezone Configuration - SINGLE SOURCE OF TRUTH
export const TIMEZONE_CONFIG = {
  // Primary timezone for the application
  TIMEZONE: process.env.APP_TIMEZONE || 'Asia/Makassar', // WITA (UTC+8)

  // Locale for formatting
  LOCALE: process.env.APP_LOCALE || 'id-ID', // Indonesian

  // Alternative timezones for future flexibility
  TIMEZONES: {
    WITA: 'Asia/Makassar', // UTC+8 - Banjarmasin, Kalimantan
    WIB: 'Asia/Jakarta', // UTC+7 - Jakarta, Java
    WIT: 'Asia/Jayapura', // UTC+9 - Papua
  },

  // Timezone display names
  TIMEZONE_NAMES: {
    'Asia/Makassar': 'WITA',
    'Asia/Jakarta': 'WIB',
    'Asia/Jayapura': 'WIT',
  },
} as const

// N8N Webhook
export const N8N_WHATSAPP_WEBHOOK_URL = process.env.N8N_WHATSAPP_WEBHOOK_URL

// Database
export const DATABASE_URL =
  process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/shalat_yuk'
if (!DATABASE_URL && IS_PRODUCTION) {
  console.error('WARNING: DATABASE_URL environment variable is missing in production')
}

// Redis
export const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379'
if (!REDIS_URL && IS_PRODUCTION) {
  console.error('WARNING: REDIS_URL environment variable is missing in production')
}

// Authentication
export const JWT_SECRET = process.env.JWT_SECRET || 'development-secret-key-replace-in-production'
if (!JWT_SECRET && IS_PRODUCTION) {
  console.error('WARNING: JWT_SECRET environment variable is missing in production')
}

// Server-side configuration object
export const serverConfig = {
  database: {
    url: DATABASE_URL,
  },
  redis: {
    url: REDIS_URL,
  },
  auth: {
    jwtSecret: JWT_SECRET,
  },
  environment: {
    nodeEnv: NODE_ENV,
    isDevelopment: IS_DEVELOPMENT,
    isProduction: IS_PRODUCTION,
    isTest: IS_TEST,
  },
  webhooks: {
    n8nWhatsapp: N8N_WHATSAPP_WEBHOOK_URL,
  },
  school: {
    name: SCHOOL_NAME,
    address: SCHOOL_ADDRESS,
    website: SCHOOL_WEBSITE,
  },
  timezone: TIMEZONE_CONFIG,
}

// Client-side safe configuration object (no secrets)
export const clientConfig = {
  environment: {
    nodeEnv: NODE_ENV,
    isDevelopment: IS_DEVELOPMENT,
    isProduction: IS_PRODUCTION,
    isTest: IS_TEST,
  },
  school: {
    name: SCHOOL_NAME,
    address: SCHOOL_ADDRESS,
    website: SCHOOL_WEBSITE,
  },
  timezone: TIMEZONE_CONFIG,
}
