/**
 * Client Matrix Export Repository - Browser Compatible
 * Clean Architecture - Infrastructure Layer
 *
 * Fetches real attendance data via API calls (browser-compatible)
 * Replaces direct database access with API calls for client-side usage
 */

import { format } from 'date-fns'

/**
 * Detailed attendance record with all necessary data for matrix export
 * Re-exported from the real repository for consistency
 */
export interface DetailedAttendanceRecord {
  uniqueCode: string
  nis: string
  name: string
  className: string
  gender: string
  attendanceDate: string // YYYY-MM-DD format

  // Prayer attendance with times
  zuhrTime: string | null
  asrTime: string | null
  ijinTime: string | null
  ijinReason: string | null

  // School attendance with times
  entryTime: string | null
  lateEntryTime: string | null
  sickTime: string | null
  sickReason: string | null
  excusedAbsenceTime: string | null
  excusedAbsenceReason: string | null
  temporaryLeaveTime: string | null
  temporaryLeaveReason: string | null
  returnFromLeaveTime: string | null
  dismissalTime: string | null
}

/**
 * API response interface
 */
interface MatrixExportApiResponse {
  success: boolean
  data: DetailedAttendanceRecord[]
  meta: {
    startDate: string
    endDate: string
    classFilter: string[] | null
    recordCount: number
  }
}

/**
 * Client matrix export repository - browser compatible
 * Fetches data via API calls instead of direct database access
 */
export class ClientMatrixExportRepository {
  constructor() {
    // No database dependencies - browser compatible
  }

  /**
   * Get detailed attendance data for matrix export
   * Fetches real data via API call
   */
  async getDetailedAttendanceForMatrix(
    startDate: Date,
    endDate: Date,
    classFilter?: string[]
  ): Promise<DetailedAttendanceRecord[]> {
    console.log('🚀 CLIENT MATRIX EXPORT: Starting API request for', {
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      classFilter: classFilter?.length || 'all',
    })

    const startTime = Date.now()

    try {
      // Prepare API request parameters
      const params = new URLSearchParams({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })

      if (classFilter && classFilter.length > 0) {
        params.append('classFilter', JSON.stringify(classFilter))
      }

      // Make API request
      const response = await fetch(`/api/reports/matrix-export?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))

        // Handle specific error cases
        if (response.status === 400 && errorData.error === 'Date range too large') {
          throw new Error(
            `Date range terlalu besar: ${errorData.details}\n\n` +
              `Maksimal ${errorData.maxDays} hari, diminta ${errorData.requestedDays} hari.\n` +
              `Silakan gunakan rentang tanggal yang lebih kecil (maksimal 3 bulan).`
          )
        }

        throw new Error(
          `API request failed: ${response.status} - ${errorData.error || 'Unknown error'}`
        )
      }

      const result: MatrixExportApiResponse = await response.json()

      if (!result.success) {
        throw new Error('API returned unsuccessful response')
      }

      const processingTime = Date.now() - startTime
      console.log(
        `🚀 CLIENT MATRIX EXPORT: Completed in ${processingTime}ms for ${result.data.length} records`
      )

      return result.data
    } catch (error) {
      console.error('🚨 CLIENT MATRIX EXPORT: Error fetching data:', error)
      throw new Error(
        `Failed to fetch attendance data: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Alternative method using POST for complex requests
   */
  async getDetailedAttendanceForMatrixPost(
    startDate: Date,
    endDate: Date,
    classFilter?: string[]
  ): Promise<DetailedAttendanceRecord[]> {
    console.log('🚀 CLIENT MATRIX EXPORT (POST): Starting API request for', {
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      classFilter: classFilter?.length || 'all',
    })

    const startTime = Date.now()

    try {
      // Make POST API request
      const response = await fetch('/api/reports/matrix-export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          classFilter: classFilter || undefined,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(
          `API request failed: ${response.status} - ${errorData.error || 'Unknown error'}`
        )
      }

      const result: MatrixExportApiResponse = await response.json()

      if (!result.success) {
        throw new Error('API returned unsuccessful response')
      }

      const processingTime = Date.now() - startTime
      console.log(
        `🚀 CLIENT MATRIX EXPORT (POST): Completed in ${processingTime}ms for ${result.data.length} records`
      )

      return result.data
    } catch (error) {
      console.error('🚨 CLIENT MATRIX EXPORT (POST): Error fetching data:', error)
      throw new Error(
        `Failed to fetch attendance data: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Get unique classes for filtering
   * This could be enhanced to fetch from API if needed
   */
  async getAvailableClasses(): Promise<string[]> {
    try {
      // For now, return empty array - this could be enhanced to fetch from API
      // or we could add another API endpoint for this
      console.log('📋 CLIENT MATRIX EXPORT: getAvailableClasses not implemented via API yet')
      return []
    } catch (error) {
      console.error('Error fetching available classes:', error)
      return []
    }
  }
}

// Export the interface for compatibility
export { DetailedAttendanceRecord }

// Create a default instance for easy import
export const clientMatrixExportRepository = new ClientMatrixExportRepository()

// For backward compatibility, also export as the old name
export { ClientMatrixExportRepository as RealMatrixExportRepository }
