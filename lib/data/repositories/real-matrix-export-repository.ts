/**
 * Real Matrix Export Repository - Production Implementation
 * Clean Architecture - Infrastructure Layer
 *
 * Fetches real attendance data from database for matrix export functionality
 * Replaces mock implementation with actual database queries
 */

import { format, eachDayOfInterval } from 'date-fns'
import { db } from '@/lib/data/drizzle/db'
import * as schema from '@/lib/data/drizzle/schema'
import { and, gte, lte, inArray, eq } from 'drizzle-orm'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { formatTimeWITA } from '@/lib/utils/date'

/**
 * Detailed attendance record with all necessary data for matrix export
 */
export interface DetailedAttendanceRecord {
  uniqueCode: string
  nis: string
  name: string
  className: string
  gender: string
  attendanceDate: string // YYYY-MM-DD format

  // Prayer attendance with times
  zuhrTime: string | null
  asrTime: string | null
  ijinTime: string | null
  ijinReason: string | null

  // Prayer permissions (specific)
  ijinZuhrTime: string | null
  ijinAsrTime: string | null

  // School attendance with times
  entryTime: string | null
  lateEntryTime: string | null
  sickTime: string | null
  sickReason: string | null
  excusedAbsenceTime: string | null
  excusedAbsenceReason: string | null
  temporaryLeaveTime: string | null // DEPRECATED: Use temporaryLeaveTimes array
  temporaryLeaveReason: string | null // DEPRECATED: Use temporaryLeaveReasons array
  returnFromLeaveTime: string | null // DEPRECATED: Use returnFromLeaveTimes array
  // NEW: Support multiple instances
  temporaryLeaveTimes: string[]
  temporaryLeaveReasons: string[]
  returnFromLeaveTimes: string[]
  dismissalTime: string | null
}

/**
 * Real matrix export repository - fetches data from database
 */
export class RealMatrixExportRepository {
  constructor() {
    // Database connection handled by imported db
  }

  /**
   * Get detailed attendance data for matrix export
   * Fetches real data from database
   */
  async getDetailedAttendanceForMatrix(
    startDate: Date,
    endDate: Date,
    classFilter?: string[]
  ): Promise<DetailedAttendanceRecord[]> {
    console.log('🚀 REAL MATRIX EXPORT: Starting processing for', {
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      classFilter: classFilter?.length || 'all',
    })

    const startTime = Date.now()

    try {
      // Fetch real data from database
      const result = await this.fetchRealAttendanceData(startDate, endDate, classFilter)

      const processingTime = Date.now() - startTime
      console.log(
        `🚀 REAL MATRIX EXPORT: Completed in ${processingTime}ms for ${result.length} records`
      )

      return result
    } catch (error) {
      console.error('🚨 REAL MATRIX EXPORT: Error fetching data:', error)
      throw new Error(
        `Failed to fetch attendance data: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Fetch real attendance data from database
   */
  private async fetchRealAttendanceData(
    startDate: Date,
    endDate: Date,
    classFilter?: string[]
  ): Promise<DetailedAttendanceRecord[]> {
    // FIXED: Use proper WITA timezone conversion for database query
    // WITA is UTC+8, so we need to create UTC dates that represent WITA day boundaries

    // Create start of day in WITA (00:00:00 WITA = 16:00:00 previous day UTC)
    const utcStartDate = new Date(
      Date.UTC(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() - 1, 16, 0, 0, 0)
    )

    // Create end of day in WITA (23:59:59 WITA = 15:59:59 current day UTC)
    const utcEndDate = new Date(
      Date.UTC(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), 15, 59, 59, 999)
    )

    console.log('🔍 Fetching attendance data from database:', {
      startDate: utcStartDate.toISOString(),
      endDate: utcEndDate.toISOString(),
      classFilter: classFilter || 'all',
    })

    // Step 1: Get all students with their class information
    // Build where conditions
    const whereConditions = [eq(schema.users.role, 'student')]

    // Apply class filter if specified
    if (classFilter && classFilter.length > 0) {
      console.log('🔍 Applying class filter:', classFilter)
      whereConditions.push(inArray(schema.classes.name, classFilter))
    }

    const studentsQuery = db
      .select({
        uniqueCode: schema.users.uniqueCode,
        nis: schema.users.nis,
        name: schema.users.name,
        gender: schema.users.gender,
        className: schema.classes.name,
      })
      .from(schema.users)
      .innerJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .where(and(...whereConditions))

    const students = await studentsQuery

    console.log(`📊 Found ${students.length} students`)
    if (students.length > 0) {
      console.log(
        '📊 Sample students:',
        students.slice(0, 3).map(s => ({
          nis: s.nis,
          name: s.name,
          className: s.className,
        }))
      )

      // Debug: Look for specific student HAIYUN HAFIZA
      const haiyunStudent = students.find(s => s.name?.includes('HAIYUN'))
      if (haiyunStudent) {
        console.log('🔍 Found HAIYUN HAFIZA:', {
          uniqueCode: haiyunStudent.uniqueCode,
          nis: haiyunStudent.nis,
          name: haiyunStudent.name,
          className: haiyunStudent.className,
        })
      } else {
        console.log('⚠️ HAIYUN HAFIZA not found in students list')
      }
    } else {
      console.log('⚠️ No students found! Checking class filter and database...')

      // Debug: Check what classes exist in database
      const allClasses = await db.select({ name: schema.classes.name }).from(schema.classes)
      console.log(
        '📋 Available classes in database:',
        allClasses.map(c => c.name)
      )

      // Debug: Check if there are any students at all
      const totalStudents = await db
        .select({ count: schema.users.id })
        .from(schema.users)
        .where(eq(schema.users.role, 'student'))
      console.log('👥 Total students in database:', totalStudents.length)
    }

    if (students.length === 0) {
      return []
    }

    // Step 2: Get all attendance records for these students in the date range
    const studentUniqueCodes = students
      .map(s => s.uniqueCode)
      .filter((code): code is string => code !== null)

    const attendanceRecords = await db
      .select({
        uniqueCode: schema.absences.uniqueCode,
        type: schema.absences.type,
        recordedAt: schema.absences.recordedAt,
        reason: schema.absences.reason,
      })
      .from(schema.absences)
      .where(
        and(
          inArray(schema.absences.uniqueCode, studentUniqueCodes),
          gte(schema.absences.recordedAt, utcStartDate),
          lte(schema.absences.recordedAt, utcEndDate)
        )
      )

    console.log(`📊 Found ${attendanceRecords.length} attendance records`)

    // Debug: Log sample attendance records for troubleshooting
    if (attendanceRecords.length > 0) {
      console.log('📋 Sample attendance records:')
      attendanceRecords.slice(0, 5).forEach(record => {
        const witaTime = formatTimeWITA(new Date(record.recordedAt))
        const witaDate = new Date(record.recordedAt).toLocaleDateString('id-ID', {
          timeZone: 'Asia/Makassar',
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        console.log(`  - ${record.uniqueCode}: ${record.type} at ${witaDate} ${witaTime} WITA`)
      })

      // Debug: Look for HAIYUN HAFIZA's attendance records
      const haiyunStudent = students.find(s => s.name?.includes('HAIYUN'))
      if (haiyunStudent) {
        const haiyunRecords = attendanceRecords.filter(
          r => r.uniqueCode === haiyunStudent.uniqueCode
        )
        console.log(`🔍 HAIYUN HAFIZA attendance records (${haiyunRecords.length} found):`)
        haiyunRecords.forEach(record => {
          const witaTime = formatTimeWITA(new Date(record.recordedAt))
          const witaDate = new Date(record.recordedAt).toLocaleDateString('id-ID', {
            timeZone: 'Asia/Makassar',
            day: '2-digit',
            month: 'short',
            year: 'numeric',
          })
          console.log(`  - ${record.type} at ${witaDate} ${witaTime} WITA`)
        })
      }
    }

    // Step 3: Generate date range for the export
    const dateRange = eachDayOfInterval({ start: startDate, end: endDate })

    console.log('📅 REAL MATRIX EXPORT: Date range details:', {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      dateRangeLength: dateRange.length,
      firstDate: dateRange[0]?.toISOString(),
      lastDate: dateRange[dateRange.length - 1]?.toISOString(),
      allDates: dateRange.map(d => format(d, 'yyyy-MM-dd')).slice(0, 10), // Show first 10 dates
    })

    // Step 4: Process data into DetailedAttendanceRecord format
    const result: DetailedAttendanceRecord[] = []

    // Group attendance records by student and date
    const attendanceByStudentAndDate = new Map<string, Map<string, typeof attendanceRecords>>()

    for (const record of attendanceRecords) {
      const dateKey = format(new Date(record.recordedAt), 'yyyy-MM-dd')
      const studentKey = record.uniqueCode

      if (!attendanceByStudentAndDate.has(studentKey)) {
        attendanceByStudentAndDate.set(studentKey, new Map())
      }

      if (!attendanceByStudentAndDate.get(studentKey)!.has(dateKey)) {
        attendanceByStudentAndDate.get(studentKey)!.set(dateKey, [])
      }

      attendanceByStudentAndDate.get(studentKey)!.get(dateKey)!.push(record)
    }

    // Generate detailed records for each student and date
    for (const student of students) {
      // Skip students without uniqueCode
      if (!student.uniqueCode) continue

      for (const date of dateRange) {
        const dateKey = format(date, 'yyyy-MM-dd')
        const studentAttendance =
          attendanceByStudentAndDate.get(student.uniqueCode)?.get(dateKey) || []

        // Initialize the detailed record
        const detailRecord: DetailedAttendanceRecord = {
          uniqueCode: student.uniqueCode,
          nis: student.nis || '',
          name: student.name || 'Unknown',
          className: student.className || 'Unknown',
          gender: student.gender === 'male' ? 'L' : student.gender === 'female' ? 'P' : '',
          attendanceDate: dateKey,

          // Initialize all times as null
          zuhrTime: null,
          asrTime: null,
          ijinTime: null,
          ijinReason: null,
          ijinZuhrTime: null,
          ijinAsrTime: null,
          entryTime: null,
          lateEntryTime: null,
          sickTime: null,
          sickReason: null,
          excusedAbsenceTime: null,
          excusedAbsenceReason: null,
          temporaryLeaveTime: null, // DEPRECATED
          temporaryLeaveReason: null, // DEPRECATED
          returnFromLeaveTime: null, // DEPRECATED
          dismissalTime: null,
          // NEW: Initialize arrays for multiple instances
          temporaryLeaveTimes: [],
          temporaryLeaveReasons: [],
          returnFromLeaveTimes: [],
        }

        // Process each attendance record for this student on this date
        for (const record of studentAttendance) {
          const timeString = formatTimeWITA(new Date(record.recordedAt))

          switch (record.type) {
            case AttendanceType.ZUHR:
              detailRecord.zuhrTime = timeString
              break
            case AttendanceType.ASR:
              detailRecord.asrTime = timeString
              break
            case AttendanceType.IJIN:
              detailRecord.ijinTime = timeString
              detailRecord.ijinReason = record.reason || null
              break
            case AttendanceType.IJIN_ZUHR:
              detailRecord.ijinZuhrTime = timeString
              break
            case AttendanceType.IJIN_ASR:
              detailRecord.ijinAsrTime = timeString
              break
            case AttendanceType.DISMISSAL:
              detailRecord.dismissalTime = timeString
              break
            case AttendanceType.ENTRY:
              detailRecord.entryTime = timeString
              break
            case AttendanceType.LATE_ENTRY:
              detailRecord.lateEntryTime = timeString
              break
            case AttendanceType.SICK:
              detailRecord.sickTime = timeString
              detailRecord.sickReason = record.reason || null
              break
            case AttendanceType.EXCUSED_ABSENCE:
              detailRecord.excusedAbsenceTime = timeString
              detailRecord.excusedAbsenceReason = record.reason || null
              break
            case AttendanceType.TEMPORARY_LEAVE:
              // FIXED: Support multiple temporary leaves
              detailRecord.temporaryLeaveTime = timeString // Keep for backward compatibility
              detailRecord.temporaryLeaveReason = record.reason || null // Keep for backward compatibility
              detailRecord.temporaryLeaveTimes.push(timeString)
              detailRecord.temporaryLeaveReasons.push(record.reason || '')
              break
            case AttendanceType.RETURN_FROM_LEAVE:
              // FIXED: Support multiple returns
              detailRecord.returnFromLeaveTime = timeString // Keep for backward compatibility
              detailRecord.returnFromLeaveTimes.push(timeString)
              break
          }
        }

        result.push(detailRecord)
      }
    }

    console.log('📊 REAL MATRIX EXPORT: Final result summary:', {
      totalRecords: result.length,
      studentsCount: students.length,
      dateRangeLength: dateRange.length,
      expectedRecords: students.length * dateRange.length,
      sampleDates: result.slice(0, 5).map(r => r.attendanceDate),
      uniqueDates: [...new Set(result.map(r => r.attendanceDate))].sort().slice(0, 10),
    })

    return result
  }

  // formatTimeWITA function removed - now using SSOT from lib/utils/date.ts

  /**
   * Get unique classes for filtering
   */
  async getAvailableClasses(): Promise<string[]> {
    try {
      const classes = await db
        .select({ name: schema.classes.name })
        .from(schema.classes)
        .orderBy(schema.classes.name)

      return classes.map(c => c.name)
    } catch (error) {
      console.error('Error fetching available classes:', error)
      return []
    }
  }
}

// Create a default instance for easy import
export const realMatrixExportRepository = new RealMatrixExportRepository()
