/**
 * Prayer Exemption Repository - Clean Architecture Data Layer
 * Handles all database operations for prayer exemptions
 */

import { db } from '@/lib/data/drizzle/db'
import * as schema from '@/lib/data/drizzle/schema'
import { eq, and, gte, lte, desc, count, or, isNull, like } from 'drizzle-orm'
import {
  PrayerExemption,
  CreatePrayerExemptionDTO,
  UpdatePrayerExemptionDTO,
  PrayerExemptionType,
  PrayerType,
} from '@/lib/domain/entities/prayer-exemption'
import { getCurrentWITATime, formatDateToString } from '@/lib/utils/date'

/**
 * Prayer Exemption Repository Implementation
 */
export class PrayerExemptionRepository {
  /**
   * Create a new prayer exemption - supports both one-time and recurring
   */
  async create(data: CreatePrayerExemptionDTO): Promise<PrayerExemption> {
    // Prepare values for database insertion
    const values: any = {
      exemptionType: data.exemptionType,
      targetId: null, // Always null for global exemptions
      recurrencePattern: data.recurrencePattern,
      prayerType: data.prayerType,
      reason: data.reason,
      createdBy: data.createdBy,
      createdAt: getCurrentWITATime(),
      isActive: true,
    }

    // Handle one-time vs recurring exemptions
    // FIXED: Use proper date formatting to avoid timezone conversion issues
    if (data.recurrencePattern === 'once') {
      values.exemptionDate = data.exemptionDate ? formatDateToString(data.exemptionDate) : null
      values.startDate = null
      values.endDate = null
      values.daysOfWeek = null
    } else {
      values.exemptionDate = null
      values.startDate = data.startDate ? formatDateToString(data.startDate) : null
      values.endDate = data.endDate ? formatDateToString(data.endDate) : null
      values.daysOfWeek = data.daysOfWeek ? JSON.stringify(data.daysOfWeek) : null
    }

    const [exemption] = await db.insert(schema.prayerExemptions).values(values).returning()

    return this.mapToPrayerExemption(exemption)
  }

  /**
   * Find exemption by ID
   */
  async findById(id: number): Promise<PrayerExemption | null> {
    const [exemption] = await db
      .select()
      .from(schema.prayerExemptions)
      .where(eq(schema.prayerExemptions.id, id))
      .limit(1)

    if (!exemption) {
      return null
    }

    return this.mapToPrayerExemption(exemption)
  }

  /**
   * Get all exemptions with pagination and filtering
   */
  async findAll(
    page: number = 1,
    limit: number = 50,
    filters?: {
      exemptionType?: PrayerExemptionType
      dateFrom?: Date
      dateTo?: Date
      isActive?: boolean
    }
  ): Promise<{ exemptions: PrayerExemption[]; total: number }> {
    const offset = (page - 1) * limit
    const conditions = []

    // Apply filters
    if (filters?.exemptionType) {
      conditions.push(eq(schema.prayerExemptions.exemptionType, filters.exemptionType))
    }

    if (filters?.dateFrom) {
      const dateStr = filters.dateFrom.toISOString().split('T')[0]
      conditions.push(gte(schema.prayerExemptions.exemptionDate, dateStr))
    }

    if (filters?.dateTo) {
      const dateStr = filters.dateTo.toISOString().split('T')[0]
      conditions.push(lte(schema.prayerExemptions.exemptionDate, dateStr))
    }

    if (filters?.isActive !== undefined) {
      conditions.push(eq(schema.prayerExemptions.isActive, filters.isActive))
    }

    // Get exemptions with pagination
    const exemptions = await db
      .select()
      .from(schema.prayerExemptions)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(schema.prayerExemptions.createdAt))
      .limit(limit)
      .offset(offset)

    // Get total count for pagination
    const countResult = await db
      .select({ count: count() })
      .from(schema.prayerExemptions)
      .where(conditions.length > 0 ? and(...conditions) : undefined)

    const totalCount = countResult[0]?.count || 0

    return {
      exemptions: exemptions.map(this.mapToPrayerExemption),
      total: totalCount,
    }
  }

  /**
   * Update exemption
   */
  async update(id: number, data: UpdatePrayerExemptionDTO): Promise<PrayerExemption | null> {
    const [exemption] = await db
      .update(schema.prayerExemptions)
      .set({
        reason: data.reason,
        isActive: data.isActive,
      })
      .where(eq(schema.prayerExemptions.id, id))
      .returning()

    if (!exemption) {
      return null
    }

    return this.mapToPrayerExemption(exemption)
  }

  /**
   * Soft delete exemption (set isActive to false)
   */
  async softDelete(id: number): Promise<boolean> {
    const [exemption] = await db
      .update(schema.prayerExemptions)
      .set({ isActive: false })
      .where(eq(schema.prayerExemptions.id, id))
      .returning()

    return !!exemption
  }

  /**
   * Hard delete exemption (permanent removal)
   */
  async hardDelete(id: number): Promise<boolean> {
    const [exemption] = await db
      .delete(schema.prayerExemptions)
      .where(eq(schema.prayerExemptions.id, id))
      .returning()

    return !!exemption
  }

  /**
   * Find active exemptions for a specific date
   */
  async findActiveExemptionsForDate(date: Date): Promise<PrayerExemption[]> {
    // FIXED: Use proper WITA timezone conversion for date string
    // The date parameter is already in WITA time from getCurrentWITATime()
    // We need to format it properly to avoid timezone conversion issues
    const dateStr = formatDateToString(date)

    const dayOfWeek = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ][date.getDay()]

    // DEBUG: Log input parameters
    console.log('🔍 DEBUG findActiveExemptionsForDate:', {
      inputDate: date.toISOString(),
      dateStr,
      dayOfWeek,
      dayIndex: date.getDay(),
      dateComponents: {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate(),
      },
    })

    const exemptions = await db
      .select()
      .from(schema.prayerExemptions)
      .where(
        and(
          eq(schema.prayerExemptions.isActive, true),
          or(
            // One-time exemptions for this date
            and(
              eq(schema.prayerExemptions.recurrencePattern, 'once'),
              eq(schema.prayerExemptions.exemptionDate, dateStr)
            ),
            // Weekly recurring exemptions that include this day
            and(
              eq(schema.prayerExemptions.recurrencePattern, 'weekly'),
              lte(schema.prayerExemptions.startDate, dateStr),
              or(
                isNull(schema.prayerExemptions.endDate),
                gte(schema.prayerExemptions.endDate, dateStr)
              ),
              like(schema.prayerExemptions.daysOfWeek, `%"${dayOfWeek}"%`)
            )
          )
        )
      )

    // DEBUG: Log query results
    console.log('🔍 DEBUG exemptions found:', {
      count: exemptions.length,
      exemptions: exemptions.map(e => ({
        id: e.id,
        recurrencePattern: e.recurrencePattern,
        daysOfWeek: e.daysOfWeek,
        prayerType: e.prayerType,
        startDate: e.startDate,
        endDate: e.endDate,
        isActive: e.isActive,
      })),
    })

    return exemptions.map(this.mapToPrayerExemption)
  }

  /**
   * Map database record to domain entity - supports recurring fields
   */
  private mapToPrayerExemption(record: any): PrayerExemption {
    return {
      id: record.id,
      exemptionType: record.exemptionType as PrayerExemptionType,
      targetId: record.targetId,
      exemptionDate: record.exemptionDate ? new Date(record.exemptionDate) : null,
      recurrencePattern: record.recurrencePattern,
      startDate: record.startDate ? new Date(record.startDate) : null,
      endDate: record.endDate ? new Date(record.endDate) : null,
      daysOfWeek: record.daysOfWeek ? JSON.parse(record.daysOfWeek) : null,
      prayerType: record.prayerType as PrayerType,
      reason: record.reason,
      createdBy: record.createdBy,
      createdAt: new Date(record.createdAt),
      isActive: record.isActive,
    }
  }
}
