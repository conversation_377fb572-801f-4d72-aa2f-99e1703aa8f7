import { eq, or, and } from 'drizzle-orm'
import { db, schema } from '../drizzle/db'
import { Admin } from '../../domain/entities/admin'
import { ADMIN_ROLES } from '../../config/role-permissions'
import bcrypt from 'bcryptjs'
import { RedisCache } from '../cache/redis'

/**
 * Admin repository implementation
 */
export class AdminRepository {
  private cache: RedisCache

  constructor(cache: RedisCache) {
    this.cache = cache
  }

  /**
   * ✅ SECURITY HELPER: Create role filter for admin queries
   */
  private getAdminRoleFilter() {
    return or(...ADMIN_ROLES.map(role => eq(schema.users.role, role)))
  }
  /**
   * Find all admins
   * ✅ SECURITY: Only return users with admin roles
   */
  async findAll(): Promise<Admin[]> {
    const admins = await db.select().from(schema.users).where(this.getAdminRoleFilter())

    return admins.map(this.mapToAdmin)
  }

  /**
   * Find an admin by ID
   * ✅ SECURITY FIX: Only return users with admin roles
   */
  async findById(id: number): Promise<Admin | null> {
    const [admin] = await db
      .select()
      .from(schema.users)
      .where(and(eq(schema.users.id, id), this.getAdminRoleFilter()))
      .limit(1)

    if (!admin) {
      return null
    }

    return this.mapToAdmin(admin)
  }

  /**
   * Find an admin by username
   * ✅ SECURITY FIX: Only return users with admin roles
   */
  async findByUsername(username: string): Promise<Admin | null> {
    const [admin] = await db
      .select()
      .from(schema.users)
      .where(and(eq(schema.users.username, username), this.getAdminRoleFilter()))
      .limit(1)

    if (!admin) {
      return null
    }

    return this.mapToAdmin(admin)
  }

  /**
   * Create a new admin
   */
  async create(adminData: Omit<Admin, 'id' | 'createdAt' | 'updatedAt'>): Promise<Admin> {
    const [admin] = await db
      .insert(schema.users)
      .values({
        role: adminData.role,
        uniqueCode: adminData.uniqueCode,
        username: adminData.username,
        name: adminData.name,
        passwordHash: adminData.passwordHash,
        createdAt: new Date(),
        classId: undefined,
      })
      .returning()

    return this.mapToAdmin(admin)
  }

  /**
   * Update an admin
   */
  async update(
    id: number,
    data: Partial<Omit<Admin, 'id' | 'createdAt' | 'updatedAt'> & { passwordHash?: string }>
  ): Promise<Admin> {
    const updateData: any = {
      updatedAt: new Date(),
    }

    if (data.name) {
      updateData.name = data.name
    }

    if (data.role) {
      updateData.role = data.role
    }

    if (data.passwordHash) {
      updateData.passwordHash = data.passwordHash
    }

    console.log(`Updating admin ${id} with data:`, updateData)

    const [admin] = await db
      .update(schema.users)
      .set(updateData)
      .where(eq(schema.users.id, id))
      .returning()

    if (!admin) {
      throw new Error(`Admin with ID ${id} not found`)
    }

    return this.mapToAdmin(admin)
  }

  /**
   * Delete an admin
   */
  async delete(id: number): Promise<void> {
    await db.delete(schema.users).where(eq(schema.users.id, id))
  }

  /**
   * Verify an admin's password
   */
  async verifyPassword(admin: Admin, password: string): Promise<boolean> {
    return bcrypt.compare(password, admin.passwordHash)
  }

  /**
   * Map a database user to an Admin entity
   */
  private mapToAdmin(user: any): Admin {
    return {
      id: user.id,
      uniqueCode: user.uniqueCode,
      username: user.username,
      name: user.name,
      passwordHash: user.passwordHash,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  }
}
