import { createClient } from 'redis'
import { serverConfig, IS_DEVELOPMENT } from '@/lib/config'

/**
 * Redis cache service implementation
 */
export class RedisCache {
  private client: ReturnType<typeof createClient> | null = null
  private connected: boolean = false
  private inMemoryCache: Map<string, { value: string; expiry: number }> = new Map()
  private connectionAttempted: boolean = false

  constructor() {
    // We'll initialize the client lazily when needed
    // This allows the application to start even if Redis is not available
  }

  /**
   * Initialize the Redis client if needed
   */
  private initializeClient(): void {
    // Skip if already initialized or if we've already attempted to connect
    if (this.client || this.connectionAttempted) {
      return
    }

    this.connectionAttempted = true

    // Get Redis URL from centralized configuration
    // For development, we're using SSH tunneling to connect to Redis
    // The tunnel script (scripts/dev-tunnel.sh) sets up the connection and updates .env.local
    const redisUrl = serverConfig.redis.url

    if (!redisUrl) {
      if (IS_DEVELOPMENT) {
        console.warn(
          'REDIS_URL is not set. Using in-memory cache. Please run the tunnel script: npm run tunnel'
        )
        this.connected = false
        return
      } else {
        throw new Error('REDIS_URL environment variable is required in production')
      }
    }

    // 🚀 OPTIMIZED REDIS CLIENT: Enhanced configuration for production performance
    this.client = createClient({
      url: redisUrl,
      socket: {
        // Improved reconnection strategy with faster initial reconnect
        reconnectStrategy: retries => {
          if (retries < 3) {
            // Fast reconnect for first 3 attempts (100ms, 200ms, 400ms)
            return Math.pow(2, retries) * 100
          }
          // Exponential backoff with maximum delay of 5 seconds for subsequent attempts
          const delay = Math.min(Math.pow(2, retries - 3) * 1000, 5000)
          return delay
        },
        // Connection timeout optimization
        connectTimeout: 10000, // 10 seconds
        // Keep alive to maintain connection
        keepAlive: 30000, // 30 seconds
        // Disable Nagle's algorithm for better performance
        noDelay: true,
      },
      // 🚀 PERFORMANCE OPTIMIZATIONS
      database: 0, // Use default database
      // Connection pool settings for better concurrency
      isolationPoolOptions: {
        min: 2, // Minimum connections
        max: 10, // Maximum connections for high load
      },
      // Command timeout
      commandsQueueMaxLength: 1000, // Allow more queued commands
    })

    // Set up event handlers
    this.client.on('error', err => {
      console.error('Redis Client Error:', err)
      this.connected = false
    })

    this.client.on('connect', () => {
      this.connected = true
    })

    this.client.on('reconnecting', () => {})

    // Connect to Redis
    this.connect()
  }

  /**
   * Connect to Redis
   */
  private async connect(): Promise<void> {
    if (!this.client || this.connected) {
      return
    }

    try {
      await this.client.connect()
    } catch (error) {
      console.error('Failed to connect to Redis:', error)
      // Fallback to in-memory cache if Redis connection fails
      this.connected = false
    }
  }

  /**
   * Ensure Redis is connected
   */
  private async ensureConnected(): Promise<boolean> {
    // Initialize client if needed
    this.initializeClient()

    // If we're using in-memory cache, return false
    if (!this.client) {
      return false
    }

    // If already connected, return true
    if (this.connected) {
      return true
    }

    // Try to connect
    try {
      if (!this.client.isOpen) {
        await this.client.connect()
      }
      this.connected = true
      return true
    } catch (error) {
      console.error('Failed to connect to Redis:', error)
      this.connected = false
      return false
    }
  }

  /**
   * Get a value from the cache
   */
  async get(key: string): Promise<string | null> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis GET: ${key}`)
        return await this.client.get(key)
      } else {
        console.log(`In-memory GET: ${key} (Redis not available)`)
        // Fallback to in-memory cache
        const item = this.inMemoryCache.get(key)
        if (!item) {
          return null
        }

        // Check if the item has expired
        if (item.expiry < Date.now()) {
          this.inMemoryCache.delete(key)
          return null
        }

        return item.value
      }
    } catch (error) {
      console.error(`Redis GET error for key ${key}:`, error)

      // Fallback to in-memory cache on error
      console.log(`Fallback to in-memory GET after Redis error: ${key}`)
      const item = this.inMemoryCache.get(key)
      if (!item || item.expiry < Date.now()) {
        return null
      }
      return item.value
    }
  }

  /**
   * 🚀 BATCH GET: Get multiple values in one operation for better performance
   */
  async mget(...keys: string[]): Promise<(string | null)[]> {
    if (keys.length === 0) return []

    try {
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis MGET: ${keys.length} keys`)
        return await this.client.mGet(keys)
      } else {
        console.log(`In-memory MGET: ${keys.length} keys (Redis not available)`)
        // Fallback to in-memory cache
        return keys.map(key => {
          const item = this.inMemoryCache.get(key)
          if (!item) return null

          // Check if the item has expired
          if (item.expiry < Date.now()) {
            this.inMemoryCache.delete(key)
            return null
          }

          return item.value
        })
      }
    } catch (error) {
      console.error(`Redis MGET error for ${keys.length} keys:`, error)
      // Fallback to individual gets
      return Promise.all(keys.map(key => this.get(key)))
    }
  }

  /**
   * Set a value in the cache with TTL (legacy method - use setWithoutTTL for write-through strategy)
   */
  async set(key: string, value: string, ttlSeconds: number): Promise<void> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis SET: ${key} (TTL: ${ttlSeconds}s)`)
        await this.client.set(key, value, { EX: ttlSeconds })
      } else {
        console.log(`In-memory SET: ${key} (TTL: ${ttlSeconds}s, Redis not available)`)
        // Fallback to in-memory cache
        const expiry = Date.now() + ttlSeconds * 1000
        this.inMemoryCache.set(key, { value, expiry })
      }
    } catch (error) {
      console.error(`Redis SET error for key ${key}:`, error)
      // Fallback to in-memory cache on error
      console.log(`Fallback to in-memory SET after Redis error: ${key}`)
      const expiry = Date.now() + ttlSeconds * 1000
      this.inMemoryCache.set(key, { value, expiry })
    }
  }

  /**
   * Set a value in the cache WITHOUT TTL (for write-through strategy)
   * Cache persists until explicitly deleted
   */
  async setWithoutTTL(key: string, value: string): Promise<void> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis SET (NO TTL): ${key} - persists until deleted`)
        await this.client.set(key, value)
      } else {
        console.log(`In-memory SET (NO TTL): ${key} - persists until deleted`)
        // Fallback to in-memory cache without expiration
        this.inMemoryCache.set(key, { value, expiry: Infinity })
      }
    } catch (error) {
      console.error(`Redis SET (NO TTL) error for key ${key}:`, error)
      // Fallback to in-memory cache on error
      console.log(`Fallback to in-memory SET (NO TTL) after Redis error: ${key}`)
      this.inMemoryCache.set(key, { value, expiry: Infinity })
    }
  }

  /**
   * Delete a value from the cache
   */
  async del(key: string): Promise<void> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis DEL: ${key}`)
        await this.client.del(key)
      } else {
        console.log(`In-memory DEL: ${key} (Redis not available)`)
        // Fallback to in-memory cache
        this.inMemoryCache.delete(key)
      }
    } catch (error) {
      console.error(`Redis DEL error for key ${key}:`, error)
      // Fallback to in-memory cache
      this.inMemoryCache.delete(key)
    }
  }

  /**
   * 🚀 BATCH DELETE: Delete multiple keys in one operation for better performance
   */
  async batchDel(keys: string[]): Promise<void> {
    if (keys.length === 0) return

    try {
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis BATCH DEL: ${keys.length} keys`)
        // Use pipeline for batch operations
        const pipeline = this.client.multi()
        keys.forEach(key => pipeline.del(key))
        await pipeline.exec()
      } else {
        console.log(`In-memory BATCH DEL: ${keys.length} keys (Redis not available)`)
        keys.forEach(key => this.inMemoryCache.delete(key))
      }
    } catch (error) {
      console.error(`Redis BATCH DEL error for ${keys.length} keys:`, error)
      // Fallback to in-memory cache
      keys.forEach(key => this.inMemoryCache.delete(key))
    }
  }

  /**
   * 🚀 BATCH SET: Set multiple key-value pairs in one operation
   */
  async batchSet(operations: Array<{ key: string; value: string; ttl?: number }>): Promise<void> {
    if (operations.length === 0) return

    try {
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis BATCH SET: ${operations.length} operations`)
        const pipeline = this.client.multi()

        operations.forEach(({ key, value, ttl }) => {
          if (ttl) {
            pipeline.set(key, value, { EX: ttl })
          } else {
            pipeline.set(key, value)
          }
        })

        await pipeline.exec()
      } else {
        console.log(`In-memory BATCH SET: ${operations.length} operations (Redis not available)`)
        operations.forEach(({ key, value, ttl }) => {
          const expiry = ttl ? Date.now() + ttl * 1000 : Number.MAX_SAFE_INTEGER
          this.inMemoryCache.set(key, { value, expiry })
        })
      }
    } catch (error) {
      console.error(`Redis BATCH SET error for ${operations.length} operations:`, error)
      // Fallback to in-memory cache
      operations.forEach(({ key, value, ttl }) => {
        const expiry = ttl ? Date.now() + ttl * 1000 : Number.MAX_SAFE_INTEGER
        this.inMemoryCache.set(key, { value, expiry })
      })
    }
  }

  /**
   * Get TTL (time to live) for a key in seconds
   */
  async ttl(key: string): Promise<number> {
    try {
      // Try to ensure Redis is connected
      const isConnected = await this.ensureConnected()

      if (this.client && isConnected) {
        console.log(`Redis TTL: ${key}`)
        return await this.client.ttl(key)
      } else {
        console.log(`In-memory TTL: ${key} (Redis not available)`)
        // Fallback to in-memory cache
        const item = this.inMemoryCache.get(key)
        if (!item) {
          return -2 // Key doesn't exist
        }

        const remainingTime = Math.max(0, Math.floor((item.expiry - Date.now()) / 1000))
        return remainingTime > 0 ? remainingTime : -1 // -1 means expired
      }
    } catch (error) {
      console.error(`Redis TTL error for key ${key}:`, error)
      return -1 // Return -1 on error (no expiry)
    }
  }
}

// Singleton instance
let redisCache: RedisCache | null = null

/**
 * Get the Redis cache instance
 */
export function getRedisCache(): RedisCache {
  if (!redisCache) {
    redisCache = new RedisCache()
  }
  return redisCache
}
