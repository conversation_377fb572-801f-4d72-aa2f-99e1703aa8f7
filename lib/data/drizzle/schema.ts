import {
  pgTable,
  unique,
  pgEnum,
  serial,
  integer,
  varchar,
  timestamp,
  index,
  date,
  boolean,
} from 'drizzle-orm/pg-core'

/**
 * User role enum
 */
export const userRoleEnum = pgEnum('user_role', [
  'student',
  'admin',
  'super_admin',
  'teacher',
  'receptionist',
])

/**
 * Attendance type enum
 */
export const attendanceTypeEnum = pgEnum('attendance_type', [
  'Zuhr',
  'Asr',
  'Pulang',
  'Ijin',
  'Ijin Zuhr',
  'Ijin Asr',
  'Entry',
  'Late Entry',
  'Sick',
  'Excused Absence',
  'Temporary Leave',
  'Return from Leave',
])

/**
 * Gender enum
 */
export const genderEnum = pgEnum('gender_type', ['male', 'female'])

/**
 * Prayer exemption type enum - SSOT for exemption types
 */
export const prayerExemptionTypeEnum = pgEnum('prayer_exemption_type', ['global'])

/**
 * Prayer type enum - SSOT for prayer types that can be exempted
 */
export const prayerTypeEnum = pgEnum('prayer_type', ['zuhr', 'asr', 'both'])

/**
 * Recurrence pattern enum - SSOT for recurring exemption patterns
 */
export const recurrencePatternEnum = pgEnum('recurrence_pattern', [
  'once', // One-time exemption
  'weekly', // Weekly recurring (specific days of week)
  'custom', // Custom pattern
])

/**
 * Classes table
 */
export const classes = pgTable(
  'classes',
  {
    id: serial('id').primaryKey(),
    name: varchar('name', { length: 10 }).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  table => {
    return {
      classes_name_unique: unique('classes_name_unique').on(table.name),
    }
  }
)

/**
 * Users table
 */
export const users = pgTable(
  'users',
  {
    id: serial('id').primaryKey(),
    role: userRoleEnum('role').notNull(),
    uniqueCode: varchar('unique_code', { length: 36 }),
    googleEmail: varchar('google_email', { length: 255 }),
    nis: varchar('nis', { length: 10 }),
    username: varchar('username', { length: 50 }),
    name: varchar('name', { length: 100 }).notNull(),
    whatsapp: varchar('whatsapp', { length: 15 }),
    classId: integer('class_id').references(() => classes.id),
    passwordHash: varchar('password_hash', { length: 255 }),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at'),
    gender: genderEnum('gender'),
  },
  table => {
    return {
      idx_users_role: index('idx_users_role').on(table.role),
      idx_users_class_id: index('idx_users_class_id').on(table.classId),
      idx_users_unique_code: index('idx_users_unique_code').on(table.uniqueCode),
      users_unique_code_unique: unique('users_unique_code_unique').on(table.uniqueCode),
      users_google_email_unique: unique('users_google_email_unique').on(table.googleEmail),
      users_username_unique: unique('users_username_unique').on(table.username),
    }
  }
)

/**
 * Absences table
 */
export const absences = pgTable(
  'absences',
  {
    id: serial('id').primaryKey(),
    uniqueCode: varchar('unique_code', { length: 36 })
      .notNull()
      .references(() => users.uniqueCode),
    type: attendanceTypeEnum('type').notNull(),
    recordedAt: timestamp('recorded_at').notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    reason: varchar('reason', { length: 500 }), // Reason for attendance (required for certain types)

    // ✅ HISTORICAL CLASS TRACKING: Store class context at time of attendance
    // These fields preserve the student's class when attendance was recorded
    // Prevents data loss when students change classes (grade progression)
    classId: integer('class_id').references(() => classes.id, { onDelete: 'set null' }), // Foreign key to classes table
    className: varchar('class_name', { length: 10 }), // Denormalized class name for performance
  },
  table => {
    return {
      // Existing index
      idx_absences_unique_code_recorded_at: index('idx_absences_unique_code_recorded_at').on(
        table.uniqueCode,
        table.recordedAt
      ),
      // NEW: Composite index for type-based filtering (prayer vs school reports)
      idx_absences_type_recorded_at: index('idx_absences_type_recorded_at').on(
        table.type,
        table.recordedAt
      ),
      // NEW: Composite index for student-specific queries with type filtering
      idx_absences_unique_code_type_recorded_at: index(
        'idx_absences_unique_code_type_recorded_at'
      ).on(table.uniqueCode, table.type, table.recordedAt),
      // NEW: Index for date-based queries (for daily/weekly/monthly reports)
      idx_absences_recorded_at_type: index('idx_absences_recorded_at_type').on(
        table.recordedAt,
        table.type
      ),

      // ✅ HISTORICAL CLASS TRACKING INDEXES: Performance optimization for class-based queries
      // Index for class-based filtering (reports by class)
      idx_absences_class_id: index('idx_absences_class_id').on(table.classId),
      // Index for class name filtering (direct class name queries)
      idx_absences_class_name: index('idx_absences_class_name').on(table.className),
      // Composite index for class + date queries (monthly/yearly reports by class)
      idx_absences_class_recorded_at: index('idx_absences_class_recorded_at').on(
        table.classId,
        table.recordedAt
      ),
      // Composite index for class name + type queries (prayer/school reports by class)
      idx_absences_class_name_type: index('idx_absences_class_name_type').on(
        table.className,
        table.type
      ),
    }
  }
)

/**
 * SQL to create the attendance_summary materialized view
 *
 * Note: This is raw SQL because Drizzle ORM doesn't directly support materialized views.
 * This SQL needs to be executed separately after the tables are created.
 */
export const createAttendanceSummaryViewSQL = `
CREATE MATERIALIZED VIEW IF NOT EXISTS attendance_summary AS
WITH pivoted AS (
  SELECT
    DATE(recorded_at) AS summary_date,
    unique_code,
    BOOL_OR(CASE WHEN type = 'Zuhr' THEN TRUE ELSE FALSE END) AS zuhr,
    BOOL_OR(CASE WHEN type = 'Asr' THEN TRUE ELSE FALSE END) AS asr,
    BOOL_OR(CASE WHEN type = 'Pulang' THEN TRUE ELSE FALSE END) AS pulang,
    BOOL_OR(CASE WHEN type = 'Ijin' THEN TRUE ELSE FALSE END) AS ijin,
    MAX(recorded_at) AS last_updated
  FROM absences
  GROUP BY DATE(recorded_at), unique_code
)
SELECT
  p.summary_date,
  u.unique_code,
  u.name,
  c.name AS class_name,
  p.zuhr,
  p.asr,
  p.pulang,
  p.ijin,
  p.last_updated AS updated_at
FROM pivoted p
JOIN users u ON p.unique_code = u.unique_code
JOIN classes c ON u.class_id = c.id
WITH NO DATA;

CREATE INDEX IF NOT EXISTS idx_attendance_summary_date ON attendance_summary(summary_date);
`

/**
 * Prayer exemptions table - SSOT for prayer exemption management
 * Only super_admin can manage these exemptions
 * Supports both one-time and recurring exemptions
 */
export const prayerExemptions = pgTable(
  'prayer_exemptions',
  {
    id: serial('id').primaryKey(),
    exemptionType: prayerExemptionTypeEnum('exemption_type').notNull(),
    targetId: varchar('target_id', { length: 50 }), // class_id for 'class', NULL for 'global'

    // For one-time exemptions
    exemptionDate: date('exemption_date'), // NULL for recurring exemptions

    // For recurring exemptions
    recurrencePattern: recurrencePatternEnum('recurrence_pattern').default('once').notNull(),
    startDate: date('start_date'), // Start date for recurring exemptions
    endDate: date('end_date'), // End date for recurring exemptions (NULL = indefinite)
    daysOfWeek: varchar('days_of_week', { length: 20 }), // JSON array: ["monday", "friday"] for weekly pattern

    prayerType: prayerTypeEnum('prayer_type').notNull(),
    reason: varchar('reason', { length: 500 }).notNull(),
    createdBy: serial('created_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    isActive: boolean('is_active').default(true).notNull(),
  },
  table => {
    return {
      // Index for efficient querying by date and type
      idx_exemption_date_type: index('idx_exemption_date_type').on(
        table.exemptionDate,
        table.exemptionType
      ),
      // Index for efficient querying by target (class or global)
      idx_exemption_target: index('idx_exemption_target').on(table.targetId),
      // Index for recurring exemption queries
      idx_recurring_exemptions: index('idx_recurring_exemptions').on(
        table.recurrencePattern,
        table.startDate,
        table.endDate
      ),
      // Index for days of week queries
      idx_days_of_week: index('idx_days_of_week').on(table.daysOfWeek),
      // Unique constraint to prevent duplicate one-time exemptions
      unique_one_time_exemption: unique('unique_one_time_exemption').on(
        table.exemptionType,
        table.targetId,
        table.exemptionDate,
        table.prayerType
      ),
    }
  }
)

/**
 * SQL to refresh the attendance_summary materialized view
 */
export const refreshAttendanceSummaryViewSQL = `
REFRESH MATERIALIZED VIEW attendance_summary;
`

/**
 * SQL to fix constraint chk_role_data to allow student without google_email
 * and enforce username and password requirements
 */
export const fixConstraintSQL = `
-- Drop existing constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data;

-- Add updated constraint
ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
  (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
  (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
);

-- Create index for username-based login
CREATE INDEX IF NOT EXISTS idx_users_username_role ON users(username, role);
`
