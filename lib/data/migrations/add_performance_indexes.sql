-- Performance Optimization: Add indexes for prayer and school reports
-- This migration adds composite indexes to optimize query performance for large datasets

-- Index for type-based filtering (prayer vs school reports)
-- This will speed up queries that filter by attendance type
CREATE INDEX IF NOT EXISTS idx_absences_type_recorded_at 
ON absences(type, recorded_at);

-- Composite index for student-specific queries with type filtering
-- This optimizes queries that need to find specific student's attendance by type
CREATE INDEX IF NOT EXISTS idx_absences_unique_code_type_recorded_at 
ON absences(unique_code, type, recorded_at);

-- Index for date-based queries with type filtering
-- This optimizes daily/weekly/monthly report queries
CREATE INDEX IF NOT EXISTS idx_absences_recorded_at_type 
ON absences(recorded_at, type);

-- Partial indexes for prayer-specific queries (more efficient for prayer reports)
-- Note: These values should match PRAYER_ATTENDANCE_TYPES from lib/domain/entities/absence.ts
CREATE INDEX IF NOT EXISTS idx_absences_prayer_types
ON absences(unique_code, recorded_at)
WHERE type IN ('Zuhr', 'Asr', 'Pulang', 'Ijin');

-- Partial indexes for school-specific queries (more efficient for school reports)
-- Note: These values should match SCHOOL_ATTENDANCE_TYPES from lib/domain/entities/absence.ts
CREATE INDEX IF NOT EXISTS idx_absences_school_types
ON absences(unique_code, recorded_at)
WHERE type IN ('Entry', 'Late Entry', 'Pulang', 'Excused Absence', 'Temporary Leave', 'Return from Leave', 'Sick');

-- Index for date range queries (for aggregated reports)
CREATE INDEX IF NOT EXISTS idx_absences_date_range 
ON absences(recorded_at DESC, type, unique_code);

-- Analyze tables to update statistics for query planner
ANALYZE absences;
ANALYZE users;
ANALYZE classes;

-- Create comments for documentation
COMMENT ON INDEX idx_absences_type_recorded_at IS 'Optimizes type-based filtering for prayer vs school reports';
COMMENT ON INDEX idx_absences_unique_code_type_recorded_at IS 'Optimizes student-specific queries with type filtering';
COMMENT ON INDEX idx_absences_recorded_at_type IS 'Optimizes date-based queries for daily/weekly/monthly reports';
COMMENT ON INDEX idx_absences_prayer_types IS 'Partial index for prayer-specific attendance types';
COMMENT ON INDEX idx_absences_school_types IS 'Partial index for school-specific attendance types';
COMMENT ON INDEX idx_absences_date_range IS 'Optimizes date range queries for aggregated reports';
