/**
 * Student Attendance Types
 * 
 * This file contains TypeScript interfaces and types for student attendance data
 * following clean architecture principles and SSOT (Single Source of Truth) patterns.
 */

import { AttendanceType } from '@/lib/domain/entities/absence'

/**
 * Prayer Attendance Status Interface
 * Represents the current prayer attendance status for a student
 */
export interface PrayerAttendanceStatus {
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  pulang: boolean
  pulangTime: string | null
  ijin: boolean
  ijinTime: string | null
}

/**
 * School Attendance Status Interface
 * Represents the current school attendance status for a student
 */
export interface SchoolAttendanceStatus {
  entry: boolean
  entryTime: string | null
  lateEntry: boolean
  lateEntryTime: string | null
  excusedAbsence: boolean
  excusedAbsenceTime: string | null
  excusedAbsenceReason: string | null
  sick: boolean
  sickTime: string | null
  sickReason: string | null
  temporaryLeave: boolean
  temporaryLeaveTime: string | null
  temporaryLeaveReason: string | null
  returnFromLeave: boolean
  returnFromLeaveTime: string | null
}

/**
 * Combined Attendance Status Interface
 * Represents both prayer and school attendance status for a student
 */
export interface StudentAttendanceStatus extends PrayerAttendanceStatus, SchoolAttendanceStatus {}

/**
 * School Attendance Item Interface
 * Represents a single school attendance item for UI display
 */
export interface SchoolAttendanceItem {
  id: string
  type: AttendanceType
  label: string
  icon: string
  status: boolean
  time: string | null
  reason?: string | null
  variant: 'success' | 'warning' | 'info' | 'neutral'
  priority: number
}

/**
 * School Attendance Section Interface
 * Represents a grouped section of school attendance items
 */
export interface SchoolAttendanceSection {
  id: string
  title: string
  items: SchoolAttendanceItem[]
  priority: number
}

/**
 * School Attendance Display Configuration
 * Configuration for displaying school attendance status
 */
export interface SchoolAttendanceDisplayConfig {
  sections: SchoolAttendanceSection[]
  showReasons: boolean
  showTimeTooltips: boolean
  compactMode: boolean
}

/**
 * School Attendance State Interface
 * State management interface for school attendance
 */
export interface SchoolAttendanceState {
  data: SchoolAttendanceStatus | null
  loading: boolean
  error: string | null
  lastUpdated: Date | null
}

/**
 * School Attendance API Response Interface
 * Response format from the school attendance API
 */
export interface SchoolAttendanceApiResponse extends StudentAttendanceStatus {
  // Inherits all fields from StudentAttendanceStatus
}

/**
 * School Attendance Hooks Options Interface
 * Options for school attendance hooks
 */
export interface UseSchoolAttendanceOptions {
  uniqueCode: string
  autoRefresh?: boolean
  refreshInterval?: number
  onSuccess?: (data: SchoolAttendanceStatus) => void
  onError?: (error: Error) => void
}

/**
 * School Attendance Action Types
 * Action types for school attendance state management
 */
export enum SchoolAttendanceActionType {
  FETCH_START = 'FETCH_START',
  FETCH_SUCCESS = 'FETCH_SUCCESS',
  FETCH_ERROR = 'FETCH_ERROR',
  REFRESH = 'REFRESH',
  RESET = 'RESET',
}

/**
 * School Attendance Action Interface
 * Action interface for school attendance state management
 */
export interface SchoolAttendanceAction {
  type: SchoolAttendanceActionType
  payload?: any
  error?: string
}

/**
 * Utility type for school attendance status keys
 */
export type SchoolAttendanceStatusKey = keyof SchoolAttendanceStatus

/**
 * Utility type for school attendance boolean fields
 */
export type SchoolAttendanceBooleanFields = {
  [K in SchoolAttendanceStatusKey]: SchoolAttendanceStatus[K] extends boolean ? K : never
}[SchoolAttendanceStatusKey]

/**
 * Utility type for school attendance time fields
 */
export type SchoolAttendanceTimeFields = {
  [K in SchoolAttendanceStatusKey]: SchoolAttendanceStatus[K] extends string | null ? K : never
}[SchoolAttendanceStatusKey]

/**
 * School Attendance Validation Result
 */
export interface SchoolAttendanceValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * School Attendance Summary Interface
 * Summary statistics for school attendance
 */
export interface SchoolAttendanceSummary {
  totalItems: number
  completedItems: number
  pendingItems: number
  completionRate: number
  lastActivity: Date | null
}
