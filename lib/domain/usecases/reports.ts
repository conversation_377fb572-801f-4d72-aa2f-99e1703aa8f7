import { AttendanceSummary } from '@/lib/domain/entities/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import {
  createWITAMonthRange,
  createWITAYearRange,
  getCurrentWITATime,
  getWITADateComponents,
} from '@/lib/utils/date'
import { unifiedCacheStrategy } from '../../services/unified-cache-strategy'

/**
 * Monthly Report Data Structure
 */
export interface MonthlyReportData {
  month: number
  year: number
  weeks: WeeklyData[]
  summary: AttendanceSummary[]
  metadata: {
    totalStudents: number
    totalAttendance: number
    cacheKey: string
    generatedAt: string
  }
}

/**
 * Weekly Data for Monthly Reports
 */
export interface WeeklyData {
  weekNumber: number
  startDate: Date
  endDate: Date
  attendanceCount: number
  data: AttendanceSummary[]
}

/**
 * Yearly Report Data Structure
 */
export interface YearlyReportData {
  year: number
  months: MonthlyData[]
  summary: AttendanceSummary[]
  metadata: {
    totalStudents: number
    totalAttendance: number
    cacheKey: string
    generatedAt: string
  }
}

/**
 * Monthly Data for Yearly Reports
 */
export interface MonthlyData {
  month: number
  monthName: string
  attendanceCount: number
  data: AttendanceSummary[]
}

/**
 * Reports Use Cases - Clean Architecture Implementation
 * Handles Monthly and Yearly reports with optimized caching strategy
 */
export class ReportsUseCases {
  constructor(private absenceRepo: AbsenceRepository) {}

  /**
   * Get Monthly Report with Week-by-Week Breakdown
   * Uses same cache strategy as daily reports with hierarchical TTL
   */
  async getMonthlyReport(
    month: number,
    year: number,
    className?: string,
    reportType: 'prayer' | 'school' | 'all' = 'all',
    forceFresh: boolean = false
  ): Promise<MonthlyReportData> {
    // Generate cache key following same pattern as daily reports
    const cacheKey = `reports:monthly:${reportType}:${year}-${month.toString().padStart(2, '0')}:${className || 'all'}`

    // Check cache first (unless forceFresh is true)
    if (!forceFresh) {
      try {
        const cachedData = await unifiedCacheStrategy.get(cacheKey)
        if (cachedData) {
          console.log(`✅ MONTHLY REPORT CACHE HIT: ${cacheKey}`)
          return cachedData
        }
        console.log(`❌ MONTHLY REPORT CACHE MISS: ${cacheKey}`)
      } catch (cacheError) {
        console.error('Error reading monthly report from cache:', cacheError)
      }
    }

    console.log(`📊 Generating Monthly Report: ${month}/${year} (${reportType})`)

    // Create WITA month range
    const { startDate, endDate } = createWITAMonthRange(year, month)

    // Get aggregated data for the entire month
    const monthlyData = await this.absenceRepo.getAggregatedAttendanceSummary(
      startDate,
      endDate,
      className,
      reportType
    )

    // Break down data by weeks
    const weeks = this.generateWeeklyBreakdown(startDate, endDate, monthlyData)

    // Create report structure
    const report: MonthlyReportData = {
      month,
      year,
      weeks,
      summary: monthlyData,
      metadata: {
        totalStudents: monthlyData.length,
        totalAttendance: monthlyData.reduce((sum, student) => {
          const counts = (student as any).aggregatedCounts || {}
          return (
            sum + Object.values(counts).reduce((a: number, b: unknown) => a + (Number(b) || 0), 0)
          )
        }, 0),
        cacheKey,
        generatedAt: getCurrentWITATime().toISOString(),
      },
    }

    // 🚀 WRITE-THROUGH CACHE: Store monthly report without TTL
    try {
      await unifiedCacheStrategy.set(cacheKey, report)
      console.log(`� MONTHLY REPORT CACHED: ${cacheKey} (NO TTL - persists until invalidated)`)
    } catch (cacheError) {
      console.error('Error caching monthly report:', cacheError)
    }

    return report
  }

  /**
   * Get Yearly Report with Month-by-Month Breakdown
   * Uses same cache strategy as daily reports with hierarchical TTL
   */
  async getYearlyReport(
    year: number,
    className?: string,
    reportType: 'prayer' | 'school' | 'all' = 'all',
    forceFresh: boolean = false
  ): Promise<YearlyReportData> {
    // Generate cache key following same pattern as daily reports
    const cacheKey = `reports:yearly:${reportType}:${year}:${className || 'all'}`

    // Check cache first (unless forceFresh is true)
    if (!forceFresh) {
      try {
        const cachedData = await unifiedCacheStrategy.get(cacheKey)
        if (cachedData) {
          console.log(`✅ YEARLY REPORT CACHE HIT: ${cacheKey}`)
          return cachedData
        }
        console.log(`❌ YEARLY REPORT CACHE MISS: ${cacheKey}`)
      } catch (cacheError) {
        console.error('Error reading yearly report from cache:', cacheError)
      }
    }

    console.log(`📊 Generating Yearly Report: ${year} (${reportType})`)

    // Create WITA year range
    const { startDate, endDate } = createWITAYearRange(year)

    // Get aggregated data for the entire year
    const yearlyData = await this.absenceRepo.getAggregatedAttendanceSummary(
      startDate,
      endDate,
      className,
      reportType
    )

    console.log(`📊 Yearly Data Retrieved: ${yearlyData.length} total students`)

    // Debug: Check sample data to understand structure
    if (yearlyData.length > 0) {
      const studentsWithAttendance = yearlyData.filter(student => {
        const counts = (student as any).aggregatedCounts || {}
        return counts.zuhr > 0 || counts.asr > 0 || counts.ijin > 0 || counts.dismissal > 0
      })

      console.log(`📊 Students with actual attendance: ${studentsWithAttendance.length}`)

      if (studentsWithAttendance.length > 0) {
        const sampleStudent = studentsWithAttendance[0]
        console.log(`📊 Sample student with attendance:`, {
          uniqueCode: sampleStudent.uniqueCode,
          summaryDate: sampleStudent.summaryDate,
          updatedAt: sampleStudent.updatedAt,
          aggregatedCounts: (sampleStudent as any).aggregatedCounts,
        })
      }
    }

    // Break down data by months
    const months = await this.generateMonthlyBreakdown(year, className, reportType, yearlyData)

    // Create report structure
    const report: YearlyReportData = {
      year,
      months,
      summary: yearlyData,
      metadata: {
        totalStudents: yearlyData.length,
        totalAttendance: yearlyData.reduce((sum, student) => {
          const counts = (student as any).aggregatedCounts || {}
          return (
            sum + Object.values(counts).reduce((a: number, b: unknown) => a + (Number(b) || 0), 0)
          )
        }, 0),
        cacheKey,
        generatedAt: getCurrentWITATime().toISOString(),
      },
    }

    // 🚀 WRITE-THROUGH CACHE: Store yearly report without TTL
    try {
      await unifiedCacheStrategy.set(cacheKey, report)
      console.log(`� YEARLY REPORT CACHED: ${cacheKey} (NO TTL - persists until invalidated)`)
    } catch (cacheError) {
      console.error('Error caching yearly report:', cacheError)
    }

    return report
  }

  /**
   * Generate weekly breakdown for monthly reports
   * ✅ FIXED: Properly distribute data across weeks based on actual attendance dates
   * @private
   */
  private generateWeeklyBreakdown(
    startDate: Date,
    endDate: Date,
    monthlyData: AttendanceSummary[]
  ): WeeklyData[] {
    const weeks: WeeklyData[] = []
    const currentDate = new Date(startDate)
    let weekNumber = 1

    // Create 5 weeks for the month (standard calendar view)
    for (let i = 0; i < 5; i++) {
      const weekStart = new Date(currentDate)
      const weekEnd = new Date(currentDate)
      weekEnd.setDate(weekEnd.getDate() + 6)

      // Don't go beyond month end
      if (weekEnd > endDate) {
        weekEnd.setTime(endDate.getTime())
      }

      // ✅ FIXED: Filter data by actual attendance dates for this week
      const weekData = monthlyData.filter(student => {
        // Check if student has any attendance records in this week
        // Use summaryDate or updatedAt to determine which week the attendance belongs to
        const attendanceDate = new Date(student.summaryDate || student.updatedAt || new Date())

        // Check if attendance date falls within this week's range
        return attendanceDate >= weekStart && attendanceDate <= weekEnd
      })

      console.log(
        `📅 Week ${weekNumber} (${weekStart.toISOString().split('T')[0]} to ${weekEnd.toISOString().split('T')[0]}): ${weekData.length} students with attendance`
      )

      // Debug: Log sample data to understand the structure
      if (weekData.length > 0) {
        const sampleStudent = weekData[0]
        console.log(`📊 Sample student data:`, {
          uniqueCode: sampleStudent.uniqueCode,
          summaryDate: sampleStudent.summaryDate,
          updatedAt: sampleStudent.updatedAt,
          aggregatedCounts: (sampleStudent as any).aggregatedCounts,
        })
      }

      weeks.push({
        weekNumber,
        startDate: new Date(weekStart),
        endDate: new Date(weekEnd),
        attendanceCount: this.calculateAttendanceCount(weekData),
        data: weekData, // Only include data for this specific week
      })

      currentDate.setDate(currentDate.getDate() + 7)
      weekNumber++

      if (currentDate > endDate) break
    }

    return weeks
  }

  /**
   * Calculate actual attendance count from aggregated data
   * @private
   */
  private calculateAttendanceCount(data: AttendanceSummary[]): number {
    return data.reduce((total, student) => {
      const counts = (student as any).aggregatedCounts || {}
      return (
        total +
        Object.values(counts).reduce((sum: number, count: unknown) => sum + (Number(count) || 0), 0)
      )
    }, 0)
  }

  /**
   * Generate monthly breakdown for yearly reports
   * @private
   */
  private async generateMonthlyBreakdown(
    year: number,
    _className?: string,
    _reportType: 'prayer' | 'school' | 'all' = 'all',
    yearlyData: AttendanceSummary[] = []
  ): Promise<MonthlyData[]> {
    const months: MonthlyData[] = []
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Ags',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ]

    console.log(`📊 Starting monthly breakdown for year ${year}`)
    console.log(`📊 Total yearly data to process: ${yearlyData.length} students`)

    for (let month = 1; month <= 12; month++) {
      // ✅ FIXED: Filter data by actual attendance dates for this month
      const monthData = yearlyData.filter(student => {
        // Check if student has any attendance records in this month
        // Use summaryDate or updatedAt to determine which month the attendance belongs to
        const summaryDate = student.summaryDate || student.updatedAt

        // ✅ FIXED: Handle invalid dates more robustly
        if (!summaryDate) {
          console.warn(`📊 Student ${student.uniqueCode} has no summaryDate or updatedAt`)
          return false
        }

        const attendanceDate = new Date(summaryDate)

        // ✅ FIXED: Check for invalid date
        if (isNaN(attendanceDate.getTime())) {
          console.warn(`📊 Student ${student.uniqueCode} has invalid date: ${summaryDate}`)
          return false
        }

        // ✅ FIXED: Use WITA-aware date comparison for consistent timezone handling
        const witaComponents = getWITADateComponents(attendanceDate)
        const isCorrectYear = witaComponents.year === year
        const isCorrectMonth = witaComponents.month === month

        return isCorrectYear && isCorrectMonth
      })

      console.log(
        `📅 Month ${month} (${monthNames[month - 1]} ${year}): ${monthData.length} students with attendance`
      )

      // ✅ FIXED: Remove fallback logic that was causing flat charts
      // If no data found for current month, keep it empty for accurate chart representation
      if (monthData.length === 0) {
        console.log(
          `📊 No attendance data found for ${monthNames[month - 1]} ${year} - keeping month empty for accurate chart`
        )
      } else {
        console.log(
          `📊 Found ${monthData.length} students with attendance for ${monthNames[month - 1]} ${year}`
        )
      }

      months.push({
        month,
        monthName: monthNames[month - 1],
        attendanceCount: this.calculateAttendanceCount(monthData),
        data: monthData, // Only include data for this specific month
      })
    }

    return months
  }

  /**
   * Invalidate cache for monthly/yearly reports
   * ✅ UNIFIED: Use unified cache strategy for consistent invalidation
   */
  async invalidateReportsCache(
    reportType?: 'monthly' | 'yearly',
    year?: number,
    month?: number,
    className?: string
  ): Promise<void> {
    console.log(`� UNIFIED CACHE INVALIDATION: ${reportType || 'all'} reports`)

    try {
      const currentWITAComponents = getWITADateComponents()
      const currentYear = currentWITAComponents.year
      const currentMonth = currentWITAComponents.month

      // Invalidate current month/year with WITA timezone
      const monthKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`

      // Monthly reports cache keys
      await unifiedCacheStrategy.del(`reports:monthly:all:${monthKey}:all`)
      await unifiedCacheStrategy.del(`reports:monthly:prayer:${monthKey}:all`)
      await unifiedCacheStrategy.del(`reports:monthly:school:${monthKey}:all`)

      // Yearly reports cache keys
      await unifiedCacheStrategy.del(`reports:yearly:all:${currentYear}:all`)
      await unifiedCacheStrategy.del(`reports:yearly:prayer:${currentYear}:all`)
      await unifiedCacheStrategy.del(`reports:yearly:school:${currentYear}:all`)

      // If specific parameters provided, invalidate those too
      if (reportType === 'monthly' && year && month) {
        const specificMonthKey = `${year}-${month.toString().padStart(2, '0')}`
        await unifiedCacheStrategy.del(
          `reports:monthly:all:${specificMonthKey}:${className || 'all'}`
        )
        await unifiedCacheStrategy.del(
          `reports:monthly:prayer:${specificMonthKey}:${className || 'all'}`
        )
        await unifiedCacheStrategy.del(
          `reports:monthly:school:${specificMonthKey}:${className || 'all'}`
        )
      }

      if (reportType === 'yearly' && year) {
        await unifiedCacheStrategy.del(`reports:yearly:all:${year}:${className || 'all'}`)
        await unifiedCacheStrategy.del(`reports:yearly:prayer:${year}:${className || 'all'}`)
        await unifiedCacheStrategy.del(`reports:yearly:school:${year}:${className || 'all'}`)
      }

      console.log('✅ UNIFIED CACHE INVALIDATION: Monthly/yearly reports cache cleared')
    } catch (error) {
      console.error('❌ Unified cache invalidation failed:', error)
    }
  }
}
