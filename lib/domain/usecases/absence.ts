import { Absence, AttendanceType, AttendanceSummary, CreateAbsenceDTO } from '../entities/absence'
import { Student } from '../entities/student'
import { NotFoundError, DuplicateError } from '../errors'
import { PrayerNotCompletedError } from '../errors/PrayerNotCompletedError'
import {
  getCurrentWITATime,
  getWITADateComponents,
  formatDateToString,
  getWITADateKey,
  createWITADate,
} from '../../utils/date'
import { unifiedCacheStrategy, createCacheEvent } from '../../services/unified-cache-strategy'
import { db } from '../../data/drizzle/db'

/**
 * Interface for absence repository
 */
export interface AbsenceRepository {
  findByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<Absence | null>
  findByUniqueCodeAndDate(uniqueCode: string, date: Date): Promise<Absence[]>
  create(absence: CreateAbsenceDTO): Promise<Absence>
  update(
    id: number,
    data: { recordedAt: Date; reason?: string; classId?: number; className?: string }
  ): Promise<Absence>
  deleteByUniqueCodeAndTypeAndDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date
  ): Promise<boolean>
  deleteAllByUniqueCode(uniqueCode: string): Promise<void>
  getAttendanceSummary(
    date?: Date,
    className?: string,
    reportType?: 'prayer' | 'school' | 'all'
  ): Promise<AttendanceSummary[]>
  refreshMaterializedView(): Promise<void>
}

/**
 * Interface for student repository
 */
export interface StudentRepository {
  findByUniqueCode(uniqueCode: string): Promise<Student | null>
}

/**
 * Interface for prayer exemption use cases
 */
export interface PrayerExemptionUseCases {
  isStudentExempted(
    uniqueCode: string,
    date: Date,
    prayerType: AttendanceType.ZUHR | AttendanceType.ASR
  ): Promise<boolean>
}

/**
 * Absence use cases
 */
export class AbsenceUseCases {
  constructor(
    private absenceRepo: AbsenceRepository,
    private studentRepo: StudentRepository,
    private prayerExemptionUseCases?: PrayerExemptionUseCases
  ) {}

  /**
   * Record a new absence
   * @param uniqueCode - The unique code of the student
   * @param type - The type of attendance
   * @param force - Whether to force update an existing attendance record
   * @param reason - Optional reason for attendance (required for certain types)
   */
  async recordAbsence(
    uniqueCode: string,
    type: AttendanceType,
    force: boolean = false,
    reason?: string
  ): Promise<Absence> {
    // Check if student exists and get class information
    const student = await this.studentRepo.findByUniqueCode(uniqueCode)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // If this is a dismissal (Pulang) attendance, check if the student has completed Zuhr and Asr prayers
    if (type === AttendanceType.DISMISSAL) {
      await this.validatePrayerAttendance(uniqueCode)
    }

    // Use database transaction for atomic operations
    // This ensures consistency when auto-creating prayer ijin records
    const result = await db.transaction(async () => {
      // ✅ HISTORICAL CLASS TRACKING: Pass student class information to record attendance
      // Record the primary attendance with class context
      const primaryAttendance = await this.recordSingleAttendance(
        uniqueCode,
        type,
        force,
        reason,
        student.classId, // Pass class ID for historical tracking
        student.className // Pass class name for historical tracking
      )

      // Auto-create prayer ijin if needed for school absence types
      if (this.shouldAutoCreatePrayerIjin(type)) {
        await this.autoCreatePrayerIjin(
          uniqueCode,
          primaryAttendance.recordedAt,
          force,
          student.classId, // Pass class ID for prayer ijin records
          student.className // Pass class name for prayer ijin records
        )
      }

      return primaryAttendance
    })

    // 🚀 UNIFIED CACHE STRATEGY: Real-time cache update without TTL
    const cacheEvent = createCacheEvent(uniqueCode, type, 'create', student?.className)

    // Always log cache invalidation for debugging real-time issues
    console.log(`🔧 DEBUG: About to invalidate cache for ${uniqueCode} - ${type}`)
    console.log(`🔧 DEBUG: Cache event:`, {
      uniqueCode: cacheEvent.uniqueCode,
      attendanceType: cacheEvent.attendanceType,
      className: cacheEvent.className,
      operation: cacheEvent.operation,
      timestamp: cacheEvent.timestamp,
    })

    // Write-through: Update cache immediately and invalidate related cache
    console.log(`🚀 STARTING CACHE OPERATIONS for ${uniqueCode} - ${type}`)

    try {
      await unifiedCacheStrategy.updateCacheOnAttendance(cacheEvent)
      console.log(`✅ CACHE UPDATE COMPLETE for ${uniqueCode} - ${type}`)
    } catch (updateError) {
      console.error(`❌ CACHE UPDATE FAILED for ${uniqueCode} - ${type}:`, updateError)
    }

    try {
      await unifiedCacheStrategy.invalidateRelatedCache(cacheEvent)
      console.log(`✅ CACHE INVALIDATION COMPLETE for ${uniqueCode} - ${type}`)
    } catch (invalidateError) {
      console.error(`❌ CACHE INVALIDATION FAILED for ${uniqueCode} - ${type}:`, invalidateError)
    }

    // If auto prayer ijin was created, also invalidate prayer cache
    if (this.shouldAutoCreatePrayerIjin(type)) {
      try {
        const prayerCacheEvent = createCacheEvent(
          uniqueCode,
          AttendanceType.IJIN,
          'create',
          student.className
        )
        await unifiedCacheStrategy.updateCacheOnAttendance(prayerCacheEvent)
        await unifiedCacheStrategy.invalidateRelatedCache(prayerCacheEvent)
        console.log(
          `✅ AUTO PRAYER IJIN CACHE: Cache operations completed for ${uniqueCode} - IJIN`
        )
      } catch (prayerCacheError) {
        console.error(`❌ AUTO PRAYER IJIN CACHE FAILED for ${uniqueCode}:`, prayerCacheError)
        // Continue execution even if prayer cache invalidation fails
      }
    }

    console.log(`🚀 UNIFIED CACHE: All operations completed for ${uniqueCode} - ${type}`)

    return result
  }

  /**
   * Record absence for a date range (for sick and excused absence types)
   * @param uniqueCode - The unique code of the student
   * @param type - The type of attendance (must be SICK or EXCUSED_ABSENCE)
   * @param startDate - Start date of the range (inclusive)
   * @param endDate - End date of the range (inclusive)
   * @param force - Whether to force update existing attendance records
   * @param reason - Optional reason for attendance (required for certain types)
   * @returns Object with successful and failed records
   */
  async recordAbsenceRange(
    uniqueCode: string,
    type: AttendanceType,
    startDate: Date,
    endDate: Date,
    force: boolean = false,
    reason?: string
  ): Promise<{
    successes: Absence[]
    failures: Array<{ date: Date; error: string }>
    totalDays: number
  }> {
    // Validate that this method is only used for supported types
    if (!this.shouldAutoCreatePrayerIjin(type)) {
      throw new Error('Date range recording is only supported for SICK and EXCUSED_ABSENCE types')
    }

    // Check if student exists
    const student = await this.studentRepo.findByUniqueCode(uniqueCode)
    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Import date utilities
    const { generateDateRange, validateDateRange } = await import('@/lib/utils/date')

    // Validate date range
    const validation = validateDateRange(startDate, endDate)
    if (!validation.isValid) {
      throw new Error(validation.error || 'Invalid date range')
    }

    // Generate array of dates in the range (excluding weekends for school attendance)
    const dates = generateDateRange(startDate, endDate, true) // Skip weekends for better performance
    const successes: Absence[] = []
    const failures: Array<{ date: Date; error: string }> = []

    console.log(
      `🔄 BULK ABSENCE: Recording ${type} for ${dates.length} working days (excluding weekends) for student ${uniqueCode}`
    )

    // Process each date in the range
    for (const date of dates) {
      try {
        // Use database transaction for each date to ensure atomicity
        const result = await db.transaction(async () => {
          // ✅ HISTORICAL CLASS TRACKING: Pass student class information for date range records
          // Record the primary attendance for this specific date with class context
          const primaryAttendance = await this.recordSingleAttendanceForDate(
            uniqueCode,
            type,
            date,
            force,
            reason,
            student.classId, // Pass class ID for historical tracking
            student.className // Pass class name for historical tracking
          )

          // Auto-create prayer ijin for this specific date
          await this.autoCreatePrayerIjinForDate(
            uniqueCode,
            date,
            force,
            student.classId, // Pass class ID for prayer ijin records
            student.className // Pass class name for prayer ijin records
          )

          return primaryAttendance
        })

        successes.push(result)
        console.log(`✅ BULK ABSENCE: Successfully recorded ${type} for ${date.toDateString()}`)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        failures.push({ date, error: errorMessage })
        console.error(
          `❌ BULK ABSENCE: Failed to record ${type} for ${date.toDateString()}:`,
          errorMessage
        )
      }
    }

    // Invalidate cache for all successful records
    for (const absence of successes) {
      try {
        const cacheEvent = createCacheEvent(uniqueCode, type, 'create', student.className)
        await unifiedCacheStrategy.updateCacheOnAttendance(cacheEvent)
        await unifiedCacheStrategy.invalidateRelatedCache(cacheEvent)

        // Also invalidate prayer cache for auto-created ijin
        const prayerCacheEvent = createCacheEvent(
          uniqueCode,
          AttendanceType.IJIN,
          'create',
          student.className
        )
        await unifiedCacheStrategy.updateCacheOnAttendance(prayerCacheEvent)
        await unifiedCacheStrategy.invalidateRelatedCache(prayerCacheEvent)
      } catch (cacheError) {
        console.error(
          `❌ BULK ABSENCE CACHE: Failed to invalidate cache for ${absence.recordedAt}:`,
          cacheError
        )
        // Continue execution even if cache invalidation fails
      }
    }

    console.log(
      `🚀 BULK ABSENCE: Completed ${successes.length}/${dates.length} records for ${uniqueCode}`
    )

    return {
      successes,
      failures,
      totalDays: dates.length,
    }
  }

  /**
   * Helper method to determine if auto prayer ijin should be created
   * @param type - The attendance type
   * @private
   */
  private shouldAutoCreatePrayerIjin(type: AttendanceType): boolean {
    return type === AttendanceType.SICK || type === AttendanceType.EXCUSED_ABSENCE
  }

  /**
   * Helper method to record a single attendance record
   * @param uniqueCode - The unique code of the student
   * @param type - The type of attendance
   * @param force - Whether to force update an existing attendance record
   * @param reason - Optional reason for attendance
   * @param classId - Class ID for historical tracking (optional for backward compatibility)
   * @param className - Class name for historical tracking (optional for backward compatibility)
   * @private
   */
  private async recordSingleAttendance(
    uniqueCode: string,
    type: AttendanceType,
    force: boolean,
    reason: string | undefined,
    classId?: number,
    className?: string
  ): Promise<Absence> {
    // Special handling for temporary leave workflow
    // These types should allow multiple records per day
    const isTemporaryLeaveWorkflow =
      type === AttendanceType.TEMPORARY_LEAVE || type === AttendanceType.RETURN_FROM_LEAVE

    // For temporary leave workflow, always create new records (no duplicate checking)
    if (isTemporaryLeaveWorkflow) {
      console.log(
        `🔄 TEMPORARY LEAVE RECORD: Creating new ${type} record for ${uniqueCode} (bypassing duplicate check)`
      )

      const absence = await this.absenceRepo.create({
        uniqueCode,
        type,
        recordedAt: getCurrentWITATime(), // Current time in WITA
        reason: reason, // Include reason if provided

        // ✅ HISTORICAL CLASS TRACKING: Include class context for temporary leave records
        classId: classId, // Store class ID at time of attendance
        className: className, // Store class name at time of attendance
      })

      console.log(`✅ TEMPORARY LEAVE: Successfully created new ${type} record for ${uniqueCode}`)
      return absence
    }

    // For other attendance types, apply normal duplicate checking
    // Use WITA time for consistent date handling
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)

    // Always check the database directly for existing attendance records
    // This ensures we have the most up-to-date information and prevents race conditions
    const existingAbsence = await this.absenceRepo.findByUniqueCodeAndTypeAndDate(
      uniqueCode,
      type,
      today
    )

    // If there's an existing absence and force is false, throw an error
    if (existingAbsence && !force) {
      throw new DuplicateError('Attendance already recorded for today')
    }

    // If there's an existing absence and force is true, update the existing record
    // Otherwise, create a new record
    let absence: Absence

    if (existingAbsence && force) {
      // Update the existing record with the current WITA time
      absence = await this.absenceRepo.update(existingAbsence.id, {
        recordedAt: getCurrentWITATime(), // Current time in WITA
        reason: reason, // Update reason if provided

        // ✅ HISTORICAL CLASS TRACKING: Update class context if provided
        classId: classId, // Update class ID if provided
        className: className, // Update class name if provided
      })
    } else {
      // Create a new record
      absence = await this.absenceRepo.create({
        uniqueCode,
        type,
        recordedAt: getCurrentWITATime(), // Current time in WITA
        reason: reason, // Include reason if provided

        // ✅ HISTORICAL CLASS TRACKING: Include class context for new records
        classId: classId, // Store class ID at time of attendance
        className: className, // Store class name at time of attendance
      })
    }

    return absence
  }

  /**
   * Helper method to record a single attendance record for a specific date
   * @param uniqueCode - The unique code of the student
   * @param type - The type of attendance
   * @param date - The specific date to record attendance for
   * @param force - Whether to force update an existing attendance record
   * @param reason - Optional reason for attendance
   * @param classId - Class ID for historical tracking (optional for backward compatibility)
   * @param className - Class name for historical tracking (optional for backward compatibility)
   * @private
   */
  private async recordSingleAttendanceForDate(
    uniqueCode: string,
    type: AttendanceType,
    date: Date,
    force: boolean,
    reason: string | undefined,
    classId?: number,
    className?: string
  ): Promise<Absence> {
    // Normalize the date to start of day for consistent comparison
    const targetDate = new Date(date)
    targetDate.setHours(0, 0, 0, 0)

    // Check for existing attendance on the target date
    const existingAbsence = await this.absenceRepo.findByUniqueCodeAndTypeAndDate(
      uniqueCode,
      type,
      targetDate
    )

    // If there's an existing absence and force is false, throw an error
    if (existingAbsence && !force) {
      throw new DuplicateError(`Attendance already recorded for ${targetDate.toDateString()}`)
    }

    // If there's an existing absence and force is true, update the existing record
    // Otherwise, create a new record
    let absence: Absence

    if (existingAbsence && force) {
      // Update the existing record, keeping the original date but updating reason
      // Use SSOT WITA utilities instead of magic numbers
      const witaComponents = getWITADateComponents(targetDate)
      const witaRecordTime = createWITADate(
        witaComponents.year,
        witaComponents.month,
        witaComponents.day,
        8, // 08:00 WITA for manual entries
        0,
        0
      )

      absence = await this.absenceRepo.update(existingAbsence.id, {
        recordedAt: witaRecordTime,
        reason: reason, // Update reason if provided

        // ✅ HISTORICAL CLASS TRACKING: Update class context for date-specific records
        classId: classId, // Update class ID if provided
        className: className, // Update class name if provided
      })
    } else {
      // Create a new record for the target date
      // Use SSOT WITA utilities instead of magic numbers
      const witaComponents = getWITADateComponents(targetDate)
      const witaRecordTime = createWITADate(
        witaComponents.year,
        witaComponents.month,
        witaComponents.day,
        8, // 08:00 WITA for manual entries
        0,
        0
      )

      absence = await this.absenceRepo.create({
        uniqueCode,
        type,
        recordedAt: witaRecordTime,
        reason: reason, // Include reason if provided

        // ✅ HISTORICAL CLASS TRACKING: Include class context for date-specific records
        classId: classId, // Store class ID at time of attendance
        className: className, // Store class name at time of attendance
      })
    }

    return absence
  }

  /**
   * Helper method to auto-create prayer ijin record
   * @param uniqueCode - The unique code of the student
   * @param recordedAt - The timestamp to use for the ijin record
   * @param force - Whether to force update an existing ijin record
   * @param classId - Class ID for historical tracking (optional for backward compatibility)
   * @param className - Class name for historical tracking (optional for backward compatibility)
   * @private
   */
  private async autoCreatePrayerIjin(
    uniqueCode: string,
    recordedAt: Date,
    force: boolean,
    classId?: number,
    className?: string
  ): Promise<void> {
    try {
      // Check for duplicate IJIN attendance on the same day
      const today = getCurrentWITATime()
      today.setHours(0, 0, 0, 0)

      const existingIjin = await this.absenceRepo.findByUniqueCodeAndTypeAndDate(
        uniqueCode,
        AttendanceType.IJIN,
        today
      )

      // If IJIN already exists and force is false, skip creation
      if (existingIjin && !force) {
        console.log(
          `🔄 AUTO PRAYER IJIN: Skipping creation for ${uniqueCode} - IJIN already exists`
        )
        return
      }

      // If IJIN exists and force is true, update the existing record
      // Otherwise, create a new IJIN record
      if (existingIjin && force) {
        await this.absenceRepo.update(existingIjin.id, {
          recordedAt: recordedAt, // Use same timestamp as the school attendance
          reason: 'Auto-generated: Tidak hadir sekolah', // Auto-generated reason

          // ✅ HISTORICAL CLASS TRACKING: Update class context for prayer ijin records
          classId: classId, // Update class ID if provided
          className: className, // Update class name if provided
        })
        console.log(`✅ AUTO PRAYER IJIN: Updated existing IJIN record for ${uniqueCode}`)
      } else {
        await this.absenceRepo.create({
          uniqueCode,
          type: AttendanceType.IJIN,
          recordedAt: recordedAt, // Use same timestamp as the school attendance
          reason: 'Auto-generated: Tidak hadir sekolah', // Auto-generated reason

          // ✅ HISTORICAL CLASS TRACKING: Include class context for prayer ijin records
          classId: classId, // Store class ID at time of attendance
          className: className, // Store class name at time of attendance
        })
        console.log(`✅ AUTO PRAYER IJIN: Created new IJIN record for ${uniqueCode}`)
      }
    } catch (error) {
      console.error(`❌ AUTO PRAYER IJIN: Failed to create IJIN record for ${uniqueCode}:`, error)
      // Re-throw the error to trigger transaction rollback
      throw error
    }
  }

  /**
   * Helper method to auto-create prayer ijin record for a specific date
   * @param uniqueCode - The unique code of the student
   * @param date - The specific date to create ijin for
   * @param force - Whether to force update an existing ijin record
   * @param classId - Class ID for historical tracking (optional for backward compatibility)
   * @param className - Class name for historical tracking (optional for backward compatibility)
   * @private
   */
  private async autoCreatePrayerIjinForDate(
    uniqueCode: string,
    date: Date,
    force: boolean,
    classId?: number,
    className?: string
  ): Promise<void> {
    try {
      // Normalize the date to start of day for consistent comparison
      const targetDate = new Date(date)
      targetDate.setHours(0, 0, 0, 0)

      // Check for duplicate IJIN attendance on the target date
      const existingIjin = await this.absenceRepo.findByUniqueCodeAndTypeAndDate(
        uniqueCode,
        AttendanceType.IJIN,
        targetDate
      )

      // If IJIN already exists and force is false, skip creation
      if (existingIjin && !force) {
        console.log(
          `🔄 AUTO PRAYER IJIN: Skipping creation for ${uniqueCode} on ${targetDate.toDateString()} - IJIN already exists`
        )
        return
      }

      // Set the ijin time to the same time as the school attendance (08:00 WITA)
      // Use SSOT WITA utilities instead of magic numbers
      const witaComponents = getWITADateComponents(targetDate)
      const ijinTime = createWITADate(
        witaComponents.year,
        witaComponents.month,
        witaComponents.day,
        8, // 08:00 WITA for manual entries
        0,
        0
      )

      // If IJIN exists and force is true, update the existing record
      // Otherwise, create a new IJIN record
      if (existingIjin && force) {
        await this.absenceRepo.update(existingIjin.id, {
          recordedAt: ijinTime,
          reason: 'Auto-generated: Tidak hadir sekolah', // Auto-generated reason

          // ✅ HISTORICAL CLASS TRACKING: Update class context for date-specific prayer ijin records
          classId: classId, // Update class ID if provided
          className: className, // Update class name if provided
        })
        console.log(
          `✅ AUTO PRAYER IJIN: Updated existing IJIN record for ${uniqueCode} on ${targetDate.toDateString()}`
        )
      } else {
        await this.absenceRepo.create({
          uniqueCode,
          type: AttendanceType.IJIN,
          recordedAt: ijinTime,
          reason: 'Auto-generated: Tidak hadir sekolah', // Auto-generated reason

          // ✅ HISTORICAL CLASS TRACKING: Include class context for date-specific prayer ijin records
          classId: classId, // Store class ID at time of attendance
          className: className, // Store class name at time of attendance
        })
        console.log(
          `✅ AUTO PRAYER IJIN: Created new IJIN record for ${uniqueCode} on ${targetDate.toDateString()}`
        )
      }
    } catch (error) {
      console.error(
        `❌ AUTO PRAYER IJIN: Failed to create IJIN record for ${uniqueCode} on ${date.toDateString()}:`,
        error
      )
      // Re-throw the error to trigger transaction rollback
      throw error
    }
  }

  /**
   * Validate that the student has completed Zuhr and Asr prayers before allowing dismissal
   * @param uniqueCode - The unique code of the student
   * @private
   */
  private async validatePrayerAttendance(uniqueCode: string): Promise<void> {
    // FIXED: Use proper SSOT WITA date handling
    const components = getWITADateComponents()
    // FIXED: Use correct month (components.month is now 1-based, so subtract 1 for Date constructor)
    const today = new Date(components.year, components.month - 1, components.day, 0, 0, 0, 0)

    // DEBUG: Log the actual date being used
    const todayStr = formatDateToString(today)

    console.log('🔍 DEBUG validatePrayerAttendance - date info:', {
      todayObject: today,
      todayISO: today.toISOString(),
      todayFormatted: todayStr,
      dayOfWeek: today.getDay(),
      dayName: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][
        today.getDay()
      ],
      witaComponents: components,
    })

    // Get all attendance records for today
    const attendanceRecords = await this.absenceRepo.findByUniqueCodeAndDate(uniqueCode, today)

    // First check if student has an Ijin record for today (backward compatibility)
    const hasIjinExemption = attendanceRecords.some(record => record.type === AttendanceType.IJIN)

    // If student has Ijin exemption, skip prayer validation
    if (hasIjinExemption) {
      return
    }

    // NEW: Check prayer exemptions if prayer exemption use case is available
    if (this.prayerExemptionUseCases) {
      console.log('🔍 DEBUG validatePrayerAttendance - checking exemptions for:', {
        uniqueCode,
        today: today.toISOString(),
        dayOfWeek: today.getDay(),
      })

      const isZuhrExempted = await this.prayerExemptionUseCases.isStudentExempted(
        uniqueCode,
        today,
        AttendanceType.ZUHR
      )
      const isAsrExempted = await this.prayerExemptionUseCases.isStudentExempted(
        uniqueCode,
        today,
        AttendanceType.ASR
      )

      console.log('🔍 DEBUG exemption results:', {
        isZuhrExempted,
        isAsrExempted,
      })

      // If both prayers are exempted, allow dismissal
      if (isZuhrExempted && isAsrExempted) {
        console.log('✅ Both prayers exempted - allowing dismissal')
        return
      }

      // Check individual prayer requirements with exemptions
      const hasZuhrAttendance = attendanceRecords.some(
        record => record.type === AttendanceType.ZUHR
      )
      const hasAsrAttendance = attendanceRecords.some(record => record.type === AttendanceType.ASR)

      console.log('🔍 DEBUG attendance status:', {
        hasZuhrAttendance,
        hasAsrAttendance,
        attendanceRecords: attendanceRecords.map(r => ({ type: r.type, recordedAt: r.recordedAt })),
      })

      const zuhrRequired = !isZuhrExempted && !hasZuhrAttendance
      const asrRequired = !isAsrExempted && !hasAsrAttendance

      console.log('🔍 DEBUG prayer requirements:', {
        zuhrRequired,
        asrRequired,
      })

      if (zuhrRequired || asrRequired) {
        const missingShalat = []
        if (zuhrRequired) missingShalat.push('Zuhur')
        if (asrRequired) missingShalat.push('Ashar')

        console.log('❌ Missing prayers:', missingShalat)
        throw new PrayerNotCompletedError(
          `Absensi pulang tidak diizinkan. Shalat ${missingShalat.join(' dan ')} belum dilakukan.`
        )
      }

      console.log('✅ All prayer requirements met - allowing dismissal')
      return
    }

    // FALLBACK: Original validation logic (when prayer exemption is not available)
    // Check if Zuhr prayer is completed
    const hasZuhrAttendance = attendanceRecords.some(record => record.type === AttendanceType.ZUHR)

    // Check if Asr prayer is completed
    const hasAsrAttendance = attendanceRecords.some(record => record.type === AttendanceType.ASR)

    // If either Zuhr or Asr prayer is not completed, throw an error
    if (!hasZuhrAttendance || !hasAsrAttendance) {
      const missingShalat = []
      if (!hasZuhrAttendance) missingShalat.push('Zuhur')
      if (!hasAsrAttendance) missingShalat.push('Ashar')

      throw new PrayerNotCompletedError(
        `Absensi pulang tidak diizinkan. Shalat ${missingShalat.join(' dan ')} belum dilakukan.`
      )
    }
  }

  /**
   * Check if a student has already recorded attendance for a specific type today
   */
  async checkDuplicateAttendance(uniqueCode: string, type: AttendanceType): Promise<boolean> {
    // Check if student exists
    const student = await this.studentRepo.findByUniqueCode(uniqueCode)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    // Special handling for temporary leave workflow
    // These types should allow multiple records per day, so never consider them duplicates
    const isTemporaryLeaveWorkflow =
      type === AttendanceType.TEMPORARY_LEAVE || type === AttendanceType.RETURN_FROM_LEAVE

    if (isTemporaryLeaveWorkflow) {
      console.log(`🔄 TEMPORARY LEAVE CHECK: ${type} for ${uniqueCode} - ALLOWING MULTIPLE RECORDS`)
      return false // Never consider temporary leave as duplicate
    }

    // For other attendance types, check for duplicates
    // Use WITA time for consistent date handling
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)

    // Always check the database directly for duplicate attendance checks
    // This ensures we have the most up-to-date information and prevents race conditions
    const existingAbsence = await this.absenceRepo.findByUniqueCodeAndTypeAndDate(
      uniqueCode,
      type,
      today
    )

    return !!existingAbsence
  }

  /**
   * Get attendance summary for reporting with optimized database-level filtering
   */
  async getAttendanceSummaryByType(
    date?: Date,
    className?: string,
    reportType: 'prayer' | 'school' | 'all' = 'all',
    forceFresh?: boolean
  ): Promise<AttendanceSummary[]> {
    // Use optimized method with database-level filtering instead of application-level filtering
    return await this.getAttendanceSummary(date, className, forceFresh, reportType)
  }

  /**
   * Get attendance summary for reporting with optimized caching and database filtering
   */
  async getAttendanceSummary(
    date?: Date,
    className?: string,
    forceFresh?: boolean,
    reportType: 'prayer' | 'school' | 'all' = 'all'
  ): Promise<AttendanceSummary[]> {
    // Generate a cache key with reportType for better cache segmentation
    // ✅ FIXED: Use WITA-aware date key generation to ensure consistent timezone handling
    const dateKey = date ? getWITADateKey(date) : 'all'

    // Cache class name, defaulting to 'all' if not provided
    const classKey = className || 'all'

    // Include reportType in cache key for proper cache separation
    const cacheKey = `absence:reports:${reportType}:${dateKey}:${classKey}:day`

    // Note: Old cache format check removed - using unified cache strategy

    // If forceFresh is true, skip the cache
    if (forceFresh) {
      // Invalidate any existing cache for this data
      try {
        await unifiedCacheStrategy.del(cacheKey)
      } catch (cacheError) {
        console.error('Error invalidating cache:', cacheError)
      }
    }

    // Try to get data from cache first if not forcing fresh data
    if (!forceFresh) {
      try {
        const cachedData = await unifiedCacheStrategy.get(cacheKey)
        if (cachedData) {
          console.log(`✅ CACHE HIT: ${cacheKey}`)
          return cachedData
        }
        console.log(`❌ CACHE MISS: ${cacheKey}`)
      } catch (cacheError) {
        console.error('Error reading from cache:', cacheError)
      }
    }

    // If we reach here, either cache miss or forceFresh is true
    console.log('📊 FETCHING FROM DATABASE: Real-time data retrieval')

    // Use the efficient method for daily reports with database-level filtering
    const summary = await this.absenceRepo.getAttendanceSummary(date, className, reportType)

    // 🚀 WRITE-THROUGH CACHE: Store data without TTL - persists until invalidated
    try {
      await unifiedCacheStrategy.set(cacheKey, summary)
      console.log(
        `🚀 WRITE-THROUGH CACHED: ${reportType} data at ${cacheKey} (NO TTL - persists until invalidated)`
      )
    } catch (cacheError) {
      console.error('Error setting cache:', cacheError)
    }

    return summary
  }

  /**
   * 🚀 SMART CACHE INVALIDATION: Only invalidate relevant cache keys
   * Refresh the materialized view for attendance summary
   */
  async refreshAttendanceSummary(): Promise<void> {
    await this.absenceRepo.refreshMaterializedView()

    // 🎯 TARGETED INVALIDATION: Only clear cache keys that are actually affected
    const currentDate = getCurrentWITATime()
    const todayKey = getWITADateKey(currentDate)

    // Batch invalidation for better performance
    const keysToInvalidate: string[] = []

    // Only invalidate today's cache (most commonly accessed)
    keysToInvalidate.push(
      `absence:reports:${todayKey}:all:day`,
      `absence:reports:today:all:day`,
      `absence:reports:school:${todayKey}:all:day`,
      `absence:reports:prayer:${todayKey}:all:day`
    )

    // Only invalidate current week if it's a weekday (avoid weekend invalidation)
    const dayOfWeek = currentDate.getDay()
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      // Monday to Friday
      const weekStart = new Date(currentDate)
      weekStart.setDate(weekStart.getDate() - 6)
      const weekStartKey = getWITADateKey(weekStart)
      const weekEndKey = getWITADateKey(currentDate)

      keysToInvalidate.push(
        `aggregated:${weekStartKey}:${weekEndKey}:all:school`,
        `aggregated:${weekStartKey}:${weekEndKey}:all:prayer`
      )
    }

    // Only invalidate monthly cache on the last day of the month or first few days
    const dayOfMonth = currentDate.getDate()
    const lastDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      0
    ).getDate()

    if (dayOfMonth <= 3 || dayOfMonth >= lastDayOfMonth - 2) {
      const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
      const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
      const monthStartKey = getWITADateKey(monthStart)
      const monthEndKey = getWITADateKey(monthEnd)

      keysToInvalidate.push(
        `aggregated:${monthStartKey}:${monthEndKey}:all:school`,
        `aggregated:${monthStartKey}:${monthEndKey}:all:prayer`
      )
    }

    // 🚀 BATCH INVALIDATION: Delete all keys in one operation for better performance
    await Promise.all(keysToInvalidate.map(key => unifiedCacheStrategy.del(key)))

    console.log(
      `🚀 SMART CACHE INVALIDATION: Cleared ${keysToInvalidate.length} targeted cache keys (${dayOfWeek === 0 || dayOfWeek === 6 ? 'weekend mode' : 'weekday mode'})`
    )
  }

  /**
   * Delete an attendance record
   * @param uniqueCode - The unique code of the student
   * @param type - The type of attendance to delete
   * @param date - The date of the attendance record
   */
  async deleteAttendance(uniqueCode: string, type: AttendanceType, date: Date): Promise<boolean> {
    try {
      // Check if student exists
      try {
        const student = await this.studentRepo.findByUniqueCode(uniqueCode)

        if (!student) {
          throw new NotFoundError('Student not found')
        }
      } catch (error) {
        console.error('Error finding student:', error)
        // Continue with deletion even if student is not found
        // This allows deleting records for users that might have been converted to admins
      }

      // Ensure we have a valid date
      if (!date || isNaN(date.getTime())) {
        console.error('Invalid date provided to deleteAttendance:', date)
        throw new Error('Invalid date')
      }

      // Delete the attendance record
      const deleted = await this.absenceRepo.deleteByUniqueCodeAndTypeAndDate(
        uniqueCode,
        type,
        date
      )

      if (!deleted) {
        return false // Record not found or deletion failed
      }

      // 🚀 UNIFIED CACHE INVALIDATION: Clear all related cache after deletion
      try {
        const student = await this.studentRepo.findByUniqueCode(uniqueCode)
        const cacheEvent = createCacheEvent(uniqueCode, type, 'delete', student?.className)

        // Event-driven cache invalidation for all related reports
        await unifiedCacheStrategy.invalidateRelatedCache(cacheEvent)

        console.log(`🚀 UNIFIED CACHE: Invalidated cache after deletion ${uniqueCode} - ${type}`)
      } catch (cacheError) {
        console.error('Cache invalidation failed after deletion:', cacheError)
        // Continue execution even if cache invalidation fails
      }

      return true
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error // Re-throw NotFoundError to be handled by the API
      }
      return false
    }
  }

  /**
   * Get all attendance records for a student on a specific day
   * HYBRID STRATEGY: Cache for performance, database validation for accuracy
   */
  async getAttendanceForDay(uniqueCode: string, date: Date): Promise<Absence[]> {
    try {
      // Validate inputs
      if (!uniqueCode) {
        console.error('Invalid uniqueCode provided to getAttendanceForDay')
        return []
      }

      if (!date || isNaN(date.getTime())) {
        console.error('Invalid date provided to getAttendanceForDay:', date)
        // Create a fallback date (today) using WITA time
        date = getCurrentWITATime()
        date.setHours(0, 0, 0, 0)
      }

      // Check if student exists
      const student = await this.studentRepo.findByUniqueCode(uniqueCode)

      if (!student) {
        console.warn(`Student with uniqueCode ${uniqueCode} not found`)
        return []
      }

      // 🔧 CRITICAL FIX: Use WITA-aware date key for consistency with cache invalidation
      const dateStr = getWITADateKey(date) // Use WITA date key instead of UTC
      const cacheKey = `absence:student:${uniqueCode}:${dateStr}`

      console.log(`🔧 DEBUG: getAttendanceForDay cache key: ${cacheKey}`)
      console.log(`🔧 DEBUG: Date input: ${date.toISOString()}, WITA key: ${dateStr}`)

      // 🚀 HYBRID STRATEGY: Check cache first, but validate with database
      let cachedRecords: Absence[] = []
      let cacheExists = false

      try {
        const cachedData = await unifiedCacheStrategy.get(cacheKey)
        if (cachedData) {
          cachedRecords = cachedData
          cacheExists = true
          console.log(`📖 CACHE FOUND: ${cachedRecords.length} cached records`)
        }
      } catch (cacheError) {
        console.error('Error getting data from cache:', cacheError)
      }

      // Always fetch from database for comparison
      console.log(`🗄️ DATABASE CHECK: Fetching fresh data from database for validation`)
      const databaseRecords = await this.absenceRepo.findByUniqueCodeAndDate(uniqueCode, date)

      console.log(`🗄️ DATABASE RESULT: Found ${databaseRecords.length} records:`)
      databaseRecords.forEach(record => {
        // Format time in WITA timezone for better readability
        const witaTime = new Date(record.recordedAt).toLocaleString('id-ID', {
          timeZone: 'Asia/Makassar',
          hour: '2-digit',
          minute: '2-digit',
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        console.log(`   - Type: ${record.type}, Time: ${witaTime} WITA`)
      })

      // Compare cache vs database
      const cacheCount = cachedRecords.length
      const dbCount = databaseRecords.length

      if (cacheExists && cacheCount === dbCount) {
        console.log(`✅ CACHE VALID: Cache (${cacheCount}) matches database (${dbCount})`)
        return cachedRecords
      } else {
        console.log(
          `❌ CACHE STALE: Cache (${cacheCount}) != Database (${dbCount}) - Using database data`
        )

        // Update cache with fresh database data
        try {
          await unifiedCacheStrategy.set(cacheKey, databaseRecords)
          console.log(`🔄 CACHE UPDATED: ${cacheKey} with ${databaseRecords.length} fresh records`)
        } catch (cacheError) {
          console.error('Error updating cache with fresh data:', cacheError)
        }

        return databaseRecords
      }
    } catch (error) {
      console.error('Error in getAttendanceForDay:', error)
      return []
    }
  }

  // Removed filterSummaryByReportType method as we now use database-level filtering
  // This eliminates the need for application-level filtering and improves performance
}
