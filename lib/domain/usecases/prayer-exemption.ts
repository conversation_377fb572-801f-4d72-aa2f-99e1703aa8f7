/**
 * Prayer Exemption Use Cases - Clean Architecture Business Logic Layer
 * Handles all business logic for prayer exemption management
 */

import {
  PrayerExemption,
  CreatePrayerExemptionDTO,
  UpdatePrayerExemptionDTO,
  PrayerExemptionType,
  RecurrencePattern,
  doesExemptionApplyToStudent,
  doesExemptionCoverPrayer,
} from '@/lib/domain/entities/prayer-exemption'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { PrayerExemptionRepository } from '@/lib/data/repositories/prayer-exemption'
import { StudentRepository } from '@/lib/data/repositories/student'
import { ClassRepository } from '@/lib/data/repositories/class'
import { NotFoundError, ValidationError, AuthorizationError } from '@/lib/domain/errors'
import { getCurrentWITATime, getTodayWITA } from '@/lib/utils/date'

/**
 * Prayer Exemption Use Cases Implementation
 */
export class PrayerExemptionUseCases {
  constructor(
    private prayerExemptionRepo: PrayerExemptionRepository,
    private studentRepo: StudentRepository,
    private classRepo: ClassRepository
  ) {}

  /**
   * Create a new prayer exemption - supports both one-time and recurring
   * Only super_admin can create exemptions
   */
  async createExemption(
    data: CreatePrayerExemptionDTO,
    createdByRole: string
  ): Promise<PrayerExemption> {
    // SECURITY: Only super_admin can create exemptions
    if (createdByRole !== 'super_admin') {
      throw new AuthorizationError('Only super admin can create prayer exemptions')
    }

    // Validate dates based on recurrence pattern
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)

    if (data.recurrencePattern === RecurrencePattern.ONCE) {
      // Validate one-time exemption date
      if (!data.exemptionDate) {
        throw new ValidationError('Exemption date is required for one-time exemptions')
      }

      const exemptionDate = new Date(data.exemptionDate)
      exemptionDate.setHours(0, 0, 0, 0)

      if (exemptionDate < today) {
        throw new ValidationError('Exemption date cannot be in the past')
      }
    } else {
      // Validate recurring exemption dates
      if (!data.startDate) {
        throw new ValidationError('Start date is required for recurring exemptions')
      }

      const startDate = new Date(data.startDate)
      startDate.setHours(0, 0, 0, 0)

      if (startDate < today) {
        throw new ValidationError('Start date cannot be in the past')
      }

      if (data.endDate) {
        const endDate = new Date(data.endDate)
        endDate.setHours(0, 0, 0, 0)

        if (endDate <= startDate) {
          throw new ValidationError('End date must be after start date')
        }
      }

      // Validate weekly pattern
      if (data.recurrencePattern === RecurrencePattern.WEEKLY) {
        if (!data.daysOfWeek || data.daysOfWeek.length === 0) {
          throw new ValidationError('Days of week are required for weekly recurring exemptions')
        }
      }
    }

    // Since we only support global exemptions, no class validation needed

    // Validate reason length
    if (!data.reason || data.reason.trim().length < 10) {
      throw new ValidationError('Reason must be at least 10 characters long')
    }

    // Create exemption
    return await this.prayerExemptionRepo.create(data)
  }

  /**
   * Get all exemptions with pagination and filtering
   */
  async getExemptions(
    page: number = 1,
    limit: number = 50,
    filters?: {
      exemptionType?: PrayerExemptionType
      dateFrom?: Date
      dateTo?: Date
      isActive?: boolean
    }
  ): Promise<{ exemptions: PrayerExemption[]; total: number; totalPages: number }> {
    const result = await this.prayerExemptionRepo.findAll(page, limit, filters)

    return {
      ...result,
      totalPages: Math.ceil(result.total / limit),
    }
  }

  /**
   * Get exemption by ID
   */
  async getExemptionById(id: number): Promise<PrayerExemption> {
    const exemption = await this.prayerExemptionRepo.findById(id)

    if (!exemption) {
      throw new NotFoundError('Prayer exemption not found')
    }

    return exemption
  }

  /**
   * Update exemption
   * Only super_admin can update exemptions
   */
  async updateExemption(
    id: number,
    data: UpdatePrayerExemptionDTO,
    updatedByRole: string
  ): Promise<PrayerExemption> {
    // SECURITY: Only super_admin can update exemptions
    if (updatedByRole !== 'super_admin') {
      throw new AuthorizationError('Only super admin can update prayer exemptions')
    }

    // Validate reason if provided
    if (data.reason && data.reason.trim().length < 10) {
      throw new ValidationError('Reason must be at least 10 characters long')
    }

    const exemption = await this.prayerExemptionRepo.update(id, data)

    if (!exemption) {
      throw new NotFoundError('Prayer exemption not found')
    }

    return exemption
  }

  /**
   * Delete exemption (soft delete)
   * Only super_admin can delete exemptions
   */
  async deleteExemption(id: number, deletedByRole: string): Promise<void> {
    // SECURITY: Only super_admin can delete exemptions
    if (deletedByRole !== 'super_admin') {
      throw new AuthorizationError('Only super admin can delete prayer exemptions')
    }

    const success = await this.prayerExemptionRepo.softDelete(id)

    if (!success) {
      throw new NotFoundError('Prayer exemption not found')
    }
  }

  /**
   * Hard delete exemption (permanent removal)
   * Only super_admin can delete exemptions
   */
  async hardDeleteExemption(id: number, deletedByRole: string): Promise<void> {
    // SECURITY: Only super_admin can delete exemptions
    if (deletedByRole !== 'super_admin') {
      throw new AuthorizationError('Only super admin can delete prayer exemptions')
    }

    const success = await this.prayerExemptionRepo.hardDelete(id)

    if (!success) {
      throw new NotFoundError('Prayer exemption not found')
    }
  }

  /**
   * Check if student is exempted from specific prayer on specific date
   * Used by attendance validation logic
   */
  async isStudentExempted(
    uniqueCode: string,
    date: Date,
    prayerType: AttendanceType.ZUHR | AttendanceType.ASR
  ): Promise<boolean> {
    // Get student to find their class
    const student = await this.studentRepo.findByUniqueCode(uniqueCode)
    if (!student) {
      return false
    }

    // Get active exemptions for the date
    const exemptions = await this.prayerExemptionRepo.findActiveExemptionsForDate(date)

    // Check if any exemption applies to this student and prayer
    for (const exemption of exemptions) {
      const appliesTo = doesExemptionApplyToStudent(exemption)
      const coversPrayer = doesExemptionCoverPrayer(exemption, prayerType)

      if (appliesTo && coversPrayer) {
        return true
      }
    }

    return false
  }

  /**
   * Get exemptions for today (for dashboard/quick view)
   */
  async getTodayExemptions(): Promise<PrayerExemption[]> {
    // FIXED: Use SSOT timezone handling for today's date
    const today = getTodayWITA()

    return await this.prayerExemptionRepo.findActiveExemptionsForDate(today)
  }

  /**
   * Get upcoming exemptions (next 7 days)
   */
  async getUpcomingExemptions(): Promise<PrayerExemption[]> {
    const today = getCurrentWITATime()
    today.setHours(0, 0, 0, 0)

    const nextWeek = new Date(today)
    nextWeek.setDate(nextWeek.getDate() + 7)

    const result = await this.prayerExemptionRepo.findAll(1, 100, {
      dateFrom: today,
      dateTo: nextWeek,
      isActive: true,
    })

    return result.exemptions
  }
}
