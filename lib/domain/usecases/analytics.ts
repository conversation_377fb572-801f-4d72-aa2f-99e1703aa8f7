/**
 * Analytics Use Cases
 * Clean Architecture - Application Layer
 */

import {
  PrayerAnalytics,
  ClassAnalytics,
  StudentAnalytics,
  DashboardKPIs,
  TrendData,
  HeatmapData,
  AnalyticsFilters,
  AnalyticsSummary,
  AttendanceAlert,
  PredictiveAnalytics,
  AnalyticsExportConfig,
} from '../entities/analytics'
import { AttendanceSummary } from '../entities/absence'
import { getWITADateKey } from '../../utils/date'

/**
 * Analytics repository interface
 */
export interface IAnalyticsRepository {
  getPrayerAnalytics(filters: AnalyticsFilters): Promise<PrayerAnalytics[]>
  getClassAnalytics(filters: AnalyticsFilters): Promise<ClassAnalytics[]>
  getStudentAnalytics(filters: AnalyticsFilters): Promise<StudentAnalytics[]>
  getDashboardKPIs(date: Date): Promise<DashboardKPIs>
  getTrendData(
    type: 'attendance' | 'prayer' | 'class',
    filters: AnalyticsFilters
  ): Promise<TrendData[]>
  getHeatmapData(filters: AnalyticsFilters): Promise<HeatmapData[]>
  getAttendanceAlerts(limit?: number): Promise<AttendanceAlert[]>
  getPredictiveAnalytics(studentCode?: string): Promise<PredictiveAnalytics[]>
}

/**
 * Cache interface for analytics
 */
export interface IAnalyticsCache {
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, ttlSeconds?: number): Promise<void>
  invalidate(pattern: string): Promise<void>
}

/**
 * Analytics use cases implementation
 */
export class AnalyticsUseCases {
  constructor(
    private analyticsRepository: IAnalyticsRepository,
    private cache: IAnalyticsCache
  ) {}

  /**
   * Get dashboard overview with KPIs and summary
   */
  async getDashboardOverview(date: Date = new Date()): Promise<{
    kpis: DashboardKPIs
    summary: AnalyticsSummary
    alerts: AttendanceAlert[]
  }> {
    // ✅ FIXED: Use WITA-aware date key generation for consistent timezone handling
    const cacheKey = `dashboard_overview_${getWITADateKey(date)}`

    // Try to get from cache first
    const cached = await this.cache.get<{
      kpis: DashboardKPIs
      summary: AnalyticsSummary
      alerts: AttendanceAlert[]
    }>(cacheKey)

    if (cached) {
      return cached
    }

    // Generate fresh data
    const filters: AnalyticsFilters = {
      dateRange: {
        start: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
        end: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59),
      },
    }

    const [kpis, classAnalytics, studentAnalytics, alerts] = await Promise.all([
      this.analyticsRepository.getDashboardKPIs(date),
      this.analyticsRepository.getClassAnalytics(filters),
      this.analyticsRepository.getStudentAnalytics(filters),
      this.analyticsRepository.getAttendanceAlerts(10),
    ])

    // Generate trends for the past 7 days using proper date utilities
    const weekFilters: AnalyticsFilters = {
      dateRange: {
        start: new Date(date.getTime() - 7 * 24 * 60 * 60 * 1000),
        end: date,
      },
    }

    const [attendanceTrends, prayerTrends] = await Promise.all([
      this.analyticsRepository.getTrendData('attendance', weekFilters),
      this.analyticsRepository.getTrendData('prayer', weekFilters),
    ])

    // Create summary
    const summary: AnalyticsSummary = {
      // ✅ FIXED: Use WITA-aware date key generation for consistent timezone handling
      period: getWITADateKey(date),
      totalStudents: kpis.todayAttendance.total,
      overallAttendanceRate: kpis.todayAttendance.percentage,
      prayerComplianceRate: kpis.prayerMetrics.overallCompliance,
      classPerformance: classAnalytics,
      topPerformers: studentAnalytics
        .filter(s => s.attendanceRate >= 90)
        .sort((a, b) => b.attendanceRate - a.attendanceRate)
        .slice(0, 10),
      atRiskStudents: studentAnalytics
        .filter(s => s.riskScore >= 70)
        .sort((a, b) => b.riskScore - a.riskScore)
        .slice(0, 10),
      trends: {
        attendance: attendanceTrends,
        prayer: prayerTrends,
        classes: [],
      },
      insights: this.generateInsights(kpis, classAnalytics, studentAnalytics),
      recommendations: this.generateRecommendations(classAnalytics, studentAnalytics),
    }

    const result = { kpis, summary, alerts }

    // Cache for 5 minutes
    await this.cache.set(cacheKey, result, 300)

    return result
  }

  /**
   * Get prayer attendance analytics
   */
  async getPrayerAnalytics(filters: AnalyticsFilters): Promise<PrayerAnalytics[]> {
    const cacheKey = `prayer_analytics_${JSON.stringify(filters)}`

    const cached = await this.cache.get<PrayerAnalytics[]>(cacheKey)
    if (cached) {
      return cached
    }

    const analytics = await this.analyticsRepository.getPrayerAnalytics(filters)

    // Cache for 10 minutes
    await this.cache.set(cacheKey, analytics, 600)

    return analytics
  }

  /**
   * Get class performance analytics
   */
  async getClassAnalytics(filters: AnalyticsFilters): Promise<ClassAnalytics[]> {
    const cacheKey = `class_analytics_${JSON.stringify(filters)}`

    const cached = await this.cache.get<ClassAnalytics[]>(cacheKey)
    if (cached) {
      return cached
    }

    const analytics = await this.analyticsRepository.getClassAnalytics(filters)

    // Cache for 10 minutes
    await this.cache.set(cacheKey, analytics, 600)

    return analytics
  }

  /**
   * Get student performance analytics
   */
  async getStudentAnalytics(filters: AnalyticsFilters): Promise<StudentAnalytics[]> {
    const cacheKey = `student_analytics_${JSON.stringify(filters)}`

    const cached = await this.cache.get<StudentAnalytics[]>(cacheKey)
    if (cached) {
      return cached
    }

    const analytics = await this.analyticsRepository.getStudentAnalytics(filters)

    // Cache for 15 minutes
    await this.cache.set(cacheKey, analytics, 900)

    return analytics
  }

  /**
   * Get attendance heatmap data
   */
  async getHeatmapData(filters: AnalyticsFilters): Promise<HeatmapData[]> {
    const cacheKey = `heatmap_data_${JSON.stringify(filters)}`

    const cached = await this.cache.get<HeatmapData[]>(cacheKey)
    if (cached) {
      return cached
    }

    const heatmapData = await this.analyticsRepository.getHeatmapData(filters)

    // Cache for 30 minutes
    await this.cache.set(cacheKey, heatmapData, 1800)

    return heatmapData
  }

  /**
   * Get predictive analytics for at-risk students
   */
  async getPredictiveAnalytics(studentCode?: string): Promise<PredictiveAnalytics[]> {
    const cacheKey = `predictive_analytics_${studentCode || 'all'}`

    const cached = await this.cache.get<PredictiveAnalytics[]>(cacheKey)
    if (cached) {
      return cached
    }

    const predictions = await this.analyticsRepository.getPredictiveAnalytics(studentCode)

    // Cache for 1 hour
    await this.cache.set(cacheKey, predictions, 3600)

    return predictions
  }

  /**
   * Generate insights from analytics data
   */
  private generateInsights(
    kpis: DashboardKPIs,
    classAnalytics: ClassAnalytics[],
    studentAnalytics: StudentAnalytics[]
  ) {
    const insights = []

    // Attendance insight
    const attendanceChange = kpis.todayAttendance.change
    insights.push({
      key: 'attendance_trend',
      value: `${attendanceChange > 0 ? '+' : ''}${attendanceChange.toFixed(1)}%`,
      trend:
        attendanceChange > 0
          ? ('positive' as const)
          : attendanceChange < 0
            ? ('negative' as const)
            : ('neutral' as const),
      description: `Attendance ${attendanceChange > 0 ? 'improved' : attendanceChange < 0 ? 'declined' : 'remained stable'} compared to yesterday`,
    })

    // Prayer compliance insight
    const prayerChange = kpis.prayerMetrics.change
    insights.push({
      key: 'prayer_compliance',
      value: `${kpis.prayerMetrics.overallCompliance.toFixed(1)}%`,
      trend:
        prayerChange > 0
          ? ('positive' as const)
          : prayerChange < 0
            ? ('negative' as const)
            : ('neutral' as const),
      description: `Prayer compliance is ${kpis.prayerMetrics.overallCompliance >= 80 ? 'excellent' : kpis.prayerMetrics.overallCompliance >= 60 ? 'good' : 'needs improvement'}`,
    })

    // Class performance insight
    const bestClass = classAnalytics.reduce((best, current) =>
      current.attendanceRate > best.attendanceRate ? current : best
    )
    insights.push({
      key: 'best_class',
      value: bestClass.className,
      trend: 'positive' as const,
      description: `${bestClass.className} has the highest attendance rate at ${bestClass.attendanceRate.toFixed(1)}%`,
    })

    // At-risk students insight
    const atRiskCount = studentAnalytics.filter(s => s.riskScore >= 70).length
    insights.push({
      key: 'at_risk_students',
      value: atRiskCount,
      trend: atRiskCount > 0 ? ('negative' as const) : ('positive' as const),
      description: `${atRiskCount} students require attention for attendance improvement`,
    })

    return insights
  }

  /**
   * Generate recommendations based on analytics
   */
  private generateRecommendations(
    classAnalytics: ClassAnalytics[],
    studentAnalytics: StudentAnalytics[]
  ) {
    const recommendations = []

    // Class-based recommendations
    const lowPerformingClasses = classAnalytics.filter(c => c.attendanceRate < 80)
    if (lowPerformingClasses.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'class' as const,
        title: 'Address Low-Performing Classes',
        description: `${lowPerformingClasses.length} classes have attendance rates below 80%`,
        actionItems: [
          'Schedule meetings with class teachers',
          'Implement targeted intervention programs',
          'Increase monitoring frequency',
          'Engage with parents of affected students',
        ],
      })
    }

    // Student-based recommendations
    const criticalStudents = studentAnalytics.filter(s => s.riskScore >= 80)
    if (criticalStudents.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'student' as const,
        title: 'Critical Student Interventions',
        description: `${criticalStudents.length} students are at critical risk`,
        actionItems: [
          'Immediate counseling sessions',
          'Parent-teacher conferences',
          'Personalized attendance plans',
          'Regular check-ins and monitoring',
        ],
      })
    }

    // Prayer compliance recommendations
    const lowPrayerCompliance = classAnalytics.filter(c => c.prayerComplianceRate < 70)
    if (lowPrayerCompliance.length > 0) {
      recommendations.push({
        priority: 'medium' as const,
        category: 'prayer' as const,
        title: 'Improve Prayer Attendance',
        description: `${lowPrayerCompliance.length} classes have low prayer compliance`,
        actionItems: [
          'Religious education reinforcement',
          'Peer mentorship programs',
          'Incentive programs for consistent attendance',
          'Family engagement initiatives',
        ],
      })
    }

    return recommendations
  }

  /**
   * Generate insights from analytics data
   */
  private generateInsights(
    kpis: DashboardKPIs,
    classAnalytics: ClassAnalytics[],
    studentAnalytics: StudentAnalytics[]
  ) {
    const insights = []

    // Attendance insight
    const attendanceChange = kpis.todayAttendance.change
    insights.push({
      key: 'attendance_trend',
      value: `${attendanceChange > 0 ? '+' : ''}${attendanceChange.toFixed(1)}%`,
      trend:
        attendanceChange > 0
          ? ('positive' as const)
          : attendanceChange < 0
            ? ('negative' as const)
            : ('neutral' as const),
      description: `Attendance ${attendanceChange > 0 ? 'improved' : attendanceChange < 0 ? 'declined' : 'remained stable'} compared to yesterday`,
    })

    // Prayer compliance insight
    const prayerChange = kpis.prayerMetrics.change
    insights.push({
      key: 'prayer_compliance',
      value: `${kpis.prayerMetrics.overallCompliance.toFixed(1)}%`,
      trend:
        prayerChange > 0
          ? ('positive' as const)
          : prayerChange < 0
            ? ('negative' as const)
            : ('neutral' as const),
      description: `Prayer compliance is ${kpis.prayerMetrics.overallCompliance >= 80 ? 'excellent' : kpis.prayerMetrics.overallCompliance >= 60 ? 'good' : 'needs improvement'}`,
    })

    // Class performance insight
    const bestClass = classAnalytics.reduce((best, current) =>
      current.attendanceRate > best.attendanceRate ? current : best
    )
    insights.push({
      key: 'best_class',
      value: bestClass.className,
      trend: 'positive' as const,
      description: `${bestClass.className} has the highest attendance rate at ${bestClass.attendanceRate.toFixed(1)}%`,
    })

    // At-risk students insight
    const atRiskCount = studentAnalytics.filter(s => s.riskScore >= 70).length
    insights.push({
      key: 'at_risk_students',
      value: atRiskCount,
      trend: atRiskCount > 0 ? ('negative' as const) : ('positive' as const),
      description: `${atRiskCount} students require attention for attendance improvement`,
    })

    return insights
  }

  /**
   * Generate recommendations based on analytics
   */
  private generateRecommendations(
    classAnalytics: ClassAnalytics[],
    studentAnalytics: StudentAnalytics[]
  ) {
    const recommendations = []

    // Class-based recommendations
    const lowPerformingClasses = classAnalytics.filter(c => c.attendanceRate < 80)
    if (lowPerformingClasses.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'class' as const,
        title: 'Address Low-Performing Classes',
        description: `${lowPerformingClasses.length} classes have attendance rates below 80%`,
        actionItems: [
          'Schedule meetings with class teachers',
          'Implement targeted intervention programs',
          'Increase monitoring frequency',
          'Engage with parents of affected students',
        ],
      })
    }

    // Student-based recommendations
    const criticalStudents = studentAnalytics.filter(s => s.riskScore >= 80)
    if (criticalStudents.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'student' as const,
        title: 'Critical Student Interventions',
        description: `${criticalStudents.length} students are at critical risk`,
        actionItems: [
          'Immediate counseling sessions',
          'Parent-teacher conferences',
          'Personalized attendance plans',
          'Regular check-ins and monitoring',
        ],
      })
    }

    // Prayer compliance recommendations
    const lowPrayerCompliance = classAnalytics.filter(c => c.prayerComplianceRate < 70)
    if (lowPrayerCompliance.length > 0) {
      recommendations.push({
        priority: 'medium' as const,
        category: 'prayer' as const,
        title: 'Improve Prayer Attendance',
        description: `${lowPrayerCompliance.length} classes have low prayer compliance`,
        actionItems: [
          'Religious education reinforcement',
          'Peer mentorship programs',
          'Incentive programs for consistent attendance',
          'Family engagement initiatives',
        ],
      })
    }

    return recommendations
  }

  /**
   * Invalidate analytics cache
   */
  async invalidateCache(pattern?: string): Promise<void> {
    await this.cache.invalidate(pattern || 'analytics_*')
  }
}
