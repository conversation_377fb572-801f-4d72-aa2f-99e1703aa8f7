import { Student } from '../entities/student'
import { Admin } from '../entities/admin'
import { SessionData, CreateSessionDTO } from '../entities/session'
import { NotFoundError, ValidationError, AuthenticationError } from '../errors'
import { v4 as uuidv4 } from 'uuid'
import {
  generateToken,
  generateRefreshToken,
  JWTPayload,
  hashPassword,
  comparePassword,
} from '@/lib/utils/auth'
import {
  generateDeviceId,
  generateSessionMetadata,
  getClientIpAddress,
  parseUserAgent,
  SESSION_CONSTANTS,
  SessionEventType,
  logSessionEvent,
} from '@/lib/utils/session'
import { SessionUseCases } from './session'
import { serverConfig } from '@/lib/config'
import crypto from 'crypto'
import {
  generateOTP,
  validateRateLimit,
  trackFailedAttempt,
  resetFailedAttempts,
  formatPhoneNumber,
} from '@/lib/utils/otp'

/**
 * Interface for student repository
 */
export interface StudentRepository {
  findByUsername(username: string): Promise<Student | null>
  findById(id: number): Promise<Student | null>
  findByWhatsApp(whatsapp: string): Promise<Student | null>
  verifyPassword(student: Student, password: string): Promise<boolean>
  updateCredentials(id: number, username?: string, password?: string): Promise<Student>
}

/**
 * Interface for admin repository
 */
export interface AdminRepository {
  findByUsername(username: string): Promise<Admin | null>
  findById(id: number): Promise<Admin | null>
  verifyPassword(admin: Admin, password: string): Promise<boolean>
}

/**
 * Interface for cache service
 */
export interface CacheService {
  get(key: string): Promise<string | null>
  set(key: string, value: string, ttlSeconds: number): Promise<void>
  del(key: string): Promise<void>
}

/**
 * Enhanced authentication use cases with session management
 */
export class EnhancedAuthUseCases {
  constructor(
    private studentRepo: StudentRepository,
    private adminRepo: AdminRepository,
    private sessionUseCases: SessionUseCases,
    private cache: CacheService,
    private jwtSecret: string
  ) {}

  /**
   * Login an admin with username and password
   */
  async loginAdmin(
    username: string,
    password: string,
    userAgent: string,
    ipAddress: string
  ): Promise<{ token: string; refreshToken: string; admin: Admin; session: SessionData }> {
    const admin = await this.adminRepo.findByUsername(username)

    if (!admin) {
      throw new NotFoundError('Admin not found')
    }

    const isPasswordValid = await this.adminRepo.verifyPassword(admin, password)

    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid password')
    }

    // Generate device ID and session metadata
    const deviceId = generateDeviceId(userAgent, ipAddress)
    const metadata = generateSessionMetadata(userAgent, ipAddress)

    // Create session
    const session = await this.sessionUseCases.createSession({
      userId: admin.id,
      role: admin.role as 'admin' | 'super_admin' | 'teacher' | 'receptionist',
      deviceId,
      ipAddress,
      userAgent,
      durationSeconds: SESSION_CONSTANTS.DEFAULT_DURATION_SECONDS,
      metadata,
    })

    // Generate JWT token with session info
    const token = this.generateToken(
      admin.id,
      admin.role as 'admin' | 'super_admin' | 'teacher' | 'receptionist',
      deviceId,
      session.sessionId
    )
    const refreshToken = this.generateRefreshToken(admin.id)

    // Store refresh token in cache
    await this.cache.set(`auth:refresh:${admin.id}`, refreshToken, 7 * 24 * 60 * 60) // 7 days

    // Log session event
    logSessionEvent(SessionEventType.CREATED, session.sessionId, admin.id, {
      method: 'credentials',
      role: admin.role,
      deviceType: metadata.deviceType,
      browser: metadata.browser,
    })

    return { token, refreshToken, admin, session }
  }

  /**
   * Login a student with username and password
   */
  async loginStudentWithCredentials(
    username: string,
    password: string,
    userAgent: string,
    ipAddress: string
  ): Promise<{ token: string; refreshToken: string; student: Student; session: SessionData }> {
    const student = await this.studentRepo.findByUsername(username)

    if (!student) {
      throw new NotFoundError('Student not found')
    }

    const isPasswordValid = await this.studentRepo.verifyPassword(student, password)

    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid password')
    }

    // Generate device ID and session metadata
    const deviceId = generateDeviceId(userAgent, ipAddress)
    const metadata = generateSessionMetadata(userAgent, ipAddress)

    // Create session
    const session = await this.sessionUseCases.createSession({
      userId: student.id,
      role: 'student',
      deviceId,
      ipAddress,
      userAgent,
      durationSeconds: SESSION_CONSTANTS.DEFAULT_DURATION_SECONDS,
      metadata,
    })

    // Generate JWT token with session info
    const token = this.generateToken(student.id, 'student', deviceId, session.sessionId)
    const refreshToken = this.generateRefreshToken(student.id)

    // Store refresh token in cache
    await this.cache.set(`auth:refresh:${student.id}`, refreshToken, 7 * 24 * 60 * 60) // 7 days

    // Log session event
    logSessionEvent(SessionEventType.CREATED, session.sessionId, student.id, {
      method: 'credentials',
      deviceType: metadata.deviceType,
      browser: metadata.browser,
    })

    return { token, refreshToken, student, session }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(sessionId: string, userId: number): Promise<boolean> {
    const success = await this.sessionUseCases.invalidateSession(sessionId)

    if (success) {
      // Invalidate refresh token
      await this.cache.del(`auth:refresh:${userId}`)

      // Log session event
      logSessionEvent(SessionEventType.INVALIDATED, sessionId, userId, {
        method: 'logout',
      })
    }

    return success
  }

  /**
   * Validate session and refresh JWT token if needed
   */
  async validateAndRefreshSession(
    sessionId: string,
    userId: number
  ): Promise<{ isValid: boolean; newToken?: string; session?: SessionData }> {
    const validation = await this.sessionUseCases.validateSession(sessionId, true)

    if (!validation.isValid || !validation.session) {
      return { isValid: false }
    }

    // Check if session needs token refresh (within 10 minutes of expiry)
    const now = new Date()
    const tenMinutesFromNow = new Date(now.getTime() + 10 * 60 * 1000)

    if (validation.session.expiresAt <= tenMinutesFromNow) {
      // Generate new token with same session
      const newToken = this.generateToken(
        validation.session.userId,
        validation.session.role,
        validation.session.deviceId,
        validation.session.sessionId
      )

      logSessionEvent(SessionEventType.REFRESHED, sessionId, userId)

      return {
        isValid: true,
        newToken,
        session: validation.session,
      }
    }

    return {
      isValid: true,
      session: validation.session,
    }
  }

  /**
   * Generate a JWT token with session information
   */
  private generateToken(
    userId: number,
    role: 'student' | 'admin' | 'super_admin' | 'teacher' | 'receptionist',
    deviceId: string,
    sessionId: string
  ): string {
    const payload: JWTPayload = {
      id: userId,
      role,
      deviceId,
      sessionId,
    }
    return generateToken(payload, this.jwtSecret)
  }

  /**
   * Generate a refresh token
   */
  private generateRefreshToken(userId: number): string {
    return generateRefreshToken({ id: userId }, this.jwtSecret)
  }
}
