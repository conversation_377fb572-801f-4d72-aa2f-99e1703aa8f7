import {
  SessionData,
  CreateSessionDTO,
  SessionSummary,
  SessionFilter,
  SessionValidationResult,
} from '../entities/session'
import { SessionRepository } from '../repositories/session-repository'
import { Student } from '../entities/student'
import { Admin } from '../entities/admin'
import { NotFoundError, AuthenticationError, ValidationError } from '../errors'
import { SessionSecurityPolicy, logSecurityEvent } from '../../config/session-security'
import { UserRole } from '@/lib/config/role-permissions'

/**
 * Interface for student repository (for session use cases)
 */
export interface StudentRepository {
  findById(id: number): Promise<Student | null>
}

/**
 * Interface for admin repository (for session use cases)
 */
export interface AdminRepository {
  findById(id: number): Promise<Admin | null>
}

/**
 * Interface for cache service (for session use cases)
 */
export interface CacheService {
  get(key: string): Promise<string | null>
  set(key: string, value: string, ttlSeconds: number): Promise<void>
  del(key: string): Promise<void>
}

/**
 * Session management use cases
 * Handles business logic for session creation, validation, and management
 */
export class SessionUseCases {
  constructor(
    private sessionRepo: SessionRepository,
    private studentRepo: StudentRepository,
    private adminRepo: AdminRepository,
    private _cache: CacheService // Prefixed with _ to indicate intentionally unused
  ) {}

  /**
   * Create a new session for a user
   * SECURITY: Implements secure "one device one login" policy
   * Best Practice: Only invalidates sessions from the SAME device, not all user sessions
   */
  async createSession(sessionData: CreateSessionDTO): Promise<SessionData> {
    // Validate user exists
    await this.validateUserExists(sessionData.userId, sessionData.role)

    console.log(
      `🔐 Creating secure session for user ${sessionData.userId} with device ${sessionData.deviceId}`
    )

    // SECURITY BEST PRACTICE: Check for existing session on THIS SPECIFIC device only
    const existingDeviceSession = await this.sessionRepo.getSessionByUserAndDevice(
      sessionData.userId,
      sessionData.deviceId
    )

    // If there's an existing session on THIS device, invalidate it
    // This prevents session hijacking and ensures clean device state
    if (existingDeviceSession) {
      console.log(
        `🔒 Found existing session ${existingDeviceSession.sessionId} on device ${sessionData.deviceId}, invalidating for security...`
      )
      await this.sessionRepo.invalidateSession(existingDeviceSession.sessionId)

      // Small delay to ensure Redis cleanup completes
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // SECURITY IMPROVEMENT: Get all user sessions to check device limits
    const allUserSessions = await this.sessionRepo.getActiveSessionsForUser(sessionData.userId)

    // SECURITY: Use security policy to determine session limits and invalidation strategy
    const sessionPolicy = SessionSecurityPolicy.canCreateSession(
      allUserSessions.length,
      sessionData.role
    )

    if (!sessionPolicy.allowed) {
      console.warn(`🚨 Session creation denied: ${sessionPolicy.reason}`)
      throw new ValidationError(sessionPolicy.reason || 'Cannot create session')
    }

    // Determine which sessions to invalidate using security policy
    const sessionsToInvalidate = SessionSecurityPolicy.getSessionsToInvalidate(
      allUserSessions.map(s => ({
        sessionId: s.sessionId,
        lastAccessedAt: s.lastAccessedAt,
        deviceId: s.deviceId,
      })),
      sessionData.deviceId
    )

    // Invalidate sessions as determined by security policy
    for (const sessionId of sessionsToInvalidate) {
      console.log(`🔒 Security policy: invalidating session ${sessionId}`)
      await this.sessionRepo.invalidateSession(sessionId)

      // Log security event
      logSecurityEvent('session_invalidated', {
        userId: sessionData.userId,
        sessionId,
        deviceId: sessionData.deviceId,
        reason: 'New session creation - security policy enforcement',
      })
    }

    // Create new session with enhanced security metadata
    const enhancedSessionData = {
      ...sessionData,
      metadata: {
        ...sessionData.metadata,
        createdAt: new Date().toISOString(),
        securityLevel: 'high',
        deviceFingerprint: this.generateDeviceFingerprint(sessionData),
      },
    }

    const newSession = await this.sessionRepo.createSession(enhancedSessionData)
    console.log(`✅ Created secure session ${newSession.sessionId} for user ${sessionData.userId}`)

    // Log security event for session creation
    logSecurityEvent('session_created', {
      userId: sessionData.userId,
      sessionId: newSession.sessionId,
      deviceId: sessionData.deviceId,
      ipAddress: sessionData.ipAddress,
      userAgent: sessionData.userAgent,
      reason: 'Secure session created with device fingerprinting',
    })

    return newSession
  }

  /**
   * Validate a session with enhanced security checks
   * SECURITY: Includes device fingerprint validation and session hijacking protection
   */
  async validateSession(
    sessionId: string,
    refreshIfValid: boolean = true,
    requestDeviceInfo?: { userAgent: string; ipAddress: string }
  ): Promise<SessionValidationResult> {
    const validation = await this.sessionRepo.validateSession(sessionId, refreshIfValid)

    if (!validation.isValid || !validation.session) {
      return validation
    }

    // SECURITY: Enhanced validation with device fingerprint check
    if (requestDeviceInfo) {
      const isDeviceValid = await this.validateDeviceSecurity(validation.session, requestDeviceInfo)
      if (!isDeviceValid) {
        console.warn(`🚨 SECURITY: Device fingerprint mismatch for session ${sessionId}`)
        // Invalidate potentially hijacked session
        await this.sessionRepo.invalidateSession(sessionId)
        return {
          isValid: false,
          error: 'Device security validation failed',
        }
      }
    }

    return validation
  }

  /**
   * Validate session and refresh JWT token if needed
   */
  async validateAndRefreshSession(
    sessionId: string,
    _userId: number // Prefixed with _ to indicate intentionally unused for now
  ): Promise<{ isValid: boolean; newToken?: string; session?: SessionData }> {
    const validation = await this.sessionRepo.validateSession(sessionId, true)

    if (!validation.isValid || !validation.session) {
      return { isValid: false }
    }

    // Check if session needs token refresh (within 10 minutes of expiry)
    const now = new Date()
    const tenMinutesFromNow = new Date(now.getTime() + 10 * 60 * 1000)

    if (validation.session.expiresAt <= tenMinutesFromNow) {
      // For now, we'll just indicate that a refresh is needed
      // The actual token generation should be handled by the auth layer
      return {
        isValid: true,
        session: validation.session,
        newToken: 'REFRESH_NEEDED', // Placeholder - actual token generation happens in auth layer
      }
    }

    return {
      isValid: true,
      session: validation.session,
    }
  }

  /**
   * Get session details by session ID
   */
  async getSession(sessionId: string): Promise<SessionData | null> {
    return await this.sessionRepo.getSession(sessionId)
  }

  /**
   * Get all active sessions for a user
   */
  async getUserSessions(userId: number): Promise<SessionData[]> {
    return await this.sessionRepo.getActiveSessionsForUser(userId)
  }

  /**
   * Invalidate a specific session
   */
  async invalidateSession(sessionId: string): Promise<boolean> {
    return await this.sessionRepo.invalidateSession(sessionId)
  }

  /**
   * Force logout a user from all devices
   */
  async forceLogoutUser(userId: number): Promise<number> {
    return await this.sessionRepo.forceLogoutUser(userId)
  }

  /**
   * List sessions with filtering (super_admin only)
   */
  async listSessions(filter?: SessionFilter, requestingUserId?: number): Promise<SessionSummary[]> {
    // Verify requesting user is super_admin
    if (requestingUserId) {
      await this.validateSuperAdminAccess(requestingUserId)
    }

    const sessions = await this.sessionRepo.listSessions(filter)

    // Enrich sessions with user names
    return await this.enrichSessionsWithUserNames(sessions)
  }

  /**
   * Get session statistics (super_admin only)
   */
  async getSessionStats(requestingUserId: number): Promise<{
    totalSessions: number
    activeSessions: number
    expiredSessions: number
    sessionsByRole: Record<string, number>
  }> {
    // Verify requesting user is super_admin
    await this.validateSuperAdminAccess(requestingUserId)

    return await this.sessionRepo.getSessionStats()
  }

  /**
   * Force logout a user by admin (super_admin only)
   */
  async adminForceLogoutUser(targetUserId: number, requestingUserId: number): Promise<number> {
    // Verify requesting user is super_admin
    await this.validateSuperAdminAccess(requestingUserId)

    // Prevent super_admin from logging out themselves
    if (targetUserId === requestingUserId) {
      throw new ValidationError('Cannot force logout yourself')
    }

    return await this.sessionRepo.forceLogoutUser(targetUserId)
  }

  /**
   * Invalidate specific session by admin (super_admin only)
   */
  async adminInvalidateSession(
    sessionId: string,
    requestingUserId: number,
    _currentSessionId?: string // Prefixed with _ to indicate intentionally unused for now
  ): Promise<boolean> {
    // Verify requesting user is super_admin
    await this.validateSuperAdminAccess(requestingUserId)

    // Get session details
    const session = await this.sessionRepo.getSession(sessionId)
    if (!session) {
      throw new NotFoundError('Session not found')
    }

    // Prevent admin from invalidating any of their own sessions
    // This ensures admin cannot accidentally logout themselves
    if (session.userId === requestingUserId) {
      throw new ValidationError('Cannot invalidate your own sessions')
    }

    return await this.sessionRepo.invalidateSession(sessionId)
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    return await this.sessionRepo.cleanupExpiredSessions()
  }

  /**
   * Get user's device sessions
   */
  async getUserDeviceSessions(userId: number): Promise<
    Array<{
      deviceId: string
      sessionId: string
      lastAccessedAt: Date
      deviceType?: string
      browser?: string
    }>
  > {
    return await this.sessionRepo.getUserDeviceSessions(userId)
  }

  /**
   * Validate that a user exists
   */
  private async validateUserExists(userId: number, role: UserRole): Promise<void> {
    if (role === 'student') {
      const student = await this.studentRepo.findById(userId)
      if (!student) {
        throw new NotFoundError('Student not found')
      }
    } else if (
      role === 'admin' ||
      role === 'super_admin' ||
      role === 'teacher' ||
      role === 'receptionist'
    ) {
      const admin = await this.adminRepo.findById(userId)
      if (!admin) {
        throw new NotFoundError('Admin not found')
      }
    }
  }

  /**
   * Validate that the requesting user is a super_admin
   */
  private async validateSuperAdminAccess(userId: number): Promise<void> {
    const admin = await this.adminRepo.findById(userId)
    if (!admin || admin.role !== 'super_admin') {
      throw new AuthenticationError('Super admin access required')
    }
  }

  /**
   * Enrich session summaries with user names (optimized batch fetching)
   */
  private async enrichSessionsWithUserNames(sessions: SessionSummary[]): Promise<SessionSummary[]> {
    if (sessions.length === 0) return []

    // Group sessions by role to batch fetch users
    const studentIds: number[] = []
    const adminIds: number[] = []

    sessions.forEach(session => {
      if (session.role === 'student') {
        studentIds.push(session.userId)
      } else {
        adminIds.push(session.userId)
      }
    })

    // Batch fetch users
    const [students, admins] = await Promise.all([
      studentIds.length > 0 ? this.batchFetchStudents(studentIds) : Promise.resolve(new Map()),
      adminIds.length > 0 ? this.batchFetchAdmins(adminIds) : Promise.resolve(new Map()),
    ])

    // Enrich sessions with user names
    return sessions.map(session => {
      let userName = 'Unknown User'

      if (session.role === 'student') {
        userName = students.get(session.userId)?.name || 'Unknown Student'
      } else {
        userName = admins.get(session.userId)?.name || 'Unknown Admin'
      }

      return {
        ...session,
        userName,
      }
    })
  }

  /**
   * Batch fetch students by IDs
   */
  private async batchFetchStudents(userIds: number[]): Promise<Map<number, { name: string }>> {
    const userMap = new Map<number, { name: string }>()

    try {
      // Use Promise.allSettled to handle individual failures gracefully
      const results = await Promise.allSettled(
        userIds.map(async id => {
          const student = await this.studentRepo.findById(id)
          return { id, student }
        })
      )

      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value.student) {
          userMap.set(result.value.id, { name: result.value.student.name })
        }
      })
    } catch (error) {
      console.error('Error batch fetching students:', error)
    }

    return userMap
  }

  /**
   * Batch fetch admins by IDs
   */
  private async batchFetchAdmins(userIds: number[]): Promise<Map<number, { name: string }>> {
    const userMap = new Map<number, { name: string }>()

    try {
      // Use Promise.allSettled to handle individual failures gracefully
      const results = await Promise.allSettled(
        userIds.map(async id => {
          const admin = await this.adminRepo.findById(id)
          return { id, admin }
        })
      )

      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value.admin) {
          userMap.set(result.value.id, { name: result.value.admin.name })
        }
      })
    } catch (error) {
      console.error('Error batch fetching admins:', error)
    }

    return userMap
  }

  /**
   * Generate device fingerprint for enhanced security
   * SECURITY: Creates unique device identifier for session validation
   */
  private generateDeviceFingerprint(sessionData: CreateSessionDTO): string {
    const crypto = require('crypto')

    // Combine device characteristics for fingerprinting
    const fingerprintData = [
      sessionData.userAgent,
      sessionData.ipAddress,
      sessionData.deviceId,
      sessionData.metadata?.deviceType || 'unknown',
      sessionData.metadata?.browser || 'unknown',
      sessionData.metadata?.os || 'unknown',
    ].join('|')

    // Create secure hash
    return crypto.createHash('sha256').update(fingerprintData).digest('hex')
  }

  /**
   * Validate device security to prevent session hijacking
   * SECURITY: Checks if request comes from the same device as session
   */
  private async validateDeviceSecurity(
    session: SessionData,
    requestInfo: { userAgent: string; ipAddress: string }
  ): Promise<boolean> {
    try {
      // Check user agent consistency (primary security check)
      if (session.userAgent !== requestInfo.userAgent) {
        console.warn(
          `🚨 User agent mismatch: session=${session.userAgent}, request=${requestInfo.userAgent}`
        )
        return false
      }

      // Check IP address (allow some flexibility for mobile networks)
      // Extract network portion for mobile-friendly validation
      const sessionIP = session.ipAddress.split('.').slice(0, 3).join('.')
      const requestIP = requestInfo.ipAddress.split('.').slice(0, 3).join('.')

      if (sessionIP !== requestIP) {
        console.warn(`🚨 IP network mismatch: session=${sessionIP}, request=${requestIP}`)
        // For production, you might want to be more lenient with IP changes
        // return false
      }

      // Validate device fingerprint if available (for sessions created with new security features)
      const sessionMetadata = session.metadata as any // Type assertion for backward compatibility
      if (sessionMetadata?.deviceFingerprint) {
        const currentFingerprint = this.generateDeviceFingerprint({
          userId: session.userId,
          role: session.role,
          deviceId: session.deviceId,
          ipAddress: requestInfo.ipAddress,
          userAgent: requestInfo.userAgent,
          metadata: session.metadata,
        })

        if (sessionMetadata.deviceFingerprint !== currentFingerprint) {
          console.warn(`🚨 Device fingerprint mismatch for session ${session.sessionId}`)
          return false
        }
      }

      return true
    } catch (error) {
      console.error('Device security validation error:', error)
      return false
    }
  }
}
