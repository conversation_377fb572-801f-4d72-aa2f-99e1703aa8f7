/**
 * 🎯 CENTRALIZED ROLE MANAGEMENT SERVICE
 * 
 * Clean Architecture Implementation:
 * - Single source of truth for role-based access control
 * - Domain-driven design with business rules
 * - Type-safe role validation
 * - Centralized permission logic
 */

import { 
  UserRole, 
  USER_ROLES, 
  RolePermissions,
  isValidUserRole,
  isAdminRole,
  isSuperAdminRole,
  AuthorizationError
} from '../types/auth-types'
import { AttendanceType, PRAYER_ATTENDANCE_TYPES, SCHOOL_ATTENDANCE_TYPES, ALL_ATTENDANCE_TYPES } from '../entities/absence'

// ✅ DOMAIN SERVICE: Centralized role management
export class RoleManagementService {
  
  /**
   * ✅ TYPE-SAFE ROLE VALIDATION: No more string comparisons
   */
  validateRole(role: string): UserRole {
    if (!isValidUserRole(role)) {
      throw new AuthorizationError(`Invalid role: ${role}`)
    }
    return role
  }

  /**
   * ✅ PERMISSION CHECKING: Centralized business rules
   */
  getRolePermissions(role: UserRole): RolePermissions {
    switch (role) {
      case USER_ROLES.STUDENT:
        return {
          canAccessAdminPanel: false,
          canManageUsers: false,
          canManageClasses: false,
          canViewReports: false,
          canManageSessions: false,
          canManagePrayerExemptions: false,
          allowedPages: ['/student/home', '/student/profile'],
          allowedAttendanceTypes: ['qr-display']
        }

      case USER_ROLES.ADMIN:
        return {
          canAccessAdminPanel: true,
          canManageUsers: false,
          canManageClasses: false,
          canViewReports: true, // Prayer reports only
          canManageSessions: false,
          canManagePrayerExemptions: false,
          allowedPages: ['/admin/home', '/admin/prayer-reports', '/admin/profile'],
          allowedAttendanceTypes: PRAYER_ATTENDANCE_TYPES
        }

      case USER_ROLES.TEACHER:
        return {
          canAccessAdminPanel: true,
          canManageUsers: false,
          canManageClasses: false,
          canViewReports: true, // School reports only
          canManageSessions: false,
          canManagePrayerExemptions: false,
          allowedPages: ['/admin/home', '/admin/school-reports', '/admin/profile'],
          allowedAttendanceTypes: SCHOOL_ATTENDANCE_TYPES
        }

      case USER_ROLES.RECEPTIONIST:
        return {
          canAccessAdminPanel: true,
          canManageUsers: false,
          canManageClasses: false,
          canViewReports: true, // School reports only
          canManageSessions: false,
          canManagePrayerExemptions: false,
          allowedPages: ['/admin/home', '/admin/school-reports', '/admin/profile'],
          allowedAttendanceTypes: ['masuk', 'ijin', 'sakit'] // Limited manual entry types
        }

      case USER_ROLES.SUPER_ADMIN:
        return {
          canAccessAdminPanel: true,
          canManageUsers: true,
          canManageClasses: true,
          canViewReports: true, // All reports
          canManageSessions: true,
          canManagePrayerExemptions: true,
          allowedPages: ['/admin/*'], // All admin pages
          allowedAttendanceTypes: ALL_ATTENDANCE_TYPES
        }

      default:
        throw new AuthorizationError(`Unknown role: ${role}`)
    }
  }

  /**
   * ✅ PAGE ACCESS CONTROL: Clean business logic
   */
  canAccessPage(role: UserRole, page: string): boolean {
    const permissions = this.getRolePermissions(role)
    
    return permissions.allowedPages.some(pattern => {
      if (pattern.endsWith('/*')) {
        // Wildcard pattern matching
        const basePath = pattern.slice(0, -2)
        return page.startsWith(basePath)
      }
      
      if (pattern.startsWith('!')) {
        // Exclusion pattern
        const excludePattern = pattern.slice(1)
        return !this.matchesPattern(page, excludePattern)
      }
      
      // Exact match
      return page === pattern
    })
  }

  /**
   * ✅ ATTENDANCE TYPE VALIDATION: Business rule enforcement
   */
  canHandleAttendanceType(role: UserRole, attendanceType: AttendanceType): boolean {
    const permissions = this.getRolePermissions(role)
    
    // Special case for QR display (students)
    if (permissions.allowedAttendanceTypes.includes('qr-display')) {
      return attendanceType === 'qr-display'
    }
    
    return permissions.allowedAttendanceTypes.includes(attendanceType)
  }

  /**
   * ✅ MANUAL ENTRY ACCESS: Specific business rules
   */
  hasManualEntryAccess(role: UserRole): boolean {
    // Only receptionist and super admin can do manual entry
    return [USER_ROLES.RECEPTIONIST, USER_ROLES.SUPER_ADMIN].includes(role)
  }

  /**
   * ✅ PRAYER EXEMPTION MANAGEMENT: Security-critical business rule
   */
  canManagePrayerExemptions(role: UserRole): boolean {
    // Only super admin can manage prayer exemptions
    return role === USER_ROLES.SUPER_ADMIN
  }

  /**
   * ✅ ROLE HIERARCHY CHECKING: Clean authorization logic
   */
  hasRequiredRole(userRole: UserRole, requiredRole: string): boolean {
    // Validate required role
    if (!isValidUserRole(requiredRole)) {
      return false
    }

    // Admin roles can access admin endpoints
    if (requiredRole === 'admin') {
      return isAdminRole(userRole)
    }
    
    // Student role for student endpoints
    if (requiredRole === 'student') {
      return userRole === USER_ROLES.STUDENT
    }
    
    // Super admin role for super admin endpoints
    if (requiredRole === 'super_admin') {
      return userRole === USER_ROLES.SUPER_ADMIN
    }
    
    // Exact role match for other cases
    return userRole === requiredRole
  }

  /**
   * ✅ NAVIGATION ITEMS: Role-based UI generation
   */
  getNavigationItems(role: UserRole): Array<{label: string, path: string, icon: string}> {
    const permissions = this.getRolePermissions(role)
    const allNavItems = [
      { label: 'Scanner', path: '/admin/home', icon: 'camera' },
      { label: 'Laporan Shalat', path: '/admin/prayer-reports', icon: 'file-text' },
      { label: 'Laporan Sekolah', path: '/admin/school-reports', icon: 'graduation-cap' },
      { label: 'Kelas', path: '/admin/classes', icon: 'users' },
      { label: 'Siswa', path: '/admin/users', icon: 'user' },
      { label: 'Admin', path: '/admin/admins', icon: 'shield' },
      { label: 'Sesi', path: '/admin/sessions', icon: 'clock' },
      { label: 'Profil', path: '/admin/profile', icon: 'user' }
    ]

    // Filter navigation items based on allowed pages
    return allNavItems.filter(item => 
      this.canAccessPage(role, item.path)
    )
  }

  /**
   * ✅ ROLE VALIDATION FOR API: Centralized endpoint protection
   */
  validateApiAccess(userRole: UserRole, endpoint: string, method: string): void {
    // Define API access rules
    const apiRules = {
      '/api/admin/sessions': [USER_ROLES.SUPER_ADMIN],
      '/api/users': [USER_ROLES.SUPER_ADMIN],
      '/api/admins': [USER_ROLES.SUPER_ADMIN],
      '/api/classes': [USER_ROLES.SUPER_ADMIN],
      '/api/prayer-exemptions': [USER_ROLES.SUPER_ADMIN],
      '/api/absence/reports': [USER_ROLES.ADMIN, USER_ROLES.SUPER_ADMIN, USER_ROLES.TEACHER, USER_ROLES.RECEPTIONIST]
    }

    // Check if endpoint requires specific roles
    for (const [pattern, allowedRoles] of Object.entries(apiRules)) {
      if (endpoint.startsWith(pattern)) {
        if (!allowedRoles.includes(userRole)) {
          throw new AuthorizationError(
            `Access denied. Required roles: ${allowedRoles.join(', ')}`,
            allowedRoles[0]
          )
        }
        return
      }
    }

    // Default: admin roles can access admin APIs, students can access student APIs
    if (endpoint.startsWith('/api/admin/') && !isAdminRole(userRole)) {
      throw new AuthorizationError('Admin access required')
    }

    if (endpoint.startsWith('/api/student/') && userRole !== USER_ROLES.STUDENT) {
      throw new AuthorizationError('Student access required')
    }
  }

  /**
   * ✅ HELPER METHODS: Clean utility functions
   */
  private matchesPattern(path: string, pattern: string): boolean {
    if (pattern.endsWith('/*')) {
      const basePath = pattern.slice(0, -2)
      return path.startsWith(basePath)
    }
    return path === pattern
  }

  /**
   * ✅ ROLE DISPLAY: User-friendly role names
   */
  getRoleDisplayName(role: UserRole): string {
    const displayNames = {
      [USER_ROLES.STUDENT]: 'Siswa',
      [USER_ROLES.ADMIN]: 'Admin',
      [USER_ROLES.SUPER_ADMIN]: 'Super Admin',
      [USER_ROLES.TEACHER]: 'Guru',
      [USER_ROLES.RECEPTIONIST]: 'Resepsionis'
    }
    
    return displayNames[role] || role
  }

  /**
   * ✅ DEFAULT REDIRECT: Role-based landing pages
   */
  getDefaultRedirect(role: UserRole): string {
    const permissions = this.getRolePermissions(role)
    return permissions.allowedPages[0] || '/'
  }
}

// ✅ SINGLETON INSTANCE: Single source of truth
export const roleManagementService = new RoleManagementService()
