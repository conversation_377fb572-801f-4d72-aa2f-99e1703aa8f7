/**
 * 🎯 CENTRALIZED ERROR HANDLING SERVICE
 * 
 * Clean Architecture Implementation:
 * - Domain-driven error handling
 * - Consistent error responses
 * - Security-aware error messages
 * - Proper logging and monitoring
 */

import { NextResponse } from 'next/server'
import { 
  AuthenticationError, 
  AuthorizationError, 
  SessionExpiredError,
  ApiResponse 
} from '../types/auth-types'
import { NotFoundError, ValidationError } from '../errors'
import { z } from 'zod'

// ✅ ERROR CATEGORIES: Domain-specific error classification
export enum ErrorCategory {
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION', 
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT = 'RATE_LIMIT',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  SYSTEM = 'SYSTEM'
}

// ✅ ERROR SEVERITY: For monitoring and alerting
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// ✅ ERROR CONTEXT: Rich error information
export interface ErrorContext {
  category: ErrorCategory
  severity: ErrorSeverity
  userMessage: string
  technicalMessage: string
  statusCode: number
  shouldLog: boolean
  shouldAlert: boolean
  metadata?: Record<string, any>
}

// ✅ DOMAIN SERVICE: Centralized error handling
export class ErrorHandlingService {
  
  /**
   * ✅ MAIN ERROR HANDLER: Single point of error processing
   */
  handleError(error: unknown, context?: Partial<ErrorContext>): NextResponse {
    const errorContext = this.categorizeError(error, context)
    
    // Log error if needed
    if (errorContext.shouldLog) {
      this.logError(error, errorContext)
    }
    
    // Send alert if critical
    if (errorContext.shouldAlert) {
      this.sendAlert(error, errorContext)
    }
    
    // Return appropriate response
    return this.createErrorResponse(errorContext)
  }

  /**
   * ✅ ERROR CATEGORIZATION: Smart error classification
   */
  private categorizeError(error: unknown, context?: Partial<ErrorContext>): ErrorContext {
    // Authentication errors
    if (error instanceof AuthenticationError) {
      return {
        category: ErrorCategory.AUTHENTICATION,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Authentication failed. Please login again.',
        technicalMessage: error.message,
        statusCode: 401,
        shouldLog: true,
        shouldAlert: false,
        ...context
      }
    }

    // Authorization errors
    if (error instanceof AuthorizationError) {
      return {
        category: ErrorCategory.AUTHORIZATION,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'You do not have permission to perform this action.',
        technicalMessage: error.message,
        statusCode: 403,
        shouldLog: true,
        shouldAlert: false,
        metadata: { requiredRole: error.requiredRole },
        ...context
      }
    }

    // Session expired errors
    if (error instanceof SessionExpiredError) {
      return {
        category: ErrorCategory.AUTHENTICATION,
        severity: ErrorSeverity.LOW,
        userMessage: 'Your session has expired. Please login again.',
        technicalMessage: error.message,
        statusCode: 401,
        shouldLog: false,
        shouldAlert: false,
        ...context
      }
    }

    // Validation errors (Zod)
    if (error instanceof z.ZodError) {
      return {
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.LOW,
        userMessage: 'Invalid input data provided.',
        technicalMessage: this.formatZodError(error),
        statusCode: 400,
        shouldLog: false,
        shouldAlert: false,
        metadata: { validationErrors: error.errors },
        ...context
      }
    }

    // Domain validation errors
    if (error instanceof ValidationError) {
      return {
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.LOW,
        userMessage: error.message,
        technicalMessage: error.message,
        statusCode: 400,
        shouldLog: false,
        shouldAlert: false,
        ...context
      }
    }

    // Not found errors
    if (error instanceof NotFoundError) {
      return {
        category: ErrorCategory.NOT_FOUND,
        severity: ErrorSeverity.LOW,
        userMessage: 'The requested resource was not found.',
        technicalMessage: error.message,
        statusCode: 404,
        shouldLog: false,
        shouldAlert: false,
        ...context
      }
    }

    // Rate limit errors
    if (this.isRateLimitError(error)) {
      return {
        category: ErrorCategory.RATE_LIMIT,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Too many requests. Please try again later.',
        technicalMessage: this.extractErrorMessage(error),
        statusCode: 429,
        shouldLog: true,
        shouldAlert: false,
        ...context
      }
    }

    // System errors (unknown)
    return {
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.HIGH,
      userMessage: 'An unexpected error occurred. Please try again.',
      technicalMessage: this.extractErrorMessage(error),
      statusCode: 500,
      shouldLog: true,
      shouldAlert: true,
      ...context
    }
  }

  /**
   * ✅ ERROR RESPONSE CREATION: Consistent API responses
   */
  private createErrorResponse(context: ErrorContext): NextResponse {
    const response: ApiResponse = {
      success: false,
      error: context.userMessage,
      message: context.userMessage,
      timestamp: new Date().toISOString()
    }

    // Add technical details in development
    if (process.env.NODE_ENV === 'development') {
      response.data = {
        category: context.category,
        severity: context.severity,
        technical: context.technicalMessage,
        metadata: context.metadata
      }
    }

    return NextResponse.json(response, { status: context.statusCode })
  }

  /**
   * ✅ ERROR LOGGING: Structured logging
   */
  private logError(error: unknown, context: ErrorContext): void {
    const logData = {
      timestamp: new Date().toISOString(),
      category: context.category,
      severity: context.severity,
      message: context.technicalMessage,
      statusCode: context.statusCode,
      stack: error instanceof Error ? error.stack : undefined,
      metadata: context.metadata
    }

    // Use appropriate log level based on severity
    switch (context.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL ERROR:', logData)
        break
      case ErrorSeverity.HIGH:
        console.error('❌ HIGH SEVERITY ERROR:', logData)
        break
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️ MEDIUM SEVERITY ERROR:', logData)
        break
      case ErrorSeverity.LOW:
        console.info('ℹ️ LOW SEVERITY ERROR:', logData)
        break
    }
  }

  /**
   * ✅ ALERT SYSTEM: Critical error notifications
   */
  private sendAlert(error: unknown, context: ErrorContext): void {
    // In production, this would integrate with monitoring services
    // like Sentry, DataDog, or custom alerting systems
    
    if (process.env.NODE_ENV === 'production') {
      // TODO: Integrate with monitoring service
      console.error('🚨 ALERT: Critical error occurred', {
        category: context.category,
        message: context.technicalMessage,
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * ✅ UTILITY METHODS: Helper functions
   */
  private formatZodError(error: z.ZodError): string {
    return error.errors
      .map(err => `${err.path.join('.')}: ${err.message}`)
      .join(', ')
  }

  private isRateLimitError(error: unknown): boolean {
    const message = this.extractErrorMessage(error).toLowerCase()
    return message.includes('rate limit') || 
           message.includes('too many') || 
           message.includes('try again later')
  }

  private extractErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message
    }
    if (typeof error === 'string') {
      return error
    }
    return 'Unknown error occurred'
  }

  /**
   * ✅ SPECIALIZED ERROR HANDLERS: Domain-specific handling
   */
  handleAuthenticationError(error: unknown): NextResponse {
    return this.handleError(error, {
      category: ErrorCategory.AUTHENTICATION,
      severity: ErrorSeverity.MEDIUM
    })
  }

  handleValidationError(error: unknown): NextResponse {
    return this.handleError(error, {
      category: ErrorCategory.VALIDATION,
      severity: ErrorSeverity.LOW
    })
  }

  handleBusinessLogicError(error: unknown, userMessage?: string): NextResponse {
    return this.handleError(error, {
      category: ErrorCategory.BUSINESS_LOGIC,
      severity: ErrorSeverity.MEDIUM,
      userMessage: userMessage || 'Business rule violation occurred.'
    })
  }

  /**
   * ✅ SUCCESS RESPONSE HELPER: Consistent success responses
   */
  createSuccessResponse<T>(data: T, message?: string): NextResponse {
    const response: ApiResponse<T> = {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(response)
  }

  /**
   * ✅ PAGINATED RESPONSE HELPER: Consistent pagination
   */
  createPaginatedResponse<T>(
    data: T[],
    pagination: {
      page: number
      limit: number
      total: number
    },
    message?: string
  ): NextResponse {
    const response = {
      success: true,
      data,
      message,
      pagination: {
        ...pagination,
        totalPages: Math.ceil(pagination.total / pagination.limit)
      },
      timestamp: new Date().toISOString()
    }

    return NextResponse.json(response)
  }
}

// ✅ SINGLETON INSTANCE: Single source of truth
export const errorHandlingService = new ErrorHandlingService()

// ✅ CONVENIENCE EXPORTS: Easy to use helpers
export const handleError = (error: unknown, context?: Partial<ErrorContext>) => 
  errorHandlingService.handleError(error, context)

export const handleAuthError = (error: unknown) => 
  errorHandlingService.handleAuthenticationError(error)

export const handleValidationError = (error: unknown) => 
  errorHandlingService.handleValidationError(error)

export const createSuccessResponse = <T>(data: T, message?: string) => 
  errorHandlingService.createSuccessResponse(data, message)

export const createPaginatedResponse = <T>(
  data: T[],
  pagination: { page: number; limit: number; total: number },
  message?: string
) => errorHandlingService.createPaginatedResponse(data, pagination, message)
