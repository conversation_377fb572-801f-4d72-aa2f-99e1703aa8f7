/**
 * Attendance type enum - SINGLE SOURCE OF TRUTH
 */
export enum AttendanceType {
  // Prayer attendance (existing)
  ZUHR = 'Zuhr',
  ASR = 'Asr',
  IJIN = 'Ijin',

  // Prayer permissions (new) - specific prayer exemptions
  IJIN_ZUHR = 'Ijin Zuhr',
  IJIN_ASR = 'Ijin Asr',

  // Departure (existing)
  DISMISSAL = 'Pulang',

  // School attendance (new)
  ENTRY = 'Entry',
  LATE_ENTRY = 'Late Entry',
  EXCUSED_ABSENCE = 'Excused Absence',
  TEMPORARY_LEAVE = 'Temporary Leave',
  RETURN_FROM_LEAVE = 'Return from Leave',
  SICK = 'Sick',
}

/**
 * SINGLE SOURCE OF TRUTH: Prayer attendance types
 * Used for prayer reports and admin role permissions
 * Note: IJIN_ZUHR and IJIN_ASR are excluded from direct scanning
 * They are only used internally in temporary leave workflow
 */
export const PRAYER_ATTENDANCE_TYPES = [
  AttendanceType.ZUHR,
  AttendanceType.ASR,
  AttendanceType.DISMISSAL, // Shared between prayer and school
  AttendanceType.IJIN,
  // Note: IJIN_ZUHR and <PERSON><PERSON><PERSON>_ASR are NOT included here
  // They are only used internally during temporary leave workflow
] as const

/**
 * SINGLE SOURCE OF TRUTH: All prayer-related attendance types
 * Including workflow-specific types for reporting purposes
 */
export const ALL_PRAYER_ATTENDANCE_TYPES = [
  ...PRAYER_ATTENDANCE_TYPES,
  AttendanceType.IJIN_ZUHR, // For reporting and Excel export
  AttendanceType.IJIN_ASR, // For reporting and Excel export
] as const

/**
 * SINGLE SOURCE OF TRUTH: School attendance types
 * Used for school reports and teacher/receptionist role permissions
 */
export const SCHOOL_ATTENDANCE_TYPES = [
  AttendanceType.ENTRY,
  AttendanceType.LATE_ENTRY,
  AttendanceType.DISMISSAL, // Shared between prayer and school
  AttendanceType.EXCUSED_ABSENCE,
  AttendanceType.TEMPORARY_LEAVE,
  AttendanceType.RETURN_FROM_LEAVE,
  AttendanceType.SICK,
] as const

/**
 * SINGLE SOURCE OF TRUTH: All attendance types
 * Union of all prayer types (including workflow types) and school types
 */
export const ALL_ATTENDANCE_TYPES = [
  ...new Set([...ALL_PRAYER_ATTENDANCE_TYPES, ...SCHOOL_ATTENDANCE_TYPES]),
] as AttendanceType[]

/**
 * SINGLE SOURCE OF TRUTH: Get attendance types by report type
 * This ensures consistency across the entire application
 */
export function getAttendanceTypesByReportType(
  reportType: 'prayer' | 'school' | 'all'
): AttendanceType[] {
  switch (reportType) {
    case 'prayer':
      // For reporting purposes, include all prayer-related types (including workflow types)
      return [...ALL_PRAYER_ATTENDANCE_TYPES]
    case 'school':
      return [...SCHOOL_ATTENDANCE_TYPES]
    case 'all':
      return [...ALL_ATTENDANCE_TYPES]
    default:
      console.warn(`⚠️ WARNING: Unknown reportType "${reportType}", returning empty array`)
      return []
  }
}

/**
 * SINGLE SOURCE OF TRUTH: Get attendance type values as strings
 * For use in database queries with inArray()
 */
export function getAttendanceTypeValues(reportType: 'prayer' | 'school' | 'all'): string[] {
  return getAttendanceTypesByReportType(reportType).map(type => type.valueOf())
}

/**
 * SINGLE SOURCE OF TRUTH: Attendance type labels for UI display
 * Maps attendance types to Indonesian labels
 */
export const ATTENDANCE_TYPE_LABELS: Record<AttendanceType, string> = {
  // Prayer attendance
  [AttendanceType.ZUHR]: 'Shalat Zuhur',
  [AttendanceType.ASR]: 'Shalat Ashar',
  [AttendanceType.IJIN]: 'Izin Tidak Shalat',

  // Prayer permissions (specific)
  [AttendanceType.IJIN_ZUHR]: 'Izin Shalat Zuhur',
  [AttendanceType.IJIN_ASR]: 'Izin Shalat Ashar',

  // Departure
  [AttendanceType.DISMISSAL]: 'Pulang',

  // School attendance
  [AttendanceType.ENTRY]: 'Masuk',
  [AttendanceType.LATE_ENTRY]: 'Masuk Terlambat',
  [AttendanceType.EXCUSED_ABSENCE]: 'Izin',
  [AttendanceType.TEMPORARY_LEAVE]: 'Izin Sementara',
  [AttendanceType.RETURN_FROM_LEAVE]: 'Kembali dari Izin',
  [AttendanceType.SICK]: 'Sakit',
} as const

/**
 * SINGLE SOURCE OF TRUTH: Get attendance type label
 * Returns Indonesian label for given attendance type
 */
export function getAttendanceTypeLabel(attendanceType: AttendanceType | string): string {
  if (attendanceType === 'qr-display') {
    return 'Tampilkan QR Code'
  }

  return ATTENDANCE_TYPE_LABELS[attendanceType as AttendanceType] || attendanceType
}

/**
 * Absence entity representing a student's attendance record
 */
export interface Absence {
  id: number
  uniqueCode: string // References Student.uniqueCode
  type: AttendanceType
  recordedAt: Date // Timestamp when the attendance was recorded
  createdAt: Date
  reason?: string // Reason for attendance (required for TEMPORARY_LEAVE, EXCUSED_ABSENCE, SICK)

  // ✅ HISTORICAL CLASS TRACKING: Class context at time of attendance
  // These fields preserve the student's class when attendance was recorded
  // Prevents data loss when students change classes (grade progression)
  classId?: number // Foreign key to classes table (nullable for backward compatibility)
  className?: string // Class name at time of attendance (nullable for backward compatibility)
}

/**
 * Data required to create a new absence record
 */
export interface CreateAbsenceDTO {
  uniqueCode: string
  type: AttendanceType
  recordedAt: Date
  reason?: string // Optional reason for attendance

  // ✅ HISTORICAL CLASS TRACKING: Class context for new attendance records
  // These fields should be populated when creating new attendance records
  // to preserve class information at time of recording
  classId?: number // Class ID at time of attendance (optional for backward compatibility)
  className?: string // Class name at time of attendance (optional for backward compatibility)
}

/**
 * Attendance summary for reporting
 */
export interface AttendanceSummary {
  summaryDate: Date | string
  uniqueCode: string
  name: string
  className: string
  nis?: string
  gender?: string

  // Prayer attendance fields
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null

  // Prayer permission fields (specific)
  ijinZuhr: boolean
  ijinZuhrTime?: string | null
  ijinAsr: boolean
  ijinAsrTime?: string | null

  // School attendance fields
  entry?: boolean
  entryTime?: string | null
  lateEntry?: boolean
  lateEntryTime?: string | null
  excusedAbsence?: boolean
  excusedAbsenceTime?: string | null
  temporaryLeave?: boolean
  temporaryLeaveTime?: string | null
  returnFromLeave?: boolean
  returnFromLeaveTime?: string | null
  sick?: boolean
  sickTime?: string | null

  // Reason field for attendance types that require it
  reason?: string | null

  // For weekly reports
  weeklyRecords?: any[]

  // For aggregated reports (30 days, monthly, yearly)
  aggregatedCounts?: {
    zuhr: number
    asr: number
    dismissal: number
    ijin: number
    ijinZuhr: number
    ijinAsr: number
    entry: number
    lateEntry: number
    excusedAbsence: number
    temporaryLeave: number
    returnFromLeave: number
    sick: number
  }

  updatedAt?: Date
}
