/**
 * Prayer Exemption Entity - SSOT for prayer exemption types and validation
 * Clean Architecture: Domain Entity Layer
 */

import { AttendanceType, getAttendanceTypeLabel } from './absence'
import { formatDateToString } from '@/lib/utils/date'

/**
 * Prayer exemption type enum - SINGLE SOURCE OF TRUTH
 */
export enum PrayerExemptionType {
  GLOBAL = 'global', // All students exempted
}

/**
 * Prayer type enum - SINGLE SOURCE OF TRUTH
 * Uses AttendanceType from absence.ts to maintain consistency
 */
export enum PrayerType {
  ZUHR = 'zuhr',
  ASR = 'asr',
  BOTH = 'both',
}

/**
 * Helper function to convert AttendanceType to PrayerType
 */
export function attendanceTypeToPrayerType(attendanceType: AttendanceType): PrayerType | null {
  switch (attendanceType) {
    case AttendanceType.ZUHR:
      return PrayerType.ZUHR
    case AttendanceType.ASR:
      return PrayerType.ASR
    default:
      return null
  }
}

/**
 * Helper function to convert PrayerType to AttendanceType
 */
export function prayerTypeToAttendanceType(prayerType: PrayerType): AttendanceType[] {
  switch (prayerType) {
    case PrayerType.ZUHR:
      return [AttendanceType.ZUHR]
    case PrayerType.ASR:
      return [AttendanceType.ASR]
    case PrayerType.BOTH:
      return [AttendanceType.ZUHR, AttendanceType.ASR]
    default:
      return []
  }
}

/**
 * Recurrence pattern enum - SINGLE SOURCE OF TRUTH
 */
export enum RecurrencePattern {
  ONCE = 'once', // One-time exemption
  WEEKLY = 'weekly', // Weekly recurring (specific days of week)
  CUSTOM = 'custom', // Custom pattern
}

/**
 * Days of the week enum - SINGLE SOURCE OF TRUTH
 */
export enum DayOfWeek {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday',
}

/**
 * Prayer Exemption domain entity - supports both one-time and recurring exemptions
 */
export interface PrayerExemption {
  id: number
  exemptionType: PrayerExemptionType
  targetId: string | null // Always null for GLOBAL exemptions (kept for backward compatibility)

  // For one-time exemptions
  exemptionDate: Date | null // null for recurring exemptions

  // For recurring exemptions
  recurrencePattern: RecurrencePattern
  startDate: Date | null // start date for recurring exemptions
  endDate: Date | null // end date for recurring exemptions (null = indefinite)
  daysOfWeek: DayOfWeek[] | null // days for weekly pattern

  prayerType: PrayerType
  reason: string
  createdBy: number // user ID of admin who created
  createdAt: Date
  isActive: boolean
}

/**
 * DTO for creating prayer exemption - supports both one-time and recurring
 */
export interface CreatePrayerExemptionDTO {
  exemptionType: PrayerExemptionType
  targetId?: string | null // Always null for GLOBAL exemptions

  // For one-time exemptions
  exemptionDate?: Date | null

  // For recurring exemptions
  recurrencePattern: RecurrencePattern
  startDate?: Date | null
  endDate?: Date | null
  daysOfWeek?: DayOfWeek[] | null

  prayerType: PrayerType
  reason: string
  createdBy: number
}

/**
 * DTO for updating prayer exemption
 */
export interface UpdatePrayerExemptionDTO {
  reason?: string
  isActive?: boolean
}

/**
 * SSOT: Get prayer exemption type label
 */
export function getPrayerExemptionTypeLabel(type: PrayerExemptionType): string {
  switch (type) {
    case PrayerExemptionType.GLOBAL:
      return 'Semua Siswa'
    default:
      return 'Unknown'
  }
}

/**
 * SSOT: Get prayer type label
 * Uses AttendanceType labels for consistency
 */
export function getPrayerTypeLabel(type: PrayerType): string {
  switch (type) {
    case PrayerType.ZUHR:
      return getAttendanceTypeLabel(AttendanceType.ZUHR)
    case PrayerType.ASR:
      return getAttendanceTypeLabel(AttendanceType.ASR)
    case PrayerType.BOTH:
      return `${getAttendanceTypeLabel(AttendanceType.ZUHR)} & ${getAttendanceTypeLabel(AttendanceType.ASR)}`
    default:
      return 'Unknown'
  }
}

/**
 * SSOT: All prayer exemption types
 */
export const ALL_PRAYER_EXEMPTION_TYPES = [PrayerExemptionType.GLOBAL] as const

/**
 * SSOT: All prayer types
 */
export const ALL_PRAYER_TYPES = [PrayerType.ZUHR, PrayerType.ASR, PrayerType.BOTH] as const

/**
 * SSOT: All recurrence patterns
 */
export const ALL_RECURRENCE_PATTERNS = [
  RecurrencePattern.ONCE,
  RecurrencePattern.WEEKLY,
  RecurrencePattern.CUSTOM,
] as const

/**
 * SSOT: All days of week
 */
export const ALL_DAYS_OF_WEEK = [
  DayOfWeek.MONDAY,
  DayOfWeek.TUESDAY,
  DayOfWeek.WEDNESDAY,
  DayOfWeek.THURSDAY,
  DayOfWeek.FRIDAY,
  DayOfWeek.SATURDAY,
  DayOfWeek.SUNDAY,
] as const

/**
 * Validation: Check if exemption applies to a student
 */
export function doesExemptionApplyToStudent(exemption: PrayerExemption): boolean {
  // Since we only support GLOBAL exemptions now, all exemptions apply to all students
  return exemption.exemptionType === PrayerExemptionType.GLOBAL
}

/**
 * Validation: Check if exemption covers specific prayer type
 * Uses SSOT from AttendanceType for consistency
 */
export function doesExemptionCoverPrayer(
  exemption: PrayerExemption,
  prayerType: AttendanceType.ZUHR | AttendanceType.ASR
): boolean {
  const exemptionAttendanceTypes = prayerTypeToAttendanceType(exemption.prayerType)

  return exemptionAttendanceTypes.includes(prayerType)
}

/**
 * SSOT: Get recurrence pattern label
 */
export function getRecurrencePatternLabel(pattern: RecurrencePattern): string {
  switch (pattern) {
    case RecurrencePattern.ONCE:
      return 'Sekali'
    case RecurrencePattern.WEEKLY:
      return 'Mingguan'
    case RecurrencePattern.CUSTOM:
      return 'Kustom'
    default:
      return 'Unknown'
  }
}

/**
 * SSOT: Get day of week label
 */
export function getDayOfWeekLabel(day: DayOfWeek): string {
  switch (day) {
    case DayOfWeek.MONDAY:
      return 'Senin'
    case DayOfWeek.TUESDAY:
      return 'Selasa'
    case DayOfWeek.WEDNESDAY:
      return 'Rabu'
    case DayOfWeek.THURSDAY:
      return 'Kamis'
    case DayOfWeek.FRIDAY:
      return 'Jumat'
    case DayOfWeek.SATURDAY:
      return 'Sabtu'
    case DayOfWeek.SUNDAY:
      return 'Minggu'
    default:
      return 'Unknown'
  }
}

/**
 * Helper: Check if a date falls on specific days of week
 */
export function isDateOnDaysOfWeek(date: Date, daysOfWeek: DayOfWeek[]): boolean {
  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  const dayOfWeek = dayNames[date.getDay()] as DayOfWeek
  return daysOfWeek.includes(dayOfWeek)
}

/**
 * Helper: Check if a date falls within a recurring exemption
 */
export function isDateInRecurringExemption(date: Date, exemption: PrayerExemption): boolean {
  if (!exemption.isActive) return false

  // For one-time exemptions
  if (exemption.recurrencePattern === RecurrencePattern.ONCE) {
    if (!exemption.exemptionDate) return false
    // FIXED: Use SSOT timezone-aware date comparison
    return formatDateToString(date) === formatDateToString(exemption.exemptionDate)
  }

  // For recurring exemptions
  if (!exemption.startDate) return false

  // Check if date is within the range
  if (date < exemption.startDate) return false
  if (exemption.endDate && date > exemption.endDate) return false

  // Check pattern-specific logic
  switch (exemption.recurrencePattern) {
    case RecurrencePattern.WEEKLY:
      return exemption.daysOfWeek ? isDateOnDaysOfWeek(date, exemption.daysOfWeek) : false

    case RecurrencePattern.CUSTOM:
      // Custom logic can be implemented here
      return false

    default:
      return false
  }
}
