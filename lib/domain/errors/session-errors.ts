/**
 * Session Domain Errors
 * Defines specific error types for session-related operations
 * Implements Clean Architecture error handling patterns
 */

/**
 * Base domain error class
 */
export abstract class DomainError extends Error {
  abstract readonly code: string
  abstract readonly statusCode: number
  
  constructor(
    message: string,
    public readonly context?: Record<string, any>
  ) {
    super(message)
    this.name = this.constructor.name
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor)
    }
  }

  /**
   * Convert error to JSON for API responses
   */
  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      context: this.context,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Session not found error
 */
export class SessionNotFoundError extends DomainError {
  readonly code = 'SESSION_NOT_FOUND'
  readonly statusCode = 404

  constructor(sessionId?: string, context?: Record<string, any>) {
    const message = sessionId 
      ? `Session ${sessionId} not found`
      : 'Session not found'
    
    super(message, { sessionId, ...context })
  }
}

/**
 * Session expired error
 */
export class SessionExpiredError extends DomainError {
  readonly code = 'SESSION_EXPIRED'
  readonly statusCode = 401

  constructor(sessionId?: string, context?: Record<string, any>) {
    const message = sessionId 
      ? `Session ${sessionId} has expired`
      : 'Session has expired'
    
    super(message, { sessionId, ...context })
  }
}

/**
 * Session security violation error
 */
export class SessionSecurityError extends DomainError {
  readonly code = 'SESSION_SECURITY_VIOLATION'
  readonly statusCode = 403

  constructor(message: string, context?: Record<string, any>) {
    super(message, context)
  }
}

/**
 * Session validation error
 */
export class SessionValidationError extends DomainError {
  readonly code = 'SESSION_VALIDATION_ERROR'
  readonly statusCode = 400

  constructor(message: string, context?: Record<string, any>) {
    super(message, context)
  }
}

/**
 * Session creation error
 */
export class SessionCreationError extends DomainError {
  readonly code = 'SESSION_CREATION_ERROR'
  readonly statusCode = 500

  constructor(message: string, context?: Record<string, any>) {
    super(message, context)
  }
}

/**
 * Session limit exceeded error
 */
export class SessionLimitExceededError extends DomainError {
  readonly code = 'SESSION_LIMIT_EXCEEDED'
  readonly statusCode = 429

  constructor(userId: number, limit: number, context?: Record<string, any>) {
    const message = `User ${userId} has exceeded session limit of ${limit}`
    super(message, { userId, limit, ...context })
  }
}

/**
 * Device fingerprint mismatch error
 */
export class DeviceFingerprintMismatchError extends DomainError {
  readonly code = 'DEVICE_FINGERPRINT_MISMATCH'
  readonly statusCode = 403

  constructor(sessionId: string, context?: Record<string, any>) {
    const message = `Device fingerprint mismatch for session ${sessionId}`
    super(message, { sessionId, ...context })
  }
}

/**
 * Session repository error
 */
export class SessionRepositoryError extends DomainError {
  readonly code = 'SESSION_REPOSITORY_ERROR'
  readonly statusCode = 500

  constructor(operation: string, cause?: Error, context?: Record<string, any>) {
    const message = `Session repository operation '${operation}' failed`
    super(message, { operation, cause: cause?.message, ...context })
  }
}

/**
 * Session cache error
 */
export class SessionCacheError extends DomainError {
  readonly code = 'SESSION_CACHE_ERROR'
  readonly statusCode = 500

  constructor(operation: string, key?: string, cause?: Error, context?: Record<string, any>) {
    const message = `Session cache operation '${operation}' failed`
    super(message, { operation, key, cause: cause?.message, ...context })
  }
}

/**
 * Type guard to check if error is a session domain error
 */
export function isSessionDomainError(error: any): error is DomainError {
  return error instanceof DomainError
}

/**
 * Type guard for specific session errors
 */
export function isSessionNotFoundError(error: any): error is SessionNotFoundError {
  return error instanceof SessionNotFoundError
}

export function isSessionExpiredError(error: any): error is SessionExpiredError {
  return error instanceof SessionExpiredError
}

export function isSessionSecurityError(error: any): error is SessionSecurityError {
  return error instanceof SessionSecurityError
}

/**
 * Error factory for creating session errors
 */
export class SessionErrorFactory {
  static notFound(sessionId?: string, context?: Record<string, any>): SessionNotFoundError {
    return new SessionNotFoundError(sessionId, context)
  }

  static expired(sessionId?: string, context?: Record<string, any>): SessionExpiredError {
    return new SessionExpiredError(sessionId, context)
  }

  static security(message: string, context?: Record<string, any>): SessionSecurityError {
    return new SessionSecurityError(message, context)
  }

  static validation(message: string, context?: Record<string, any>): SessionValidationError {
    return new SessionValidationError(message, context)
  }

  static creation(message: string, context?: Record<string, any>): SessionCreationError {
    return new SessionCreationError(message, context)
  }

  static limitExceeded(userId: number, limit: number, context?: Record<string, any>): SessionLimitExceededError {
    return new SessionLimitExceededError(userId, limit, context)
  }

  static deviceMismatch(sessionId: string, context?: Record<string, any>): DeviceFingerprintMismatchError {
    return new DeviceFingerprintMismatchError(sessionId, context)
  }

  static repository(operation: string, cause?: Error, context?: Record<string, any>): SessionRepositoryError {
    return new SessionRepositoryError(operation, cause, context)
  }

  static cache(operation: string, key?: string, cause?: Error, context?: Record<string, any>): SessionCacheError {
    return new SessionCacheError(operation, key, cause, context)
  }
}

/**
 * Error handler utility for consistent error responses
 */
export class SessionErrorHandler {
  static handle(error: any): { statusCode: number; body: any } {
    if (isSessionDomainError(error)) {
      return {
        statusCode: error.statusCode,
        body: {
          success: false,
          error: error.toJSON()
        }
      }
    }

    // Handle unknown errors
    console.error('Unknown session error:', error)
    return {
      statusCode: 500,
      body: {
        success: false,
        error: {
          name: 'InternalServerError',
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          statusCode: 500,
          timestamp: new Date().toISOString()
        }
      }
    }
  }
}

export default {
  SessionNotFoundError,
  SessionExpiredError,
  SessionSecurityError,
  SessionValidationError,
  SessionCreationError,
  SessionLimitExceededError,
  DeviceFingerprintMismatchError,
  SessionRepositoryError,
  SessionCacheError,
  SessionErrorFactory,
  SessionErrorHandler,
  isSessionDomainError,
  isSessionNotFoundError,
  isSessionExpiredError,
  isSessionSecurityError
}
