/**
 * 🎯 CENTRALIZED AUTHENTICATION TYPES
 *
 * Clean Architecture Implementation:
 * - Type-safe role definitions
 * - Centralized authentication interfaces
 * - Domain-driven design principles
 * - No more 'as any' type casting needed
 */

// ✅ CORE DOMAIN TYPES: Centralized and type-safe
export const USER_ROLES = {
  STUDENT: 'student',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin',
  TEACHER: 'teacher',
  RECEPTIONIST: 'receptionist',
} as const

export type UserRole = (typeof USER_ROLES)[keyof typeof USER_ROLES]

// ✅ TYPE GUARDS: Safe type checking
export function isValidUserRole(role: string): role is UserRole {
  return Object.values(USER_ROLES).includes(role as UserRole)
}

export function isAdminRole(role: UserRole): boolean {
  return [
    USER_ROLES.ADMIN,
    USER_ROLES.SUPER_ADMIN,
    USER_ROLES.TEACHER,
    USER_ROLES.RECEPTIONIST,
  ].includes(role)
}

export function isSuperAdminRole(role: UserRole): boolean {
  return role === USER_ROLES.SUPER_ADMIN
}

// ✅ AUTHENTICATION RESULT TYPES: Consistent across all layers
export interface AuthenticationResult {
  isValid: boolean
  userId?: number
  role?: UserRole
  sessionId?: string
  deviceId?: string
  error?: string
  needsRefresh?: boolean
}

export interface LoginResult {
  success: boolean
  user?: {
    id: number
    username: string
    role: UserRole
    name?: string
  }
  tokens?: {
    accessToken: string
    refreshToken: string
    sessionId: string
    expiresIn: number
  }
  error?: string
}

// ✅ SESSION TYPES: Clean domain models
export interface SessionInfo {
  sessionId: string
  userId: number
  role: UserRole
  deviceId: string
  ipAddress: string
  userAgent: string
  createdAt: Date
  lastAccessedAt: Date
  expiresAt: Date
  isActive: boolean
}

export interface DeviceInfo {
  deviceId: string
  userAgent: string
  ipAddress: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  browser: string
}

// ✅ PERMISSION TYPES: Role-based access control
export interface RolePermissions {
  canAccessAdminPanel: boolean
  canManageUsers: boolean
  canManageClasses: boolean
  canViewReports: boolean
  canManageSessions: boolean
  canManagePrayerExemptions: boolean
  allowedPages: string[]
  allowedAttendanceTypes: string[]
}

// ✅ ERROR TYPES: Domain-specific errors
export class AuthenticationError extends Error {
  constructor(
    message: string,
    public code?: string
  ) {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends Error {
  constructor(
    message: string,
    public requiredRole?: UserRole
  ) {
    super(message)
    this.name = 'AuthorizationError'
  }
}

export class SessionExpiredError extends Error {
  constructor(message: string = 'Session has expired') {
    super(message)
    this.name = 'SessionExpiredError'
  }
}

// ✅ CONSTANTS: No more magic numbers
export const AUTH_CONSTANTS = {
  // Token expiry times (in seconds)
  ACCESS_TOKEN_EXPIRY: 30 * 60, // 30 minutes
  REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60, // 7 days
  SESSION_EXPIRY: 24 * 60 * 60, // 24 hours

  // Security settings
  MAX_LOGIN_ATTEMPTS: 5,
  LOGIN_LOCKOUT_DURATION: 15 * 60, // 15 minutes

  // Cookie settings
  COOKIE_NAMES: {
    ADMIN_AUTH: 'admin_auth_token',
    STUDENT_AUTH: 'student_auth_token',
  },

  // Redis prefixes
  REDIS_PREFIXES: {
    SESSION: 'session:',
    USER_SESSION: 'user_session:',
    DEVICE_SESSION: 'device_session:',
    REFRESH_TOKEN: 'refresh:',
    LOGIN_ATTEMPTS: 'login_attempts:',
  },
} as const

// ✅ VALIDATION SCHEMAS: Input validation types
export interface LoginRequest {
  username: string
  password: string
  deviceInfo?: Partial<DeviceInfo>
}

export interface RefreshTokenRequest {
  refreshToken: string
  sessionId: string
}

export interface LogoutRequest {
  sessionId: string
  logoutAllDevices?: boolean
}

// ✅ API RESPONSE TYPES: Consistent API contracts
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  timestamp: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// ✅ UTILITY TYPES: Helper types for better DX
export type RequireAuth<T> = T & {
  user: {
    id: number
    role: UserRole
    sessionId: string
  }
}

export type AdminOnly<T> = RequireAuth<T> & {
  user: {
    role: Exclude<UserRole, 'student'>
  }
}

export type SuperAdminOnly<T> = RequireAuth<T> & {
  user: {
    role: 'super_admin'
  }
}

// ✅ EXPORT ALL TYPES: Single source of truth
export type {
  UserRole,
  AuthenticationResult,
  LoginResult,
  SessionInfo,
  DeviceInfo,
  RolePermissions,
  LoginRequest,
  RefreshTokenRequest,
  LogoutRequest,
  ApiResponse,
  PaginatedResponse,
  RequireAuth,
  AdminOnly,
  SuperAdminOnly,
}

// ✅ All exports are already declared above with individual export statements
