/**
 * Unified Session Service
 * Consolidates all session management logic into a single service
 * Implements Clean Architecture principles with proper separation of concerns
 */

import { SessionRepository } from '../domain/repositories/session-repository'
import { SessionUseCases } from '../domain/usecases/session'
import { 
  SessionData, 
  SessionValidationR<PERSON>ult, 
  CreateSessionDTO 
} from '../domain/entities/session'
import { UserRole } from '../config/role-permissions'
import { SessionSecurityError, SessionExpiredError, SessionNotFoundError } from '../domain/errors/session-errors'

export interface SessionServiceConfig {
  maxConcurrentSessions: number
  sessionTTL: number
  refreshThreshold: number
  securityChecks: boolean
}

export class SessionService {
  private static instance: SessionService
  private config: SessionServiceConfig

  private constructor(
    private sessionUseCases: SessionUseCases,
    private repository: SessionRepository,
    config?: Partial<SessionServiceConfig>
  ) {
    this.config = {
      maxConcurrentSessions: 1, // One device one login
      sessionTTL: 3600, // 1 hour
      refreshThreshold: 300, // 5 minutes before expiry
      securityChecks: true,
      ...config
    }
  }

  /**
   * Get singleton instance
   */
  static getInstance(
    sessionUseCases?: SessionUseCases,
    repository?: SessionRepository,
    config?: Partial<SessionServiceConfig>
  ): SessionService {
    if (!SessionService.instance) {
      if (!sessionUseCases || !repository) {
        throw new Error('SessionService requires sessionUseCases and repository for initialization')
      }
      SessionService.instance = new SessionService(sessionUseCases, repository, config)
    }
    return SessionService.instance
  }

  /**
   * Create a new session with security checks
   */
  async createSession(dto: CreateSessionDTO): Promise<SessionData> {
    try {
      // Validate user and device
      if (this.config.securityChecks) {
        await this.validateSecurityConstraints(dto.userId, dto.deviceFingerprint)
      }

      // Create session through use case
      const session = await this.sessionUseCases.createSession(dto)
      
      console.log(`✅ Session created: ${session.sessionId} for user ${dto.userId}`)
      return session
    } catch (error) {
      console.error('Failed to create session:', error)
      throw error
    }
  }

  /**
   * Validate session with automatic refresh
   */
  async validateSession(sessionId: string, autoRefresh: boolean = true): Promise<SessionValidationResult> {
    try {
      // Get session from repository
      const session = await this.repository.getSession(sessionId)
      
      if (!session) {
        throw new SessionNotFoundError(`Session ${sessionId} not found`)
      }

      // Check if session is expired
      if (this.isSessionExpired(session)) {
        await this.repository.deleteSession(sessionId)
        throw new SessionExpiredError(`Session ${sessionId} has expired`)
      }

      // Auto-refresh if needed
      let refreshed = false
      if (autoRefresh && this.shouldRefreshSession(session)) {
        await this.refreshSession(sessionId)
        refreshed = true
      }

      return {
        isValid: true,
        session,
        refreshed
      }
    } catch (error) {
      if (error instanceof SessionExpiredError || error instanceof SessionNotFoundError) {
        return {
          isValid: false,
          session: null,
          refreshed: false,
          error: error.message
        }
      }
      throw error
    }
  }

  /**
   * Refresh session TTL
   */
  async refreshSession(sessionId: string): Promise<SessionData> {
    const session = await this.repository.getSession(sessionId)
    
    if (!session) {
      throw new SessionNotFoundError(`Session ${sessionId} not found`)
    }

    // Update last activity and extend TTL
    const updatedSession: SessionData = {
      ...session,
      lastActivity: new Date(),
      expiresAt: new Date(Date.now() + this.config.sessionTTL * 1000)
    }

    await this.repository.updateSession(sessionId, updatedSession)
    console.log(`🔄 Session refreshed: ${sessionId}`)
    
    return updatedSession
  }

  /**
   * Invalidate session
   */
  async invalidateSession(sessionId: string, reason?: string): Promise<boolean> {
    try {
      const success = await this.sessionUseCases.invalidateSession(sessionId, reason)
      
      if (success) {
        console.log(`❌ Session invalidated: ${sessionId} - ${reason || 'Manual logout'}`)
      }
      
      return success
    } catch (error) {
      console.error(`Failed to invalidate session ${sessionId}:`, error)
      return false
    }
  }

  /**
   * Invalidate all user sessions except current
   */
  async invalidateOtherUserSessions(userId: number, currentSessionId: string): Promise<number> {
    try {
      const count = await this.sessionUseCases.invalidateUserSessions(userId, currentSessionId)
      console.log(`❌ Invalidated ${count} other sessions for user ${userId}`)
      return count
    } catch (error) {
      console.error(`Failed to invalidate user sessions for ${userId}:`, error)
      return 0
    }
  }

  /**
   * Get user's active sessions
   */
  async getUserSessions(userId: number): Promise<SessionData[]> {
    return this.repository.getUserSessions(userId)
  }

  /**
   * Batch validate multiple sessions (for admin operations)
   */
  async batchValidateSessions(sessionIds: string[]): Promise<Map<string, SessionValidationResult>> {
    const results = new Map<string, SessionValidationResult>()
    
    // Use Promise.allSettled for parallel validation
    const validations = await Promise.allSettled(
      sessionIds.map(id => this.validateSession(id, false))
    )

    sessionIds.forEach((sessionId, index) => {
      const result = validations[index]
      if (result.status === 'fulfilled') {
        results.set(sessionId, result.value)
      } else {
        results.set(sessionId, {
          isValid: false,
          session: null,
          refreshed: false,
          error: result.reason?.message || 'Validation failed'
        })
      }
    })

    return results
  }

  /**
   * Private helper methods
   */
  private async validateSecurityConstraints(userId: number, deviceFingerprint?: string): Promise<void> {
    if (!deviceFingerprint) {
      throw new SessionSecurityError('Device fingerprint required for security')
    }

    // Check for existing sessions on same device
    const existingSessions = await this.repository.getUserSessions(userId)
    const deviceSessions = existingSessions.filter(s => s.deviceFingerprint === deviceFingerprint)
    
    if (deviceSessions.length >= this.config.maxConcurrentSessions) {
      // Invalidate oldest session
      const oldestSession = deviceSessions.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      )[0]
      
      await this.invalidateSession(oldestSession.sessionId, 'New login from same device')
    }
  }

  private isSessionExpired(session: SessionData): boolean {
    return new Date() > new Date(session.expiresAt)
  }

  private shouldRefreshSession(session: SessionData): boolean {
    const timeUntilExpiry = new Date(session.expiresAt).getTime() - Date.now()
    return timeUntilExpiry < (this.config.refreshThreshold * 1000)
  }
}

/**
 * Factory function for easy instantiation
 */
export function createSessionService(
  sessionUseCases: SessionUseCases,
  repository: SessionRepository,
  config?: Partial<SessionServiceConfig>
): SessionService {
  return SessionService.getInstance(sessionUseCases, repository, config)
}

export default SessionService
