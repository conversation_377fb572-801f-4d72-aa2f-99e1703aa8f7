/**
 * Cache Warming Service
 * ✅ UNIFIED CACHE STRATEGY: Implements background cache warming with unified cache strategy
 */

import { AbsenceUseCases } from '../domain/usecases/absence'
import { AbsenceRepository } from '../data/repositories/absence'
import { StudentRepository } from '../data/repositories/student'
import { getCurrentWITATime } from '../utils/date'

export class CacheWarmingService {
  private absenceUseCases: AbsenceUseCases
  private isWarming = false

  constructor() {
    // ✅ UNIFIED CACHE STRATEGY: Initialize without cache parameter
    const absenceRepo = new AbsenceRepository()
    const studentRepo = new StudentRepository()
    this.absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo)
  }

  /**
   * Warm cache for today's data (most frequently accessed)
   */
  async warmTodayCache(): Promise<void> {
    if (this.isWarming) {
      console.log('🔥 Cache warming already in progress, skipping...')
      return
    }

    this.isWarming = true
    console.log('🔥 CACHE WARMING: Starting today cache warming...')

    try {
      const today = getCurrentWITATime()
      const reportTypes: ('prayer' | 'school' | 'all')[] = ['prayer', 'school', 'all']

      // Warm cache for all report types
      const warmingPromises = reportTypes.map(async reportType => {
        try {
          console.log(`🔥 Warming ${reportType} reports for today...`)

          // Warm all classes data
          await this.absenceUseCases.getAttendanceSummaryByType(
            today,
            undefined, // all classes
            reportType,
            false // don't force fresh, use cache if available
          )

          // Warm popular classes (you can customize this list)
          const popularClasses = ['XII RPL 1', 'XII RPL 2', 'XI RPL 1', 'XI RPL 2']

          for (const className of popularClasses) {
            await this.absenceUseCases.getAttendanceSummaryByType(
              today,
              className,
              reportType,
              false
            )
          }

          console.log(`✅ Warmed ${reportType} reports cache`)
        } catch (error) {
          console.error(`❌ Error warming ${reportType} cache:`, error)
        }
      })

      await Promise.allSettled(warmingPromises)
      console.log('🔥 CACHE WARMING: Today cache warming completed')
    } catch (error) {
      console.error('❌ Cache warming failed:', error)
    } finally {
      this.isWarming = false
    }
  }

  /**
   * Warm cache for yesterday's data (frequently accessed for comparison)
   */
  async warmYesterdayCache(): Promise<void> {
    console.log('🔥 CACHE WARMING: Starting yesterday cache warming...')

    try {
      const yesterday = new Date(getCurrentWITATime().getTime() - 24 * 60 * 60 * 1000)
      const reportTypes: ('prayer' | 'school' | 'all')[] = ['prayer', 'school']

      const warmingPromises = reportTypes.map(async reportType => {
        try {
          await this.absenceUseCases.getAttendanceSummaryByType(
            yesterday,
            undefined, // all classes
            reportType,
            false
          )
          console.log(`✅ Warmed ${reportType} reports cache for yesterday`)
        } catch (error) {
          console.error(`❌ Error warming yesterday ${reportType} cache:`, error)
        }
      })

      await Promise.allSettled(warmingPromises)
      console.log('🔥 CACHE WARMING: Yesterday cache warming completed')
    } catch (error) {
      console.error('❌ Yesterday cache warming failed:', error)
    }
  }

  /**
   * Warm cache for weekly aggregated data
   */
  async warmWeeklyCache(): Promise<void> {
    console.log('🔥 CACHE WARMING: Starting weekly cache warming...')

    try {
      const today = getCurrentWITATime()
      const reportTypes: ('prayer' | 'school' | 'all')[] = ['prayer', 'school']

      // Note: Aggregated summary cache warming removed with summary export functionality
      const warmingPromises = reportTypes.map(async reportType => {
        try {
          // Warm regular attendance summary cache instead
          await this.absenceUseCases.getAttendanceSummary(
            today,
            undefined, // all classes
            false,
            reportType
          )
          console.log(`✅ Warmed ${reportType} daily attendance cache`)
        } catch (error) {
          console.error(`❌ Error warming daily ${reportType} cache:`, error)
        }
      })

      await Promise.allSettled(warmingPromises)
      console.log('🔥 CACHE WARMING: Weekly cache warming completed')
    } catch (error) {
      console.error('❌ Weekly cache warming failed:', error)
    }
  }

  /**
   * Comprehensive cache warming (run during low-traffic periods)
   */
  async warmAllCache(): Promise<void> {
    console.log('🔥 CACHE WARMING: Starting comprehensive cache warming...')

    const startTime = Date.now()

    try {
      // Warm in priority order
      await this.warmTodayCache()
      await this.warmYesterdayCache()
      await this.warmWeeklyCache()

      const duration = Date.now() - startTime
      console.log(`🔥 CACHE WARMING: Comprehensive warming completed in ${duration}ms`)
    } catch (error) {
      console.error('❌ Comprehensive cache warming failed:', error)
    }
  }

  /**
   * Schedule cache warming (call this from a cron job or background task)
   */
  async scheduleWarmingTasks(): Promise<void> {
    // Warm today's cache every 5 minutes during school hours (6 AM - 6 PM WITA)
    const now = getCurrentWITATime()
    const hour = now.getHours()

    if (hour >= 6 && hour <= 18) {
      await this.warmTodayCache()
    }

    // Warm yesterday's cache once per hour
    if (now.getMinutes() === 0) {
      await this.warmYesterdayCache()
    }

    // Warm weekly cache once per day at 1 AM
    if (hour === 1 && now.getMinutes() === 0) {
      await this.warmWeeklyCache()
    }
  }

  /**
   * Get cache warming status
   */
  getWarmingStatus(): { isWarming: boolean } {
    return { isWarming: this.isWarming }
  }
}

// Export singleton instance
export const cacheWarmingService = new CacheWarmingService()
