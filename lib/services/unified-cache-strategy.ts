/**
 * Unified Write-Through Cache Strategy for Real-Time Reports
 *
 * ✅ NO TTL - Cache persists until explicitly invalidated
 * ✅ Write-Through - Data written to cache immediately when written to database
 * ✅ Event-Driven - Cache invalidated only when data changes
 * ✅ Real-Time - Immediate visibility of new data
 * ✅ Consistent - Same strategy across daily, monthly, yearly reports
 *
 * Based on Redis best practices and Netflix EVCache patterns
 */

import { getRedisCache } from '../data/cache/redis'
import { getCurrentWITATime, getWITADate<PERSON><PERSON>, getWITADateComponents } from '../utils/date'
import { AttendanceType, getAttendanceTypesByReportType } from '../domain/entities/absence'

// 🚀 REQUEST DEDUPLICATION: Prevent multiple identical requests
const pendingRequests = new Map<string, Promise<any>>()

/**
 * Request deduplication wrapper
 * Prevents multiple identical requests from hitting the database simultaneously
 */
async function withRequestDeduplication<T>(key: string, operation: () => Promise<T>): Promise<T> {
  // Check if the same request is already in progress
  const existingRequest = pendingRequests.get(key)
  if (existingRequest) {
    console.log(`🔄 REQUEST DEDUPLICATION: Waiting for existing request ${key}`)
    return existingRequest
  }

  // Start new request and store the promise
  const requestPromise = operation()
  pendingRequests.set(key, requestPromise)

  try {
    const result = await requestPromise
    console.log(`✅ REQUEST DEDUPLICATION: Completed request ${key}`)
    return result
  } finally {
    // Clean up the pending request
    pendingRequests.delete(key)
  }
}

export interface CacheEvent {
  uniqueCode: string
  attendanceType: AttendanceType
  className?: string
  operation: 'create' | 'update' | 'delete'
  timestamp: Date
}

/**
 * Unified Cache Strategy - Single Source of Truth for All Cache Operations
 */
export class UnifiedCacheStrategy {
  private cache = getRedisCache()

  /**
   * WRITE-THROUGH: Set data in cache without TTL with versioning
   * Cache persists until explicitly invalidated
   */
  async set(key: string, data: any): Promise<void> {
    try {
      // Add version and timestamp for change detection
      const versionedData = {
        data,
        version: Date.now(),
        timestamp: new Date().toISOString(),
        // Add data hash for integrity check
        dataHash: this.generateDataHash(data),
      }

      const jsonData = JSON.stringify(versionedData)

      // Use Redis SET without expiration (no TTL)
      await this.cache.setWithoutTTL(key, jsonData)
      // Production: Conditional logging for performance
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `🔄 WRITE-THROUGH SET: ${key} (v${versionedData.version}, hash:${versionedData.dataHash.substring(0, 8)}, NO TTL)`
        )
      }
    } catch (error) {
      console.error(`❌ Cache SET error for key ${key}:`, error)
      throw error
    }
  }

  /**
   * GET data from cache with version support
   */
  async get(key: string): Promise<any | null> {
    try {
      const data = await this.cache.get(key)
      console.log(`📖 CACHE GET: ${key} - ${data ? 'HIT' : 'MISS'}`)

      if (!data) return null

      const parsedData = JSON.parse(data)

      // Handle versioned data
      if (
        parsedData &&
        typeof parsedData === 'object' &&
        'data' in parsedData &&
        'version' in parsedData
      ) {
        console.log(`📖 VERSIONED CACHE: ${key} (v${parsedData.version}, ${parsedData.timestamp})`)
        return parsedData.data
      }

      // Handle legacy data (backward compatibility)
      return parsedData
    } catch (error) {
      console.error(`❌ Cache GET error for key ${key}:`, error)
      return null
    }
  }

  /**
   * DELETE data from cache
   */
  async del(key: string): Promise<void> {
    try {
      await this.cache.del(key)
      console.log(`🗑️ CACHE DEL: ${key}`)
    } catch (error) {
      console.error(`❌ Cache DEL error for key ${key}:`, error)
    }
  }

  /**
   * 🚀 GET WITH REQUEST DEDUPLICATION
   * Prevents multiple identical cache misses from hitting the database simultaneously
   */
  async getWithDeduplication<T>(
    key: string,
    fallbackOperation: () => Promise<T>
  ): Promise<T | null> {
    // First try to get from cache
    const cachedData = await this.get(key)
    if (cachedData) {
      return cachedData
    }

    // If cache miss, use request deduplication for the fallback operation
    const deduplicationKey = `fallback:${key}`
    return withRequestDeduplication(deduplicationKey, async () => {
      // Double-check cache in case another request already populated it
      const doubleCheckCache = await this.get(key)
      if (doubleCheckCache) {
        console.log(`🔄 DOUBLE-CHECK CACHE HIT: ${key} (populated by concurrent request)`)
        return doubleCheckCache
      }

      // Execute fallback operation and cache the result
      const result = await fallbackOperation()
      if (result) {
        await this.set(key, result)
      }
      return result
    })
  }

  /**
   * UPDATE CACHE ON NEW ATTENDANCE
   * Write-through strategy: immediately update all related cache entries
   */
  async updateCacheOnAttendance(event: CacheEvent): Promise<void> {
    console.log(`🚀 WRITE-THROUGH UPDATE: ${event.uniqueCode} - ${event.attendanceType}`)

    try {
      // Get affected report types
      const affectedReportTypes = this.getAffectedReportTypes(event.attendanceType)

      // Update cache for each affected report type
      const updatePromises = affectedReportTypes.map(reportType =>
        this.updateReportTypeCache(reportType, event)
      )

      await Promise.allSettled(updatePromises)

      // 🚀 IMMEDIATE INVALIDATION: Clear student cache immediately for real-time updates
      await this.immediateStudentCacheInvalidation(event.uniqueCode)

      // 🚀 CACHE WARMING: Warm student-specific cache immediately
      await this.warmStudentCache(event.uniqueCode)

      console.log(
        `✅ WRITE-THROUGH COMPLETE: Updated ${affectedReportTypes.join(', ')} reports, invalidated and warmed student cache`
      )
    } catch (error) {
      console.error('❌ Write-through update failed:', error)
    }
  }

  /**
   * COMPREHENSIVE CACHE INVALIDATION
   * Event-driven: invalidate all related cache when data changes
   */
  async invalidateRelatedCache(event: CacheEvent): Promise<void> {
    // Always log cache invalidation for real-time debugging
    console.log(`🗑️ EVENT-DRIVEN INVALIDATION: ${event.operation} ${event.uniqueCode}`)
    console.log(`🔧 DEBUG: Event details:`, {
      uniqueCode: event.uniqueCode,
      attendanceType: event.attendanceType,
      className: event.className,
      operation: event.operation,
      timestamp: event.timestamp,
    })

    try {
      const today = getCurrentWITATime()
      const todayKey = getWITADateKey(today)
      const witaComponents = getWITADateComponents(today)
      const yesterdayKey = getWITADateKey(new Date(today.getTime() - 24 * 60 * 60 * 1000))

      console.log(`🔧 DEBUG: Date keys for cache invalidation:`)
      console.log(`   Today: ${todayKey} (from ${today.toISOString()})`)
      console.log(`   Yesterday: ${yesterdayKey}`)
      console.log(`   WITA Components:`, witaComponents)

      // Get affected report types
      const affectedReportTypes = this.getAffectedReportTypes(event.attendanceType)

      // Generate cache keys to invalidate
      const cacheKeysToInvalidate: string[] = []

      for (const reportType of affectedReportTypes) {
        // Daily reports
        cacheKeysToInvalidate.push(
          `absence:reports:${reportType}:${todayKey}:all:day`,
          `absence:reports:${reportType}:${yesterdayKey}:all:day`
        )

        // Monthly reports
        const monthKey = `${witaComponents.year}-${witaComponents.month.toString().padStart(2, '0')}`
        cacheKeysToInvalidate.push(`reports:monthly:${reportType}:${monthKey}:all`)

        // Yearly reports
        cacheKeysToInvalidate.push(`reports:yearly:${reportType}:${witaComponents.year}:all`)

        // Aggregated cache - CRITICAL FIX: Include monthly and yearly ranges
        cacheKeysToInvalidate.push(
          `aggregated:${todayKey}:${todayKey}:all:${reportType}`,
          `aggregated:${yesterdayKey}:${yesterdayKey}:all:${reportType}`
        )

        // CRITICAL FIX: Add monthly aggregated cache invalidation
        const monthStart = new Date(witaComponents.year, witaComponents.month - 1, 1)
        const monthEnd = new Date(witaComponents.year, witaComponents.month, 0)
        const monthStartKey = getWITADateKey(monthStart)
        const monthEndKey = getWITADateKey(monthEnd)
        cacheKeysToInvalidate.push(`aggregated:${monthStartKey}:${monthEndKey}:all:${reportType}`)

        // CRITICAL FIX: Add yearly aggregated cache invalidation
        const yearStart = new Date(witaComponents.year, 0, 1)
        const yearEnd = new Date(witaComponents.year, 11, 31)
        const yearStartKey = getWITADateKey(yearStart)
        const yearEndKey = getWITADateKey(yearEnd)
        cacheKeysToInvalidate.push(`aggregated:${yearStartKey}:${yearEndKey}:all:${reportType}`)

        console.log(`🔧 CRITICAL FIX: Added aggregated cache invalidation for ${reportType}:`)
        console.log(`   Monthly: aggregated:${monthStartKey}:${monthEndKey}:all:${reportType}`)
        console.log(`   Yearly: aggregated:${yearStartKey}:${yearEndKey}:all:${reportType}`)

        // Class-specific cache if applicable
        if (event.className) {
          cacheKeysToInvalidate.push(
            `absence:reports:${reportType}:${todayKey}:${event.className}:day`,
            `absence:reports:${reportType}:${yesterdayKey}:${event.className}:day`,
            `reports:monthly:${reportType}:${monthKey}:${event.className}`,
            `reports:yearly:${reportType}:${witaComponents.year}:${event.className}`,
            `aggregated:${todayKey}:${todayKey}:${event.className}:${reportType}`,
            // CRITICAL FIX: Add class-specific monthly/yearly aggregated cache
            `aggregated:${monthStartKey}:${monthEndKey}:${event.className}:${reportType}`,
            `aggregated:${yearStartKey}:${yearEndKey}:${event.className}:${reportType}`
          )
        }
      }

      // Add 'all' report type cache keys
      if (!affectedReportTypes.includes('all')) {
        const monthKey = `${witaComponents.year}-${witaComponents.month.toString().padStart(2, '0')}`
        cacheKeysToInvalidate.push(
          `absence:reports:all:${todayKey}:all:day`,
          `absence:reports:all:${yesterdayKey}:all:day`,
          `reports:monthly:all:${monthKey}:all`,
          `reports:yearly:all:${witaComponents.year}:all`
        )
      }

      // 🚀 CRITICAL FIX: Add student-specific cache invalidation
      // This is the key missing piece for real-time student updates
      const studentTodayKey = `absence:student:${event.uniqueCode}:${todayKey}`
      const studentYesterdayKey = `absence:student:${event.uniqueCode}:${yesterdayKey}`

      cacheKeysToInvalidate.push(studentTodayKey, studentYesterdayKey)

      // Always log critical cache operations for real-time debugging
      console.log(`🔧 CRITICAL FIX: Added student-specific cache invalidation:`)
      console.log(`   Today: ${studentTodayKey}`)
      console.log(`   Yesterday: ${studentYesterdayKey}`)
      console.log(`🔧 DEBUG: Total cache keys to invalidate: ${cacheKeysToInvalidate.length}`)

      // Remove duplicates and invalidate in parallel
      const uniqueKeys = [...new Set(cacheKeysToInvalidate)]
      const invalidationPromises = uniqueKeys.map(key => this.del(key))

      await Promise.allSettled(invalidationPromises)

      console.log(
        `✅ EVENT-DRIVEN INVALIDATION COMPLETE: Cleared ${uniqueKeys.length} cache keys (including student-specific cache)`
      )
    } catch (error) {
      console.error('❌ Cache invalidation failed:', error)
    }
  }

  /**
   * Update cache for specific report type
   */
  private async updateReportTypeCache(
    reportType: 'prayer' | 'school' | 'all',
    event: CacheEvent
  ): Promise<void> {
    const today = getCurrentWITATime()
    const todayKey = getWITADateKey(today)
    const witaComponents = getWITADateComponents(today)

    // Generate cache keys to update
    const cacheKeys = [
      `absence:reports:${reportType}:${todayKey}:all:day`,
      `reports:monthly:${reportType}:${witaComponents.year}-${witaComponents.month.toString().padStart(2, '0')}:all`,
      `reports:yearly:${reportType}:${witaComponents.year}:all`,
      `aggregated:${todayKey}:${todayKey}:all:${reportType}`,
    ]

    // Add class-specific cache keys
    if (event.className) {
      cacheKeys.push(
        `absence:reports:${reportType}:${todayKey}:${event.className}:day`,
        `reports:monthly:${reportType}:${witaComponents.year}-${witaComponents.month.toString().padStart(2, '0')}:${event.className}`,
        `reports:yearly:${reportType}:${witaComponents.year}:${event.className}`,
        `aggregated:${todayKey}:${todayKey}:${event.className}:${reportType}`
      )
    }

    // Update each cache key by invalidating it (let it be refreshed on next request)
    const updatePromises = cacheKeys.map(key => this.del(key))
    await Promise.allSettled(updatePromises)
  }

  /**
   * IMMEDIATE STUDENT CACHE INVALIDATION: Clear student cache immediately for real-time updates
   * This ensures stale cache is cleared before warming with fresh data
   */
  private async immediateStudentCacheInvalidation(uniqueCode: string): Promise<void> {
    try {
      const today = getCurrentWITATime()
      today.setHours(0, 0, 0, 0)
      const todayKey = getWITADateKey(today)
      const yesterdayKey = getWITADateKey(new Date(today.getTime() - 24 * 60 * 60 * 1000))

      const studentTodayKey = `absence:student:${uniqueCode}:${todayKey}`
      const studentYesterdayKey = `absence:student:${uniqueCode}:${yesterdayKey}`

      // Always log immediate invalidation for real-time debugging
      console.log(`🚀 IMMEDIATE INVALIDATION: Clearing student cache for ${uniqueCode}`)
      console.log(`   Today: ${studentTodayKey}`)
      console.log(`   Yesterday: ${studentYesterdayKey}`)

      // Clear both today and yesterday cache
      await Promise.allSettled([this.del(studentTodayKey), this.del(studentYesterdayKey)])

      // Always log completion for real-time debugging
      console.log(`✅ IMMEDIATE INVALIDATION COMPLETE: Student cache cleared for ${uniqueCode}`)
    } catch (error) {
      console.error(`❌ Immediate invalidation failed for ${uniqueCode}:`, error)
      // Don't throw error - continue with other operations
    }
  }

  /**
   * CACHE WARMING: Pre-load student cache after attendance update
   * This ensures the next API call will have fresh data immediately available
   */
  private async warmStudentCache(uniqueCode: string): Promise<void> {
    try {
      const today = getCurrentWITATime()
      today.setHours(0, 0, 0, 0)
      const todayKey = getWITADateKey(today)
      const cacheKey = `absence:student:${uniqueCode}:${todayKey}`

      console.log(`🔥 CACHE WARMING: Pre-loading student cache for ${uniqueCode}`)

      // Import the absence use cases to fetch fresh data
      const { AbsenceUseCases } = await import('../domain/usecases/absence')
      const { AbsenceRepository } = await import('../data/repositories/absence')
      const { StudentRepository } = await import('../data/repositories/student')

      const absenceRepo = new AbsenceRepository()
      const studentRepo = new StudentRepository()
      const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo)

      // Fetch fresh data from database
      const freshData = await absenceUseCases.getAttendanceForDay(uniqueCode, today)

      // Store in cache immediately
      await this.set(cacheKey, freshData)

      console.log(
        `✅ CACHE WARMING COMPLETE: Student cache warmed for ${uniqueCode} with ${freshData.length} records`
      )
    } catch (error) {
      console.error(`❌ Cache warming failed for ${uniqueCode}:`, error)
      // Don't throw error - warming is optional optimization
    }
  }

  /**
   * Get affected report types based on attendance type
   */
  private getAffectedReportTypes(attendanceType: AttendanceType): ('prayer' | 'school' | 'all')[] {
    const prayerTypes = getAttendanceTypesByReportType('prayer')
    const schoolTypes = getAttendanceTypesByReportType('school')

    const affectedTypes: ('prayer' | 'school' | 'all')[] = []

    if (prayerTypes.includes(attendanceType)) {
      affectedTypes.push('prayer')
    }

    if (schoolTypes.includes(attendanceType)) {
      affectedTypes.push('school')
    }

    // 'all' report type is always affected
    affectedTypes.push('all')

    return affectedTypes
  }

  /**
   * Generate data hash for integrity check
   */
  private generateDataHash(data: any): string {
    try {
      const dataString = JSON.stringify(data)
      // Simple hash function for data integrity
      let hash = 0
      for (let i = 0; i < dataString.length; i++) {
        const char = dataString.charCodeAt(i)
        hash = (hash << 5) - hash + char
        hash = hash & hash // Convert to 32-bit integer
      }
      return Math.abs(hash).toString(16)
    } catch (error) {
      console.error('Error generating data hash:', error)
      return 'unknown'
    }
  }

  /**
   * Get cache version for a specific key
   */
  async getCacheVersion(key: string): Promise<number | null> {
    try {
      const data = await this.cache.get(key)
      if (!data) return null

      const parsedData = JSON.parse(data)

      // Return version if available
      if (parsedData && typeof parsedData === 'object' && 'version' in parsedData) {
        return parsedData.version
      }

      return null
    } catch (error) {
      console.error(`❌ Cache version check error for key ${key}:`, error)
      return null
    }
  }

  /**
   * ENHANCED CACHE HEALTH CHECK with versioning support
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    redis: boolean
    strategy: string
    versioning: boolean
    recommendations: string[]
  }> {
    try {
      const testKey = `health_check_${Date.now()}`
      await this.set(testKey, { test: 'data' })
      const testData = await this.get(testKey)
      const version = await this.getCacheVersion(testKey)
      await this.del(testKey)

      const isHealthy = testData?.test === 'data'
      const hasVersioning = version !== null

      return {
        status: isHealthy ? 'healthy' : 'degraded',
        redis: isHealthy,
        strategy: 'Write-Through (No TTL)',
        versioning: hasVersioning,
        recommendations: isHealthy
          ? ['Cache is operating normally with write-through strategy', 'Versioning is active']
          : ['Check Redis connection', 'Verify cache configuration'],
      }
    } catch (error) {
      console.error('Cache health check failed:', error)
      return {
        status: 'unhealthy',
        redis: false,
        strategy: 'Write-Through (No TTL)',
        versioning: false,
        recommendations: [
          'Redis is not accessible',
          'Check Redis server status',
          'Verify network connectivity',
        ],
      }
    }
  }
}

// Singleton instance
export const unifiedCacheStrategy = new UnifiedCacheStrategy()

/**
 * Helper function to create cache event
 */
export function createCacheEvent(
  uniqueCode: string,
  attendanceType: AttendanceType,
  operation: 'create' | 'update' | 'delete',
  className?: string
): CacheEvent {
  return {
    uniqueCode,
    attendanceType,
    className,
    operation,
    timestamp: getCurrentWITATime(),
  }
}
