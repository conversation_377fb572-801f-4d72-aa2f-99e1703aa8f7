/**
 * Absence Service Factory - Clean Architecture Service Layer
 * Creates properly configured AbsenceUseCases with all dependencies
 */

import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { PrayerExemptionUseCases } from '@/lib/domain/usecases/prayer-exemption'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { PrayerExemptionRepository } from '@/lib/data/repositories/prayer-exemption'
import { ClassRepository } from '@/lib/data/repositories/class'

/**
 * Factory function to create AbsenceUseCases with all dependencies
 * ✅ UNIFIED CACHE STRATEGY: Uses unified cache strategy without cache parameter
 */
export function createAbsenceUseCases(): AbsenceUseCases {
  // ✅ UNIFIED CACHE STRATEGY: Initialize repositories without cache parameter
  const absenceRepo = new AbsenceRepository()
  const studentRepo = new StudentRepository()
  const prayerExemptionRepo = new PrayerExemptionRepository()
  const classRepo = new ClassRepository()

  // Initialize prayer exemption use cases
  const prayerExemptionUseCases = new PrayerExemptionUseCases(
    prayerExemptionRepo,
    studentRepo,
    classRepo
  )

  // ✅ UNIFIED CACHE STRATEGY: Create absence use cases without cache parameter
  return new AbsenceUseCases(absenceRepo, studentRepo, prayerExemptionUseCases)
}

/**
 * Factory function to create PrayerExemptionUseCases
 * ✅ UNIFIED CACHE STRATEGY: Uses unified cache strategy without cache parameter
 */
export function createPrayerExemptionUseCases(): PrayerExemptionUseCases {
  // ✅ UNIFIED CACHE STRATEGY: Initialize repositories without cache parameter
  const prayerExemptionRepo = new PrayerExemptionRepository()
  const studentRepo = new StudentRepository()
  const classRepo = new ClassRepository()

  return new PrayerExemptionUseCases(prayerExemptionRepo, studentRepo, classRepo)
}
