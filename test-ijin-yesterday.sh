#!/bin/bash

# Script untuk test memberikan ijin kepada siswa yang sudah melakukan Zuhur kemarin
# Ini adalah script test untuk memastikan logic berfungsi dengan benar

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Load DATABASE_URL
export DATABASE_URL=$(grep "^DATABASE_URL=" .env.local | head -1 | cut -d'=' -f2-)

echo "🧪 TEST: Memberikan ijin kepada siswa yang sudah Zuhur kemarin"
echo "=============================================================="

# Check data kemarin
print_status "Checking data Zuhur kemarin (2025-07-17)..."

ZUHR_YESTERDAY=$(psql "$DATABASE_URL" -t -c "
SELECT COUNT(*) 
FROM absences 
WHERE type = 'Zuhr' 
    AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17';
" | xargs)

print_status "Total siswa Zuhur kemarin: $ZUHR_YESTERDAY"

# Check existing Ijin kemarin
IJIN_YESTERDAY=$(psql "$DATABASE_URL" -t -c "
SELECT COUNT(*) 
FROM absences 
WHERE type = 'Ijin' 
    AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17';
" | xargs)

print_status "Total siswa Ijin kemarin: $IJIN_YESTERDAY"

# Preview siswa yang akan mendapat ijin (untuk kemarin)
print_status "Preview siswa yang akan mendapat ijin (kemarin)..."

PREVIEW_QUERY="
SELECT 
    u.name,
    u.nis,
    c.name as class_name,
    a.recorded_at AT TIME ZONE 'Asia/Makassar' as zuhr_time
FROM absences a
JOIN users u ON a.unique_code = u.unique_code
JOIN classes c ON u.class_id = c.id
WHERE a.type = 'Zuhr' 
    AND DATE(a.recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17'
    AND a.unique_code NOT IN (
        SELECT unique_code 
        FROM absences 
        WHERE type = 'Ijin' 
            AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17'
    )
ORDER BY c.name, u.name
LIMIT 10;
"

echo ""
echo "📋 PREVIEW (10 siswa pertama yang akan mendapat ijin):"
echo "====================================================="

PREVIEW_RESULT=$(psql "$DATABASE_URL" -t -c "$PREVIEW_QUERY" 2>/dev/null || echo "")

if [ -n "$PREVIEW_RESULT" ] && [ "$PREVIEW_RESULT" != " " ]; then
    echo "$PREVIEW_RESULT" | while IFS='|' read -r name nis class_name zuhr_time; do
        name=$(echo "$name" | xargs)
        nis=$(echo "$nis" | xargs)
        class_name=$(echo "$class_name" | xargs)
        zuhr_time=$(echo "$zuhr_time" | xargs)
        echo "• $name ($nis) - $class_name - Zuhur: $zuhr_time"
    done
else
    print_warning "Tidak ada siswa yang memenuhi kriteria"
fi

# Count total yang akan mendapat ijin
ELIGIBLE_COUNT=$(psql "$DATABASE_URL" -t -c "
SELECT COUNT(*) 
FROM absences 
WHERE type = 'Zuhr' 
    AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17'
    AND unique_code NOT IN (
        SELECT unique_code 
        FROM absences 
        WHERE type = 'Ijin' 
            AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17'
    );
" | xargs)

echo ""
print_status "Total siswa yang eligible untuk ijin kemarin: $ELIGIBLE_COUNT"

echo ""
print_warning "KONFIRMASI TEST"
echo "Apakah Anda ingin menjalankan test dengan memberikan ijin kepada siswa kemarin?"
echo "Ini akan menambahkan record 'Ijin' untuk tanggal 2025-07-17"
echo ""
read -p "Ketik 'TEST' untuk melanjutkan, atau tekan Enter untuk membatalkan: " confirmation

if [ "$confirmation" != "TEST" ]; then
    print_warning "Test dibatalkan"
    exit 0
fi

# Execute test operation
print_status "Menjalankan test operation..."

TEST_QUERY="
INSERT INTO absences (unique_code, type, recorded_at, reason)
SELECT 
    unique_code,
    'Ijin' as type,
    '2025-07-17 15:00:00+08'::timestamptz as recorded_at,
    'TEST: Ijin otomatis - sudah melakukan Zuhur kemarin' as reason
FROM absences 
WHERE type = 'Zuhr' 
    AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17'
    AND unique_code NOT IN (
        SELECT unique_code 
        FROM absences 
        WHERE type = 'Ijin' 
            AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17'
    );
"

RESULT=$(psql "$DATABASE_URL" -c "$TEST_QUERY" 2>&1)

if echo "$RESULT" | grep -q "INSERT"; then
    INSERTED_COUNT=$(echo "$RESULT" | grep "INSERT" | awk '{print $3}')
    print_success "TEST BERHASIL: $INSERTED_COUNT siswa mendapat ijin untuk kemarin"
    
    # Show final count
    FINAL_IJIN_COUNT=$(psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM absences 
    WHERE type = 'Ijin' 
        AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17';
    " | xargs)
    
    print_status "Total ijin kemarin sekarang: $FINAL_IJIN_COUNT"
    
else
    print_error "TEST GAGAL!"
    echo "Error: $RESULT"
fi

echo ""
print_warning "ROLLBACK INFO:"
echo "Untuk menghapus test data, gunakan:"
echo "psql \"\$DATABASE_URL\" -c \"DELETE FROM absences WHERE reason LIKE 'TEST:%' AND DATE(recorded_at AT TIME ZONE 'Asia/Makassar') = '2025-07-17';\""

print_success "Test selesai!"
