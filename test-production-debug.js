/**
 * Production Debug Script for Prayer Reports
 * Run this script to test the prayer reports API in production
 */

const PRODUCTION_URL = 'https://shalatyuk.libstudio.my.id'

async function testPrayerReportsAPI() {
  console.log('🔍 Testing Prayer Reports API in Production...\n')

  const tests = [
    {
      name: 'Debug Today Prayer Reports',
      url: `${PRODUCTION_URL}/api/absence/reports?debug=true&date=today&reportType=prayer`,
      description: 'Get debug info for today\'s prayer reports'
    },
    {
      name: 'Force Refresh Today',
      url: `${PRODUCTION_URL}/api/absence/reports?force_fresh=true&date=today&reportType=prayer`,
      description: 'Force refresh cache for today\'s data'
    },
    {
      name: 'Debug Specific Date',
      url: `${PRODUCTION_URL}/api/absence/reports?debug=true&date=2025-06-29&reportType=prayer`,
      description: 'Debug specific date (2025-06-29)'
    },
    {
      name: 'All Reports (No Filter)',
      url: `${PRODUCTION_URL}/api/absence/reports?debug=true&date=today&reportType=all`,
      description: 'Get all attendance types for debugging'
    }
  ]

  for (const test of tests) {
    console.log(`\n📋 ${test.name}`)
    console.log(`📝 ${test.description}`)
    console.log(`🔗 ${test.url}`)
    
    try {
      const response = await fetch(test.url, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        console.log(`❌ HTTP ${response.status}: ${response.statusText}`)
        continue
      }

      const data = await response.json()
      
      if (data.debug) {
        console.log('🐛 Debug Info:')
        console.log(`   Server Time: ${data.debug.serverTime}`)
        console.log(`   WITA Time: ${data.debug.witaTime}`)
        console.log(`   Record Count: ${data.debug.recordCount}`)
        console.log(`   Recent Records: ${data.debug.recentRecords?.length || 0}`)
        
        if (data.debug.recentRecords && data.debug.recentRecords.length > 0) {
          console.log('📊 Recent Attendance Records:')
          data.debug.recentRecords.slice(0, 3).forEach((record, index) => {
            console.log(`   ${index + 1}. ${record.uniqueCode} - ${record.type} at ${record.recordedAt}`)
          })
        }
      } else {
        console.log(`📊 Data Records: ${Array.isArray(data) ? data.length : 'Invalid format'}`)
        
        if (Array.isArray(data) && data.length > 0) {
          const prayerData = data.filter(record => record.zuhr || record.asr || record.ijin)
          console.log(`🕌 Prayer Records: ${prayerData.length}`)
          
          if (prayerData.length > 0) {
            console.log('📋 Sample Prayer Data:')
            prayerData.slice(0, 3).forEach((record, index) => {
              console.log(`   ${index + 1}. ${record.name} - Zuhr: ${record.zuhr}, Asr: ${record.asr}`)
            })
          }
        }
      }
      
      console.log('✅ Test completed successfully')
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
    
    console.log('─'.repeat(80))
  }

  console.log('\n🎯 Summary:')
  console.log('1. Check if debug info shows correct WITA time')
  console.log('2. Verify recent records exist in database')
  console.log('3. Compare record count between debug and actual data')
  console.log('4. Check if force_fresh parameter works')
  console.log('\n💡 If records exist but count is 0, timezone conversion issue confirmed')
}

// Run the test
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch')
  testPrayerReportsAPI().catch(console.error)
} else {
  // Browser environment
  console.log('Run this in browser console or Node.js')
  window.testPrayerReportsAPI = testPrayerReportsAPI
}
