# 🚀 Quick Test Reference - SMK 3 Banjarmasin

## ✅ Data Loaded Successfully!

**3000 siswa realistis** dan **141,085 records absensi** telah berhasil dibuat untuk testing aplikasi ShalatYuk.

## 🔑 Sample Login Credentials

### Admin Access

```
Username: superadmin
Password: student123
Role: Super Admin
```

```
Username: kepsek
Password: student123
Role: Kepala <PERSON>
```

```
Username: guru_pai
Password: student123
Role: Guru PAI
```

### Sample Students (Password: student123)

| Username     | Name           | NIS      | Kelas   | WhatsApp      |
| ------------ | -------------- | -------- | ------- | ------------- |
| fajar0001    | Fajar <PERSON> | 20230001 | X IPA 1 | 6281000000001 |
| surya0002    | Surya <PERSON>   | 20230002 | X IPA 1 | 6281000000002 |
| abdul0003    | <PERSON>  | 20230003 | X IPA 1 | 6281000000003 |
| citra0004    | Citra Maharani | 20230004 | X IPA 1 | 6281000000004 |
| lutfi0005    | Lutfi Novita   | 20230005 | X IPA 1 | 6281000000005 |
| muhammad0201 | Muhammad ...   | 20230201 | X IPA 2 | 6281000000201 |
| siti0401     | Siti ...       | 20230401 | X IPS 1 | 6281000000401 |

_Pattern username: `firstname + last4digitsNIS`_

## 📊 Data Summary

- **Total Siswa**: 3,000 (200 per kelas)
- **Total Staff**: 6 orang
- **Total Records Absensi**: 141,085 records
- **Period Data**: 3 bulan terakhir
- **Classes**: 15 kelas (X, XI, XII IPA/IPS)

### Distribusi Absensi:

- **Pulang**: 49,708 records (95% siswa pulang)
- **Zuhr**: 47,034 records (90% ikut sholat Dzuhur)
- **Asr**: 44,343 records (85% ikut sholat Ashar)

## 🧪 Quick Test Commands

### Access Adminer

```
URL: http://localhost:8080
Server: postgres-dev
Username: shalatdev
Password: shalatdev123
Database: shalat_yuk_dev
```

### Start Application

```bash
npm run dev
# Akses: http://localhost:3000
```

### Sample Database Queries

```sql
-- Cek siswa per kelas
SELECT c.name, COUNT(u.id) as siswa
FROM classes c
LEFT JOIN users u ON c.id = u.class_id AND u.role = 'student'
GROUP BY c.name ORDER BY c.name;

-- Absensi hari ini
SELECT type, COUNT(*) FROM absences
WHERE DATE(recorded_at) = CURRENT_DATE
GROUP BY type;

-- Top 10 siswa rajin sholat
SELECT u.name, c.name as kelas, COUNT(a.id) as total_sholat
FROM users u
JOIN classes c ON u.class_id = c.id
JOIN absences a ON u.unique_code = a.unique_code
WHERE a.type IN ('Zuhr', 'Asr')
GROUP BY u.unique_code, u.name, c.name
ORDER BY total_sholat DESC LIMIT 10;
```

## 🎯 Testing Scenarios

1. **Login Testing**: Try different user roles
2. **Attendance Display**: View class attendance lists
3. **Prayer Records**: Check Zuhr/Asr participation
4. **Date Filtering**: Filter by date ranges
5. **Class Comparison**: Compare attendance between classes
6. **Search Function**: Search students by name/NIS
7. **Reporting**: Generate attendance reports

## 📱 Real Indonesian Context

- **Names**: Authentic Indonesian names from Kalimantan Selatan
- **NIS Format**: 2023XXXX (realistic school ID format)
- **WhatsApp**: Indonesian phone number format (628...)
- **Classes**: Real SMK structure (IPA/IPS tracks)
- **Prayer Times**: Realistic Islamic prayer schedule
- **Attendance Patterns**: Monday effect, weekend gaps

## 🚨 Development Notes

- **Environment**: Development only, NOT for production
- **Password**: All users use 'student123' for easy testing
- **Performance**: 3K students + 141K records = good load testing
- **Realistic Patterns**: Weekend gaps, Monday lower attendance

---

## 🎉 Ready for Real Testing!

**Data SMK 3 Banjarmasin siap digunakan untuk testing aplikasi yang realistis!**

Login dan mulai testing dengan data yang benar-benar menyerupai kondisi sekolah sesungguhnya.
