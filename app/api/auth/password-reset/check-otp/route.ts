import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'
import { z } from 'zod'
import { ValidationError, NotFoundError } from '@/lib/domain/errors'
import { formatPhoneNumber } from '@/lib/utils/otp'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository()
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Validation schema
const checkOtpSchema = z.object({
  whatsapp: z
    .string()
    .regex(/^(\+?)[0-9]{10,15}$/, 'Format nomor WhatsApp tidak valid')
    .transform(val => (val.startsWith('+') ? val.substring(1) : val)),
  otp: z.string().length(6, 'OTP harus 6 digit'),
})

/**
 * POST /api/auth/password-reset/check-otp
 * Verify OTP validity without resetting password
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const result = checkOtpSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Input tidak valid', details: result.error.format() },
        { status: 400 }
      )
    }

    const { whatsapp, otp } = result.data
    const normalizedWhatsapp = formatPhoneNumber(whatsapp)

    try {
      // Check if the OTP is valid
      const cacheKey = `password_reset:otp:${normalizedWhatsapp}`
      const storedOtp = await cache.get(cacheKey)

      if (!storedOtp) {
        return NextResponse.json(
          { error: 'OTP sudah kedaluwarsa. Silakan minta OTP baru.' },
          { status: 400 }
        )
      }

      if (storedOtp !== otp) {
        // Check if there have been too many failed attempts
        const failKey = `fail:otp:${normalizedWhatsapp}`
        const attempts = await cache.get(failKey)

        let attemptCount = 1
        if (attempts) {
          attemptCount = parseInt(attempts, 10) + 1
        }

        // Store the attempt count
        const MAX_ATTEMPTS = 5
        const TTL = 10 * 60 // 10 minutes
        await cache.set(failKey, attemptCount.toString(), TTL)

        if (attemptCount >= MAX_ATTEMPTS) {
          return NextResponse.json(
            {
              error: 'Terlalu banyak percobaan gagal. Silakan minta OTP baru.',
              isRateLimit: true,
            },
            { status: 429 }
          )
        }

        return NextResponse.json(
          {
            error:
              'OTP tidak valid. Anda memiliki sisa ' +
              (MAX_ATTEMPTS - attemptCount) +
              ' percobaan.',
            attemptsLeft: MAX_ATTEMPTS - attemptCount,
          },
          { status: 400 }
        )
      }

      // Find student by WhatsApp to make sure they exist
      const student = await studentRepo.findByWhatsApp(normalizedWhatsapp)
      if (!student) {
        return NextResponse.json({ error: 'Akun tidak ditemukan' }, { status: 404 })
      }

      // If we reach here, OTP is valid
      return NextResponse.json({
        success: true,
        message: 'OTP valid',
      })
    } catch (error) {
      console.error('OTP verification error:', error)

      if (error instanceof ValidationError) {
        return NextResponse.json({ error: error.message }, { status: 400 })
      }

      if (error instanceof NotFoundError) {
        return NextResponse.json({ error: 'Akun tidak ditemukan' }, { status: 404 })
      }

      return NextResponse.json({ error: 'Gagal verifikasi OTP' }, { status: 500 })
    }
  } catch (error) {
    console.error('OTP verification error:', error)
    return NextResponse.json({ error: 'Gagal memproses verifikasi OTP' }, { status: 500 })
  }
}
