/**
 * 🎯 HYBRID AUTH LOGIN ENDPOINT
 *
 * Root Cause Fix Implementation:
 * - Single login endpoint for all user types
 * - JWT + Redis hybrid authentication
 * - One device per user policy
 * - Immediate session control
 * - Simplified architecture
 */

import { NextRequest, NextResponse } from 'next/server'
import { hybridAuth } from '@/lib/auth/hybrid-auth-manager'
import { extractDeviceInfo } from '@/lib/middleware/hybrid-auth-middleware'
import { comparePassword } from '@/lib/utils/auth'
// Import will be handled dynamically in getUserByUsername function

interface LoginRequest {
  username: string
  password: string
  userType: 'admin' | 'student'
}

export async function POST(req: NextRequest) {
  try {
    const startTime = Date.now()
    const body: LoginRequest = await req.json()
    const { username, password, userType } = body

    // ✅ STEP 1: Validate input
    if (!username || !password || !userType) {
      return NextResponse.json(
        { error: 'Username, password, and user type are required' },
        { status: 400 }
      )
    }

    // ✅ STEP 2: Get user from database
    const user = await getUserByUsername(username, userType)

    if (!user) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 })
    }

    // ✅ STEP 3: Verify password
    const isPasswordValid = await comparePassword(password, user.passwordHash)

    if (!isPasswordValid) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 })
    }

    // ✅ STEP 4: Extract device information
    const deviceInfo = extractDeviceInfo(req)

    console.log(
      `🔐 HYBRID LOGIN: User ${user.id} (${user.role}) from device ${deviceInfo.deviceId}`
    )

    // ✅ STEP 5: Create hybrid session (JWT + Redis)
    const authResult = await hybridAuth.login(user.id, user.role, deviceInfo)

    // ✅ STEP 6: Set secure cookies
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        name: user.name,
      },
      sessionId: authResult.sessionId,
      expiresIn: authResult.expiresIn,
    })

    // Set access token cookie (short-lived)
    const cookieName = userType === 'admin' ? 'admin_auth_token' : 'student_auth_token'

    response.cookies.set(cookieName, authResult.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: authResult.expiresIn,
      path: '/',
    })

    // Set refresh token cookie (long-lived)
    response.cookies.set(`${cookieName}_refresh`, authResult.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    })

    const responseTime = Date.now() - startTime
    console.log(`✅ HYBRID LOGIN SUCCESS: User ${user.id} logged in (${responseTime}ms)`)

    return response
  } catch (error) {
    console.error('Hybrid login error:', error)
    return NextResponse.json({ error: 'Login failed' }, { status: 500 })
  }
}

// ✅ HELPER: Get user by username (using existing repositories)
async function getUserByUsername(username: string, userType: 'admin' | 'student') {
  try {
    if (userType === 'admin') {
      const { AdminRepository } = await import('@/lib/data/repositories/admin')
      const { getRedisCache } = await import('@/lib/data/cache/redis')
      const cache = getRedisCache()
      const adminRepo = new AdminRepository(cache)
      return await adminRepo.findByUsername(username)
    } else {
      const { StudentRepository } = await import('@/lib/data/repositories/student')
      const studentRepo = new StudentRepository()
      return await studentRepo.findByUsername(username)
    }
  } catch (error) {
    console.error('Error getting user:', error)
    return null
  }
}
