/**
 * 🎯 HYBRID AUTH SESSION CHECK ENDPOINT
 * 
 * Features:
 * - Fast JWT validation
 * - <PERSON><PERSON> calls
 * - Automatic token refresh
 * - Simple session status
 */

import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest, refreshAuthToken } from '@/lib/middleware/hybrid-auth-middleware'

export async function GET(req: NextRequest) {
  try {
    const startTime = Date.now()

    // ✅ STEP 1: Validate current session (fast hybrid check)
    const authResult = await authenticateRequest(req)
    
    if (!authResult.isValid) {
      return NextResponse.json(
        { 
          isValid: false,
          error: authResult.error || 'Invalid session'
        },
        { status: 401 }
      )
    }

    // ✅ STEP 2: Check if token needs refresh
    let newAccessToken: string | undefined

    if (authResult.needsRefresh) {
      console.log(`🔄 TOKEN REFRESH: User ${authResult.userId} needs token refresh`)
      
      // Get refresh token from cookie
      const refreshToken = req.cookies.get('admin_auth_token_refresh')?.value ||
                          req.cookies.get('student_auth_token_refresh')?.value
      
      if (refreshToken) {
        const refreshResult = await refreshAuthToken(refreshToken)
        
        if (refreshResult) {
          newAccessToken = refreshResult.accessToken
          console.log(`✅ TOKEN REFRESHED: User ${authResult.userId}`)
        }
      }
    }

    // ✅ STEP 3: Return session status
    const response = NextResponse.json({
      isValid: true,
      user: {
        id: authResult.userId,
        role: authResult.role
      },
      sessionId: authResult.sessionId,
      needsRefresh: authResult.needsRefresh,
      refreshed: !!newAccessToken
    })

    // ✅ STEP 4: Set new access token if refreshed
    if (newAccessToken) {
      const cookieName = authResult.role === 'student' ? 'student_auth_token' : 'admin_auth_token'
      
      response.cookies.set(cookieName, newAccessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 30 * 60, // 30 minutes
        path: '/'
      })
    }

    const responseTime = Date.now() - startTime
    console.log(`✅ SESSION CHECK: User ${authResult.userId} (${responseTime}ms)`)

    return response

  } catch (error) {
    console.error('Session check error:', error)
    return NextResponse.json(
      { 
        isValid: false,
        error: 'Session check failed'
      },
      { status: 500 }
    )
  }
}
