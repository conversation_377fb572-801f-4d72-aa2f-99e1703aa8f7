/**
 * 🎯 HYBRID AUTH LOGOUT ENDPOINT
 *
 * Features:
 * - Immediate session invalidation
 * - One device per user support
 * - Clean cookie removal
 * - Redis cleanup
 */

import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest, logoutSession } from '@/lib/middleware/hybrid-auth-middleware'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

export async function POST(req: NextRequest) {
  try {
    const startTime = Date.now()

    // ✅ STEP 1: Authenticate current session
    const authResult = await authenticateRequest(req)

    if (!authResult.isValid || !authResult.sessionId) {
      return NextResponse.json({ error: 'No active session found' }, { status: 401 })
    }

    console.log(`🔓 HYBRID LOGOUT: User ${authResult.userId} session ${authResult.sessionId}`)

    // ✅ STEP 2: Invalidate session in Redis
    await logoutSession(authResult.sessionId)

    // ✅ STEP 3: Clear cookies
    const response = NextResponse.json({
      success: true,
      message: 'Logged out successfully',
    })

    // Clear both admin and student cookies (in case user switched roles)
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: 0, // Expire immediately
      path: '/',
    }

    response.cookies.set('admin_auth_token', '', cookieOptions)
    response.cookies.set('admin_auth_token_refresh', '', cookieOptions)
    response.cookies.set('student_auth_token', '', cookieOptions)
    response.cookies.set('student_auth_token_refresh', '', cookieOptions)

    const responseTime = Date.now() - startTime
    console.log(`✅ HYBRID LOGOUT SUCCESS: Session invalidated (${responseTime}ms)`)

    return response
  } catch (error) {
    console.error('Hybrid logout error:', error)
    return NextResponse.json({ error: 'Logout failed' }, { status: 500 })
  }
}
