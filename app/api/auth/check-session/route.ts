import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithSession, clearAuthCookies } from '@/lib/middleware/enhanced-auth'

/**
 * GET /api/auth/check-session
 * Check if current session is valid and clear cookies if not
 */
export async function GET(request: NextRequest) {
  try {
    // Try to authenticate with session
    const authResult = await authenticateWithSession(request)

    return NextResponse.json({
      valid: true,
      user: {
        id: authResult.id,
        role: authResult.role,
        sessionId: authResult.sessionId,
      },
    })
  } catch (error) {

    // Session is invalid, create response to clear cookies
    const response = NextResponse.json(
      {
        valid: false,
        error: 'Session invalid or expired',
      },
      { status: 401 }
    )

    // Always clear both admin and student cookies to ensure complete logout
    const adminCleared = clearAuthCookies(response, 'admin')
    const bothCleared = clearAuthCookies(adminCleared, 'student')

    // Also clear any legacy cookies
    bothCleared.cookies.set('auth_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/',
    })

    bothCleared.cookies.set('refresh_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/',
    })

    return bothCleared
  }
}

/**
 * POST /api/auth/check-session
 * Check session for specific role
 */
export async function POST(request: NextRequest) {
  let role: string | undefined

  try {
    const body = await request.json()
    role = body.role

    if (!role || !['admin', 'student'].includes(role)) {
      return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 })
    }

    // Try to authenticate with specific role
    const authResult = await authenticateWithSession(request, role as 'admin' | 'student')

    return NextResponse.json({
      valid: true,
      user: {
        id: authResult.id,
        role: authResult.role,
        sessionId: authResult.sessionId,
      },
    })
  } catch (error) {
    console.log('Session check failed for role:', role, error)

    // Session is invalid, create response to clear cookies
    const response = NextResponse.json(
      {
        valid: false,
        error: 'Session invalid or expired',
      },
      { status: 401 }
    )

    // Always clear both admin and student cookies to ensure complete logout
    const adminCleared = clearAuthCookies(response, 'admin')
    const bothCleared = clearAuthCookies(adminCleared, 'student')

    // Also clear any legacy cookies
    bothCleared.cookies.set('auth_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/',
    })

    bothCleared.cookies.set('refresh_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/',
    })

    return bothCleared
  }
}
