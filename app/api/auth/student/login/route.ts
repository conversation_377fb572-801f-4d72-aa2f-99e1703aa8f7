/**
 * 🎯 HYBRID AUTH STUDENT LOGIN ENDPOINT
 *
 * Migrated to use hybrid JWT + Redis authentication system
 * Features:
 * - JWT + Redis session management
 * - One device per user policy
 * - Device fingerprinting
 * - Secure cookie handling
 */

import { NextRequest, NextResponse } from 'next/server'
import { hybridAuth } from '@/lib/auth/hybrid-auth-manager'
import { extractDeviceInfo } from '@/lib/middleware/hybrid-auth-middleware'
import { StudentRepository } from '@/lib/data/repositories/student'
import { getRedisCache } from '@/lib/data/cache/redis'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository()

// Input validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
})

/**
 * ✅ HYBRID AUTH: Student login with JWT + Redis session management
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const result = loginSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.format() },
        { status: 400 }
      )
    }

    const { username, password } = result.data

    // ✅ STEP 1: Get student from database
    const student = await studentRepo.findByUsername(username)

    if (!student) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 })
    }

    // ✅ STEP 2: Verify password
    const isPasswordValid = await studentRepo.verifyPassword(student, password)

    if (!isPasswordValid) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 })
    }

    // ✅ STEP 3: Extract device information
    const deviceInfo = extractDeviceInfo(request)

    console.log(`🔐 HYBRID LOGIN: Student ${student.id} from device ${deviceInfo.deviceId}`)

    // ✅ STEP 4: Create hybrid session (JWT + Redis)
    const authResult = await hybridAuth.login(student.id, 'student', deviceInfo)

    // ✅ STEP 5: Set secure cookies
    const response = NextResponse.json({
      success: true,
      student: {
        id: student.id,
        name: student.name,
        uniqueCode: student.uniqueCode,
      },
      sessionId: authResult.sessionId,
      expiresIn: authResult.expiresIn,
    })

    // Set access token cookie (short-lived)
    response.cookies.set('student_auth_token', authResult.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: authResult.expiresIn,
      path: '/',
    })

    // Set refresh token cookie (long-lived)
    response.cookies.set('student_auth_token_refresh', authResult.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    })

    return response
  } catch (error) {
    console.error('Hybrid student login error:', error)
    return NextResponse.json({ error: 'Login failed' }, { status: 500 })
  }
}
