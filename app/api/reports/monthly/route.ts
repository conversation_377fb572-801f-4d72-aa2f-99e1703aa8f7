import { NextRequest, NextResponse } from 'next/server'
import { ReportsUseCases } from '@/lib/domain/usecases/reports'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { authenticateRequest } from "@/lib/middleware/hybrid-auth-middleware"
import { getCurrentWITATime } from '@/lib/utils/date'
import { z } from 'zod'

// Initialize dependencies with clean architecture
const absenceRepository = new AbsenceRepository()
const reportsUseCases = new ReportsUseCases(absenceRepository)

// Request validation schema
const MonthlyReportRequestSchema = z.object({
  month: z.coerce.number().min(1).max(12),
  year: z.coerce.number().min(2020).max(2030),
  class: z.string().optional(),
  reportType: z.enum(['prayer', 'school', 'all']).default('all'),
  forceFresh: z.coerce.boolean().default(false),
})

/**
 * GET /api/reports/monthly
 * Get monthly attendance report with week-by-week breakdown
 *
 * Query Parameters:
 * - month: Month number (1-12)
 * - year: Year (2020-2030)
 * - class: Class name filter (optional)
 * - reportType: 'prayer' | 'school' | 'all' (default: 'all')
 * - forceFresh: Force fresh data (default: false)
 */
export async function GET(req: NextRequest) {
  const startTime = Date.now()

  try {
    // Authenticate admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    // Parse and validate query parameters
    const searchParams = req.nextUrl.searchParams
    const rawParams = {
      month: searchParams.get('month'),
      year: searchParams.get('year'),
      class: searchParams.get('class') || undefined, // ✅ FIXED: Convert null to undefined
      reportType: searchParams.get('reportType') || 'all',
      forceFresh: searchParams.get('forceFresh') === 'true',
    }

    const validationResult = MonthlyReportRequestSchema.safeParse(rawParams)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid parameters',
          details: validationResult.error.errors,
          timestamp: getCurrentWITATime().toISOString(),
        },
        { status: 400 }
      )
    }

    const { month, year, class: className, reportType, forceFresh } = validationResult.data

    console.log(
      `📊 Monthly Report Request: ${month}/${year} (${reportType}) - Class: ${className || 'all'}`
    )

    // Generate monthly report using clean architecture
    const report = await reportsUseCases.getMonthlyReport(
      month,
      year,
      className,
      reportType,
      forceFresh
    )

    const queryTime = Date.now() - startTime

    // ✅ UNIFIED CACHE STRATEGY: No TTL - cache managed by unified strategy
    const headers = new Headers()
    headers.set('Cache-Control', 'no-store, max-age=0') // No browser cache - unified strategy handles caching
    headers.set('X-Query-Time', queryTime.toString())
    headers.set('X-Cache-Key', report.metadata.cacheKey)
    headers.set('X-Generated-At', report.metadata.generatedAt)

    return NextResponse.json(
      {
        success: true,
        data: report,
        metadata: {
          queryTime,
          requestId: Math.random().toString(36).substring(2, 8),
          timestamp: getCurrentWITATime().toISOString(),
          cacheStrategy: 'unified-write-through-no-ttl',
          reportType: 'monthly',
        },
      },
      { headers }
    )
  } catch (error) {
    console.error('Monthly report API error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to generate monthly report',
        timestamp: getCurrentWITATime().toISOString(),
        requestId: Math.random().toString(36).substring(2, 8),
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/reports/monthly
 * Invalidate monthly report cache
 *
 * Body:
 * - month: Month number (optional)
 * - year: Year (optional)
 * - class: Class name (optional)
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    const body = await req.json()
    const { month, year, class: className } = body

    console.log(`📊 Monthly Report Cache Invalidation: ${month || 'all'}/${year || 'all'}`)

    // Invalidate cache using clean architecture
    await reportsUseCases.invalidateReportsCache('monthly', year, month, className)

    return NextResponse.json({
      success: true,
      message: 'Monthly report cache invalidated successfully',
      timestamp: getCurrentWITATime().toISOString(),
    })
  } catch (error) {
    console.error('Monthly report cache invalidation error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to invalidate monthly report cache',
        timestamp: getCurrentWITATime().toISOString(),
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/reports/monthly
 * Clear all monthly report cache
 */
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    console.log('📊 Clearing All Monthly Report Cache')

    // Clear all monthly report cache
    await reportsUseCases.invalidateReportsCache('monthly')

    return NextResponse.json({
      success: true,
      message: 'All monthly report cache cleared successfully',
      timestamp: getCurrentWITATime().toISOString(),
    })
  } catch (error) {
    console.error('Monthly report cache clear error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to clear monthly report cache',
        timestamp: getCurrentWITATime().toISOString(),
      },
      { status: 500 }
    )
  }
}
