import { NextRequest, NextResponse } from 'next/server'
import { ReportsUseCases } from '@/lib/domain/usecases/reports'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { authenticateRequest } from "@/lib/middleware/hybrid-auth-middleware"
import { getCurrentWITATime } from '@/lib/utils/date'
import { z } from 'zod'

// Initialize dependencies with clean architecture
const absenceRepository = new AbsenceRepository()
const reportsUseCases = new ReportsUseCases(absenceRepository)

// Request validation schema
const YearlyReportRequestSchema = z.object({
  year: z.coerce.number().min(2020).max(2030),
  class: z.string().optional(),
  reportType: z.enum(['prayer', 'school', 'all']).default('all'),
  forceFresh: z.coerce.boolean().default(false),
})

/**
 * GET /api/reports/yearly
 * Get yearly attendance report with month-by-month breakdown
 *
 * Query Parameters:
 * - year: Year (2020-2030)
 * - class: Class name filter (optional)
 * - reportType: 'prayer' | 'school' | 'all' (default: 'all')
 * - forceFresh: Force fresh data (default: false)
 */
export async function GET(req: NextRequest) {
  const startTime = Date.now()

  try {
    // Authenticate admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    // Parse and validate query parameters
    const searchParams = req.nextUrl.searchParams
    const rawParams = {
      year: searchParams.get('year'),
      class: searchParams.get('class') || undefined, // ✅ FIXED: Convert null to undefined
      reportType: searchParams.get('reportType') || 'all',
      forceFresh: searchParams.get('forceFresh') === 'true',
    }

    const validationResult = YearlyReportRequestSchema.safeParse(rawParams)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid parameters',
          details: validationResult.error.errors,
          timestamp: getCurrentWITATime().toISOString(),
        },
        { status: 400 }
      )
    }

    const { year, class: className, reportType, forceFresh } = validationResult.data

    console.log(`📊 Yearly Report Request: ${year} (${reportType}) - Class: ${className || 'all'}`)

    // Generate yearly report using clean architecture
    const report = await reportsUseCases.getYearlyReport(year, className, reportType, forceFresh)

    const queryTime = Date.now() - startTime

    // ✅ UNIFIED CACHE STRATEGY: No TTL - cache managed by unified strategy
    const headers = new Headers()
    headers.set('Cache-Control', 'no-store, max-age=0') // No browser cache - unified strategy handles caching
    headers.set('X-Query-Time', queryTime.toString())
    headers.set('X-Cache-Key', report.metadata.cacheKey)
    headers.set('X-Generated-At', report.metadata.generatedAt)

    return NextResponse.json(
      {
        success: true,
        data: report,
        metadata: {
          queryTime,
          requestId: Math.random().toString(36).substring(2, 8),
          timestamp: getCurrentWITATime().toISOString(),
          cacheStrategy: 'unified-write-through-no-ttl',
          reportType: 'yearly',
        },
      },
      { headers }
    )
  } catch (error) {
    console.error('Yearly report API error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to generate yearly report',
        timestamp: getCurrentWITATime().toISOString(),
        requestId: Math.random().toString(36).substring(2, 8),
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/reports/yearly
 * Invalidate yearly report cache
 *
 * Body:
 * - year: Year (optional)
 * - class: Class name (optional)
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    const body = await req.json()
    const { year, class: className } = body

    console.log(`📊 Yearly Report Cache Invalidation: ${year || 'all'}`)

    // Invalidate cache using clean architecture
    await reportsUseCases.invalidateReportsCache('yearly', year, undefined, className)

    return NextResponse.json({
      success: true,
      message: 'Yearly report cache invalidated successfully',
      timestamp: getCurrentWITATime().toISOString(),
    })
  } catch (error) {
    console.error('Yearly report cache invalidation error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to invalidate yearly report cache',
        timestamp: getCurrentWITATime().toISOString(),
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/reports/yearly
 * Clear all yearly report cache
 */
export async function DELETE(req: NextRequest) {
  try {
    // Authenticate admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    console.log('📊 Clearing All Yearly Report Cache')

    // Clear all yearly report cache
    await reportsUseCases.invalidateReportsCache('yearly')

    return NextResponse.json({
      success: true,
      message: 'All yearly report cache cleared successfully',
      timestamp: getCurrentWITATime().toISOString(),
    })
  } catch (error) {
    console.error('Yearly report cache clear error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to clear yearly report cache',
        timestamp: getCurrentWITATime().toISOString(),
      },
      { status: 500 }
    )
  }
}
