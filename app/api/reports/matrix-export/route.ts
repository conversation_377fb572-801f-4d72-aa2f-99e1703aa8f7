import { NextRequest, NextResponse } from 'next/server'
import { RealMatrixExportRepository } from '@/lib/data/repositories/real-matrix-export-repository'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { z } from 'zod'

/**
 * API endpoint for fetching matrix export data
 * Server-side only - handles database queries
 */

const matrixExportSchema = z.object({
  startDate: z.string(),
  endDate: z.string(),
  classFilter: z.array(z.string()).optional(),
})

/**
 * GET /api/reports/matrix-export
 * Fetch detailed attendance data for matrix export
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate admin
    const authResult = await authenticateRequest(req, 'admin')
    if (!authResult.isValid)
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })

    // Parse query parameters
    const { searchParams } = new URL(req.url)
    const startDateStr = searchParams.get('startDate')
    const endDateStr = searchParams.get('endDate')
    const classFilterStr = searchParams.get('classFilter')

    if (!startDateStr || !endDateStr) {
      return NextResponse.json({ error: 'startDate and endDate are required' }, { status: 400 })
    }

    // Parse and validate parameters
    const startDate = new Date(startDateStr)
    const endDate = new Date(endDateStr)

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
    }

    // Parse class filter if provided
    let classFilter: string[] | undefined
    if (classFilterStr) {
      try {
        classFilter = JSON.parse(classFilterStr)
      } catch {
        return NextResponse.json({ error: 'Invalid classFilter format' }, { status: 400 })
      }
    }

    // SAFETY CHECK: Prevent memory overload
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

    // Allow yearly reports (up to 366 days) for single class, otherwise limit to 93 days
    const isYearlyWithSingleClass =
      classFilter && classFilter.length === 1 && daysDiff > 93 && daysDiff <= 366
    const maxDays = isYearlyWithSingleClass ? 366 : 93

    if (daysDiff > maxDays) {
      const errorMessage = isYearlyWithSingleClass
        ? `Maximum ${maxDays} days allowed for yearly reports. Requested: ${daysDiff} days.`
        : `Maximum ${maxDays} days allowed. Requested: ${daysDiff} days. Please use smaller date ranges or select exactly 1 class for yearly reports.`

      return NextResponse.json(
        {
          error: 'Date range too large',
          details: errorMessage,
          maxDays,
          requestedDays: daysDiff,
        },
        { status: 400 }
      )
    }

    console.log('🚀 API MATRIX EXPORT: Processing request', {
      startDate: startDateStr,
      endDate: endDateStr,
      classFilter: classFilter?.length || 'all',
      classFilterValues: classFilter,
      daysDiff,
      maxDays,
    })

    // Initialize repository and fetch data
    const repository = new RealMatrixExportRepository()
    const data = await repository.getDetailedAttendanceForMatrix(startDate, endDate, classFilter)

    console.log(`✅ API MATRIX EXPORT: Returning ${data.length} records`)

    return NextResponse.json({
      success: true,
      data,
      meta: {
        startDate: startDateStr,
        endDate: endDateStr,
        classFilter: classFilter || null,
        recordCount: data.length,
      },
    })
  } catch (error) {
    console.error('❌ API MATRIX EXPORT: Error:', error)

    if (error instanceof Error && error.message.includes('Unauthorized')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch matrix export data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/reports/matrix-export
 * Alternative endpoint for complex requests
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate admin
    const authResult = await authenticateRequest(req, 'admin')
    if (!authResult.isValid)
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })

    // Parse request body
    const body = await req.json()
    const {
      startDate: startDateStr,
      endDate: endDateStr,
      classFilter,
    } = matrixExportSchema.parse(body)

    const startDate = new Date(startDateStr)
    const endDate = new Date(endDateStr)

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
    }

    // SAFETY CHECK: Prevent memory overload
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

    // Allow yearly reports (up to 366 days) for single class, otherwise limit to 93 days
    const isYearlyWithSingleClass =
      classFilter && classFilter.length === 1 && daysDiff > 93 && daysDiff <= 366
    const maxDays = isYearlyWithSingleClass ? 366 : 93

    if (daysDiff > maxDays) {
      const errorMessage = isYearlyWithSingleClass
        ? `Maximum ${maxDays} days allowed for yearly reports. Requested: ${daysDiff} days.`
        : `Maximum ${maxDays} days allowed. Requested: ${daysDiff} days. Please use smaller date ranges or select exactly 1 class for yearly reports.`

      return NextResponse.json(
        {
          error: 'Date range too large',
          details: errorMessage,
          maxDays,
          requestedDays: daysDiff,
        },
        { status: 400 }
      )
    }

    console.log('🚀 API MATRIX EXPORT (POST): Processing request', {
      startDate: startDateStr,
      endDate: endDateStr,
      classFilter: classFilter?.length || 'all',
      daysDiff,
      maxDays,
    })

    // Initialize repository and fetch data
    const repository = new RealMatrixExportRepository()
    const data = await repository.getDetailedAttendanceForMatrix(startDate, endDate, classFilter)

    console.log(`✅ API MATRIX EXPORT (POST): Returning ${data.length} records`)

    return NextResponse.json({
      success: true,
      data,
      meta: {
        startDate: startDateStr,
        endDate: endDateStr,
        classFilter: classFilter || null,
        recordCount: data.length,
      },
    })
  } catch (error) {
    console.error('❌ API MATRIX EXPORT (POST): Error:', error)

    if (error instanceof Error && error.message.includes('Unauthorized')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        error: 'Failed to fetch matrix export data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
