import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from "@/lib/middleware/hybrid-auth-middleware"
import { z } from 'zod'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { parse } from 'csv-parse/sync'
import ExcelJS from 'exceljs'
// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository()
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)
// Schema for a single student row in CSV
const csvStudentSchema = z.object({
  name: z.string().min(1, 'Nama wajib diisi'),
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username hanya boleh berisi huruf, angka, dan underscore'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
  className: z.string().min(1, 'Nama kelas wajib diisi'),
  nis: z.string().optional(),
  googleEmail: z
    .string()
    .refine(val => val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'Format email tidak valid',
    })
    .optional()
    .transform(val => (val === '' ? undefined : val)),
  whatsapp: z.string().optional(),
  gender: z.string().min(1, 'Gender wajib diisi'),
})
// Helper to normalize gender
function normalizeGender(g: any): 'male' | 'female' | undefined {
  if (!g) return undefined
  if (typeof g === 'string') {
    const s = g.trim().toLowerCase()
    if (s === 'laki-laki' || s === 'male' || s === 'l') return 'male'
    if (s === 'perempuan' || s === 'female' || s === 'p') return 'female'
  }
  return undefined
}
/**
 * POST /api/users/bulk-upload
 * Upload a CSV file with multiple students
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }
    // Check if the request is multipart/form-data
    const contentType = req.headers.get('content-type') || ''
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { message: 'Content-Type harus multipart/form-data' },
        { status: 400 }
      )
    }
    // Parse the form data
    const formData = await req.formData()
    const file = formData.get('file') as File | null
    if (!file) {
      return NextResponse.json({ message: 'File tidak ditemukan' }, { status: 400 })
    }
    // Verify file extension
    const fileName = file.name.toLowerCase()
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      return NextResponse.json(
        { message: 'File harus berformat CSV atau Excel (.csv, .xlsx, .xls)' },
        { status: 400 }
      )
    }
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ message: 'Ukuran file maksimal 10MB' }, { status: 400 })
    }
    // Parse file content based on file type
    let records: any[]
    try {
      if (fileName.endsWith('.csv')) {
        // Parse CSV file
        const fileContent = await file.text()
        records = parse(fileContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
          delimiter: ',',
          quote: '"',
          escape: '"',
        })
      } else {
        // Parse Excel file (.xlsx or .xls)
        const buffer = await file.arrayBuffer()
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(buffer)
        const worksheet = workbook.getWorksheet(1) // Get first worksheet
        if (!worksheet) {
          return NextResponse.json(
            { message: 'File Excel tidak berisi worksheet' },
            { status: 400 }
          )
        }
        // Convert worksheet to array of objects
        const headers: string[] = []
        const rows: any[] = []
        // Get headers from first row
        const headerRow = worksheet.getRow(1)
        headerRow.eachCell((cell, colNumber) => {
          headers[colNumber - 1] = String(cell.value || '').trim()
        })
        // Get data rows
        for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
          const row = worksheet.getRow(rowNumber)
          const rowData: any = {}
          let hasData = false
          row.eachCell((cell, colNumber) => {
            const header = headers[colNumber - 1]
            if (header) {
              const value = String(cell.value || '').trim()
              rowData[header] = value
              if (value) hasData = true
            }
          })
          if (hasData) {
            rows.push(rowData)
          }
        }
        records = rows
      }
    } catch (error) {
      console.error('Failed to parse file:', error)
      return NextResponse.json(
        { message: 'Format file tidak valid', error: (error as Error).message },
        { status: 400 }
      )
    }
    if (records.length === 0) {
      return NextResponse.json({ message: 'File tidak berisi data' }, { status: 400 })
    }
    // Check for maximum records limit
    const maxRecords = 1000
    if (records.length > maxRecords) {
      return NextResponse.json(
        { message: `Maksimal ${maxRecords} siswa dapat diimport sekaligus` },
        { status: 400 }
      )
    }
    // Validate and process the records in batches
    const batchSize = 100 // Process 100 records at a time
    const results = {
      total: records.length,
      success: 0,
      failed: 0,
      errors: [] as { row: number; message: string; data: any }[],
    }
    // Cache class IDs to reduce database queries
    const classIdCache = new Map<string, number>()
    // Process in batches
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize)
      const batchPromises = batch.map(async (record, index) => {
        const rowIndex = i + index + 1 // CSV row number (1-indexed)
        try {
          // Validate CSV row data
          const validationResult = csvStudentSchema.safeParse(record)
          if (!validationResult.success) {
            const errorMessages = validationResult.error.errors
              .map(err => `${err.path.join('.')}: ${err.message}`)
              .join(', ')
            results.failed++
            results.errors.push({
              row: rowIndex,
              message: 'Validasi gagal: ' + errorMessages,
              data: record,
            })
            return null
          }
          const validData = validationResult.data
          // Validate gender normalization
          const normalizedGender = normalizeGender(validData.gender)
          if (!normalizedGender) {
            results.failed++
            results.errors.push({
              row: rowIndex,
              message: 'Gender harus "male", "female", "Laki-laki", atau "Perempuan"',
              data: record,
            })
            return null
          }
          // Check if username already exists (more efficient than in use case)
          const existingStudent = await studentRepo.findByUsername(validData.username)
          if (existingStudent) {
            results.failed++
            results.errors.push({
              row: rowIndex,
              message: `Username '${validData.username}' sudah ada di database`,
              data: record,
            })
            return null
          }
          // Find or create class with better error handling
          let classId: number
          if (classIdCache.has(validData.className)) {
            classId = classIdCache.get(validData.className)!
          } else {
            try {
              const classObj = await userUseCases.findOrCreateClass(validData.className)
              classId = classObj.id
              classIdCache.set(validData.className, classId)
            } catch (classError) {
              const errorMessage =
                classError instanceof Error ? classError.message : 'Unknown error'
              results.failed++
              results.errors.push({
                row: rowIndex,
                message: `Error dengan kelas '${validData.className}': ${errorMessage}`,
                data: record,
              })
              return null
            }
          }
          // Create student
          await userUseCases.createStudent({
            name: validData.name,
            username: validData.username,
            password: validData.password,
            classId: classId,
            nis: validData.nis,
            googleEmail: validData.googleEmail,
            whatsapp: validData.whatsapp,
            gender: normalizedGender,
          })
          results.success++
          return true
        } catch (error) {
          results.failed++
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          // Handle specific database errors
          if (
            errorMessage.toLowerCase().includes('duplicate') ||
            errorMessage.toLowerCase().includes('already exists') ||
            errorMessage.toLowerCase().includes('unique constraint')
          ) {
            results.errors.push({
              row: rowIndex,
              message: `Username '${record.username}' sudah ada: ${errorMessage}`,
              data: record,
            })
          } else {
            results.errors.push({
              row: rowIndex,
              message: 'Error saat menyimpan: ' + errorMessage,
              data: record,
            })
          }
          return null
        }
      })
      // Wait for the current batch to complete
      await Promise.all(batchPromises)
      // Clear cache after each batch
      await cache.del('users:all')
      await cache.del('classes:all')
    }
    return NextResponse.json({
      message: `Bulk upload selesai. Berhasil: ${results.success}, Gagal: ${results.failed} dari ${results.total}`,
      results,
    })
  } catch (error) {
    console.error('Bulk upload error:', error instanceof Error ? error.stack : error)
    return NextResponse.json(
      {
        message: 'Gagal memproses bulk upload',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
