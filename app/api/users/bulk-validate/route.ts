import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { z } from 'zod'
import { parse } from 'csv-parse/sync'
import ExcelJS from 'exceljs'

// Schema for a single student row in CSV (same as bulk-upload)
const csvStudentSchema = z.object({
  name: z.string().min(1, 'Nama wajib diisi'),
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username hanya boleh berisi huruf, angka, dan underscore'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
  className: z.string().min(1, 'Nama kelas wajib diisi'),
  nis: z.string().optional(),
  googleEmail: z
    .string()
    .refine(val => val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'Format email tidak valid',
    })
    .optional()
    .transform(val => (val === '' ? undefined : val)),
  whatsapp: z.string().optional(),
  gender: z.string().min(1, 'Gender wajib diisi'),
})

// Gender normalization function (same as bulk-upload)
function normalizeGender(gender: string): 'male' | 'female' | null {
  const normalized = gender.toLowerCase().trim()
  if (normalized === 'male' || normalized === 'laki-laki') {
    return 'male'
  }
  if (normalized === 'female' || normalized === 'perempuan') {
    return 'female'
  }
  return null
}

interface ValidationResult {
  isValid: boolean
  totalRecords: number
  validRecords: number
  errors: Array<{
    row: number
    field: string
    message: string
    value: any
  }>
  warnings: Array<{
    row: number
    field: string
    message: string
    value: any
  }>
  preview: {
    newClasses: string[]
    duplicateUsernames: Array<{
      row: number
      username: string
    }>
    sampleData: any[]
  }
}

/**
 * POST /api/users/bulk-validate
 * Validate CSV file with comprehensive database checks before import
 */
export async function POST(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Check if the request is multipart/form-data
    const contentType = req.headers.get('content-type') || ''
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { message: 'Content-Type harus multipart/form-data' },
        { status: 400 }
      )
    }

    // Parse the form data
    const formData = await req.formData()
    const file = formData.get('file') as File | null

    if (!file) {
      return NextResponse.json({ message: 'File tidak ditemukan' }, { status: 400 })
    }

    // Enhanced file validation
    const fileName = file.name.toLowerCase()
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      return NextResponse.json(
        { message: 'File harus berformat CSV atau Excel (.csv, .xlsx, .xls)' },
        { status: 400 }
      )
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ message: 'Ukuran file maksimal 10MB' }, { status: 400 })
    }

    // Check if file is empty
    if (file.size === 0) {
      return NextResponse.json({ message: 'File kosong' }, { status: 400 })
    }

    // Initialize repositories
    const cache = getRedisCache()
    const studentRepo = new StudentRepository()
    const adminRepo = new AdminRepository(cache)
    const classRepo = new ClassRepository()
    const absenceRepo = new AbsenceRepository()
    const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

    // Parse file content
    let records: any[] = []
    try {
      const fileBuffer = await file.arrayBuffer()
      const fileName = file.name.toLowerCase()

      if (fileName.endsWith('.csv')) {
        const csvContent = new TextDecoder('utf-8').decode(fileBuffer)
        records = parse(csvContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
        })
      } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(fileBuffer)
        const worksheet = workbook.getWorksheet(1)

        if (!worksheet) {
          return NextResponse.json({ message: 'Worksheet tidak ditemukan' }, { status: 400 })
        }

        const headers: string[] = []
        const firstRow = worksheet.getRow(1)
        firstRow.eachCell((cell, colNumber) => {
          headers[colNumber - 1] = cell.text.trim()
        })

        records = []
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber === 1) return // Skip header row

          const record: any = {}
          row.eachCell((cell, colNumber) => {
            const header = headers[colNumber - 1]
            if (header) {
              record[header] = cell.text.trim()
            }
          })

          if (Object.keys(record).length > 0) {
            records.push(record)
          }
        })
      } else {
        return NextResponse.json(
          { message: 'Format file tidak didukung. Gunakan CSV atau Excel.' },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error('Failed to parse file:', error)
      return NextResponse.json(
        { message: 'Format file tidak valid', error: (error as Error).message },
        { status: 400 }
      )
    }

    if (records.length === 0) {
      return NextResponse.json({ message: 'File tidak berisi data' }, { status: 400 })
    }

    // Check for maximum records limit
    const maxRecords = 1000
    if (records.length > maxRecords) {
      return NextResponse.json(
        { message: `Maksimal ${maxRecords} siswa dapat divalidasi sekaligus` },
        { status: 400 }
      )
    }

    // Perform comprehensive validation
    const validationResult = await performComprehensiveValidation(
      records,
      userUseCases,
      studentRepo,
      classRepo,
      cache
    )

    return NextResponse.json(validationResult)
  } catch (error) {
    console.error('Bulk validation error:', error)
    return NextResponse.json(
      { message: 'Terjadi kesalahan saat validasi', error: (error as Error).message },
      { status: 500 }
    )
  }
}

async function performComprehensiveValidation(
  records: any[],
  userUseCases: UserUseCases,
  studentRepo: StudentRepository,
  classRepo: ClassRepository,
  cache: any
): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    totalRecords: records.length,
    validRecords: 0,
    errors: [],
    warnings: [],
    preview: {
      newClasses: [],
      duplicateUsernames: [],
      sampleData: records.slice(0, 5), // Show first 5 records as preview
    },
  }

  // Get existing data for validation (fresh from database)
  // Clear all relevant caches to ensure fresh data
  await cache.del('classes:all')
  await cache.del('users:all')

  // Clear individual student caches for the usernames and NIS we're checking
  const allUsernames = records.map(r => r.username).filter(Boolean)
  const allNIS = records.map(r => r.nis).filter(Boolean)

  // Clear username caches
  for (const username of allUsernames) {
    await cache.del(`student:username:${username}`)
  }

  // Clear NIS caches
  for (const nis of allNIS) {
    await cache.del(`student:nis:${nis}`)
  }

  const existingClasses = await classRepo.findAll()
  const existingClassNames = new Set(existingClasses.map(c => c.name))

  // Batch check existing usernames and NIS (using variables already declared above)
  const existingUsernames = await batchCheckUsernames(allUsernames, studentRepo)
  const existingNIS = await batchCheckNIS(allNIS, studentRepo)

  // Track usernames within the CSV for duplicates
  const csvUsernames = new Set<string>()
  const csvDuplicates = new Set<string>()

  // Validate each record
  for (let i = 0; i < records.length; i++) {
    const record = records[i]
    const rowIndex = i + 1

    try {
      // 1. Schema validation
      const validationResult = csvStudentSchema.safeParse(record)
      if (!validationResult.success) {
        validationResult.error.errors.forEach(err => {
          result.errors.push({
            row: rowIndex,
            field: err.path.join('.'),
            message: err.message,
            value: record[err.path[0]],
          })
        })
        continue
      }

      const validData = validationResult.data

      // 2. Check username duplicates in database
      if (existingUsernames.has(validData.username)) {
        result.errors.push({
          row: rowIndex,
          field: 'username',
          message: `Username '${validData.username}' sudah ada di database`,
          value: validData.username,
        })
        result.preview.duplicateUsernames.push({
          row: rowIndex,
          username: validData.username,
        })
      }

      // 3. Check username duplicates within CSV
      if (csvUsernames.has(validData.username)) {
        csvDuplicates.add(validData.username)
        result.errors.push({
          row: rowIndex,
          field: 'username',
          message: `Username '${validData.username}' duplikat dalam file CSV`,
          value: validData.username,
        })
      } else {
        csvUsernames.add(validData.username)
      }

      // 4. Check NIS duplicates in database and within CSV
      if (validData.nis) {
        if (existingNIS.has(validData.nis)) {
          result.errors.push({
            row: rowIndex,
            field: 'nis',
            message: `NIS '${validData.nis}' sudah ada di database`,
            value: validData.nis,
          })
        }

        // Check NIS duplicates within CSV
        const csvNISKey = `nis_${validData.nis}`
        if (csvUsernames.has(csvNISKey)) {
          result.errors.push({
            row: rowIndex,
            field: 'nis',
            message: `NIS '${validData.nis}' duplikat dalam file CSV`,
            value: validData.nis,
          })
        } else {
          csvUsernames.add(csvNISKey)
        }
      }

      // 4. Check class name and track new classes
      if (!existingClassNames.has(validData.className)) {
        if (!result.preview.newClasses.includes(validData.className)) {
          result.preview.newClasses.push(validData.className)
          result.warnings.push({
            row: rowIndex,
            field: 'className',
            message: `Kelas '${validData.className}' akan dibuat baru`,
            value: validData.className,
          })
        }
      }

      // 5. Test class creation to catch constraint errors early
      try {
        // Try to find or create class to validate it works
        await userUseCases.findOrCreateClass(validData.className)
      } catch (classError) {
        const errorMessage = classError instanceof Error ? classError.message : 'Unknown error'
        if (
          errorMessage.toLowerCase().includes('duplicate') ||
          errorMessage.toLowerCase().includes('unique constraint')
        ) {
          // This shouldn't happen if our logic is correct, but handle it
          result.warnings.push({
            row: rowIndex,
            field: 'className',
            message: `Kelas '${validData.className}' sudah ada (akan digunakan yang existing)`,
            value: validData.className,
          })
        } else {
          result.errors.push({
            row: rowIndex,
            field: 'className',
            message: `Error dengan kelas '${validData.className}': ${errorMessage}`,
            value: validData.className,
          })
        }
      }

      // 5. Validate gender normalization
      const normalizedGender = normalizeGender(validData.gender)
      if (!normalizedGender) {
        result.errors.push({
          row: rowIndex,
          field: 'gender',
          message: 'Gender harus "male", "female", "Laki-laki", atau "Perempuan"',
          value: validData.gender,
        })
      }

      // If no errors for this record, count as valid
      const recordErrors = result.errors.filter(e => e.row === rowIndex)
      if (recordErrors.length === 0) {
        result.validRecords++
      }
    } catch (error) {
      result.errors.push({
        row: rowIndex,
        field: 'general',
        message: `Error validasi: ${(error as Error).message}`,
        value: record,
      })
    }
  }

  // Set overall validation status
  result.isValid = result.errors.length === 0

  return result
}

async function batchCheckUsernames(
  usernames: string[],
  studentRepo: StudentRepository
): Promise<Set<string>> {
  const existingUsernames = new Set<string>()

  // Check in batches to avoid overwhelming the database
  const batchSize = 50
  for (let i = 0; i < usernames.length; i += batchSize) {
    const batch = usernames.slice(i, i + batchSize)

    // Check each username in the batch
    const promises = batch.map(async username => {
      const existing = await studentRepo.findByUsername(username)
      if (existing) {
        existingUsernames.add(username)
      }
    })

    await Promise.all(promises)
  }

  return existingUsernames
}

async function batchCheckNIS(
  nisList: string[],
  studentRepo: StudentRepository
): Promise<Set<string>> {
  const existingNIS = new Set<string>()

  // Check in batches to avoid overwhelming the database
  const batchSize = 50
  for (let i = 0; i < nisList.length; i += batchSize) {
    const batch = nisList.slice(i, i + batchSize)

    // Check each NIS in the batch
    const promises = batch.map(async nis => {
      const existing = await studentRepo.findByNIS(nis)
      if (existing) {
        existingNIS.add(nis)
      }
    })

    await Promise.all(promises)
  }

  return existingNIS
}
