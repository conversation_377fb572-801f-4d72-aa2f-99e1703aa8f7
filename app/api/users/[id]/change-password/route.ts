import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { z } from 'zod'
import { hashPassword } from '@/lib/utils/auth'
import { NotFoundError } from '@/lib/domain/errors'
// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)
// Schema for password change request
const changePasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(8, 'Password minimal 8 karakter')
      .max(100, 'Password maksimal 100 karakter'),
    confirmPassword: z
      .string()
      .min(8, 'Konfirmasi password minimal 8 karakter')
      .max(100, 'Konfirmasi password maksimal 100 karakter'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Password dan konfirmasi password tidak cocok',
    path: ['confirmPassword'],
  })
/**
 * POST /api/users/[id]/change-password
 * Change a student's password by Super Admin
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const adminData = { role: authResult.role }
    // Verify that the authenticated user is a Super Admin
    if (adminData.role !== 'super_admin') {
      return NextResponse.json(
        { error: 'Access denied. Only Super Admin can change student passwords.' },
        { status: 403 }
      )
    }
    // Get and validate user ID
    const { id } = await params
    const userId = parseInt(id)
    if (isNaN(userId)) {
      return NextResponse.json({ error: 'ID User tidak valid' }, { status: 400 })
    }
    // Parse and validate the request body
    let body
    try {
      body = await req.json()
    } catch (jsonError) {
      console.error('Failed to parse request body:', jsonError)
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 })
    }
    const validation = changePasswordSchema.safeParse(body)
    if (!validation.success) {
      console.error('Validation failed:', validation.error.format())
      return NextResponse.json(
        { error: 'Validation failed', details: validation.error.format() },
        { status: 400 }
      )
    }
    const { newPassword } = validation.data
    // Verify that the user exists and is a student
    const user = await userUseCases.getUserById(userId)
    if (!user) {
      return NextResponse.json({ error: 'User tidak ditemukan' }, { status: 404 })
    }
    if (user.role !== 'student') {
      return NextResponse.json(
        { error: 'Hanya password siswa yang dapat diubah melalui endpoint ini' },
        { status: 400 }
      )
    }
    try {
      // Use the domain use case to change the student's password
      await userUseCases.changeStudentPasswordByAdmin(userId, newPassword, adminData.id)
      return NextResponse.json({
        message: 'Password berhasil diubah',
        success: true,
      })
    } catch (error) {
      console.error('Error changing student password:', error)
      if (error instanceof NotFoundError) {
        return NextResponse.json({ error: 'Student tidak ditemukan' }, { status: 404 })
      }
      return NextResponse.json(
        {
          error: 'Gagal mengubah password',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error in change password endpoint:', error)
    return NextResponse.json(
      {
        error: 'Gagal memproses permintaan perubahan password',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
