import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from "@/lib/middleware/hybrid-auth-middleware"
import { z } from 'zod'
import { DuplicateError, NotFoundError } from '@/lib/domain/errors'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Validation schema for updating a class
const updateClassSchema = z.object({
  name: z.string().min(1, 'Nama kelas tidak boleh kosong'),
})

/**
 * GET /api/classes/[id]
 * Get a specific class by ID
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Authenticate the admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    const resolvedParams = await params
    const id = Number(resolvedParams.id)
    if (!id || isNaN(id)) {
      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 })
    }

    // Get the class
    const classEntity = await classRepo.findById(id)
    if (!classEntity) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 })
    }

    // Return the class
    return NextResponse.json(classEntity)
  } catch (error) {
    console.error('Error getting class:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * PUT /api/classes/[id]
 * Update a specific class
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Authenticate the admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    const resolvedParams = await params
    const id = Number(resolvedParams.id)
    if (!id || isNaN(id)) {
      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 })
    }

    try {
      // Parse and validate the request body
      const body = await req.json()
      const { name } = updateClassSchema.parse(body)

      // Update the class
      const updatedClass = await userUseCases.updateClass(id, name)

      // Return the updated class
      return NextResponse.json(updatedClass)
    } catch (error: unknown) {
      if (error instanceof z.ZodError) {
        return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 })
      }

      // Handle not found error
      if (error instanceof NotFoundError) {
        return NextResponse.json({ error: error.message }, { status: 404 })
      }

      // Handle duplicate class error
      if (error instanceof DuplicateError) {
        return NextResponse.json({ error: error.message }, { status: 409 })
      }

      throw error // Re-throw to be caught by outer catch block
    }
  } catch (error) {
    console.error('Error updating class:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * DELETE /api/classes/[id]
 * Delete a specific class
 */
export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Authenticate the admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    const resolvedParams = await params
    const id = Number(resolvedParams.id)
    if (!id || isNaN(id)) {
      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 })
    }

    try {
      // Delete the class
      await userUseCases.deleteClass(id)

      // Return success message
      return NextResponse.json({ message: 'Class deleted successfully' }, { status: 200 })
    } catch (error: unknown) {
      // Handle not found error
      if (error instanceof NotFoundError) {
        return NextResponse.json({ error: error.message }, { status: 404 })
      }

      throw error // Re-throw to be caught by outer catch block
    }
  } catch (error) {
    console.error('Error deleting class:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
