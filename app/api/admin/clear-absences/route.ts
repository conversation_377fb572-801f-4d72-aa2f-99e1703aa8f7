/**
 * Admin API: Clear Absences Data
 *
 * Secure endpoint for Super Admin to clear absences data
 * Includes backup creation and audit logging
 */

import { NextRequest, NextResponse } from 'next/server'
import { db, schema } from '@/lib/data/drizzle/db'
import { sql } from 'drizzle-orm'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import type { UserRole } from '@/lib/config/role-permissions'
import { getCurrentWITATime } from '@/lib/utils/date'

interface ClearAbsencesRequest {
  confirmationCode: string
  createBackup: boolean
  reason: string
}

interface AbsencesStats {
  total: number
  zuhr: number
  asr: number
  entry: number
  departure: number
  ijin: number
  oldest: string | null
  newest: string | null
}

/**
 * Get absences statistics
 */
async function getAbsencesStats(): Promise<AbsencesStats> {
  const stats = await db
    .select({
      total: sql<number>`COUNT(*)`,
      zuhr: sql<number>`COUNT(CASE WHEN type = 'zuhr' THEN 1 END)`,
      asr: sql<number>`COUNT(CASE WHEN type = 'asr' THEN 1 END)`,
      entry: sql<number>`COUNT(CASE WHEN type = 'entry' THEN 1 END)`,
      departure: sql<number>`COUNT(CASE WHEN type = 'departure' THEN 1 END)`,
      ijin: sql<number>`COUNT(CASE WHEN type = 'ijin' THEN 1 END)`,
      oldest: sql<string>`MIN(recorded_at)::text`,
      newest: sql<string>`MAX(recorded_at)::text`,
    })
    .from(schema.absences)

  return stats[0] as AbsencesStats
}

/**
 * Create backup of absences data
 */
async function createAbsencesBackup(): Promise<any[]> {
  const absencesData = await db.select().from(schema.absences)
  return absencesData
}

/**
 * Clear all absences data
 */
async function clearAllAbsences(): Promise<number> {
  // Delete all records
  const result = await db.delete(schema.absences)

  // Reset auto-increment sequence
  await db.execute(sql`ALTER SEQUENCE absences_id_seq RESTART WITH 1`)

  return result.rowCount || 0
}

/**
 * Log admin action for audit trail
 */
async function logAdminAction(adminId: string, action: string, details: any) {
  // In a real application, you would log this to an audit table
  console.log('🔒 ADMIN ACTION LOGGED:', {
    adminId,
    action,
    details,
    timestamp: getCurrentWITATime().toISOString(),
  })
}

/**
 * POST /api/admin/clear-absences
 * Clear all absences data (Super Admin only)
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate admin
    const authResult = await authenticateAdmin(req)
    if (!authResult.success || !authResult.user) {
      return handleAuthError(authResult)
    }

    // Check if user is Super Admin
    if (authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        {
          error: 'Access denied',
          message: 'Only Super Admin can clear absences data',
        },
        { status: 403 }
      )
    }

    // Parse request body
    const body: ClearAbsencesRequest = await req.json()
    const { confirmationCode, createBackup, reason } = body

    // Validate confirmation code
    const expectedCode = 'CLEAR_ALL_ABSENCES_DATA'
    if (confirmationCode !== expectedCode) {
      return NextResponse.json(
        {
          error: 'Invalid confirmation code',
          message: `Expected: "${expectedCode}"`,
        },
        { status: 400 }
      )
    }

    // Validate reason
    if (!reason || reason.trim().length < 10) {
      return NextResponse.json(
        {
          error: 'Reason required',
          message: 'Please provide a detailed reason (minimum 10 characters)',
        },
        { status: 400 }
      )
    }

    // Get current statistics
    const statsBefore = await getAbsencesStats()

    if (statsBefore.total === 0) {
      return NextResponse.json({
        success: true,
        message: 'No absences data found to clear',
        stats: statsBefore,
      })
    }

    let backupData = null

    // Create backup if requested
    if (createBackup) {
      backupData = await createAbsencesBackup()
    }

    // Clear all absences data
    const deletedCount = await clearAllAbsences()

    // Get statistics after deletion
    const statsAfter = await getAbsencesStats()

    // Log admin action
    await logAdminAction(authResult.user.id, 'CLEAR_ABSENCES_DATA', {
      reason,
      statsBefore,
      statsAfter,
      deletedCount,
      backupCreated: createBackup,
      backupSize: backupData?.length || 0,
    })

    return NextResponse.json({
      success: true,
      message: 'Absences data cleared successfully',
      data: {
        statsBefore,
        statsAfter,
        deletedCount,
        backupCreated: createBackup,
        backupData: createBackup ? backupData : null,
        timestamp: getCurrentWITATime().toISOString(),
        adminId: authResult.user.id,
        reason,
      },
    })
  } catch (error) {
    console.error('❌ Error clearing absences data:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to clear absences data',
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/admin/clear-absences
 * Get absences statistics (for confirmation dialog)
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate admin
    const authResult = await authenticateAdmin(req)
    if (!authResult.success || !authResult.user) {
      return handleAuthError(authResult)
    }

    // Check if user is Super Admin
    if (authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        {
          error: 'Access denied',
          message: 'Only Super Admin can access this endpoint',
        },
        { status: 403 }
      )
    }

    // Get current statistics
    const stats = await getAbsencesStats()

    return NextResponse.json({
      success: true,
      data: {
        stats,
        confirmationCode: 'CLEAR_ALL_ABSENCES_DATA',
        warning: 'This action will permanently delete all absences data',
        timestamp: getCurrentWITATime().toISOString(),
      },
    })
  } catch (error) {
    console.error('❌ Error getting absences stats:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to get absences statistics',
      },
      { status: 500 }
    )
  }
}
