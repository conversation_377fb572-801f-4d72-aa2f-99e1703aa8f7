import { NextRequest, NextResponse } from 'next/server'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const adminRepo = new AdminRepository(cache)

// Input validation schema
const updateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required'),
})

/**
 * GET /api/admin/profile
 * Gets the admin's profile data
 */
export async function GET(request: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Get admin data from database
    const admin = await adminRepo.findById(authResult.userId!)

    if (!admin) {
      return NextResponse.json({ error: 'Admin not found' }, { status: 404 })
    }

    // Return admin profile data
    return NextResponse.json({
      id: admin.id,
      username: admin.username,
      name: admin.name,
      role: admin.role,
      uniqueCode: admin.uniqueCode,
      createdAt: admin.createdAt,
      updatedAt: admin.updatedAt,
    })
  } catch (error) {
    console.error('Get admin profile error:', error)
    return NextResponse.json({ error: 'Failed to get admin profile' }, { status: 500 })
  }
}

/**
 * PATCH /api/admin/profile
 * Updates the admin's profile
 */
export async function PATCH(request: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Parse and validate the request body
    const body = await request.json()
    const result = updateProfileSchema.safeParse(body)

    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: result.error.format() },
        { status: 400 }
      )
    }

    const { name } = result.data

    // Update the admin's profile
    const admin = await adminRepo.update(authResult.userId!, { name })

    // Return the updated admin data (excluding sensitive information)
    return NextResponse.json({
      success: true,
      admin: {
        id: admin.id,
        username: admin.username,
        name: admin.name,
      },
    })
  } catch (error) {
    console.error('Error updating admin profile:', error)
    return NextResponse.json({ error: 'Failed to update admin profile' }, { status: 500 })
  }
}
