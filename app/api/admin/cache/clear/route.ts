import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { getRedisCache } from '@/lib/data/cache/redis'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'

/**
 * POST /api/admin/cache/clear
 * Clear specific cache patterns or all cache
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate admin user
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const body = await req.json()
    const { pattern = '*', type = 'pattern' } = body

    const cache = getRedisCache()

    // Initialize use cases for materialized view refresh
    const absenceRepo = new AbsenceRepository()
    const studentRepo = new StudentRepository(cache)
    const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo, cache)

    if (type === 'all') {
      // Clear all absence report cache
      const patterns = ['absence:reports:*', 'absence:student:*', 'aggregated:*']

      let clearedCount = 0
      for (const pat of patterns) {
        try {
          // Note: Redis keys() method might not be available in all Redis setups
          // This is a simplified approach - in production, consider using SCAN
          await cache.del(pat)
          clearedCount++
        } catch (error) {
          console.error(`Failed to clear pattern ${pat}:`, error)
        }
      }

      return NextResponse.json({
        status: 'success',
        message: `Cleared ${clearedCount} cache patterns`,
        patterns: patterns,
        timestamp: new Date().toISOString(),
      })
    }

    if (type === 'prayer') {
      // Clear only prayer report cache + refresh materialized view
      const today = new Date().toISOString().split('T')[0]
      const prayerKeys = [
        `absence:reports:prayer:${today}:all:day`,
        `absence:reports:prayer:${today}:*:day`,
        'absence:reports:prayer:today:all:day',
        'absence:reports:prayer:*',
      ]

      // First, refresh materialized view to ensure fresh data
      try {
        await absenceUseCases.refreshAttendanceSummary()
        console.log('✅ Materialized view refreshed')
      } catch (error) {
        console.error('❌ Failed to refresh materialized view:', error)
      }

      let clearedCount = 0
      for (const key of prayerKeys) {
        try {
          await cache.del(key)
          clearedCount++
          console.log(`🗑️ Cleared cache key: ${key}`)
        } catch (error) {
          console.error(`Failed to clear key ${key}:`, error)
        }
      }

      return NextResponse.json({
        status: 'success',
        message: `Refreshed materialized view and cleared ${clearedCount} prayer cache keys`,
        keys: prayerKeys,
        timestamp: new Date().toISOString(),
      })
    }

    if (type === 'pattern') {
      // Clear specific pattern
      try {
        await cache.del(pattern)
        console.log(`🗑️ Cleared cache pattern: ${pattern}`)

        return NextResponse.json({
          status: 'success',
          message: `Cleared cache pattern: ${pattern}`,
          pattern: pattern,
          timestamp: new Date().toISOString(),
        })
      } catch (error) {
        console.error(`Failed to clear pattern ${pattern}:`, error)
        return NextResponse.json(
          {
            status: 'error',
            error: 'Failed to clear cache pattern',
            details: error instanceof Error ? error.message : 'Unknown error',
          },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Invalid type. Use "all", "prayer", or "pattern".' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Cache clear error:', error)
    return NextResponse.json(
      {
        status: 'error',
        error: 'Failed to clear cache',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/admin/cache/clear
 * Get cache clear status and available options
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate admin user
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    return NextResponse.json({
      status: 'ready',
      message: 'Cache clear endpoint is ready',
      options: {
        all: 'Clear all absence report cache',
        prayer: 'Clear only prayer report cache',
        pattern: 'Clear specific cache pattern',
      },
      examples: {
        clearAll: 'POST with { "type": "all" }',
        clearPrayer: 'POST with { "type": "prayer" }',
        clearPattern: 'POST with { "type": "pattern", "pattern": "absence:reports:*" }',
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Cache clear status error:', error)
    return NextResponse.json(
      {
        status: 'error',
        error: 'Failed to get cache clear status',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
