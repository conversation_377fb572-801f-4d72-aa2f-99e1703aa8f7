import { NextRequest, NextResponse } from 'next/server'
import { unifiedCacheStrategy } from '@/lib/services/unified-cache-strategy'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'

/**
 * GET /api/admin/cache/write-through
 * Get write-through cache strategy status and health check
 */
export async function GET(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Perform health check
    const healthCheck = await unifiedCacheStrategy.healthCheck()

    return NextResponse.json({
      status: 'success',
      data: {
        strategy: 'Unified Write-Through Cache (No TTL)',
        description:
          'Updates cache immediately when new attendance is recorded, persists until invalidated',
        healthCheck,
        features: {
          immediateUpdates: 'Reports show new data instantly after attendance',
          cacheConsistency: 'Cache always reflects latest database state',
          fallbackStrategy: 'Auto-delete cache on update failure',
          preemptiveWarming: 'Cache warmed before requests arrive',
        },
        benefits: {
          userExperience: 'No delay in seeing new attendance data',
          performance: 'Fast report loading with up-to-date data',
          reliability: 'Automatic fallback on cache update failures',
          scalability: 'Efficient for high-frequency attendance recording',
        },
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Write-through cache status error:', error)
    return NextResponse.json(
      {
        status: 'error',
        error: 'Failed to get write-through cache status',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/admin/cache/write-through
 * Test write-through cache strategy or trigger cache warming
 */
export async function POST(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const body = await req.json()
    const { action = 'test' } = body

    if (action === 'test') {
      // Test unified cache strategy with sample data
      const { createCacheEvent } = await import('@/lib/services/unified-cache-strategy')
      const { AttendanceType } = await import('@/lib/domain/entities/absence')

      const testEvent = createCacheEvent(
        'TEST_USER_001',
        AttendanceType.ZUHR,
        'create',
        'XII RPL 1'
      )

      // Test the unified cache update
      await unifiedCacheStrategy.updateCacheOnAttendance(testEvent)
      await unifiedCacheStrategy.invalidateRelatedCache(testEvent)

      return NextResponse.json({
        status: 'success',
        message: 'Unified cache strategy test completed',
        testEvent: {
          uniqueCode: testEvent.uniqueCode,
          attendanceType: testEvent.attendanceType,
          className: testEvent.className,
          operation: testEvent.operation,
          timestamp: testEvent.timestamp,
        },
        result: 'Cache updated and invalidated successfully with test data',
      })
    }

    if (action === 'warm') {
      // Note: Unified cache strategy doesn't need warming (no TTL)
      // Cache persists until explicitly invalidated

      return NextResponse.json({
        status: 'success',
        message: 'Cache warming not needed',
        result: 'Unified cache strategy persists data until invalidated (no TTL)',
      })
    }

    if (action === 'health') {
      // Perform detailed health check
      const healthCheck = await unifiedCacheStrategy.healthCheck()

      return NextResponse.json({
        status: 'success',
        message: 'Health check completed',
        healthCheck,
        recommendations:
          healthCheck.status === 'healthy'
            ? ['Cache is working optimally']
            : ['Check Redis connection', 'Verify cache configuration', 'Monitor error logs'],
      })
    }

    return NextResponse.json(
      { error: 'Invalid action. Use "test", "warm", or "health".' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Write-through cache action error:', error)
    return NextResponse.json(
      {
        status: 'error',
        error: 'Write-through cache action failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
