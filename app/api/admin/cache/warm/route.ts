import { NextRequest, NextResponse } from 'next/server'
import { cacheWarmingService } from '@/lib/services/cache-warming'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'

/**
 * POST /api/admin/cache/warm
 * Manually trigger cache warming
 */
export async function POST(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const body = await req.json()
    const { type = 'today' } = body

    console.log(`🔥 Manual cache warming triggered: ${type}`)

    let result: any = {}
    const startTime = Date.now()

    switch (type) {
      case 'today':
        await cacheWarmingService.warmTodayCache()
        result = { message: 'Today cache warming completed' }
        break

      case 'yesterday':
        await cacheWarmingService.warmYesterdayCache()
        result = { message: 'Yesterday cache warming completed' }
        break

      case 'weekly':
        await cacheWarmingService.warmWeeklyCache()
        result = { message: 'Weekly cache warming completed' }
        break

      case 'all':
        await cacheWarmingService.warmAllCache()
        result = { message: 'Comprehensive cache warming completed' }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid warming type. Use: today, yesterday, weekly, or all' },
          { status: 400 }
        )
    }

    const duration = Date.now() - startTime
    result.duration = `${duration}ms`
    result.timestamp = new Date().toISOString()

    return NextResponse.json(result)
  } catch (error) {
    console.error('Cache warming error:', error)
    return NextResponse.json(
      {
        error: 'Cache warming failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/admin/cache/warm
 * Get cache warming status
 */
export async function GET(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const status = cacheWarmingService.getWarmingStatus()

    return NextResponse.json({
      ...status,
      timestamp: new Date().toISOString(),
      availableTypes: ['today', 'yesterday', 'weekly', 'all'],
    })
  } catch (error) {
    console.error('Cache status error:', error)
    return NextResponse.json({ error: 'Failed to get cache status' }, { status: 500 })
  }
}
