import { NextRequest, NextResponse } from 'next/server'
import {
  authenticateSuperAdmin,
  createResponseWithRefreshedToken,
} from '@/lib/middleware/enhanced-auth'
import { SessionUseCases } from '@/lib/domain/usecases/session'
import { RedisSessionRepository } from '@/lib/data/repositories/redis-session-repository'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { SessionFilter } from '@/lib/domain/entities/session'
import { z } from 'zod'
import { UserRole } from '@/lib/config/role-permissions'

// Initialize dependencies
const cache = getRedisCache()
const sessionRepo = new RedisSessionRepository(cache)
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const sessionUseCases = new SessionUseCases(sessionRepo, studentRepo, adminRepo, cache)

// Helper function to get valid roles as literal array for Zod
const getValidRoles = () => {
  return ['student', 'admin', 'super_admin', 'teacher', 'receptionist'] as const
}

// Validation schemas
const sessionFilterSchema = z.object({
  userId: z
    .string()
    .optional()
    .transform(val => (val ? parseInt(val, 10) : undefined)),
  role: z.enum(getValidRoles()).optional(),
  isActive: z
    .string()
    .optional()
    .transform(val => (val === 'true' ? true : val === 'false' ? false : undefined)),
  deviceId: z.string().optional(),
  limit: z
    .string()
    .optional()
    .transform(val => (val ? parseInt(val, 10) : 50)),
  offset: z
    .string()
    .optional()
    .transform(val => (val ? parseInt(val, 10) : 0)),
  createdAfter: z
    .string()
    .optional()
    .transform(val => (val ? new Date(val) : undefined)),
  createdBefore: z
    .string()
    .optional()
    .transform(val => (val ? new Date(val) : undefined)),
})

const deleteSessionSchema = z.object({
  sessionId: z.string().min(1, 'Session ID is required'),
})

const forceLogoutSchema = z.object({
  userId: z.number().min(1, 'User ID is required'),
})

/**
 * GET /api/admin/sessions
 * List sessions with optional filtering (super_admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate as super admin
    const authResult = await authenticateSuperAdmin(request)

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const filterParams = Object.fromEntries(searchParams.entries())

    // Validate filter parameters
    const validationResult = sessionFilterSchema.safeParse(filterParams)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid filter parameters',
          details: validationResult.error.errors,
        },
        { status: 400 }
      )
    }

    const filter: SessionFilter = {
      ...validationResult.data,
      role: validationResult.data.role as UserRole | undefined,
    }

    // Create cache key for this request
    const cacheKey = `sessions:list:${JSON.stringify(filter)}`

    // Check cache first (30 second TTL to reduce repeated calls)
    const forceRefresh = searchParams.get('t') // Force refresh if timestamp param present
    let sessions, totalCount

    if (!forceRefresh) {
      const cached = await cache.get(cacheKey)
      if (cached) {
        const cachedData = JSON.parse(cached)
        sessions = cachedData.sessions
        totalCount = cachedData.totalCount
      }
    }

    // If not cached or force refresh, fetch fresh data
    if (!sessions || !totalCount) {
      sessions = await sessionUseCases.listSessions(filter, authResult.id)
      totalCount = await sessionRepo.getSessionCount(filter)

      // Cache the result for 30 seconds
      await cache.set(cacheKey, JSON.stringify({ sessions, totalCount }), 30)
    }

    // Create response
    const response = NextResponse.json({
      success: true,
      data: {
        sessions,
        pagination: {
          total: totalCount,
          limit: filter.limit || 50,
          offset: filter.offset || 0,
          hasMore: (filter.offset || 0) + sessions.length < totalCount,
        },
      },
    })

    // Handle token refresh if needed
    return createResponseWithRefreshedToken(response, authResult, 'admin')
  } catch (error) {
    console.error('Error listing sessions:', error)

    if (error instanceof Error) {
      if (error.message.includes('Super admin access required')) {
        return NextResponse.json(
          { error: 'Forbidden: Super admin access required' },
          { status: 403 }
        )
      }
      if (error.message.includes('Authentication required')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * DELETE /api/admin/sessions
 * Invalidate a specific session or force logout a user (super_admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate as super admin
    const authResult = await authenticateSuperAdmin(request)

    // Parse request body
    const body = await request.json()

    // Check if it's a session invalidation or user force logout
    if (body.sessionId) {
      // Validate session ID
      const validationResult = deleteSessionSchema.safeParse(body)
      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: 'Invalid request body',
            details: validationResult.error.errors,
          },
          { status: 400 }
        )
      }

      // Invalidate specific session
      const success = await sessionUseCases.adminInvalidateSession(
        validationResult.data.sessionId,
        authResult.id,
        authResult.sessionId // Pass current session ID to prevent self-invalidation
      )

      if (!success) {
        return NextResponse.json(
          { error: 'Session not found or already invalidated' },
          { status: 404 }
        )
      }

      const response = NextResponse.json({
        success: true,
        message: 'Session invalidated successfully',
      })

      return createResponseWithRefreshedToken(response, authResult, 'admin')
    } else if (body.userId) {
      // Validate user ID
      const validationResult = forceLogoutSchema.safeParse(body)
      if (!validationResult.success) {
        return NextResponse.json(
          {
            error: 'Invalid request body',
            details: validationResult.error.errors,
          },
          { status: 400 }
        )
      }

      // Force logout user
      const invalidatedCount = await sessionUseCases.adminForceLogoutUser(
        validationResult.data.userId,
        authResult.id
      )

      const response = NextResponse.json({
        success: true,
        message: `${invalidatedCount} session(s) invalidated successfully`,
        invalidatedCount,
      })

      return createResponseWithRefreshedToken(response, authResult, 'admin')
    } else {
      return NextResponse.json(
        { error: 'Either sessionId or userId must be provided' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error deleting session:', error)

    if (error instanceof Error) {
      if (error.message.includes('Super admin access required')) {
        return NextResponse.json(
          { error: 'Forbidden: Super admin access required' },
          { status: 403 }
        )
      }
      if (error.message.includes('Authentication required')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
      if (
        error.message.includes('Cannot invalidate your own sessions') ||
        error.message.includes('Cannot force logout yourself')
      ) {
        return NextResponse.json({ error: 'Cannot invalidate your own sessions' }, { status: 400 })
      }
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
