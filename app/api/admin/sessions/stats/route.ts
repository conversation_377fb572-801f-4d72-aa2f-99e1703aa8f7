import { NextRequest, NextResponse } from 'next/server'
import {
  authenticateSuperAdmin,
  createResponseWithRefreshedToken,
} from '@/lib/middleware/enhanced-auth'
import { SessionUseCases } from '@/lib/domain/usecases/session'
import { RedisSessionRepository } from '@/lib/data/repositories/redis-session-repository'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'

// Initialize dependencies
const cache = getRedisCache()
const sessionRepo = new RedisSessionRepository(cache)
const studentRepo = new StudentRepository()
const adminRepo = new AdminRepository(cache)
const sessionUseCases = new SessionUseCases(sessionRepo, studentRepo, adminRepo, cache)

/**
 * GET /api/admin/sessions/stats
 * Get session statistics (super_admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate as super admin
    const authResult = await authenticateSuperAdmin(request)

    // Get session statistics
    const stats = await sessionUseCases.getSessionStats(authResult.id)

    // Create response
    const response = NextResponse.json({
      success: true,
      data: stats,
    })

    // Handle token refresh if needed
    return createResponseWithRefreshedToken(response, authResult, 'admin')
  } catch (error) {
    console.error('Error getting session stats:', error)

    if (error instanceof Error) {
      if (error.message.includes('Super admin access required')) {
        return NextResponse.json(
          { error: 'Forbidden: Super admin access required' },
          { status: 403 }
        )
      }
      if (error.message.includes('Authentication required')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
