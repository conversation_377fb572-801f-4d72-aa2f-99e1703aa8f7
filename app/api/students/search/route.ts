import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { db } from '@/lib/data/drizzle/db'
import { users, classes } from '@/lib/data/drizzle/schema'
import { ilike, or, and, eq } from 'drizzle-orm'
import { authenticateRequest } from "@/lib/middleware/hybrid-auth-middleware"

const searchSchema = z.object({
  q: z.string().min(2, 'Query must be at least 2 characters'),
})

export async function GET(request: NextRequest) {
  try {
    // Authenticate admin (includes teacher and receptionist)
    try {
      await authenticateAdmin(request)
    } catch (authError) {
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    const validatedData = searchSchema.parse({ q: query })

    // Search students by name or NIS with class information
    const students = await db
      .select({
        uniqueCode: users.uniqueCode,
        name: users.name,
        className: classes.name,
        nis: users.nis,
      })
      .from(users)
      .leftJoin(classes, eq(users.classId, classes.id))
      .where(
        and(
          eq(users.role, 'student'),
          or(ilike(users.name, `%${validatedData.q}%`), ilike(users.nis, `%${validatedData.q}%`))
        )
      )
      .limit(10)
      .orderBy(users.name)

    return NextResponse.json(students)
  } catch (error) {
    console.error('Error searching students:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid query parameters', errors: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json({ message: 'Internal server error' }, { status: 500 })
  }
}
