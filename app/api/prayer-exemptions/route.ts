/**
 * Prayer Exemptions API - CRUD operations for prayer exemption management
 * Only super_admin can access these endpoints
 */

import { NextRequest, NextResponse } from 'next/server'
import { PrayerExemptionRepository } from '@/lib/data/repositories/prayer-exemption'
import { StudentRepository } from '@/lib/data/repositories/student'
import { ClassRepository } from '@/lib/data/repositories/class'
import { PrayerExemptionUseCases } from '@/lib/domain/usecases/prayer-exemption'
import {
  CreatePrayerExemptionDTO,
  PrayerExemptionType,
  PrayerType,
} from '@/lib/domain/entities/prayer-exemption'
import { canManagePrayerExemptions } from '@/lib/config/role-permissions'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { parseDateStringWITA } from '@/lib/utils/date'

// Initialize repositories and use cases
const cache = getRedisCache()
const prayerExemptionRepo = new PrayerExemptionRepository()
const studentRepo = new StudentRepository()
const classRepo = new ClassRepository()
const adminRepo = new AdminRepository(cache)
const prayerExemptionUseCases = new PrayerExemptionUseCases(
  prayerExemptionRepo,
  studentRepo,
  classRepo
)

/**
 * GET /api/prayer-exemptions - Get all prayer exemptions with pagination
 */
export async function GET(request: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Check authorization - only super admin can manage prayer exemptions
    if (!canManagePrayerExemptions(authResult.role!)) {
      return NextResponse.json(
        { error: 'Unauthorized. Only super admin can access prayer exemptions.' },
        { status: 403 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const exemptionType = searchParams.get('exemptionType') as PrayerExemptionType | null
    const dateFrom = searchParams.get('dateFrom')
      ? new Date(searchParams.get('dateFrom')!)
      : undefined
    const dateTo = searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined
    const isActive = searchParams.get('isActive')
      ? searchParams.get('isActive') === 'true'
      : undefined // No default filter since we use hard delete

    // Get exemptions with filters
    const result = await prayerExemptionUseCases.getExemptions(page, limit, {
      exemptionType: exemptionType || undefined,
      dateFrom,
      dateTo,
      isActive,
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching prayer exemptions:', error)
    return NextResponse.json({ error: 'Failed to fetch prayer exemptions' }, { status: 500 })
  }
}

/**
 * POST /api/prayer-exemptions - Create new prayer exemption
 */
export async function POST(request: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Check authorization - only super admin can create prayer exemptions
    if (!canManagePrayerExemptions(authResult.role!)) {
      return NextResponse.json(
        { error: 'Unauthorized. Only super admin can create prayer exemptions.' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const {
      exemptionType,
      exemptionDate,
      recurrencePattern = 'once',
      startDate,
      endDate,
      daysOfWeek,
      prayerType,
      reason,
    } = body

    // Validate required fields based on recurrence pattern
    if (!exemptionType || !prayerType || !reason || !recurrencePattern) {
      return NextResponse.json(
        { error: 'Missing required fields: exemptionType, prayerType, reason, recurrencePattern' },
        { status: 400 }
      )
    }

    // Validate dates based on recurrence pattern
    if (recurrencePattern === 'once') {
      if (!exemptionDate) {
        return NextResponse.json(
          { error: 'Exemption date is required for one-time exemptions' },
          { status: 400 }
        )
      }
    } else {
      if (!startDate) {
        return NextResponse.json(
          { error: 'Start date is required for recurring exemptions' },
          { status: 400 }
        )
      }

      if (recurrencePattern === 'weekly' && (!daysOfWeek || daysOfWeek.length === 0)) {
        return NextResponse.json(
          { error: 'Days of week are required for weekly recurring exemptions' },
          { status: 400 }
        )
      }
    }

    // Validate enum values
    if (!Object.values(PrayerExemptionType).includes(exemptionType)) {
      return NextResponse.json(
        { error: 'Invalid exemptionType. Must be: global, class' },
        { status: 400 }
      )
    }

    if (!Object.values(PrayerType).includes(prayerType)) {
      return NextResponse.json(
        { error: 'Invalid prayerType. Must be: zuhr, asr, both' },
        { status: 400 }
      )
    }

    // Debug logging
    console.log('Admin data:', {
      id: authResult.userId,
      role: authResult.role,
    })

    // Create exemption DTO - supports both one-time and recurring
    // FIXED: Use SSOT timezone handling for date parsing
    const createDTO: CreatePrayerExemptionDTO = {
      exemptionType,
      targetId: null, // Always null for global exemptions
      recurrencePattern,
      exemptionDate: exemptionDate ? parseDateStringWITA(exemptionDate) : null,
      startDate: startDate ? parseDateStringWITA(startDate) : null,
      endDate: endDate ? parseDateStringWITA(endDate) : null,
      daysOfWeek: daysOfWeek || null,
      prayerType,
      reason: reason.trim(),
      createdBy: authResult.userId!,
    }

    // Create exemption
    const exemption = await prayerExemptionUseCases.createExemption(createDTO, authResult.role!)

    return NextResponse.json(exemption, { status: 201 })
  } catch (error: any) {
    console.error('Error creating prayer exemption:', error)

    // Handle specific error types
    if (error.message.includes('Only super admin')) {
      return NextResponse.json({ error: error.message }, { status: 403 })
    }

    if (
      error.message.includes('Cannot create exemption for past dates') ||
      error.message.includes('Reason must be at least')
    ) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    return NextResponse.json({ error: 'Failed to create prayer exemption' }, { status: 500 })
  }
}
