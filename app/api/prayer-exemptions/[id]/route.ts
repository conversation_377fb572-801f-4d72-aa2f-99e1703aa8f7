/**
 * Prayer Exemptions API - Individual exemption operations
 * Only super_admin can access these endpoints
 */

import { NextRequest, NextResponse } from 'next/server'
import { PrayerExemptionRepository } from '@/lib/data/repositories/prayer-exemption'
import { StudentRepository } from '@/lib/data/repositories/student'
import { ClassRepository } from '@/lib/data/repositories/class'
import { PrayerExemptionUseCases } from '@/lib/domain/usecases/prayer-exemption'
import { UpdatePrayerExemptionDTO } from '@/lib/domain/entities/prayer-exemption'
import { canManagePrayerExemptions } from '@/lib/config/role-permissions'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'

// Initialize repositories and use cases
const cache = getRedisCache()
const prayerExemptionRepo = new PrayerExemptionRepository()
const studentRepo = new StudentRepository()
const classRepo = new ClassRepository()
const adminRepo = new AdminRepository(cache)
const prayerExemptionUseCases = new PrayerExemptionUseCases(
  prayerExemptionRepo,
  studentRepo,
  classRepo
)

/**
 * GET /api/prayer-exemptions/[id] - Get specific prayer exemption
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Check authorization - only super admin can access prayer exemptions
    if (!canManagePrayerExemptions(authResult.role!)) {
      return NextResponse.json(
        { error: 'Unauthorized. Only super admin can access prayer exemptions.' },
        { status: 403 }
      )
    }

    const resolvedParams = await params
    const id = parseInt(resolvedParams.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid exemption ID' }, { status: 400 })
    }

    const exemption = await prayerExemptionUseCases.getExemptionById(id)
    return NextResponse.json(exemption)
  } catch (error: any) {
    console.error('Error fetching prayer exemption:', error)

    if (error.message.includes('not found')) {
      return NextResponse.json({ error: 'Prayer exemption not found' }, { status: 404 })
    }

    return NextResponse.json({ error: 'Failed to fetch prayer exemption' }, { status: 500 })
  }
}

/**
 * PATCH /api/prayer-exemptions/[id] - Update prayer exemption
 */
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Check authorization - only super admin can update prayer exemptions
    if (!canManagePrayerExemptions(authResult.role!)) {
      return NextResponse.json(
        { error: 'Unauthorized. Only super admin can update prayer exemptions.' },
        { status: 403 }
      )
    }

    const resolvedParams = await params
    const id = parseInt(resolvedParams.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid exemption ID' }, { status: 400 })
    }

    // Parse request body
    const body = await request.json()
    const { reason, isActive } = body

    // Create update DTO
    const updateDTO: UpdatePrayerExemptionDTO = {}
    if (reason !== undefined) updateDTO.reason = reason.trim()
    if (isActive !== undefined) updateDTO.isActive = isActive

    // Update exemption
    const exemption = await prayerExemptionUseCases.updateExemption(id, updateDTO, adminData.role)

    return NextResponse.json(exemption)
  } catch (error: any) {
    console.error('Error updating prayer exemption:', error)

    // Handle specific error types
    if (error.message.includes('Only super admin')) {
      return NextResponse.json({ error: error.message }, { status: 403 })
    }

    if (error.message.includes('not found')) {
      return NextResponse.json({ error: 'Prayer exemption not found' }, { status: 404 })
    }

    if (error.message.includes('Reason must be at least')) {
      return NextResponse.json({ error: error.message }, { status: 400 })
    }

    return NextResponse.json({ error: 'Failed to update prayer exemption' }, { status: 500 })
  }
}

/**
 * DELETE /api/prayer-exemptions/[id] - Delete prayer exemption
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(request, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Check authorization - only super admin can delete prayer exemptions
    if (!canManagePrayerExemptions(authResult.role!)) {
      return NextResponse.json(
        { error: 'Unauthorized. Only super admin can delete prayer exemptions.' },
        { status: 403 }
      )
    }

    const resolvedParams = await params
    const id = parseInt(resolvedParams.id)
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid exemption ID' }, { status: 400 })
    }

    // Delete exemption (hard delete)
    await prayerExemptionUseCases.hardDeleteExemption(id, adminData.role)

    return NextResponse.json({ message: 'Prayer exemption deleted successfully' })
  } catch (error: any) {
    console.error('Error deleting prayer exemption:', error)

    // Handle specific error types
    if (error.message.includes('Only super admin')) {
      return NextResponse.json({ error: error.message }, { status: 403 })
    }

    if (error.message.includes('not found')) {
      return NextResponse.json({ error: 'Prayer exemption not found' }, { status: 404 })
    }

    return NextResponse.json({ error: 'Failed to delete prayer exemption' }, { status: 500 })
  }
}
