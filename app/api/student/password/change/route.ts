import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { serverConfig } from '@/lib/config'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository() // ✅ FIXED: StudentRepository doesn't take cache parameter
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Validation schema for password change
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
})

/**
 * POST /api/student/password/change
 * Change student's password with current password verification
 */
export async function POST(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'student')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const studentId = authResult.userId!

    // Parse and validate the request body
    let body
    try {
      body = await req.json()
      console.log('Password change request body:', {
        ...body,
        currentPassword: body.currentPassword ? '(provided)' : '(missing)',
        newPassword: body.newPassword ? '(provided)' : '(missing)',
      })
    } catch (jsonError) {
      console.error('Failed to parse request body:', jsonError)
      return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 })
    }

    const validation = changePasswordSchema.safeParse(body)

    if (!validation.success) {
      console.error('Validation failed:', validation.error.format())
      return NextResponse.json(
        { error: 'Validation failed', details: validation.error.format() },
        { status: 400 }
      )
    }

    const { currentPassword, newPassword } = validation.data

    try {
      // Change the student's password

      await authUseCases.changeStudentPassword(studentId, currentPassword, newPassword)

      return NextResponse.json({
        message: 'Password changed successfully',
      })
    } catch (error) {
      console.error('Error changing password:', error)

      if (error instanceof Error && error.message.includes('incorrect')) {
        return NextResponse.json({ error: 'Current password is incorrect' }, { status: 401 })
      }

      return NextResponse.json(
        {
          error: 'Failed to change password',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    // ✅ HYBRID AUTH: Simplified error handling

    console.error('Error in password change endpoint:', error)
    return NextResponse.json(
      {
        error: 'Failed to process password change',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
