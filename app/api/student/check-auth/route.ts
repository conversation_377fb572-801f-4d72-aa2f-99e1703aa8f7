import { NextRequest, NextResponse } from 'next/server'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'

/**
 * GET /api/student/check-auth
 * Checks if the student is authenticated
 */
export async function GET(req: NextRequest) {
  try {
    // This will throw if not authenticated
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'student')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const studentId = authResult.userId!

    // If we get here, the student is authenticated
    return NextResponse.json({
      authenticated: true,
      studentId,
    })
  } catch (error) {
    // Return an auth error response
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
  }
}
