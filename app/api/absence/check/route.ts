import { NextRequest, NextResponse } from 'next/server'
import { AttendanceType, ALL_ATTENDANCE_TYPES } from '@/lib/domain/entities/absence'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { formatTimeWITA } from '@/lib/utils/date'
import { z } from 'zod'
import { createAbsenceUseCases } from '@/lib/services/absence-service-factory'

// Force dynamic rendering to prevent build-time analysis
export const dynamic = 'force-dynamic'

// Initialize dependencies with prayer exemption support
const absenceUseCases = createAbsenceUseCases()

// Validation schema for checking absence with more robust validation
const checkAbsenceSchema = z.object({
  // Accept any string format that might be a UUID and validate in the handler
  uniqueCode: z
    .string()
    .min(32, 'Unique code must be at least 32 characters (with or without hyphens)')
    .max(40, 'Unique code too long')
    .transform(value => {
      // If it's already a valid UUID format, return it
      if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
        return value
      }

      // If it's a UUID without hyphens, format it
      if (/^[0-9a-f]{32}$/i.test(value)) {
        return `${value.substring(0, 8)}-${value.substring(8, 12)}-${value.substring(12, 16)}-${value.substring(16, 20)}-${value.substring(20)}`
      }

      // If it contains a UUID somewhere, extract it
      const match = value.match(/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i)
      if (match && match[1]) {
        return match[1]
      }

      // Return the original value, further validation will happen in the handler
      return value
    }),

  // Be more flexible with attendance type - use centralized enum values
  type: z.union([
    z.enum(ALL_ATTENDANCE_TYPES as [AttendanceType, ...AttendanceType[]]),
    z.string().transform(value => {
      // Allow for case insensitive matching and some variations
      const normalized = value.toLowerCase()
      if (normalized === 'zuhr' || normalized === 'zuhur' || normalized === 'duhur') {
        return AttendanceType.ZUHR
      }
      if (normalized === 'asr' || normalized === 'ashar' || normalized === 'ashr') {
        return AttendanceType.ASR
      }
      if (normalized === 'dismissal' || normalized === 'pulang' || normalized === 'home') {
        return AttendanceType.DISMISSAL
      }
      if (normalized === 'ijin' || normalized === 'izin' || normalized === 'permit') {
        return AttendanceType.IJIN
      }
      if (normalized === 'entry' || normalized === 'masuk') {
        return AttendanceType.ENTRY
      }
      if (normalized === 'late entry' || normalized === 'masuk terlambat') {
        return AttendanceType.LATE_ENTRY
      }
      if (normalized === 'excused absence' || normalized === 'izin') {
        return AttendanceType.EXCUSED_ABSENCE
      }
      if (normalized === 'temporary leave' || normalized === 'izin sementara') {
        return AttendanceType.TEMPORARY_LEAVE
      }
      if (normalized === 'return from leave' || normalized === 'kembali dari izin') {
        return AttendanceType.RETURN_FROM_LEAVE
      }
      if (normalized === 'sick' || normalized === 'sakit') {
        return AttendanceType.SICK
      }
      // Default to the provided value, will be caught by the enum check
      return value as AttendanceType
    }),
  ]),
})

/**
 * POST /api/absence/check
 * Check if a student has already recorded attendance for a specific type today
 */
export async function POST(req: NextRequest) {
  try {
    // Authenticate the request - check both student and admin tokens
    const adminAuthToken = req.cookies.get('admin_auth_token')?.value
    const studentAuthToken = req.cookies.get('student_auth_token')?.value
    const authToken = adminAuthToken || studentAuthToken

    if (!authToken) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    try {
      // Verify the token
      // ✅ HYBRID AUTH: Use hybrid authentication instead of direct token verification
      const authResult = await authenticateRequest(req)

      if (!authResult.isValid) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
      }

      const decoded = { id: authResult.userId, role: authResult.role }

      if (!decoded || !decoded.id) {
        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
      }

      // Parse and validate the request body
      let body
      try {
        body = await req.json()
      } catch (jsonError) {
        console.error('Error parsing JSON:', jsonError)
        return NextResponse.json(
          {
            error: 'Invalid JSON in request body',
            details: 'The request body could not be parsed as JSON',
          },
          { status: 400 }
        )
      }

      try {
        const { uniqueCode, type } = checkAbsenceSchema.parse(body)

        // Additional validation to ensure UUID format
        if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uniqueCode)) {
          return NextResponse.json(
            {
              error: 'Invalid format for uniqueCode',
              details: 'The uniqueCode must be a valid UUID',
            },
            { status: 400 }
          )
        }

        // Production: Remove debug logging

        // Check for duplicate attendance - type is already an AttendanceType enum value
        const isDuplicate = await absenceUseCases.checkDuplicateAttendance(uniqueCode, type)

        // Return the result
        return NextResponse.json({ isDuplicate })
      } catch (zodError) {
        if (zodError instanceof z.ZodError) {
          console.error('Zod validation failed:', zodError.errors)
          return NextResponse.json(
            {
              error: 'Invalid input',
              details: zodError.errors,
            },
            { status: 400 }
          )
        }
        throw zodError // Re-throw if it's not a Zod error
      }
    } catch (error) {
      console.error('Token verification failed:', error)
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 })
    }
  } catch (error) {
    console.error('Error checking absence:', error)

    // More specific error handling
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid input',
          details: error.errors,
          message: 'Please ensure the unique code and attendance type are valid.',
        },
        { status: 400 }
      )
    }

    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: 'Internal server error',
          message: error.message || 'An unexpected error occurred when checking attendance.',
        },
        { status: 500 }
      )
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * GET /api/absence/check
 * Get attendance status for a student for today
 */
export async function GET(req: NextRequest) {
  try {
    // Get the uniqueCode from query parameters
    const searchParams = req.nextUrl.searchParams
    let uniqueCode = searchParams.get('uniqueCode')

    if (!uniqueCode) {
      return NextResponse.json({ error: 'Missing uniqueCode parameter' }, { status: 400 })
    }

    // Validate the uniqueCode format
    if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uniqueCode)) {
      console.warn(`Invalid UUID format in GET request: ${uniqueCode}`)
      // Try to normalize it
      let normalizedUniqueCode = uniqueCode

      // Check if it's a UUID without hyphens
      if (/^[0-9a-f]{32}$/i.test(uniqueCode)) {
        normalizedUniqueCode = `${uniqueCode.substring(0, 8)}-${uniqueCode.substring(8, 12)}-${uniqueCode.substring(12, 16)}-${uniqueCode.substring(16, 20)}-${uniqueCode.substring(20)}`
        console.log(`Normalized UUID without hyphens to: ${normalizedUniqueCode}`)
      } else {
        // Try to extract a UUID if embedded in the string
        const match = uniqueCode.match(
          /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i
        )
        if (match && match[1]) {
          normalizedUniqueCode = match[1]
          console.log(`Extracted UUID from string: ${normalizedUniqueCode}`)
        } else {
          return NextResponse.json(
            {
              error: 'Invalid uniqueCode format',
              message: 'The uniqueCode must be a valid UUID',
            },
            { status: 400 }
          )
        }
      }

      // Continue with the normalized code
      console.log(
        `Continuing with normalized uniqueCode: ${normalizedUniqueCode.substring(0, 8)}...`
      )
      uniqueCode = normalizedUniqueCode
    }

    // Get attendance status for today
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    try {
      // Get attendance records for today
      const attendanceRecords = await absenceUseCases.getAttendanceForDay(uniqueCode, today)

      // No mock data - use only real data from the database

      // Format the response with both prayer and school attendance
      const response: {
        // Prayer attendance (existing)
        zuhr: boolean
        zuhrTime: string | null
        asr: boolean
        asrTime: string | null
        pulang: boolean
        pulangTime: string | null
        ijin: boolean
        ijinTime: string | null
        // Prayer-specific ijin (new)
        ijinZuhr: boolean
        ijinZuhrTime: string | null
        ijinAsr: boolean
        ijinAsrTime: string | null
        // School attendance (new)
        entry: boolean
        entryTime: string | null
        lateEntry: boolean
        lateEntryTime: string | null
        excusedAbsence: boolean
        excusedAbsenceTime: string | null
        excusedAbsenceReason: string | null
        sick: boolean
        sickTime: string | null
        sickReason: string | null
        temporaryLeave: boolean
        temporaryLeaveTime: string | null
        temporaryLeaveReason: string | null
        returnFromLeave: boolean
        returnFromLeaveTime: string | null
      } = {
        // Prayer attendance (existing)
        zuhr: false,
        zuhrTime: null,
        asr: false,
        asrTime: null,
        pulang: false,
        pulangTime: null,
        ijin: false,
        ijinTime: null,
        // Prayer-specific ijin (new)
        ijinZuhr: false,
        ijinZuhrTime: null,
        ijinAsr: false,
        ijinAsrTime: null,
        // School attendance (new)
        entry: false,
        entryTime: null,
        lateEntry: false,
        lateEntryTime: null,
        excusedAbsence: false,
        excusedAbsenceTime: null,
        excusedAbsenceReason: null,
        sick: false,
        sickTime: null,
        sickReason: null,
        temporaryLeave: false,
        temporaryLeaveTime: null,
        temporaryLeaveReason: null,
        returnFromLeave: false,
        returnFromLeaveTime: null,
      }

      // Log the attendance records for debugging
      console.log(
        `Retrieved ${attendanceRecords.length} attendance records for uniqueCode=${uniqueCode.substring(0, 8)}...`
      )

      // Update response based on attendance records
      for (const record of attendanceRecords) {
        // Use safe type checking to avoid issues with Date objects
        const recordType = record.type?.toString()

        // Helper function to format time safely
        const formatRecordTime = (recordedAt: Date | string | null): string | null => {
          if (!recordedAt) return null
          const recordDate = new Date(recordedAt)
          return !isNaN(recordDate.getTime()) ? formatTimeWITA(recordDate) : null
        }

        // Prayer attendance (existing)
        if (recordType === AttendanceType.ZUHR) {
          response.zuhr = true
          response.zuhrTime = formatRecordTime(record.recordedAt)
        } else if (recordType === AttendanceType.ASR) {
          response.asr = true
          response.asrTime = formatRecordTime(record.recordedAt)
        } else if (recordType === AttendanceType.DISMISSAL) {
          response.pulang = true
          response.pulangTime = formatRecordTime(record.recordedAt)
        } else if (recordType === AttendanceType.IJIN) {
          response.ijin = true
          response.ijinTime = formatRecordTime(record.recordedAt)
        } else if (recordType === AttendanceType.IJIN_ZUHR) {
          response.ijinZuhr = true
          response.ijinZuhrTime = formatRecordTime(record.recordedAt)
        } else if (recordType === AttendanceType.IJIN_ASR) {
          response.ijinAsr = true
          response.ijinAsrTime = formatRecordTime(record.recordedAt)
        }
        // School attendance (new)
        else if (recordType === AttendanceType.ENTRY) {
          response.entry = true
          response.entryTime = formatRecordTime(record.recordedAt)
        } else if (recordType === AttendanceType.LATE_ENTRY) {
          response.lateEntry = true
          response.lateEntryTime = formatRecordTime(record.recordedAt)
        } else if (recordType === AttendanceType.EXCUSED_ABSENCE) {
          response.excusedAbsence = true
          response.excusedAbsenceTime = formatRecordTime(record.recordedAt)
          response.excusedAbsenceReason = record.reason || null
        } else if (recordType === AttendanceType.SICK) {
          response.sick = true
          response.sickTime = formatRecordTime(record.recordedAt)
          response.sickReason = record.reason || null
        } else if (recordType === AttendanceType.TEMPORARY_LEAVE) {
          response.temporaryLeave = true
          response.temporaryLeaveTime = formatRecordTime(record.recordedAt)
          response.temporaryLeaveReason = record.reason || null
        } else if (recordType === AttendanceType.RETURN_FROM_LEAVE) {
          response.returnFromLeave = true
          response.returnFromLeaveTime = formatRecordTime(record.recordedAt)
        }
      }

      return NextResponse.json(response)
    } catch (innerError) {
      console.error('Error fetching attendance records:', innerError)

      // Fallback response if there's an error
      return NextResponse.json({
        // Prayer attendance (existing)
        zuhr: false,
        zuhrTime: null,
        asr: false,
        asrTime: null,
        pulang: false,
        pulangTime: null,
        ijin: false,
        ijinTime: null,
        // Prayer-specific ijin (new)
        ijinZuhr: false,
        ijinZuhrTime: null,
        ijinAsr: false,
        ijinAsrTime: null,
        // School attendance (new)
        entry: false,
        entryTime: null,
        lateEntry: false,
        lateEntryTime: null,
        excusedAbsence: false,
        excusedAbsenceTime: null,
        excusedAbsenceReason: null,
        sick: false,
        sickTime: null,
        sickReason: null,
        temporaryLeave: false,
        temporaryLeaveTime: null,
        temporaryLeaveReason: null,
        returnFromLeave: false,
        returnFromLeaveTime: null,
      })
    }
  } catch (error) {
    console.error('Error getting attendance status:', error)
    return NextResponse.json({ error: 'Failed to get attendance status' }, { status: 500 })
  }
}
