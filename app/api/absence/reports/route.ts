import { NextRequest, NextResponse } from 'next/server'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { TIMEZONE_CONFIG } from '@/lib/config'
import {
  createWITADateRange,
  createWITAMonthRange,
  createWITAYearRange,
  createWITACustomRange,
  getCurrentWITATime,
  getWITADateComponents,
} from '@/lib/utils/date'

// Initialize dependencies
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository()
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo)

/**
 * Creates a WITA-aware date object for a specific day with time set to 00:00:00
 * This function ensures consistent date creation using WITA timezone
 *
 * @param dayOffset - Number of days to offset (negative for past, positive for future)
 * @returns Date object set to the start of the day in WITA timezone
 */
function createWITADateForDay(dayOffset = 0): Date {
  // ✅ FIXED: Use WITA-aware date creation for consistent timezone handling
  const currentWITA = getCurrentWITATime()
  const components = getWITADateComponents(currentWITA)

  // Create date with offset
  const targetDate = new Date(
    components.year,
    components.month - 1,
    components.day + dayOffset,
    0,
    0,
    0,
    0
  )

  return targetDate
}

// Note: parseDatabaseDate function removed - not used with unified cache strategy

/**
 * GET /api/absence/reports
 * Get attendance summary for reporting
 */
export async function GET(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Note: Request ID removed - not used with unified cache strategy

    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const dateParam = searchParams.get('date')
    const className = searchParams.get('class')
    const reportType = searchParams.get('reportType') || 'all' // 'prayer' | 'school' | 'all'
    const forceFresh = searchParams.get('force_fresh') === 'true'
    const debug = searchParams.get('debug') === 'true'
    const monthParam = searchParams.get('month')
    const yearParam = searchParams.get('year')

    // Parse date parameter
    let date: Date | undefined
    let isRangeFilter = false
    let startDate: Date | undefined
    let endDate: Date | undefined

    if (dateParam) {
      if (dateParam === 'today') {
        // Use current date set to start of day
        date = createWITADateForDay()
      } else if (dateParam === 'yesterday') {
        // Create date for yesterday using the offset approach
        date = createWITADateForDay(-1)
      } else if (dateParam === 'current-month') {
        // Current month filter
        isRangeFilter = true
        const now = createWITADateForDay()

        // ✅ SSOT: Use centralized WITA month range creation
        const currentMonthRange = createWITAMonthRange(now.getFullYear(), now.getMonth() + 1)
        startDate = currentMonthRange.startDate
        endDate = currentMonthRange.endDate

        // ✅ SSOT: Current month range created successfully

        // Production: Remove debug logging
      } else if (dateParam === 'last-3months') {
        // Last 3 months filter
        isRangeFilter = true
        const now = createWITADateForDay()

        // ✅ SSOT: Create 3-month range correctly for WITA timezone
        const endDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        const startDay = new Date(endDay)
        startDay.setMonth(startDay.getMonth() - 3)

        const threeMonthRange = createWITACustomRange(
          startDay.getFullYear(),
          startDay.getMonth() + 1,
          startDay.getDate(),
          endDay.getFullYear(),
          endDay.getMonth() + 1,
          endDay.getDate()
        )

        startDate = threeMonthRange.startDate
        endDate = threeMonthRange.endDate

        // ✅ SSOT: Last 3 months range created successfully

        // Production: Remove debug logging
      } else if (dateParam === 'monthly') {
        // Monthly summary - use range filter to get all data for the month
        isRangeFilter = true
        const month = monthParam ? parseInt(monthParam) : new Date().getMonth() + 1
        const year = yearParam ? parseInt(yearParam) : new Date().getFullYear()

        // ✅ SSOT: Use centralized WITA month range creation
        const monthRange = createWITAMonthRange(year, month)
        startDate = monthRange.startDate
        endDate = monthRange.endDate

        // ✅ SSOT: Monthly range created successfully
      } else if (dateParam === 'yearly') {
        // Yearly summary - return aggregated yearly data
        isRangeFilter = true
        const year = yearParam ? parseInt(yearParam) : new Date().getFullYear()

        // ✅ SSOT: Use centralized WITA year range creation
        const yearRange = createWITAYearRange(year)
        startDate = yearRange.startDate
        endDate = yearRange.endDate

        // ✅ SSOT: Yearly range created successfully

        // Production: Remove debug logging
      } else {
        // Parse the date string (should be in YYYY-MM-DD format)
        try {
          const [year, month, day] = dateParam.split('-').map(Number)

          // ✅ SSOT: Use centralized WITA date range creation for specific dates
          const dateRange = createWITADateRange(year, month, day)

          // For single date queries, we use the start date
          date = dateRange.startDate

          if (isNaN(date.getTime())) {
            return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
          }

          // ✅ SSOT: Specific date range created successfully
        } catch (error) {
          return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
        }
      }
    } else {
      // Default to today if no date parameter is provided
      date = createWITADateForDay()
    }

    // If forceFresh is true, refresh the materialized view and clear cache
    if (forceFresh) {
      try {
        // Refresh the materialized view
        await absenceUseCases.refreshAttendanceSummary()
        console.log('Materialized view refreshed due to forceFresh=true')
      } catch (error) {
        console.error('Error refreshing materialized view:', error)
      }
    }

    // Get attendance summary with report type filtering
    let summary
    if (isRangeFilter && startDate && endDate) {
      // Use range-based aggregated summary for longer periods
      summary = await absenceUseCases.getAggregatedAttendanceSummary(
        startDate,
        endDate,
        className || undefined,
        reportType as 'prayer' | 'school' | 'all',
        forceFresh
      )
    } else {
      // Use regular summary for daily/weekly reports
      summary = await absenceUseCases.getAttendanceSummaryByType(
        date,
        className || undefined,
        reportType as 'prayer' | 'school' | 'all',
        forceFresh
      )
    }

    if (debug) {
      // For debugging, get the server and database time information
      const serverTime = new Date()
      const witaTime = new Intl.DateTimeFormat('en-CA', {
        timeZone: TIMEZONE_CONFIG.TIMEZONE,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      }).formatToParts(serverTime)

      // Get recent attendance records for debugging
      const recentRecords = await absenceRepo.getRecentAttendanceRecords(5)

      // Return diagnostic information along with the results
      return NextResponse.json(
        {
          debug: {
            serverTime: serverTime.toISOString(),
            serverTimezoneOffset: -serverTime.getTimezoneOffset() / 60,
            witaTime: `${witaTime.find(p => p.type === 'year')?.value}-${witaTime.find(p => p.type === 'month')?.value}-${witaTime.find(p => p.type === 'day')?.value} ${witaTime.find(p => p.type === 'hour')?.value}:${witaTime.find(p => p.type === 'minute')?.value}:${witaTime.find(p => p.type === 'second')?.value} WITA`,
            requestedDate: date ? date.toISOString() : null,
            dateParam,

            isRangeFilter,
            startDate: startDate ? startDate.toISOString() : null,
            endDate: endDate ? endDate.toISOString() : null,
            recordCount: summary.length,
            recentRecords: recentRecords.map((r: any) => ({
              uniqueCode: r.uniqueCode,
              type: r.type,
              recordedAt: r.recordedAt.toISOString(),
            })),
          },
          data: summary,
        },
        {
          headers: {
            'Cache-Control': 'no-store, max-age=0',
            'X-Request-Time': new Date().toISOString(),
          },
        }
      )
    }

    // Return the summary with cache control headers
    return NextResponse.json(summary, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'X-Request-Time': new Date().toISOString(),
      },
    })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
