import { NextRequest, NextResponse } from 'next/server'
import { AbsenceUseCases } from '@/lib/domain/usecases/absence'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { StudentRepository } from '@/lib/data/repositories/student'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { createWITACustomRange } from '@/lib/utils/date'

// ✅ UNIFIED CACHE STRATEGY: Initialize dependencies without cache parameter
const absenceRepo = new AbsenceRepository()
const studentRepo = new StudentRepository()
const absenceUseCases = new AbsenceUseCases(absenceRepo, studentRepo)

/**
 * GET /api/absence/reports/range
 * Get attendance summary for reporting within a date range
 */
export async function GET(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Create a unique log identifier for this request to track it in logs
    const requestId = Math.random().toString(36).substring(2, 8)

    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const startDateParam = searchParams.get('startDate')
    const endDateParam = searchParams.get('endDate')
    const className = searchParams.get('class')
    const reportType = searchParams.get('reportType') || 'all' // ✅ FIXED: Add reportType parameter
    const forceFresh = searchParams.get('force_fresh') === 'true'
    const debug = searchParams.get('debug') === 'true'

    // Validate required date parameters
    if (!startDateParam || !endDateParam) {
      return NextResponse.json(
        { error: 'startDate and endDate parameters are required' },
        { status: 400 }
      )
    }

    // Parse date parameters
    let startDate: Date
    let endDate: Date

    try {
      // Parse the date strings (should be in YYYY-MM-DD format)
      const [startYear, startMonth, startDay] = startDateParam.split('-').map(Number)
      const [endYear, endMonth, endDay] = endDateParam.split('-').map(Number)

      // ✅ SSOT: Use centralized WITA date range creation
      const dateRange = createWITACustomRange(
        startYear,
        startMonth,
        startDay,
        endYear,
        endMonth,
        endDay
      )

      startDate = dateRange.startDate
      endDate = dateRange.endDate

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.error(
          `[${requestId}] Invalid date format received: ${startDateParam} or ${endDateParam}`
        )
        return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
      }

      // ✅ SSOT: Custom date range created successfully

      // Validate date range
      if (startDate > endDate) {
        return NextResponse.json(
          { error: 'startDate must be before or equal to endDate' },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error(`[${requestId}] Error parsing dates ${startDateParam}, ${endDateParam}:`, error)
      return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
    }

    // If forceFresh is true, refresh the materialized view and clear cache
    if (forceFresh) {
      try {
        // Refresh the materialized view
        await absenceUseCases.refreshAttendanceSummary()
      } catch (error) {
        console.error(`[${requestId}] Error refreshing materialized view:`, error)
        // Continue execution even if this fails
      }
    }

    // Collect all reports for the date range
    const reports = []
    let currentDate = new Date(startDate)

    // Loop through each date in the range
    while (currentDate <= endDate) {
      // ✅ FIXED: Get attendance summary for the current date with reportType filter
      const dailyReports = await absenceUseCases.getAttendanceSummary(
        new Date(currentDate),
        className || undefined,
        false, // Don't force refresh again
        reportType as 'prayer' | 'school' | 'all' // Pass reportType parameter
      )

      // Add reports to the collection
      reports.push(...dailyReports)

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1)
    }

    // Return the reports with cache control headers
    return NextResponse.json(reports, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'X-Request-Time': new Date().toISOString(),
      },
    })
  } catch (error) {
    console.error('Error getting attendance summary for date range:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
