import { NextRequest, NextResponse } from 'next/server'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { db, schema } from '@/lib/data/drizzle/db'
import { and, eq, gte, lte } from 'drizzle-orm'
import { authenticateRequest } from "@/lib/middleware/hybrid-auth-middleware"
import { createWITADateRange } from '@/lib/utils/date'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

/**
 * GET /api/debug/absence
 * Debug endpoint to check attendance records directly
 */
export async function GET(req: NextRequest) {
  try {
    // Authenticate the admin
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    // Get query parameters
    const searchParams = req.nextUrl.searchParams
    const uniqueCode = searchParams.get('uniqueCode')
    const type = searchParams.get('type')
    const dateStr = searchParams.get('date') || new Date().toISOString().split('T')[0]

    if (!uniqueCode) {
      return NextResponse.json({ error: 'Missing uniqueCode parameter' }, { status: 400 })
    }

    // ✅ SSOT: Use centralized WITA date range creation
    const [year, month, day] = dateStr.split('-').map(Number)
    const dateRange = createWITADateRange(year, month, day)

    // Format dates as ISO strings for SQL
    const startDateStr = dateRange.startDate.toISOString()
    const endDateStr = dateRange.endDate.toISOString()

    // ✅ SSOT: Debug date range created successfully

    // Build the query conditions
    let conditions = [
      eq(schema.absences.uniqueCode, uniqueCode),
      gte(schema.absences.recordedAt, new Date(startDateStr)),
      lte(schema.absences.recordedAt, new Date(endDateStr)),
    ]

    // Add type filter if provided
    if (type) {
      let attendanceType: AttendanceType
      switch (type.toUpperCase()) {
        case 'ZUHR':
          attendanceType = AttendanceType.ZUHR
          break
        case 'ASR':
          attendanceType = AttendanceType.ASR
          break
        case 'DISMISSAL':
        case 'PULANG':
          attendanceType = AttendanceType.DISMISSAL
          break
        default:
          return NextResponse.json(
            { error: 'Invalid type parameter. Must be one of: ZUHR, ASR, DISMISSAL' },
            { status: 400 }
          )
      }
      conditions.push(eq(schema.absences.type, attendanceType))
    }

    // Query the database
    const absences = await db
      .select()
      .from(schema.absences)
      .where(and(...conditions))

    // Return debug information
    return NextResponse.json({
      debug: {
        uniqueCode,
        type,
        date: dateStr,
        startOfDay: startDateStr,
        endOfDay: endDateStr,
        attendanceTypeEnum: {
          ZUHR: AttendanceType.ZUHR,
          ASR: AttendanceType.ASR,
          DISMISSAL: AttendanceType.DISMISSAL,
        },
      },
      records: absences,
      count: absences.length,
    })
  } catch (error) {
    console.error('Error in debug absence endpoint:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
