import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/data/drizzle/db'
import { authenticateRequest } from "@/lib/middleware/hybrid-auth-middleware"
import { hashPassword } from '@/lib/utils/auth'

/**
 * POST /api/fix-constraint
 * Endpoint untuk memperbaiki constraint database
 */
export async function POST(req: NextRequest) {
  try {
    // Only allow admins to run this
    try {
      const authResult = await authenticateRequest(req, "admin"); if (!authResult.isValid) return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    } catch (authError) {
      return NextResponse.json({ error: "Authentication failed" }, { status: 401 })
    }

    // Get a direct connection to postgres for raw SQL execution
    const postgres = await import('postgres')
    const sql = postgres.default(process.env.DATABASE_URL || '')

    // First, update any students missing username/password
    const violatingStudents = await sql`
      SELECT id, name FROM users 
      WHERE role = 'student' 
      AND (username IS NULL OR password_hash IS NULL)
    `

    

    // Update each record
    for (const student of violatingStudents) {
      const defaultUsername = `student${student.id}`
      const defaultPassword = await hashPassword('changeme123')

      await sql`
        UPDATE users 
        SET username = ${defaultUsername}, 
            password_hash = ${defaultPassword}
        WHERE id = ${student.id}
      `
    }

    // Remove the constraint
    await sql`
      ALTER TABLE users DROP CONSTRAINT IF EXISTS chk_role_data
    `

    // Add the new constraint
    await sql`
      ALTER TABLE users ADD CONSTRAINT chk_role_data CHECK (
        (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR 
        (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
      )
    `

    // Create index
    await sql`
      CREATE INDEX IF NOT EXISTS idx_users_username_role ON users(username, role)
    `

    // Verify the constraint
    const constraint = await sql`
      SELECT conname, pg_get_constraintdef(oid) as definition
      FROM pg_constraint
      WHERE conname = 'chk_role_data'
    `

    await sql.end()

    return NextResponse.json({
      message: 'Constraint updated successfully',
      constraint,
      studentsUpdated: violatingStudents.length,
    })
  } catch (error) {
    console.error('Error updating constraint:', error)
    return NextResponse.json(
      { message: 'Failed to update constraint', error: String(error) },
      { status: 500 }
    )
  }
}
