import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { z } from 'zod'
import { parse } from 'csv-parse/sync'
import ExcelJS from 'exceljs'

// Schema for a single admin row in CSV (same as bulk-upload)
const csvAdminSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama wajib diisi')
    .max(250, 'Nama maksimal 250 karakter')
    .regex(/^[a-zA-Z\s]+$/, 'Nama hanya boleh berisi huruf dan spasi'),
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username hanya boleh berisi huruf, angka, dan underscore'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
  role: z.enum(['admin', 'super_admin', 'teacher', 'receptionist'], {
    errorMap: () => ({
      message: 'Role harus salah satu dari: admin, super_admin, teacher, receptionist',
    }),
  }),
})

interface ValidationResult {
  isValid: boolean
  totalRecords: number
  validRecords: number
  errors: Array<{
    row: number
    field: string
    message: string
    value: any
  }>
  warnings: Array<{
    row: number
    field: string
    message: string
    value: any
  }>
  preview: {
    duplicateUsernames: Array<{
      row: number
      username: string
    }>
    sampleData: any[]
  }
}

/**
 * POST /api/admins/bulk-validate
 * Validate CSV file with comprehensive database checks before import
 */
export async function POST(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const adminId = authResult.userId!

    // Get admin details to check role
    const adminRepo = new AdminRepository()
    const currentAdmin = await adminRepo.findById(adminId)
    if (!currentAdmin || currentAdmin.role !== 'super_admin') {
      return NextResponse.json(
        { message: 'Akses ditolak. Hanya Super Admin yang dapat melakukan bulk import admin.' },
        { status: 403 }
      )
    }

    // Check if the request is multipart/form-data
    const contentType = req.headers.get('content-type') || ''
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { message: 'Content-Type harus multipart/form-data' },
        { status: 400 }
      )
    }

    // Parse the form data
    const formData = await req.formData()
    const file = formData.get('file') as File | null

    if (!file) {
      return NextResponse.json({ message: 'File tidak ditemukan' }, { status: 400 })
    }

    // Verify file extension
    const fileName = file.name.toLowerCase()
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      return NextResponse.json(
        { message: 'File harus berformat CSV atau Excel (.csv, .xlsx, .xls)' },
        { status: 400 }
      )
    }

    // Parse the file content
    let records: any[] = []
    try {
      if (fileName.endsWith('.csv')) {
        // Handle CSV files
        const fileContent = await file.text()
        records = parse(fileContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
        })
      } else {
        // Handle Excel files
        const buffer = await file.arrayBuffer()
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(buffer)

        const worksheet = workbook.getWorksheet(1)
        if (!worksheet) {
          throw new Error('Worksheet tidak ditemukan')
        }

        const headers: string[] = []
        const firstRow = worksheet.getRow(1)
        firstRow.eachCell((cell, colNumber) => {
          headers[colNumber - 1] = cell.text.trim()
        })

        records = []
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber === 1) return // Skip header row

          const record: any = {}
          row.eachCell((cell, colNumber) => {
            const header = headers[colNumber - 1]
            if (header) {
              record[header] = cell.text.trim()
            }
          })

          // Only add non-empty records
          if (Object.values(record).some(value => value !== '')) {
            records.push(record)
          }
        })
      }
    } catch (error) {
      console.error('Failed to parse file:', error)
      return NextResponse.json(
        { message: 'Format file tidak valid', error: (error as Error).message },
        { status: 400 }
      )
    }

    if (records.length === 0) {
      return NextResponse.json({ message: 'File tidak berisi data' }, { status: 400 })
    }

    // Check for maximum records limit
    const maxRecords = 1000
    if (records.length > maxRecords) {
      return NextResponse.json(
        { message: `Maksimal ${maxRecords} admin dapat divalidasi sekaligus` },
        { status: 400 }
      )
    }

    // Initialize repositories and cache
    const userUseCases = new UserUseCases()
    const cache = getRedisCache()

    // Perform comprehensive validation
    const validationResult = await performComprehensiveValidation(
      records,
      userUseCases,
      adminRepo,
      cache
    )

    return NextResponse.json(validationResult)
  } catch (error) {
    console.error('Admin bulk validation error:', error)
    return NextResponse.json(
      { message: 'Terjadi kesalahan saat validasi', error: (error as Error).message },
      { status: 500 }
    )
  }
}

async function performComprehensiveValidation(
  records: any[],
  userUseCases: UserUseCases,
  adminRepo: AdminRepository,
  cache: any
): Promise<ValidationResult> {
  const result: ValidationResult = {
    isValid: true,
    totalRecords: records.length,
    validRecords: 0,
    errors: [],
    warnings: [],
    preview: {
      duplicateUsernames: [],
      sampleData: records.slice(0, 5), // Show first 5 records as preview
    },
  }

  // Clear cache to ensure fresh data
  await cache.del('users:all')

  // Get all usernames from records for batch checking
  const allUsernames = records.map(r => r.username).filter(Boolean)

  // Clear username caches
  for (const username of allUsernames) {
    await cache.del(`admin:username:${username}`)
  }

  // Batch check existing usernames
  const existingUsernames = await batchCheckUsernames(allUsernames, adminRepo)

  // Track usernames within the CSV for duplicates
  const csvUsernames = new Set<string>()
  const csvDuplicates = new Set<string>()

  // Validate each record
  for (let i = 0; i < records.length; i++) {
    const record = records[i]
    const rowIndex = i + 1

    try {
      // 1. Schema validation
      const validationResult = csvAdminSchema.safeParse(record)
      if (!validationResult.success) {
        validationResult.error.errors.forEach(err => {
          result.errors.push({
            row: rowIndex,
            field: err.path.join('.'),
            message: err.message,
            value: record[err.path[0]],
          })
        })
        continue
      }

      const validData = validationResult.data

      // 2. Check username duplicates in database
      if (existingUsernames.has(validData.username)) {
        result.errors.push({
          row: rowIndex,
          field: 'username',
          message: `Username '${validData.username}' sudah ada di database`,
          value: validData.username,
        })
        result.preview.duplicateUsernames.push({
          row: rowIndex,
          username: validData.username,
        })
      }

      // 3. Check username duplicates within CSV
      if (csvUsernames.has(validData.username)) {
        csvDuplicates.add(validData.username)
        result.errors.push({
          row: rowIndex,
          field: 'username',
          message: `Username '${validData.username}' duplikat dalam file CSV`,
          value: validData.username,
        })
      } else {
        csvUsernames.add(validData.username)
      }

      // If no errors for this record, increment valid count
      if (result.errors.filter(e => e.row === rowIndex).length === 0) {
        result.validRecords++
      }
    } catch (error) {
      result.errors.push({
        row: rowIndex,
        field: 'general',
        message: `Error validasi: ${error instanceof Error ? error.message : 'Unknown error'}`,
        value: null,
      })
    }
  }

  // Set overall validation status
  result.isValid = result.errors.length === 0

  return result
}

async function batchCheckUsernames(
  usernames: string[],
  adminRepo: AdminRepository
): Promise<Set<string>> {
  const existingUsernames = new Set<string>()

  // Check in batches to avoid overwhelming the database
  const batchSize = 50
  for (let i = 0; i < usernames.length; i += batchSize) {
    const batch = usernames.slice(i, i + batchSize)

    // Check each username in the batch
    const promises = batch.map(async username => {
      const existing = await adminRepo.findByUsername(username)
      if (existing) {
        existingUsernames.add(username)
      }
    })

    await Promise.all(promises)
  }

  return existingUsernames
}
