import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { z } from 'zod'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { parse } from 'csv-parse/sync'
import ExcelJS from 'exceljs'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository()
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Schema for a single admin row in CSV
const csvAdminSchema = z.object({
  name: z
    .string()
    .min(1, 'Nama wajib diisi')
    .max(250, 'Nama maksimal 250 karakter')
    .regex(/^[a-zA-Z\s]+$/, 'Nama hanya boleh berisi huruf dan spasi'),
  username: z
    .string()
    .min(3, 'Username minimal 3 karakter')
    .max(50, 'Username maksimal 50 karakter')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username hanya boleh berisi huruf, angka, dan underscore'),
  password: z.string().min(6, 'Password minimal 6 karakter'),
  role: z.enum(['admin', 'super_admin', 'teacher', 'receptionist'], {
    errorMap: () => ({
      message: 'Role harus salah satu dari: admin, super_admin, teacher, receptionist',
    }),
  }),
})

/**
 * POST /api/admins/bulk-upload
 * Upload a CSV file with multiple admin users
 */
export async function POST(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const adminId = authResult.userId!

    // Get admin details to check role
    const currentAdmin = await adminRepo.findById(adminId)
    if (!currentAdmin || currentAdmin.role !== 'super_admin') {
      return NextResponse.json(
        { message: 'Akses ditolak. Hanya Super Admin yang dapat melakukan bulk import admin.' },
        { status: 403 }
      )
    }

    // Check if the request is multipart/form-data
    const contentType = req.headers.get('content-type') || ''
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { message: 'Content-Type harus multipart/form-data' },
        { status: 400 }
      )
    }

    // Parse the form data
    const formData = await req.formData()
    const file = formData.get('file') as File | null

    if (!file) {
      return NextResponse.json({ message: 'File CSV tidak ditemukan' }, { status: 400 })
    }

    // Verify file extension
    const fileName = file.name.toLowerCase()
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      return NextResponse.json(
        { message: 'File harus berformat CSV atau Excel (.csv, .xlsx, .xls)' },
        { status: 400 }
      )
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ message: 'Ukuran file maksimal 10MB' }, { status: 400 })
    }

    // Parse file content based on file type
    let records: any[]
    try {
      if (fileName.endsWith('.csv')) {
        // Parse CSV file
        const fileContent = await file.text()
        records = parse(fileContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
          delimiter: ',',
          quote: '"',
          escape: '"',
        })
      } else {
        // Parse Excel file (.xlsx or .xls)
        const buffer = await file.arrayBuffer()
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(buffer)

        const worksheet = workbook.getWorksheet(1) // Get first worksheet
        if (!worksheet) {
          return NextResponse.json(
            { message: 'File Excel tidak berisi worksheet' },
            { status: 400 }
          )
        }

        // Convert Excel data to records
        records = []
        const headers: string[] = []

        // Get headers from first row
        const headerRow = worksheet.getRow(1)
        headerRow.eachCell((cell, colNumber) => {
          headers[colNumber - 1] = cell.text.trim()
        })

        // Get data from subsequent rows
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber === 1) return // Skip header row

          const record: any = {}
          let hasData = false

          row.eachCell((cell, colNumber) => {
            const header = headers[colNumber - 1]
            if (header) {
              record[header] = cell.text.trim()
              if (cell.text.trim()) hasData = true
            }
          })

          if (hasData) {
            records.push(record)
          }
        })
      }
    } catch (error) {
      console.error('Failed to parse file:', error)
      return NextResponse.json(
        { message: 'Format file tidak valid', error: (error as Error).message },
        { status: 400 }
      )
    }

    if (records.length === 0) {
      return NextResponse.json({ message: 'CSV tidak berisi data' }, { status: 400 })
    }

    // Check for maximum records limit
    const maxRecords = 1000
    if (records.length > maxRecords) {
      return NextResponse.json(
        { message: `Maksimal ${maxRecords} admin dapat diimport sekaligus` },
        { status: 400 }
      )
    }

    // Validate and process the records in batches
    const batchSize = 100 // Process 100 records at a time
    const results = {
      total: records.length,
      success: 0,
      failed: 0,
      errors: [] as { row: number; message: string; data: any }[],
    }

    // Process in batches
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize)
      const batchPromises = batch.map(async (record, index) => {
        const rowIndex = i + index + 1 // CSV row number (1-indexed)

        try {
          // Validate CSV row data
          const validationResult = csvAdminSchema.safeParse(record)

          if (!validationResult.success) {
            const errorMessages = validationResult.error.errors
              .map(err => `${err.path.join('.')}: ${err.message}`)
              .join(', ')
            results.failed++
            results.errors.push({
              row: rowIndex,
              message: 'Validasi gagal: ' + errorMessages,
              data: record,
            })
            return null
          }

          const validData = validationResult.data

          // Check if username already exists
          const existingAdmin = await adminRepo.findByUsername(validData.username)
          if (existingAdmin) {
            results.failed++
            results.errors.push({
              row: rowIndex,
              message: `Username '${validData.username}' sudah digunakan`,
              data: record,
            })
            return null
          }

          // Create admin
          await userUseCases.createAdmin({
            name: validData.name,
            username: validData.username,
            password: validData.password,
            role: validData.role,
          })

          results.success++
          return true
        } catch (error) {
          results.failed++
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'

          // Handle specific error types
          if (
            errorMessage.toLowerCase().includes('duplicate') ||
            errorMessage.toLowerCase().includes('already exists') ||
            errorMessage.toLowerCase().includes('unique constraint')
          ) {
            results.errors.push({
              row: rowIndex,
              message: `Username '${record.username}' sudah ada dalam sistem`,
              data: record,
            })
          } else {
            results.errors.push({
              row: rowIndex,
              message: 'Error: ' + errorMessage,
              data: record,
            })
          }
          return null
        }
      })

      // Wait for the current batch to complete
      await Promise.all(batchPromises)

      // Clear cache after each batch to ensure fresh data
      try {
        await cache.del('users:all')
        console.log('Cache cleared after batch processing')
      } catch (cacheError) {
        console.warn('Failed to clear cache after batch:', cacheError)
      }
    }

    // Final cache cleanup to ensure admin list is refreshed
    try {
      await cache.del('users:all')
      console.log('Final cache cleanup completed')
    } catch (cacheError) {
      console.warn('Failed to clear cache in final cleanup:', cacheError)
    }

    return NextResponse.json({
      message: `Bulk upload selesai. Berhasil: ${results.success}, Gagal: ${results.failed} dari ${results.total}`,
      results,
    })
  } catch (error) {
    console.error('Bulk admin upload error:', error instanceof Error ? error.stack : error)
    return NextResponse.json(
      {
        message: 'Gagal memproses bulk upload admin',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
