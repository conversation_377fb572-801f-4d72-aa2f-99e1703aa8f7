import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateRequest } from '@/lib/middleware/hybrid-auth-middleware'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository()
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Validation schema for bulk delete request
const bulkDeleteSchema = z.object({
  adminIds: z.array(z.number().int().positive('Admin ID harus angka positif')),
})

/**
 * POST /api/admins/bulk-delete
 * Delete multiple admin users at once
 */
export async function POST(req: NextRequest) {
  try {
    // ✅ HYBRID AUTH: Authenticate with JWT + Redis session validation
    const authResult = await authenticateRequest(req, 'admin')

    if (!authResult.isValid) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const currentAdminId = authResult.userId!

    // Get current admin details to check role
    const currentAdmin = await adminRepo.findById(currentAdminId)
    if (!currentAdmin || currentAdmin.role !== 'super_admin') {
      return NextResponse.json(
        { message: 'Akses ditolak. Hanya Super Admin yang dapat melakukan bulk delete admin.' },
        { status: 403 }
      )
    }

    const body = await req.json()
    const validation = bulkDeleteSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { message: 'Validasi gagal', errors: validation.error.format() },
        { status: 400 }
      )
    }

    const { adminIds } = validation.data

    if (adminIds.length === 0) {
      return NextResponse.json({ message: 'Tidak ada ID admin yang diberikan' }, { status: 400 })
    }

    // Prevent self-deletion
    if (adminIds.includes(currentAdminId)) {
      return NextResponse.json(
        { message: 'Tidak dapat menghapus akun Anda sendiri' },
        { status: 400 }
      )
    }

    // Get all users to find admins by ID
    const users = await userUseCases.getAllUsers()

    // Filter only admin users from the provided IDs
    const adminsToDelete = adminIds
      .map(id =>
        users.find(
          user =>
            user.id === id &&
            ['admin', 'super_admin', 'teacher', 'receptionist'].includes(user.role)
        )
      )
      .filter(admin => admin !== undefined) as any[]

    if (adminsToDelete.length === 0) {
      return NextResponse.json(
        { message: 'Tidak ada admin valid yang ditemukan dari ID yang diberikan' },
        { status: 404 }
      )
    }

    // Check if trying to delete the last super_admin
    const superAdmins = users.filter(user => user.role === 'super_admin')
    const superAdminsToDelete = adminsToDelete.filter(admin => admin.role === 'super_admin')

    if (superAdminsToDelete.length >= superAdmins.length) {
      return NextResponse.json(
        {
          message:
            'Tidak dapat menghapus semua Super Admin. Minimal satu Super Admin harus tetap ada.',
        },
        { status: 400 }
      )
    }

    console.info(`Starting bulk delete for ${adminsToDelete.length} admins`)

    // Track results
    const results = {
      success: 0,
      failed: 0,
      total: adminsToDelete.length,
      errors: [] as Array<{ adminId: number; name: string; message: string }>,
    }

    // Process deletions in batches to avoid overwhelming the system
    const batchSize = 10 // Delete 10 admins at a time
    for (let i = 0; i < adminsToDelete.length; i += batchSize) {
      const batch = adminsToDelete.slice(i, i + batchSize)

      // Process each admin deletion in parallel within the batch
      await Promise.all(
        batch.map(async admin => {
          try {
            // Delete the admin user with proper role parameter
            await userUseCases.deleteUser(admin.id, admin.role, false) // false for deleteAttendanceRecords since admins don't have attendance
            results.success++
            console.info(`Successfully deleted admin: ${admin.name} (ID: ${admin.id})`)
          } catch (error) {
            results.failed++
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            results.errors.push({
              adminId: admin.id,
              name: admin.name,
              message: errorMessage,
            })
            console.error(`Failed to delete admin ${admin.name} (ID: ${admin.id}):`, errorMessage)
          }
        })
      )

      // Clear cache after each batch to ensure fresh data
      try {
        await cache.del('users:all')
        // Also clear any individual user caches that might exist
        for (const admin of batch) {
          await cache.del(`user:${admin.id}`)
        }
        console.log('Cache cleared after batch processing')
      } catch (cacheError) {
        console.warn('Failed to clear cache after batch:', cacheError)
      }
    }

    // Final comprehensive cache cleanup
    try {
      await cache.del('users:all')
      // Clear all individual admin caches
      for (const admin of adminsToDelete) {
        await cache.del(`user:${admin.id}`)
      }
      // Add a small delay to ensure cache propagation
      await new Promise(resolve => setTimeout(resolve, 100))
      console.log('Final cache cleanup completed')
    } catch (cacheError) {
      console.warn('Failed to clear cache in final cleanup:', cacheError)
    }

    console.info(`Bulk delete completed. Success: ${results.success}, Failed: ${results.failed}`)

    return NextResponse.json({
      message: `Bulk delete selesai. Berhasil: ${results.success}, Gagal: ${results.failed} dari ${results.total}`,
      results,
    })
  } catch (error) {
    console.error('Error in bulk delete admins:', error)
    return NextResponse.json(
      {
        message: 'Terjadi kesalahan saat menghapus admin',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
