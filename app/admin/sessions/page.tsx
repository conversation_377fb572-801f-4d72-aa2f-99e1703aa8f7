'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { Loader2, <PERSON>f<PERSON><PERSON><PERSON>, <PERSON>, Shield } from 'lucide-react'
import { useAdminPermission } from '@/hooks/use-admin-permission'
import { useAdminAuth } from '@/hooks/use-hybrid-auth'
import { toWITATime, formatTimeWITA } from '@/lib/utils/date'
import { TIMEZONE_CONFIG } from '@/lib/config'
import { UserRole } from '@/lib/config/role-permissions'
import { SessionFilterBar } from './components/SessionFilterBar'
import { SessionTable } from './components/SessionTable'
import { Pagination } from '@/components/admin/Pagination'

interface SessionSummary {
  sessionId: string
  userId: number
  userName: string
  role: UserRole
  deviceId: string
  ipAddress: string
  deviceType: string
  browser: string
  createdAt: string
  lastAccessedAt: string
  expiresAt: string
  isActive: boolean
}

export default function SessionManagementPage() {
  const { permission, loading: permissionLoading } = useAdminPermission('super_admin')
  const { user: admin } = useAdminAuth()

  // State management following user/admin management patterns
  const [sessions, setSessions] = useState<SessionSummary[]>([])
  const [filteredSessions, setFilteredSessions] = useState<SessionSummary[]>([])
  const [paginatedSessions, setPaginatedSessions] = useState<SessionSummary[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  // Removed auto-refresh related states - using manual refresh only

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [userIdSearch, setUserIdSearch] = useState('')

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(50)

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'ascending' | 'descending'
  } | null>(null)

  // Fetch sessions - simplified API call following consistent patterns
  const fetchSessions = async (forceRefresh = false) => {
    try {
      setLoading(true)
      const params = new URLSearchParams()

      // Only include necessary filters
      if (roleFilter && roleFilter !== 'all') params.append('role', roleFilter)
      if (userIdSearch) params.append('userId', userIdSearch)
      params.append('isActive', 'true') // Always show only active sessions
      params.append('limit', '1000') // Get all sessions for client-side filtering
      params.append('offset', '0')

      const url = forceRefresh
        ? `/api/admin/sessions?${params}&t=${Date.now()}`
        : `/api/admin/sessions?${params}`
      const response = await fetch(url, {
        cache: forceRefresh ? 'no-cache' : 'default',
        headers: forceRefresh
          ? {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
              Expires: '0',
            }
          : {},
      })

      if (!response.ok) {
        // Handle authentication errors specifically
        if (response.status === 401) {
          console.log('🔒 Authentication failed')
          toast.error('Sesi telah berakhir. Silakan refresh halaman.')
          return
        }
        throw new Error(`Failed to fetch sessions: ${response.status}`)
      }

      const data = await response.json()
      const sessions = data.data.sessions || []

      setSessions(sessions)
    } catch (error) {
      console.error('Error fetching sessions:', error)

      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('401')) {
        toast.error('Sesi telah berakhir. Silakan refresh halaman.')
      } else {
        toast.error('Gagal mengambil data sesi')
      }

      setSessions([])
    } finally {
      setLoading(false)
    }
  }

  // Filter and sort sessions following user/admin management patterns
  useEffect(() => {
    let result = [...sessions]

    // Filter by role if not 'all'
    if (roleFilter !== 'all') {
      result = result.filter(session => session.role === roleFilter)
    }

    // Filter by user ID search
    if (userIdSearch) {
      result = result.filter(session => session.userId.toString().includes(userIdSearch))
    }

    // Filter by general search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        session =>
          session.userName.toLowerCase().includes(query) ||
          session.userId.toString().includes(query) ||
          session.ipAddress.toLowerCase().includes(query) ||
          session.deviceId.toLowerCase().includes(query) ||
          session.deviceType.toLowerCase().includes(query) ||
          session.browser.toLowerCase().includes(query)
      )
    }

    // Apply sorting
    if (sortConfig) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.key as keyof SessionSummary]
        const bValue = b[sortConfig.key as keyof SessionSummary]

        if (aValue < bValue) {
          return sortConfig.direction === 'ascending' ? -1 : 1
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'ascending' ? 1 : -1
        }
        return 0
      })
    }

    setFilteredSessions(result)
  }, [sessions, roleFilter, userIdSearch, searchQuery, sortConfig])

  // Update paginated sessions when filtered sessions or pagination settings change
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    setPaginatedSessions(filteredSessions.slice(startIndex, endIndex))
  }, [filteredSessions, currentPage, itemsPerPage])

  // Sorting functions
  const handleSort = (key: string) => {
    if (sortConfig && sortConfig.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'ascending' ? 'descending' : 'ascending',
      })
    } else {
      setSortConfig({ key, direction: 'ascending' })
    }
  }

  const renderSortIndicator = (key: string) => {
    if (sortConfig?.key !== key) {
      return <span className="ml-1 text-gray-400">↕</span>
    }
    return <span className="ml-1">{sortConfig.direction === 'ascending' ? '↑' : '↓'}</span>
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [roleFilter, userIdSearch, searchQuery, sortConfig])

  // Invalidate a specific session
  const invalidateSession = async (sessionId: string) => {
    if (actionLoading) return

    try {
      setActionLoading(sessionId)
      const response = await fetch('/api/admin/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || 'Gagal menghentikan sesi'

        if (errorMessage.includes('Cannot invalidate your own sessions')) {
          toast.error('Keamanan: Anda tidak dapat menghentikan sesi Anda sendiri')
        } else {
          toast.error(errorMessage)
        }
        return
      }

      toast.success('Sesi berhasil dihentikan')
      fetchSessions()
    } catch (error) {
      console.error('Error invalidating session:', error)
      toast.error('Gagal terhubung ke server')
    } finally {
      setActionLoading(null)
    }
  }

  // Force logout a user - simplified
  const forceLogoutUser = async (userId: number, userName: string) => {
    if (actionLoading) return

    try {
      setActionLoading(`user-${userId}`)
      const response = await fetch('/api/admin/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || 'Gagal paksa logout user'

        if (errorMessage.includes('Cannot force logout yourself')) {
          toast.error('Keamanan: Anda tidak dapat logout diri sendiri')
        } else {
          toast.error(errorMessage)
        }
        return
      }

      const data = await response.json()
      toast.success(`${data.invalidatedCount || 1} sesi ${userName} berhasil dihentikan`)

      // Refresh data after a short delay
      setTimeout(() => {
        fetchSessions()
      }, 1000)
    } catch (error) {
      console.error('Error forcing logout:', error)
      toast.error('Gagal terhubung ke server')
    } finally {
      setActionLoading(null)
    }
  }

  // Memoize fetchSessions to prevent infinite re-renders
  const memoizedFetchSessions = useCallback(() => {
    fetchSessions()
  }, [roleFilter, userIdSearch])

  // Load data on component mount and when filters change
  useEffect(() => {
    if (admin && permission) {
      memoizedFetchSessions()
    }
  }, [admin, permission, memoizedFetchSessions])

  // Check if session belongs to current admin
  const isCurrentUserSession = (session: SessionSummary) => {
    return admin && session.userId === admin.id
  }

  // Get role badge color - simplified
  const getRoleBadgeColor = (
    role: UserRole
  ): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (role) {
      case 'super_admin':
        return 'destructive'
      case 'admin':
        return 'default'
      case 'student':
        return 'secondary'
      case 'teacher':
        return 'default'
      case 'receptionist':
        return 'outline'
      default:
        return 'outline'
    }
  }

  // Format date to WITA timezone using single source of truth
  const formatDateToWITA = (dateString: string) => {
    try {
      const date = new Date(dateString)
      const witaDate = toWITATime(date)

      // Use formatTimeWITA for consistent formatting
      const timeStr = formatTimeWITA(witaDate)
      const dateStr = witaDate.toLocaleDateString(TIMEZONE_CONFIG.LOCALE, {
        timeZone: TIMEZONE_CONFIG.TIMEZONE,
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      })

      return `${dateStr}, ${timeStr}`
    } catch (error) {
      console.error('Error formatting date:', error)
      return 'Invalid Date'
    }
  }

  // Removed tab visibility tracking - no auto-refresh needed

  // Initial data load
  useEffect(() => {
    if (!permissionLoading && permission?.isSuperAdmin) {
      fetchSessions()
    }
  }, [permissionLoading, permission?.isSuperAdmin])

  // Removed auto-refresh - using manual refresh only

  if (permissionLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-900 border-t-transparent"></div>
      </div>
    )
  }

  if (!permission?.isSuperAdmin) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Akses Ditolak
            </CardTitle>
            <CardDescription>
              Anda memerlukan hak akses Super Admin untuk mengelola sesi.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Manajemen Sesi</h1>
          <p className="text-muted-foreground">Pantau dan kelola sesi pengguna aktif</p>
        </div>
        <Button onClick={() => fetchSessions(true)} disabled={loading} size="sm">
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Simple Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Ringkasan Sesi Aktif
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-600">{filteredSessions.length}</div>
              <p className="text-sm text-muted-foreground">Total sesi aktif</p>
            </div>
            {/* Manual refresh only - no auto-refresh indicators */}
          </div>
        </CardContent>
      </Card>

      {/* Filter Bar */}
      <Card>
        <CardHeader>
          <CardTitle>Filter & Pencarian</CardTitle>
        </CardHeader>
        <CardContent>
          <SessionFilterBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            roleFilter={roleFilter}
            setRoleFilter={setRoleFilter}
            userIdSearch={userIdSearch}
            setUserIdSearch={setUserIdSearch}
          />
        </CardContent>
      </Card>

      {/* Sessions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Sesi Aktif ({filteredSessions.length})</CardTitle>
          <CardDescription>
            {filteredSessions.length === 0 && !loading
              ? 'Tidak ada sesi aktif ditemukan'
              : `Menampilkan ${paginatedSessions.length} dari ${filteredSessions.length} sesi`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2 text-slate-500">Memuat data...</span>
            </div>
          ) : filteredSessions.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Shield className="mb-4 h-12 w-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium">Tidak Ada Sesi Aktif</h3>
              <p className="text-gray-500">Saat ini tidak ada sesi pengguna yang aktif.</p>
            </div>
          ) : (
            <SessionTable
              paginatedSessions={paginatedSessions}
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
              actionLoading={actionLoading}
              handleSort={handleSort}
              renderSortIndicator={renderSortIndicator}
              invalidateSession={invalidateSession}
              forceLogoutUser={forceLogoutUser}
              formatDateToWITA={formatDateToWITA}
              getRoleBadgeColor={getRoleBadgeColor}
              isCurrentUserSession={isCurrentUserSession}
            />
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {filteredSessions.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalItems={filteredSessions.length}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={newItemsPerPage => {
            setItemsPerPage(newItemsPerPage)
            setCurrentPage(1)
          }}
        />
      )}
    </div>
  )
}
