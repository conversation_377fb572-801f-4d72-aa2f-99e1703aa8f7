'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Trash2, LogOut, AlertTriangle, Loader2 } from 'lucide-react'
import { UserRole } from '@/lib/config/role-permissions'

interface SessionSummary {
  sessionId: string
  userId: number
  userName: string
  role: UserRole
  deviceId: string
  ipAddress: string
  deviceType: string
  browser: string
  createdAt: string
  lastAccessedAt: string
  expiresAt: string
  isActive: boolean
}

interface SessionTableProps {
  paginatedSessions: SessionSummary[]
  currentPage: number
  itemsPerPage: number
  actionLoading: string | null
  handleSort: (key: string) => void
  renderSortIndicator: (key: string) => React.ReactNode
  invalidateSession: (sessionId: string) => void
  forceLogoutUser: (userId: number, userName: string) => void
  formatDateToWITA: (dateString: string) => string
  getRoleBadgeColor: (role: UserRole) => 'default' | 'secondary' | 'destructive' | 'outline'
  isCurrentUserSession: (session: SessionSummary) => boolean
}

const getRoleLabel = (role: UserRole): string => {
  switch (role) {
    case 'super_admin':
      return 'Super Admin'
    case 'admin':
      return 'Admin'
    case 'teacher':
      return 'Teacher'
    case 'receptionist':
      return 'Receptionist'
    case 'student':
      return 'Siswa'
    default:
      return role
  }
}

export const SessionTable: React.FC<SessionTableProps> = ({
  paginatedSessions,
  currentPage,
  itemsPerPage,
  actionLoading,
  handleSort,
  renderSortIndicator,
  invalidateSession,
  forceLogoutUser,
  formatDateToWITA,
  getRoleBadgeColor,
  isCurrentUserSession,
}) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px] text-center">#</TableHead>
            <TableHead
              className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
              onClick={() => handleSort('userName')}
            >
              <div className="flex items-center">
                Pengguna
                {renderSortIndicator('userName')}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
              onClick={() => handleSort('role')}
            >
              <div className="flex items-center">
                Role
                {renderSortIndicator('role')}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
              onClick={() => handleSort('deviceType')}
            >
              <div className="flex items-center">
                Perangkat
                {renderSortIndicator('deviceType')}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
              onClick={() => handleSort('ipAddress')}
            >
              <div className="flex items-center">
                IP Address
                {renderSortIndicator('ipAddress')}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
              onClick={() => handleSort('createdAt')}
            >
              <div className="flex items-center">
                Dibuat
                {renderSortIndicator('createdAt')}
              </div>
            </TableHead>
            <TableHead
              className="cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-800"
              onClick={() => handleSort('lastAccessedAt')}
            >
              <div className="flex items-center">
                Terakhir Akses
                {renderSortIndicator('lastAccessedAt')}
              </div>
            </TableHead>
            <TableHead className="text-right">Aksi</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {paginatedSessions.map((session, index) => {
            const isOwnSession = isCurrentUserSession(session)
            const isSessionLoading =
              actionLoading === session.sessionId || actionLoading === `user-${session.userId}`

            return (
              <TableRow
                key={session.sessionId}
                className={`hover:bg-slate-50 dark:hover:bg-slate-800 ${
                  isOwnSession ? 'bg-blue-50 dark:bg-blue-950/20' : ''
                }`}
              >
                <TableCell className="text-center font-medium">
                  {(currentPage - 1) * itemsPerPage + index + 1}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{session.userName}</span>
                        {isOwnSession && (
                          <Badge variant="outline" className="text-xs">
                            Anda
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">ID: {session.userId}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getRoleBadgeColor(session.role)}>
                    {getRoleLabel(session.role)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="text-sm">{session.deviceType}</div>
                    <div className="text-xs text-muted-foreground">{session.browser}</div>
                  </div>
                </TableCell>
                <TableCell className="font-mono text-sm">{session.ipAddress}</TableCell>
                <TableCell className="text-sm">{formatDateToWITA(session.createdAt)}</TableCell>
                <TableCell className="text-sm">
                  {formatDateToWITA(session.lastAccessedAt)}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    {/* Individual Session Invalidation */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={isSessionLoading}
                          title="Hentikan sesi ini"
                        >
                          {isSessionLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Hentikan Sesi</AlertDialogTitle>
                          <AlertDialogDescription>
                            {isOwnSession ? (
                              <>
                                <span className="flex items-center gap-2 font-medium text-amber-600">
                                  <AlertTriangle className="h-4 w-4" />
                                  Peringatan: Ini adalah sesi Anda!
                                </span>
                                <br />
                                Anda tidak dapat menghentikan sesi Anda sendiri karena alasan
                                keamanan.
                              </>
                            ) : (
                              `Yakin ingin menghentikan sesi ini? Pengguna akan ter-logout secara langsung.`
                            )}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => invalidateSession(session.sessionId)}
                            disabled={isOwnSession || isSessionLoading}
                          >
                            {isSessionLoading ? 'Memproses...' : 'Hentikan Sesi'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                    {/* Force Logout User */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="destructive"
                          size="sm"
                          disabled={isSessionLoading}
                          title="Paksa logout semua sesi pengguna"
                        >
                          {isSessionLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <LogOut className="h-4 w-4" />
                          )}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Paksa Logout</AlertDialogTitle>
                          <AlertDialogDescription>
                            {isOwnSession ? (
                              <>
                                <span className="flex items-center gap-2 font-medium text-amber-600">
                                  <AlertTriangle className="h-4 w-4" />
                                  Peringatan: Anda tidak dapat logout diri sendiri!
                                </span>
                                <br />
                                Ini diblokir untuk alasan keamanan mencegah logout tidak sengaja.
                              </>
                            ) : (
                              `Yakin ingin paksa logout ${session.userName}? Semua sesi aktif mereka akan dihentikan.`
                            )}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => forceLogoutUser(session.userId, session.userName)}
                            disabled={isOwnSession || isSessionLoading}
                          >
                            {isSessionLoading ? 'Memproses...' : 'Paksa Logout'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
