'use client'

import type React from 'react'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { useToast } from '@/components/ui/use-toast'
import {
  Edit,
  Plus,
  Trash2,
  Loader2,
  GraduationCap,
  School,
  Info,
  AlertTriangle,
  AlertCircle,
} from 'lucide-react'
import { useAdminAuth } from '@/hooks/use-hybrid-auth'
import { useAdminPermission } from '@/hooks/use-admin-permission'
import { useRouter } from 'next/navigation'
import { TIMEZONE_CONFIG } from '@/lib/config'

// Class type definition
interface Class {
  id: number
  name: string
  createdAt: string
}

// Form data type
interface FormData {
  name: string
}

// Define the dialog mode type explicitly
type DialogMode = 'add' | 'edit'

export default function AdminClasses() {
  const { toast } = useToast()
  const { user: admin } = useAdminAuth()
  const router = useRouter()
  const { permission, loading: permissionLoading } = useAdminPermission('super_admin')
  const [showDialog, setShowDialog] = useState(false)
  const [dialogMode, setDialogMode] = useState<DialogMode>('add')
  const [selectedClass, setSelectedClass] = useState<null | Class>(null)
  const [formData, setFormData] = useState<FormData>({
    name: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [classes, setClasses] = useState<Class[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [apiStatus, setApiStatus] = useState<string | null>(null)

  // Check API endpoint availability
  useEffect(() => {
    const checkApiEndpoint = async () => {
      try {
        const response = await fetch('/api/classes', { method: 'HEAD' })
        if (!response.ok) {
          console.warn('API endpoint /api/classes may not be implemented correctly')
          setApiStatus('API endpoint may not be available')
        } else {
          setApiStatus(null)
        }
      } catch (error) {
        console.error('Error checking API endpoint:', error)
        setApiStatus('API endpoint is not responding')
      }
    }

    checkApiEndpoint()
  }, [])

  // Fetch classes from API
  const fetchClasses = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/classes')

      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }

      const data = await response.json()
      setClasses(data)
    } catch (error) {
      console.error('Error fetching classes:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengambil data kelas',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    fetchClasses()
  }, [])

  const handleAddClass = () => {
    setDialogMode('add')
    setFormData({
      name: '',
    })
    setShowDialog(true)
  }

  const handleEditClass = (classData: Class) => {
    setDialogMode('edit')
    setSelectedClass(classData)
    setFormData({
      name: classData.name,
    })
    setShowDialog(true)
  }

  const handleDeleteClick = (classData: Class) => {
    setSelectedClass(classData)
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedClass) return

    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/classes/${selectedClass.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete class')
      }

      // Close the dialog and refresh the data
      setShowDeleteDialog(false)
      fetchClasses()

      toast({
        title: 'Kelas dihapus',
        description: `${selectedClass.name} telah dihapus dari sistem`,
      })
    } catch (error) {
      console.error('Error deleting class:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Gagal menghapus kelas',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let endpoint = '/api/classes'
      let method = 'POST'

      // Validasi nama kelas tidak boleh kosong
      if (!formData.name.trim()) {
        throw new Error('Nama kelas tidak boleh kosong')
      }

      if (dialogMode === 'edit') {
        if (!selectedClass) throw new Error('No class selected for edit')
        endpoint = `/api/classes/${selectedClass.id}`
        method = 'PUT'
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: formData.name }),
      })

      if (!response.ok) {
        let errorMessage = `Error ${response.status}: ${response.statusText}`

        try {
          // Try to parse response as JSON
          const errorData = await response.json()
          if (errorData && errorData.error) {
            errorMessage = errorData.error
          }
        } catch (e) {
          // If parsing as JSON fails, try to get text
          try {
            const textError = await response.text()
            if (textError) errorMessage = textError
          } catch (_) {
            // If we can't get text either, use the default error message
          }
        }

        throw new Error(errorMessage)
      }

      // If response is OK, parse the response body
      let data
      try {
        data = await response.json()
      } catch (e) {}

      // Close the dialog and refresh the data
      setShowDialog(false)
      fetchClasses()

      toast({
        title: dialogMode === 'add' ? 'Kelas ditambahkan' : 'Kelas diperbarui',
        description:
          dialogMode === 'add'
            ? `${formData.name} telah ditambahkan`
            : `Data kelas ${formData.name} telah diperbarui`,
      })
    } catch (error) {
      console.error(`Error ${dialogMode} class:`, error)
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : `Gagal ${dialogMode === 'add' ? 'menambahkan' : 'memperbarui'} kelas`,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // If permission is loading, show a loading state
  if (permissionLoading) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Kelas</h1>
          <ThemeToggle />
        </header>

        <main className="container mx-auto px-4">
          <Card className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            <span className="ml-2 text-slate-500">Memeriksa izin akses...</span>
          </Card>
        </main>

        <AdminBottomNav activeTab="classes" />
      </div>
    )
  }

  // If the user is not a super_admin, show an access denied message
  // The hook will automatically redirect, but we show this message in case there's a delay
  if (permission && !permission.isSuperAdmin) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Kelas</h1>
          <ThemeToggle />
        </header>

        <main className="container mx-auto px-4">
          <Card className="p-8 text-center">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
            <h2 className="mb-2 text-xl font-bold text-red-500">Akses Ditolak</h2>
            <p className="mb-4 text-slate-600 dark:text-slate-400">
              Anda tidak memiliki izin untuk mengakses halaman ini. Hanya Super Admin yang dapat
              mengakses halaman Manajemen Kelas.
            </p>
            <Button
              onClick={() => router.push('/admin/home')}
              className="bg-indigo-600 text-white hover:bg-indigo-700"
            >
              Kembali ke Beranda
            </Button>
          </Card>
        </main>

        <AdminBottomNav activeTab="classes" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="flex items-center justify-between p-4">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Kelas</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto px-4">
        {apiStatus && (
          <div className="mb-4 rounded-md bg-yellow-50 p-4 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200">
            <p className="text-sm font-medium">Peringatan: {apiStatus}</p>
            <p className="mt-1 text-xs">
              Endpoint API untuk manajemen kelas (/api/classes) mungkin belum diimplementasikan.
              Fitur ini tidak akan berfungsi sampai API tersedia.
            </p>
          </div>
        )}

        <div className="mb-6 flex flex-col-reverse gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex items-center">
            <School className="mr-2 h-5 w-5 text-indigo-600" />
            <h2 className="text-lg font-medium text-slate-700 dark:text-slate-300">
              {classes.length > 0 ? `Total ${classes.length} Kelas` : 'Belum Ada Kelas'}
            </h2>
          </div>
          <Button onClick={handleAddClass} className="bg-indigo-600 text-white hover:bg-indigo-700">
            <Plus className="mr-2 h-4 w-4" />
            Tambah Kelas
          </Button>
        </div>

        <Card className="overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
              <span className="ml-2 text-slate-500">Memuat data...</span>
            </div>
          ) : classes.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <div className="rounded-full bg-slate-100 p-3 dark:bg-slate-800">
                <Info className="h-8 w-8 text-slate-400" />
              </div>
              <h3 className="mt-4 text-lg font-medium">Belum Ada Data Kelas</h3>
              <p className="mt-2 max-w-sm text-sm text-slate-500 dark:text-slate-400">
                Silakan tambahkan kelas baru untuk mulai mengelola kelas di sekolah Anda.
              </p>
              <Button
                onClick={handleAddClass}
                className="mt-4 bg-indigo-600 text-white hover:bg-indigo-700"
              >
                <Plus className="mr-2 h-4 w-4" />
                Tambah Kelas Pertama
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-1/2">Nama Kelas</TableHead>
                  <TableHead className="w-1/3">Tanggal Dibuat</TableHead>
                  <TableHead className="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {classes.map(classData => (
                  <TableRow
                    key={classData.id}
                    className="hover:bg-indigo-50 dark:hover:bg-slate-800"
                  >
                    <TableCell>
                      <div className="flex items-center">
                        <School className="mr-2 h-4 w-4 text-indigo-600" />
                        <span className="font-medium">{classData.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(classData.createdAt).toLocaleDateString(TIMEZONE_CONFIG.LOCALE, {
                        timeZone: TIMEZONE_CONFIG.TIMEZONE,
                      })}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditClass(classData)}
                        >
                          <Edit className="mr-1 h-4 w-4" />
                          <span className="hidden sm:inline">Edit</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950"
                          onClick={() => handleDeleteClick(classData)}
                        >
                          <Trash2 className="mr-1 h-4 w-4" />
                          <span className="hidden sm:inline">Hapus</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </Card>

        <div className="mt-6">
          <div className="rounded-md border border-slate-200 bg-white p-4 shadow-sm dark:border-slate-700 dark:bg-slate-800">
            <h2 className="mb-2 flex items-center text-lg font-semibold">
              <GraduationCap className="mr-2 h-5 w-5 text-indigo-600" />
              Panduan Penamaan Kelas
            </h2>
            <p className="mb-3 text-sm text-slate-600 dark:text-slate-400">
              Anda dapat menggunakan format nama kelas yang fleksibel sesuai kebutuhan sekolah:
            </p>

            <div className="mb-4 grid gap-4 md:grid-cols-2">
              <div className="rounded-md border border-slate-200 bg-slate-50 p-3 dark:border-slate-700 dark:bg-slate-900/30">
                <h3 className="mb-2 font-medium text-slate-800 dark:text-slate-200">
                  Format Tradisional
                </h3>
                <div className="mb-2 flex flex-wrap gap-2">
                  <code className="rounded bg-slate-100 px-1.5 py-0.5 dark:bg-slate-700">
                    X IPA 1
                  </code>
                  <code className="rounded bg-slate-100 px-1.5 py-0.5 dark:bg-slate-700">
                    XI IPS 2
                  </code>
                  <code className="rounded bg-slate-100 px-1.5 py-0.5 dark:bg-slate-700">
                    XII MIPA 3
                  </code>
                </div>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  Cocok untuk sekolah dengan struktur tingkat, jurusan, dan nomor rombel yang jelas.
                </p>
              </div>

              <div className="rounded-md border border-slate-200 bg-slate-50 p-3 dark:border-slate-700 dark:bg-slate-900/30">
                <h3 className="mb-2 font-medium text-slate-800 dark:text-slate-200">
                  Format Sederhana
                </h3>
                <div className="mb-2 flex flex-wrap gap-2">
                  <code className="rounded bg-slate-100 px-1.5 py-0.5 dark:bg-slate-700">X</code>
                  <code className="rounded bg-slate-100 px-1.5 py-0.5 dark:bg-slate-700">11A</code>
                  <code className="rounded bg-slate-100 px-1.5 py-0.5 dark:bg-slate-700">
                    12 IPA
                  </code>
                  <code className="rounded bg-slate-100 px-1.5 py-0.5 dark:bg-slate-700">
                    Kelas 7
                  </code>
                </div>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  Cocok untuk penamaan yang lebih ringkas atau struktur kelas yang berbeda.
                </p>
              </div>
            </div>

            <div className="rounded-md bg-indigo-50 p-3 text-sm text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-200">
              <div className="flex items-start">
                <Info className="mr-2 h-4 w-4 flex-shrink-0 translate-y-0.5" />
                <div>
                  <p className="font-medium">Tips:</p>
                  <p className="mt-1 text-xs">
                    Anda dapat mengubah nama kelas yang sudah ada kapan saja, tetapi pastikan untuk
                    menginformasikan perubahan kepada pengguna lain. Nama kelas akan digunakan untuk
                    mengelompokkan siswa dan laporan kehadiran.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Add/Edit Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{dialogMode === 'add' ? 'Tambah Kelas Baru' : 'Edit Kelas'}</DialogTitle>
            <DialogDescription>
              {dialogMode === 'add'
                ? 'Masukkan informasi untuk kelas baru'
                : 'Perbarui informasi kelas'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nama Kelas</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                placeholder="Contoh: X IPA 1, XI, 12A"
                disabled={isSubmitting}
                required
              />
              <p className="text-xs text-slate-500">
                Nama kelas bebas sesuai kebutuhan sekolah Anda
              </p>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>
                Batal
              </Button>
              <Button
                type="submit"
                className="bg-indigo-600 text-white hover:bg-indigo-700"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {dialogMode === 'add' ? 'Menambahkan...' : 'Menyimpan...'}
                  </>
                ) : dialogMode === 'add' ? (
                  'Tambah'
                ) : (
                  'Simpan'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Hapus Kelas</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus kelas ini? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>

          {selectedClass && (
            <div className="py-4">
              <p className="text-sm font-medium">Detail Kelas:</p>
              <ul className="mt-2 space-y-1 text-sm text-slate-500 dark:text-slate-400">
                <li>
                  <span className="font-medium">Nama:</span> {selectedClass.name}
                </li>
                <li>
                  <span className="font-medium">Dibuat:</span>{' '}
                  {new Date(selectedClass.createdAt).toLocaleDateString(TIMEZONE_CONFIG.LOCALE, {
                    timeZone: TIMEZONE_CONFIG.TIMEZONE,
                  })}
                </li>
              </ul>
              <div className="mt-4 rounded-md bg-yellow-50 p-3 text-sm text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200">
                <div className="flex items-start">
                  <AlertTriangle className="mr-2 h-4 w-4 flex-shrink-0 translate-y-0.5" />
                  <div>
                    <p className="font-medium">Perhatian!</p>
                    <p className="mt-1 text-xs">
                      Menghapus kelas dapat mempengaruhi data siswa yang terhubung dengan kelas ini.
                      Pastikan tidak ada siswa yang masih terdaftar dalam kelas ini sebelum
                      menghapus.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex space-x-2 sm:justify-end">
            <Button type="button" variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bottom navigation - hanya tampil di mobile */}
      <AdminBottomNav activeTab="classes" />
    </div>
  )
}
