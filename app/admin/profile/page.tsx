'use client'

import type React from 'react'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { useToast } from '@/hooks/use-toast'
import { useAdminAuth } from '@/hooks/use-hybrid-auth'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

export default function AdminProfile() {
  const { toast } = useToast()
  const { user: admin, isLoading: sessionLoading, logout } = useAdminAuth()
  const [loading, setLoading] = useState(false)
  const [name, setName] = useState('')
  const [error, setError] = useState<string | null>(null)

  // Set nama admin saat data admin tersedia
  useEffect(() => {
    if (admin && admin.name) {
      setName(admin.name)
    }
  }, [admin])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/admin/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
        },
        credentials: 'include',
        body: JSON.stringify({ name }),
      })

      if (!response.ok) {
        let errorData
        try {
          errorData = await response.json()
        } catch (parseError) {
          console.error('Error parsing error response:', parseError)
          throw new Error(`Failed with status ${response.status}`)
        }

        throw new Error(errorData.error || 'Gagal memperbarui profil')
      }

      toast({
        title: 'Profil diperbarui',
        description: 'Perubahan profil berhasil disimpan',
      })
    } catch (error) {
      console.error('Update profile error:', error)

      // Tampilkan pesan error yang sesuai
      let errorMessage = 'Gagal memperbarui profil. Silakan coba lagi.'

      if (error instanceof TypeError && error.message.includes('fetch')) {
        errorMessage = 'Koneksi terputus. Periksa koneksi internet Anda dan coba lagi.'
      } else if (error instanceof DOMException && error.name === 'AbortError') {
        errorMessage = 'Permintaan timeout. Server tidak merespons, coba lagi nanti.'
      } else if (error instanceof Error) {
        errorMessage = error.message
      }

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Tampilkan loading state jika sedang memeriksa sesi
  if (sessionLoading) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </header>

        <main className="container mx-auto max-w-lg px-4">
          <Card className="border-indigo-100 shadow-md dark:border-slate-700">
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  // Jika tidak ada admin, jangan render apa-apa (akan di-redirect oleh AdminLayout)
  if (!admin) {
    return null
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="flex items-center justify-between p-4">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Profil Admin</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto max-w-lg px-4">
        <Card className="border-indigo-100 shadow-md dark:border-slate-700">
          <CardHeader>
            <CardTitle className="text-xl">Informasi Admin</CardTitle>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={admin?.username || ''}
                  disabled
                  className="bg-slate-100 dark:bg-slate-800"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">Nama</Label>
                <Input
                  id="name"
                  value={name || ''}
                  onChange={e => setName(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>
              <Button
                type="submit"
                className="relative w-full bg-indigo-600 text-white hover:bg-indigo-700"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="opacity-0">Simpan</span>
                    <span className="absolute inset-0 flex items-center justify-center">
                      <svg
                        className="h-5 w-5 animate-spin text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span className="ml-2">Menyimpan...</span>
                    </span>
                  </>
                ) : (
                  'Simpan'
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                className="w-full border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950"
                onClick={logout}
                disabled={loading}
              >
                Logout
              </Button>
            </form>
          </CardContent>
        </Card>
      </main>

      <AdminBottomNav activeTab="profile" />
    </div>
  )
}
