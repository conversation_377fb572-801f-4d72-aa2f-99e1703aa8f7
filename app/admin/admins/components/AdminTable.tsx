'use client'

import type React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Edit, Trash2, Shield, ShieldCheck, GraduationCap, Users, Loader2 } from 'lucide-react'

// Admin type definition
interface Admin {
  id: number
  name: string
  username: string
  role: 'admin' | 'super_admin' | 'teacher' | 'receptionist'
}

interface AdminTableProps {
  admins: Admin[]
  paginatedAdmins: Admin[]
  selectedAdmins: Set<number>
  currentPage: number
  itemsPerPage: number
  isSubmitting: boolean
  isLoading: boolean
  deletingAdminId: number | null
  handleEditAdmin: (admin: Admin) => void
  handleDeleteClick: (admin: Admin) => void
  toggleAdminSelection: (adminId: number) => void
  toggleAllAdmins: () => void
  handleSort: (key: string) => void
  renderSortIndicator: (key: string) => React.ReactNode
}

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'super_admin':
      return <ShieldCheck className="h-4 w-4 text-red-500" />
    case 'admin':
      return <Shield className="h-4 w-4 text-blue-500" />
    case 'teacher':
      return <GraduationCap className="h-4 w-4 text-green-500" />
    case 'receptionist':
      return <Users className="h-4 w-4 text-purple-500" />
    default:
      return null
  }
}

const getRoleLabel = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'Super Admin'
    case 'admin':
      return 'Admin'
    case 'teacher':
      return 'Teacher'
    case 'receptionist':
      return 'Receptionist'
    default:
      return role
  }
}

const getRoleColor = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'text-red-600'
    case 'admin':
      return 'text-blue-600'
    case 'teacher':
      return 'text-green-600'
    case 'receptionist':
      return 'text-purple-600'
    default:
      return 'text-gray-600'
  }
}

export const AdminTable: React.FC<AdminTableProps> = ({
  admins,
  paginatedAdmins,
  selectedAdmins,
  currentPage,
  itemsPerPage,
  isSubmitting,
  isLoading,
  deletingAdminId,
  handleEditAdmin,
  handleDeleteClick,
  toggleAdminSelection,
  toggleAllAdmins,
  handleSort,
  renderSortIndicator,
}) => {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[50px]">
            <div className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                checked={
                  paginatedAdmins.length > 0 && selectedAdmins.size === paginatedAdmins.length
                }
                onChange={toggleAllAdmins}
                title={
                  selectedAdmins.size === paginatedAdmins.length
                    ? 'Batalkan semua seleksi'
                    : 'Pilih semua admin di halaman ini'
                }
              />
            </div>
          </TableHead>
          <TableHead className="text-center">#</TableHead>
          <TableHead
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
            onClick={() => handleSort('name')}
          >
            Nama {renderSortIndicator('name')}
          </TableHead>
          <TableHead
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
            onClick={() => handleSort('username')}
          >
            Username {renderSortIndicator('username')}
          </TableHead>
          <TableHead
            className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
            onClick={() => handleSort('role')}
          >
            Role {renderSortIndicator('role')}
          </TableHead>
          <TableHead className="text-right">Aksi</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {admins.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="h-24 text-center">
              Tidak ada data admin
            </TableCell>
          </TableRow>
        ) : paginatedAdmins.length === 0 ? (
          <TableRow>
            <TableCell colSpan={6} className="h-24 text-center">
              Tidak ada hasil yang cocok dengan pencarian
            </TableCell>
          </TableRow>
        ) : (
          paginatedAdmins.map((admin, index) => (
            <TableRow
              key={admin.id}
              className={`hover:bg-indigo-50 dark:hover:bg-slate-800 ${
                selectedAdmins.has(admin.id) ? 'bg-indigo-50 dark:bg-slate-800/60' : ''
              } ${deletingAdminId === admin.id ? 'bg-red-50 opacity-50 dark:bg-red-950/20' : ''}`}
            >
              <TableCell>
                <input
                  type="checkbox"
                  className="h-5 w-5 cursor-pointer rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  checked={selectedAdmins.has(admin.id)}
                  onChange={() => toggleAdminSelection(admin.id)}
                  disabled={deletingAdminId === admin.id}
                  title={
                    deletingAdminId === admin.id
                      ? 'Admin sedang dihapus'
                      : 'Pilih/batalkan pilih admin ini'
                  }
                />
              </TableCell>
              <TableCell className="text-center font-medium">
                {(currentPage - 1) * itemsPerPage + index + 1}
              </TableCell>
              <TableCell className="font-medium">{admin.name}</TableCell>
              <TableCell>{admin.username}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  {getRoleIcon(admin.role)}
                  <span className={getRoleColor(admin.role)}>{getRoleLabel(admin.role)}</span>
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditAdmin(admin)}
                    disabled={isSubmitting || isLoading || deletingAdminId === admin.id}
                  >
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">Edit</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950"
                    onClick={() => handleDeleteClick(admin)}
                    disabled={isSubmitting || isLoading || deletingAdminId === admin.id}
                  >
                    {deletingAdminId === admin.id ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                    <span className="sr-only">Delete</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  )
}
