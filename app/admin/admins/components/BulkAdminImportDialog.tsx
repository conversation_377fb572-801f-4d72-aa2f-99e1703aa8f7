'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/components/ui/use-toast'
import {
  Upload,
  Download,
  Loader2,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Users,
} from 'lucide-react'
import Papa from 'papaparse'

interface BulkAdminImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete: () => void
}

type ImportStep = 'select' | 'validate' | 'upload' | 'complete'

interface ImportResults {
  total: number
  success: number
  failed: number
  errors: Array<{
    row: number
    message: string
    data: any
  }>
}

interface ValidationError {
  row: number
  field: string
  message: string
  value: any
}

export function BulkAdminImportDialog({
  open,
  onOpenChange,
  onImportComplete,
}: BulkAdminImportDialogProps) {
  const { toast } = useToast()
  const [currentStep, setCurrentStep] = useState<ImportStep>('select')
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState<any[]>([])
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [isValidating, setIsValidating] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isDragOver, setIsDragOver] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [importResults, setImportResults] = useState<ImportResults | null>(null)
  const [abortController, setAbortController] = useState<AbortController | null>(null)

  const resetDialog = () => {
    // Cancel any ongoing upload
    if (abortController) {
      abortController.abort()
      setAbortController(null)
    }

    setCurrentStep('select')
    setCsvFile(null)
    setCsvData([])
    setValidationErrors([])
    setImportResults(null)
    setIsValidating(false)
    setIsUploading(false)
    setUploadProgress(0)
    setIsDragOver(false)
    setIsDownloading(false)
  }

  const validateAndSetFile = (file: File) => {
    // Validate file type
    const fileName = file.name.toLowerCase()
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
      toast({
        title: 'Format File Tidak Valid',
        description: 'Hanya file CSV dan Excel (.csv, .xlsx, .xls) yang diperbolehkan',
        variant: 'destructive',
      })
      return false
    }

    // Validate file size (10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      toast({
        title: 'File Terlalu Besar',
        description: 'Ukuran file maksimal 10MB',
        variant: 'destructive',
      })
      return false
    }

    // Check if file is empty
    if (file.size === 0) {
      toast({
        title: 'File Kosong',
        description: 'File yang dipilih kosong. Silakan pilih file yang berisi data.',
        variant: 'destructive',
      })
      return false
    }

    setCsvFile(file)
    return true
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      validateAndSetFile(file)
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragOver(false)

    const files = event.dataTransfer.files
    if (files.length > 0) {
      const file = files[0]
      validateAndSetFile(file)
    }
  }

  const validateFile = async () => {
    if (!csvFile) return

    setIsValidating(true)
    setValidationErrors([])

    try {
      // Use API validation for comprehensive database checks
      const formData = new FormData()
      formData.append('file', csvFile)

      const response = await fetch('/api/admins/bulk-validate', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Validasi gagal')
      }

      const validationResult = await response.json()

      // Convert API response to ValidationError format
      const errors: ValidationError[] = validationResult.errors.map((error: any) => ({
        row: error.row,
        field: error.field,
        message: error.message,
        value: error.value,
      }))

      setValidationErrors(errors)
      setCsvData(validationResult.preview.sampleData || [])

      if (validationResult.isValid) {
        setCurrentStep('upload')
        toast({
          title: 'Validasi Berhasil',
          description: `${validationResult.validRecords} dari ${validationResult.totalRecords} admin siap untuk diimport`,
        })
      } else {
        setCurrentStep('validate')
        toast({
          title: 'Validasi Gagal',
          description: `Ditemukan ${validationResult.errors.length} error yang perlu diperbaiki`,
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('API validation error:', error)
      // Fallback to client-side validation
      await validateFileClientSide()
    }

    setIsValidating(false)
  }

  // Fallback client-side validation (for when API fails)
  const validateFileClientSide = async () => {
    try {
      const fileName = csvFile!.name.toLowerCase()

      if (fileName.endsWith('.csv')) {
        // Handle CSV files
        const fileContent = await csvFile!.text()

        Papa.parse(fileContent, {
          header: true,
          skipEmptyLines: true,
          complete: results => {
            const data = results.data as any[]
            setCsvData(data)
            validateDataClientSide(data)
          },
          error: (error: any) => {
            toast({
              title: 'Error Parsing CSV',
              description: error.message,
              variant: 'destructive',
            })
            setIsValidating(false)
          },
        })
      } else {
        // Handle Excel files - skip frontend validation, let backend handle it
        setCsvData([])
        setCurrentStep('upload')
        toast({
          title: 'File Excel Diterima',
          description: 'File Excel akan divalidasi saat upload (Mode Offline)',
        })
        setIsValidating(false)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Gagal memproses file',
        variant: 'destructive',
      })
      setIsValidating(false)
    }
  }

  const validateDataClientSide = (data: any[]) => {
    // Validate data structure and content
    const errors: ValidationError[] = []
    const requiredFields = ['name', 'username', 'password', 'role']
    const validRoles = ['admin', 'super_admin', 'teacher', 'receptionist']

    // Check if required columns exist
    if (data.length > 0) {
      const firstRow = data[0]
      const missingFields = requiredFields.filter(field => !(field in firstRow))

      if (missingFields.length > 0) {
        errors.push({
          row: 0,
          field: 'header',
          message: `Kolom yang diperlukan tidak ditemukan: ${missingFields.join(', ')}`,
          value: null,
        })
      }
    }

    // Validate each row
    data.forEach((row, index) => {
      const rowNumber = index + 1

      // Validate name
      if (!row.name || row.name.trim().length === 0) {
        errors.push({
          row: rowNumber,
          field: 'name',
          message: 'Nama wajib diisi',
          value: row.name,
        })
      } else if (row.name.length > 250) {
        errors.push({
          row: rowNumber,
          field: 'name',
          message: 'Nama maksimal 250 karakter',
          value: row.name,
        })
      } else if (!/^[a-zA-Z\s]+$/.test(row.name)) {
        errors.push({
          row: rowNumber,
          field: 'name',
          message: 'Nama hanya boleh berisi huruf dan spasi',
          value: row.name,
        })
      }

      // Validate username
      if (!row.username || row.username.trim().length === 0) {
        errors.push({
          row: rowNumber,
          field: 'username',
          message: 'Username wajib diisi',
          value: row.username,
        })
      } else if (row.username.length < 3 || row.username.length > 50) {
        errors.push({
          row: rowNumber,
          field: 'username',
          message: 'Username harus 3-50 karakter',
          value: row.username,
        })
      } else if (!/^[a-zA-Z0-9_]+$/.test(row.username)) {
        errors.push({
          row: rowNumber,
          field: 'username',
          message: 'Username hanya boleh berisi huruf, angka, dan underscore',
          value: row.username,
        })
      }

      // Validate password
      if (!row.password || row.password.length < 6) {
        errors.push({
          row: rowNumber,
          field: 'password',
          message: 'Password minimal 6 karakter',
          value: row.password ? '***' : '',
        })
      }

      // Validate role
      if (!row.role || !validRoles.includes(row.role)) {
        errors.push({
          row: rowNumber,
          field: 'role',
          message: `Role harus salah satu dari: ${validRoles.join(', ')}`,
          value: row.role,
        })
      }
    })

    setValidationErrors(errors)

    if (errors.length === 0) {
      setCurrentStep('upload')
      toast({
        title: 'Validasi Berhasil (Mode Offline)',
        description: `${data.length} admin siap untuk diimport. Catatan: Validasi database tidak dapat dilakukan.`,
      })
    } else {
      setCurrentStep('validate')
      toast({
        title: 'Validasi Gagal',
        description: `Ditemukan ${errors.length} error yang perlu diperbaiki`,
        variant: 'destructive',
      })
    }
  }

  const handleUpload = async () => {
    if (!csvFile) return

    // Create abort controller for cancellation
    const controller = new AbortController()
    setAbortController(controller)
    setIsUploading(true)
    setUploadProgress(0)

    try {
      const formData = new FormData()
      formData.append('file', csvFile)

      // Simulate progress for better UX (since we can't track actual upload progress easily)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) return prev // Stop at 90% until response
          return prev + Math.random() * 10
        })
      }, 500)

      const response = await fetch('/api/admins/bulk-upload', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
        // Add timeout
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Upload gagal' }))
        throw new Error(errorData.message || 'Upload gagal')
      }

      const result = await response.json()
      setImportResults(result.results)
      setCurrentStep('complete')

      toast({
        title: 'Import Selesai',
        description: result.message,
      })

      onImportComplete()
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        toast({
          title: 'Upload Dibatalkan',
          description: 'Upload telah dibatalkan oleh user',
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Error Upload',
          description: error instanceof Error ? error.message : 'Gagal mengupload file',
          variant: 'destructive',
        })
      }
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
      setAbortController(null)
    }
  }

  const handleCancelUpload = () => {
    if (abortController) {
      abortController.abort()
    }
  }

  const downloadSampleCsv = async () => {
    setIsDownloading(true)

    try {
      // Add small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 300))

      const sampleData = [
        {
          name: 'John Doe',
          username: 'johndoe',
          password: 'password123',
          role: 'admin',
        },
        {
          name: 'Jane Smith',
          username: 'janesmith',
          password: 'securepass456',
          role: 'teacher',
        },
        {
          name: 'Bob Wilson',
          username: 'bobwilson',
          password: 'mypass789',
          role: 'receptionist',
        },
        {
          name: 'Alice Brown',
          username: 'alicebrown',
          password: 'adminpass321',
          role: 'super_admin',
        },
      ]

      const csv = Papa.unparse(sampleData)
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', 'sample_admin_import.csv')
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: 'Download Berhasil',
        description: 'Sample CSV telah didownload',
      })
    } catch (error) {
      toast({
        title: 'Download Gagal',
        description: 'Gagal mendownload sample CSV',
        variant: 'destructive',
      })
    } finally {
      setIsDownloading(false)
    }
  }

  const handleClose = () => {
    // Prevent closing during upload
    if (isUploading) {
      toast({
        title: 'Upload Sedang Berlangsung',
        description: 'Tunggu hingga upload selesai atau batalkan upload terlebih dahulu',
        variant: 'destructive',
      })
      return
    }

    resetDialog()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Bulk Import Admin
          </DialogTitle>
          <DialogDescription>
            Import multiple admin users sekaligus menggunakan file CSV atau Excel
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step Indicator */}
          <div className="flex items-center justify-center space-x-4">
            <div
              className={`flex items-center space-x-2 ${currentStep === 'select' ? 'text-blue-600' : currentStep === 'validate' || currentStep === 'upload' || currentStep === 'complete' ? 'text-green-600' : 'text-gray-400'}`}
            >
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 'select' ? 'border-2 border-blue-600 bg-blue-100' : currentStep === 'validate' || currentStep === 'upload' || currentStep === 'complete' ? 'border-2 border-green-600 bg-green-100' : 'border-2 border-gray-300 bg-gray-100'}`}
              >
                {currentStep === 'validate' ||
                currentStep === 'upload' ||
                currentStep === 'complete' ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">1</span>
                )}
              </div>
              <span className="text-sm font-medium">Pilih File</span>
            </div>

            <div
              className={`h-0.5 w-8 ${currentStep === 'validate' || currentStep === 'upload' || currentStep === 'complete' ? 'bg-green-600' : 'bg-gray-300'}`}
            />

            <div
              className={`flex items-center space-x-2 ${currentStep === 'validate' ? 'text-blue-600' : currentStep === 'upload' || currentStep === 'complete' ? 'text-green-600' : 'text-gray-400'}`}
            >
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 'validate' ? 'border-2 border-blue-600 bg-blue-100' : currentStep === 'upload' || currentStep === 'complete' ? 'border-2 border-green-600 bg-green-100' : 'border-2 border-gray-300 bg-gray-100'}`}
              >
                {currentStep === 'upload' || currentStep === 'complete' ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">2</span>
                )}
              </div>
              <span className="text-sm font-medium">Validasi</span>
            </div>

            <div
              className={`h-0.5 w-8 ${currentStep === 'upload' || currentStep === 'complete' ? 'bg-green-600' : 'bg-gray-300'}`}
            />

            <div
              className={`flex items-center space-x-2 ${currentStep === 'upload' ? 'text-blue-600' : currentStep === 'complete' ? 'text-green-600' : 'text-gray-400'}`}
            >
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${currentStep === 'upload' ? 'border-2 border-blue-600 bg-blue-100' : currentStep === 'complete' ? 'border-2 border-green-600 bg-green-100' : 'border-2 border-gray-300 bg-gray-100'}`}
              >
                {currentStep === 'complete' ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">3</span>
                )}
              </div>
              <span className="text-sm font-medium">Import</span>
            </div>
          </div>

          {/* Step Content */}
          {currentStep === 'select' && (
            <div className="space-y-4">
              <Alert>
                <FileText className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p>
                      <strong>Format yang diperlukan:</strong>
                    </p>
                    <ul className="list-inside list-disc space-y-1 text-sm">
                      <li>
                        <strong>name:</strong> Nama lengkap admin (huruf dan spasi saja)
                      </li>
                      <li>
                        <strong>username:</strong> Username unik (3-50 karakter, huruf, angka,
                        underscore)
                      </li>
                      <li>
                        <strong>password:</strong> Password (minimal 6 karakter)
                      </li>
                      <li>
                        <strong>role:</strong> admin, super_admin, teacher, atau receptionist
                      </li>
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>

              {/* Drag & Drop File Upload */}
              <div className="space-y-2">
                <Label>File CSV/Excel</Label>
                <div
                  className={`relative rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                    isDragOver
                      ? 'border-blue-400 bg-blue-50'
                      : csvFile
                        ? 'border-green-400 bg-green-50'
                        : 'border-gray-300 bg-gray-50 hover:border-gray-400'
                  } ${isValidating ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  onClick={() => document.getElementById('csvFile')?.click()}
                >
                  <input
                    id="csvFile"
                    type="file"
                    accept=".csv,.xlsx,.xls"
                    onChange={handleFileChange}
                    disabled={isValidating}
                    className="hidden"
                  />

                  {csvFile ? (
                    <div className="space-y-2">
                      <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                      <div>
                        <p className="font-medium text-green-700">{csvFile.name}</p>
                        <p className="text-sm text-green-600">
                          {(csvFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={e => {
                          e.stopPropagation()
                          setCsvFile(null)
                          const input = document.getElementById('csvFile') as HTMLInputElement
                          if (input) input.value = ''
                        }}
                        disabled={isValidating}
                      >
                        Ganti File
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div>
                        <p className="font-medium">
                          {isDragOver
                            ? 'Lepaskan file di sini'
                            : 'Drag & drop file atau klik untuk pilih'}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Maksimal 10MB, format: CSV, Excel (.csv, .xlsx, .xls)
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={downloadSampleCsv}
                  disabled={isDownloading}
                  className="flex items-center gap-2"
                >
                  {isDownloading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Downloading...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4" />
                      Download Sample CSV
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {currentStep === 'validate' && (
            <div className="space-y-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Ditemukan {validationErrors.length} error yang perlu diperbaiki sebelum import.
                </AlertDescription>
              </Alert>

              <div className="max-h-60 overflow-y-auto rounded-lg border">
                <div className="space-y-2 p-4">
                  {validationErrors.map((error, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-2 rounded border-l-4 border-red-400 bg-red-50 p-2"
                    >
                      <XCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-red-500" />
                      <div className="flex-1 text-sm">
                        <p className="font-medium">
                          Baris {error.row} - {error.field}
                        </p>
                        <p className="text-red-700">{error.message}</p>
                        {error.value && (
                          <p className="text-xs text-gray-600">
                            Nilai: {error.field === 'password' ? '***' : error.value}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Silakan perbaiki error di file CSV Anda dan upload ulang file yang sudah
                  diperbaiki.
                </AlertDescription>
              </Alert>
            </div>
          )}

          {currentStep === 'upload' && (
            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  File berhasil divalidasi! {csvData.length} admin siap untuk diimport.
                </AlertDescription>
              </Alert>

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Upload Progress</span>
                    <span className="text-sm text-gray-500">{Math.round(uploadProgress)}%</span>
                  </div>
                  <Progress value={uploadProgress} className="h-2" />
                  <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Mengupload dan memproses file...</span>
                  </div>
                </div>
              )}

              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium">Preview Data:</h4>
                <div className="max-h-40 overflow-y-auto">
                  <div className="mb-2 grid grid-cols-4 gap-2 text-xs font-medium">
                    <div>Nama</div>
                    <div>Username</div>
                    <div>Password</div>
                    <div>Role</div>
                  </div>
                  {csvData.slice(0, 5).map((row, index) => (
                    <div key={index} className="grid grid-cols-4 gap-2 border-t py-1 text-xs">
                      <div className="truncate">{row.name}</div>
                      <div className="truncate">{row.username}</div>
                      <div>***</div>
                      <div className="truncate">{row.role}</div>
                    </div>
                  ))}
                  {csvData.length > 5 && (
                    <div className="mt-2 text-xs text-gray-500">
                      ... dan {csvData.length - 5} admin lainnya
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {currentStep === 'complete' && importResults && (
            <div className="space-y-4">
              <div className="text-center">
                <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-500" />
                <h3 className="text-lg font-medium">Import Selesai!</h3>
              </div>

              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="rounded-lg bg-blue-50 p-4">
                  <div className="text-2xl font-bold text-blue-600">{importResults.total}</div>
                  <div className="text-sm text-blue-700">Total</div>
                </div>
                <div className="rounded-lg bg-green-50 p-4">
                  <div className="text-2xl font-bold text-green-600">{importResults.success}</div>
                  <div className="text-sm text-green-700">Berhasil</div>
                </div>
                <div className="rounded-lg bg-red-50 p-4">
                  <div className="text-2xl font-bold text-red-600">{importResults.failed}</div>
                  <div className="text-sm text-red-700">Gagal</div>
                </div>
              </div>

              {importResults.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-red-600">Error yang Terjadi:</h4>
                  <div className="max-h-40 overflow-y-auto rounded-lg border bg-red-50 p-2">
                    {importResults.errors.map((error, index) => (
                      <div key={index} className="border-b py-1 text-sm last:border-b-0">
                        <span className="font-medium">Baris {error.row}:</span> {error.message}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {currentStep === 'complete' ? 'Tutup' : 'Batal'}
          </Button>

          {currentStep === 'select' && (
            <Button onClick={validateFile} disabled={!csvFile || isValidating}>
              {isValidating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Validasi...
                </>
              ) : (
                'Validasi File'
              )}
            </Button>
          )}

          {currentStep === 'validate' && (
            <Button onClick={() => setCurrentStep('select')} variant="outline">
              Pilih File Lain
            </Button>
          )}

          {currentStep === 'upload' && (
            <>
              {isUploading && (
                <Button onClick={handleCancelUpload} variant="outline">
                  Batalkan Upload
                </Button>
              )}
              <Button onClick={handleUpload} disabled={isUploading}>
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Mengimport...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Import Admin
                  </>
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
