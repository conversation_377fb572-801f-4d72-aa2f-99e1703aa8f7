'use client'

import type React from 'react'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { useToast } from '@/components/ui/use-toast'
import {
  Plus,
  Loader2,
  AlertCircle,
  Upload,
  Trash,
  XCircle,
  Shield,
  ShieldCheck,
  GraduationCap,
  Users,
} from 'lucide-react'
import { PasswordInput } from '@/components/ui/password-input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAdminAuth } from '@/hooks/use-hybrid-auth'
import { useAdminPermission } from '@/hooks/use-admin-permission'
import { useRouter } from 'next/navigation'
import { BulkAdminImportDialog } from './components/BulkAdminImportDialog'
import { AdminTable } from './components/AdminTable'
import { AdminFilterBar } from './components/AdminFilterBar'
import { Pagination } from '@/components/admin/Pagination'

// Admin type definition
interface Admin {
  id: number
  name: string
  username: string
  role: 'admin' | 'super_admin' | 'teacher' | 'receptionist'
}

// Form data type
interface FormData {
  id: string
  name: string
  username: string
  role: string
  password: string
}

// Interface for form validation errors
interface FormErrors {
  password?: string
  username?: string
  name?: string
}

// Define the dialog mode type explicitly
type DialogMode = 'add' | 'edit'

export default function AdminManagement() {
  const { toast } = useToast()
  const { user: _admin } = useAdminAuth()
  const { permission, loading: permissionLoading } = useAdminPermission('super_admin')
  const router = useRouter()
  const [showDialog, setShowDialog] = useState(false)
  const [dialogMode, setDialogMode] = useState<DialogMode>('add')
  const [selectedAdmin, setSelectedAdmin] = useState<null | Admin>(null)
  const [formData, setFormData] = useState<FormData>({
    id: '',
    name: '',
    username: '',
    role: 'admin',
    password: '',
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [admins, setAdmins] = useState<Admin[]>([])
  const [filteredAdmins, setFilteredAdmins] = useState<Admin[]>([])
  const [paginatedAdmins, setPaginatedAdmins] = useState<Admin[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteError, setDeleteError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [showBulkImportDialog, setShowBulkImportDialog] = useState(false)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)

  // Bulk operations state
  const [selectedAdmins, setSelectedAdmins] = useState<Set<number>>(new Set())
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteProgress, setDeleteProgress] = useState<{
    success: number
    failed: number
    total: number
  } | null>(null)
  const [deleteErrors, setDeleteErrors] = useState<
    Array<{ adminId: number; name: string; message: string }>
  >([])
  const [showDeleteErrors, setShowDeleteErrors] = useState(false)
  const [deleteStatus, setDeleteStatus] = useState<'idle' | 'deleting' | 'completed'>('idle')
  const [deletingAdminId, setDeletingAdminId] = useState<number | null>(null)

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'ascending' | 'descending'
  } | null>(null)

  // Fetch admins from API
  const fetchAdmins = async (forceRefresh = false) => {
    try {
      setIsLoading(true)
      const url = forceRefresh ? `/api/admins?t=${Date.now()}` : '/api/admins'
      const response = await fetch(url, {
        // Force no-cache when refreshing after updates
        cache: forceRefresh ? 'no-cache' : 'default',
        headers: forceRefresh
          ? {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
              Expires: '0',
            }
          : {},
      })

      if (!response.ok) {
        throw new Error('Failed to fetch admins')
      }

      const data = await response.json()
      // Data is already filtered to admin and super_admin users
      setAdmins(data)
      return data
    } catch (error) {
      console.error('Error fetching admins:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengambil data admin',
        variant: 'destructive',
      })
      return []
    } finally {
      setIsLoading(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    fetchAdmins()
  }, [])

  // Natural sorting function for better string/number sorting
  const naturalSort = (a: string, b: string): number => {
    const pattern = /(\d+|\D+)/g
    const partsA = String(a).match(pattern) || []
    const partsB = String(b).match(pattern) || []

    for (let i = 0; i < Math.min(partsA.length, partsB.length); i++) {
      const partA = partsA[i]
      const partB = partsB[i]
      const numA = !isNaN(Number(partA))
      const numB = !isNaN(Number(partB))

      if (numA && numB) {
        const diff = parseInt(partA) - parseInt(partB)
        if (diff !== 0) return diff
      } else {
        const diff = partA.localeCompare(partB)
        if (diff !== 0) return diff
      }
    }
    return partsA.length - partsB.length
  }

  // Filter and sort admins whenever search query, role filter, sorting, or admins array changes
  useEffect(() => {
    let result = [...admins]

    // Filter by role if not 'all'
    if (roleFilter !== 'all') {
      result = result.filter(admin => admin.role === roleFilter)
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        admin =>
          admin.name.toLowerCase().includes(query) || admin.username.toLowerCase().includes(query)
      )
    }

    // Apply sorting if configured
    if (sortConfig !== null) {
      result.sort((a, b) => {
        let valueA: any = a[sortConfig.key as keyof Admin]
        let valueB: any = b[sortConfig.key as keyof Admin]

        if (valueA === null || valueA === undefined) valueA = ''
        if (valueB === null || valueB === undefined) valueB = ''

        valueA = String(valueA).toLowerCase()
        valueB = String(valueB).toLowerCase()

        const comparison = naturalSort(valueA, valueB)
        return sortConfig.direction === 'ascending' ? comparison : -comparison
      })
    }

    setFilteredAdmins(result)
    setCurrentPage(1) // Reset to first page when filtering/sorting changes
  }, [admins, searchQuery, roleFilter, sortConfig])

  // Update paginated admins when filtered admins or pagination settings change
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    setPaginatedAdmins(filteredAdmins.slice(startIndex, endIndex))
  }, [filteredAdmins, currentPage, itemsPerPage])

  const handleAddAdmin = () => {
    setDialogMode('add')
    setFormData({
      id: '',
      name: '',
      username: '',
      role: 'admin',
      password: '',
    })
    setFormErrors({})
    setShowDialog(true)
  }

  const handleBulkImport = () => {
    setShowBulkImportDialog(true)
  }

  const handleBulkImportComplete = () => {
    fetchAdmins(true) // Force refresh the admin list after bulk import
  }

  const handleEditAdmin = (admin: Admin) => {
    setDialogMode('edit')
    setSelectedAdmin(admin)
    setFormData({
      id: admin.id.toString(),
      name: admin.name,
      username: admin.username,
      role: admin.role,
      password: '',
    })
    setFormErrors({})
    setShowDialog(true)
  }

  const handleDeleteClick = (admin: Admin) => {
    setSelectedAdmin(admin)
    setDeleteError(null)
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedAdmin) return

    try {
      setIsSubmitting(true)
      setDeleteError(null)
      setDeletingAdminId(selectedAdmin.id)

      // Show immediate feedback
      toast({
        title: 'Menghapus Admin',
        description: `Sedang menghapus ${selectedAdmin.name}...`,
      })

      const response = await fetch(`/api/admins/${selectedAdmin.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || errorData.message || 'Failed to delete admin')
      }

      // Show success immediately
      toast({
        title: 'Berhasil',
        description: `${selectedAdmin.name} telah dihapus dari sistem`,
      })

      // Close dialog immediately after success
      setShowDeleteDialog(false)

      // Update UI optimistically - remove from local state first
      const deletedAdminId = selectedAdmin.id
      setAdmins(prevAdmins => prevAdmins.filter(admin => admin.id !== deletedAdminId))

      // Then refresh from server in background to ensure consistency
      setTimeout(() => {
        fetchAdmins(true)
      }, 100)
    } catch (error) {
      console.error('Error deleting admin:', error)
      const errorMessage = error instanceof Error ? error.message : 'Gagal menghapus admin'
      setDeleteError(errorMessage)
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
      setDeletingAdminId(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Clear any previous errors
    setFormErrors({})

    // Validation
    const errors: FormErrors = {}

    if (!formData.name.trim()) {
      errors.name = 'Nama wajib diisi'
    }

    if (!formData.username.trim()) {
      errors.username = 'Username wajib diisi'
    } else if (formData.username.length < 3) {
      errors.username = 'Username minimal 3 karakter'
    }

    // Password validation
    if (dialogMode === 'add' && formData.password.length < 6) {
      errors.password = 'Password minimal 6 karakter'
    }

    if (dialogMode === 'edit' && formData.password && formData.password.length < 6) {
      errors.password = 'Password minimal 6 karakter'
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)

    try {
      let endpoint = '/api/admins'
      let method = 'POST'
      let requestData: Record<string, any> = {}

      if (dialogMode === 'add') {
        requestData = {
          role: formData.role,
          name: formData.name,
          username: formData.username,
          password: formData.password,
        }
      } else {
        if (!selectedAdmin) throw new Error('No admin selected for edit')
        endpoint = `/api/admins/${selectedAdmin.id}`
        method = 'PATCH'
        requestData = {
          role: formData.role,
          name: formData.name,
          ...(formData.password ? { password: formData.password } : {}),
        }
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || errorData.message || 'Failed to save admin')
      }

      setShowDialog(false)
      // Reset form data
      setFormData({
        id: '',
        name: '',
        username: '',
        password: '',
        role: 'admin',
      })
      setFormErrors({})

      // Force refresh to bypass browser cache after update
      await fetchAdmins(true)

      toast({
        title: dialogMode === 'add' ? 'Admin ditambahkan' : 'Admin diperbarui',
        description:
          dialogMode === 'add'
            ? `${formData.name} telah ditambahkan sebagai ${formData.role}`
            : `Data ${formData.name} telah diperbarui`,
      })
    } catch (error) {
      console.error(`Error ${dialogMode === 'add' ? 'adding' : 'updating'} admin:`, error)
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : `Gagal ${dialogMode === 'add' ? 'menambahkan' : 'memperbarui'} admin`,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Bulk operations functions
  const toggleAdminSelection = (adminId: number) => {
    const newSelected = new Set(selectedAdmins)
    if (newSelected.has(adminId)) {
      newSelected.delete(adminId)
    } else {
      newSelected.add(adminId)
    }
    setSelectedAdmins(newSelected)
  }

  const toggleAllAdmins = () => {
    if (selectedAdmins.size === paginatedAdmins.length) {
      // Deselect all visible admins
      setSelectedAdmins(new Set())
    } else {
      // Select all visible admins
      const newSelected = new Set(selectedAdmins)
      paginatedAdmins.forEach(admin => newSelected.add(admin.id))
      setSelectedAdmins(newSelected)
    }
  }

  const clearAllSelections = () => {
    setSelectedAdmins(new Set())
  }

  const handleBulkDelete = () => {
    if (selectedAdmins.size === 0) {
      toast({
        title: 'Error',
        description: 'Harap pilih minimal satu admin untuk dihapus',
        variant: 'destructive',
      })
      return
    }

    setShowBulkDeleteDialog(true)
    setDeleteProgress(null)
    setDeleteErrors([])
    setShowDeleteErrors(false)
  }

  const handleBulkDeleteConfirm = async () => {
    try {
      setIsDeleting(true)
      setDeleteStatus('deleting')
      setDeleteProgress(null)
      setDeleteErrors([])

      toast({
        title: 'Menghapus Admin',
        description: `Sedang menghapus ${selectedAdmins.size} admin...`,
      })

      const response = await fetch('/api/admins/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          adminIds: Array.from(selectedAdmins),
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to delete: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      setDeleteProgress({
        success: result.results.success,
        failed: result.results.failed,
        total: result.results.total,
      })

      if (result.results.errors && result.results.errors.length > 0) {
        setDeleteErrors(result.results.errors)
      }

      toast({
        title: result.results.success > 0 ? 'Berhasil' : 'Selesai',
        description: result.message,
        variant: result.results.failed > 0 ? 'destructive' : 'default',
      })

      if (result.results.success > 0) {
        await fetchAdmins(true) // Refresh admin list
        setSelectedAdmins(new Set()) // Clear selections
        setDeleteStatus('completed')

        // Auto-close dialog after 1.5 seconds
        setTimeout(() => {
          setShowBulkDeleteDialog(false)
          setDeleteStatus('idle')
        }, 1500)
      }
    } catch (error) {
      console.error('Error deleting admins:', error)
      setDeleteStatus('idle')

      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Gagal menghapus admin',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Sorting functions
  const handleSort = (key: string) => {
    if (sortConfig && sortConfig.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'ascending' ? 'descending' : 'ascending',
      })
    } else {
      setSortConfig({ key, direction: 'ascending' })
    }
  }

  const renderSortIndicator = (key: string) => {
    if (sortConfig?.key !== key) {
      return <span className="ml-1 text-gray-400">↕</span>
    }
    return <span className="ml-1">{sortConfig.direction === 'ascending' ? '↑' : '↓'}</span>
  }

  // Show loading state while checking permissions
  if (permissionLoading) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Admin</h1>
          <ThemeToggle />
        </header>
        <main className="container mx-auto px-4">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </main>
        <AdminBottomNav activeTab="admins" />
      </div>
    )
  }

  // Show access denied if not super admin
  if (!permission) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Admin</h1>
          <ThemeToggle />
        </header>

        <main className="container mx-auto px-4">
          <Card className="p-8 text-center">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
            <h2 className="mb-2 text-xl font-bold text-red-500">Akses Ditolak</h2>
            <p className="mb-4 text-slate-600 dark:text-slate-400">
              Anda tidak memiliki izin untuk mengakses halaman ini. Hanya Super Admin yang dapat
              mengakses halaman Manajemen Admin.
            </p>
            <Button
              onClick={() => router.push('/admin/home')}
              className="bg-indigo-600 text-white hover:bg-indigo-700"
            >
              Kembali ke Beranda
            </Button>
          </Card>
        </main>

        <AdminBottomNav activeTab="admins" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="flex items-center justify-between p-4">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Admin</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto px-4">
        {/* Search and Filter Controls */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-1 flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
                <AdminFilterBar
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                  roleFilter={roleFilter}
                  setRoleFilter={setRoleFilter}
                />
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={handleBulkImport}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  <span className="hidden sm:inline">Bulk Import</span>
                  <span className="sm:hidden">Import</span>
                </Button>
                <Button
                  onClick={handleAddAdmin}
                  className="flex items-center gap-2 bg-indigo-600 text-white hover:bg-indigo-700"
                >
                  <Plus className="h-4 w-4" />
                  <span className="hidden sm:inline">Tambah Admin</span>
                  <span className="sm:hidden">Tambah</span>
                </Button>
                {selectedAdmins.size > 0 && (
                  <>
                    <Button
                      onClick={handleBulkDelete}
                      variant="destructive"
                      className="flex gap-1 sm:flex-nowrap"
                      disabled={isDeleting || isLoading}
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Menghapus...</span>
                        </>
                      ) : (
                        <>
                          <Trash className="h-4 w-4" />
                          <span>Hapus ({selectedAdmins.size})</span>
                        </>
                      )}
                    </Button>
                    <Button
                      onClick={clearAllSelections}
                      variant="outline"
                      className="flex gap-1 sm:flex-nowrap"
                      title="Batalkan semua seleksi"
                    >
                      <XCircle className="h-4 w-4" />
                      <span className="sm:hidden">Clear</span>
                      <span className="hidden sm:inline">Clear Seleksi</span>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Admin Table */}
        <Card className="relative overflow-hidden">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Daftar Admin ({filteredAdmins.length})</span>
              {isDeleting && (
                <div className="flex items-center space-x-2 text-sm text-blue-600">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Menghapus admin...</span>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="relative p-0">
            {/* Loading Overlay */}
            {isDeleting && (
              <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/80 backdrop-blur-sm">
                <div className="flex flex-col items-center space-y-3 rounded-lg border bg-white p-6 shadow-lg">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                  <div className="text-center">
                    <div className="font-medium text-slate-900">Menghapus Admin</div>
                    <div className="mt-1 text-sm text-slate-500">
                      {deleteProgress && deleteProgress.total > 0
                        ? `Menghapus ${deleteProgress.success + deleteProgress.failed} dari ${deleteProgress.total} admin...`
                        : 'Mohon tunggu...'}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2 text-slate-500">Memuat data...</span>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <AdminTable
                    admins={filteredAdmins}
                    paginatedAdmins={paginatedAdmins}
                    selectedAdmins={selectedAdmins}
                    currentPage={currentPage}
                    itemsPerPage={itemsPerPage}
                    isSubmitting={isSubmitting || isDeleting}
                    isLoading={isLoading || isDeleting}
                    deletingAdminId={deletingAdminId}
                    handleEditAdmin={handleEditAdmin}
                    handleDeleteClick={handleDeleteClick}
                    toggleAdminSelection={toggleAdminSelection}
                    toggleAllAdmins={toggleAllAdmins}
                    handleSort={handleSort}
                    renderSortIndicator={renderSortIndicator}
                  />
                </div>

                {/* Pagination */}
                {filteredAdmins.length > 0 && (
                  <Pagination
                    currentPage={currentPage}
                    totalItems={filteredAdmins.length}
                    itemsPerPage={itemsPerPage}
                    onPageChange={setCurrentPage}
                    onItemsPerPageChange={newItemsPerPage => {
                      setItemsPerPage(newItemsPerPage)
                      setCurrentPage(1)
                    }}
                  />
                )}
              </>
            )}
          </CardContent>
        </Card>
      </main>

      <AdminBottomNav activeTab="admins" />

      {/* Add/Edit Admin Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{dialogMode === 'add' ? 'Tambah Admin Baru' : 'Edit Admin'}</DialogTitle>
            <DialogDescription>
              {dialogMode === 'add' ? 'Masukkan informasi admin baru' : 'Perbarui informasi admin'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Nama
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={e => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  required
                />
                {formErrors.name && (
                  <div className="col-span-4 text-sm text-red-500">{formErrors.name}</div>
                )}
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="username" className="text-right">
                  Username
                </Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={e => setFormData({ ...formData, username: e.target.value })}
                  className="col-span-3"
                  required
                  disabled={dialogMode === 'edit'}
                />
                {formErrors.username && (
                  <div className="col-span-4 text-sm text-red-500">{formErrors.username}</div>
                )}
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">
                  Role
                </Label>
                <Select
                  value={formData.role}
                  onValueChange={value => setFormData({ ...formData, role: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Pilih role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-blue-500" />
                        <span>Admin</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="super_admin">
                      <div className="flex items-center gap-2">
                        <ShieldCheck className="h-4 w-4 text-red-500" />
                        <span>Super Admin</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="teacher">
                      <div className="flex items-center gap-2">
                        <GraduationCap className="h-4 w-4 text-green-500" />
                        <span>Teacher</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="receptionist">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-purple-500" />
                        <span>Receptionist</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password" className="text-right">
                  Password
                </Label>
                <PasswordInput
                  id="password"
                  value={formData.password}
                  onChange={e => setFormData({ ...formData, password: e.target.value })}
                  containerClassName="col-span-3"
                  placeholder={dialogMode === 'edit' ? 'Kosongkan jika tidak ingin mengubah' : ''}
                  required={dialogMode === 'add'}
                />
                {formErrors.password && (
                  <div className="col-span-4 text-sm text-red-500">{formErrors.password}</div>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>
                Batal
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {dialogMode === 'add' ? 'Menambahkan...' : 'Memperbarui...'}
                  </>
                ) : dialogMode === 'add' ? (
                  'Tambah'
                ) : (
                  'Perbarui'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus Admin</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus admin <strong>{selectedAdmin?.name}</strong>?
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {deleteError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{deleteError}</AlertDescription>
            </Alert>
          )}
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Admin Import Dialog */}
      <BulkAdminImportDialog
        open={showBulkImportDialog}
        onOpenChange={setShowBulkImportDialog}
        onImportComplete={handleBulkImportComplete}
      />

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-600">
              <Trash className="mr-2 h-5 w-5" />
              {deleteStatus === 'completed' ? 'Delete Selesai' : 'Konfirmasi Bulk Delete Admin'}
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="text-sm text-muted-foreground">
                {deleteStatus === 'idle' && (
                  <span>
                    Apakah Anda yakin ingin menghapus {selectedAdmins.size} admin yang dipilih?
                    Tindakan ini tidak dapat dibatalkan.
                  </span>
                )}
                {deleteStatus === 'deleting' && (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                    <span>Sedang menghapus admin...</span>
                  </div>
                )}
                {deleteStatus === 'completed' && (
                  <div className="flex items-center space-x-2 text-green-600">
                    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-green-600">
                      <div className="h-2 w-2 rounded-full bg-white"></div>
                    </div>
                    <span>Proses delete berhasil diselesaikan!</span>
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>

          {deleteProgress && (
            <div className="mt-4 space-y-3">
              <div className="text-sm font-medium">Progress:</div>

              {/* Progress Bar */}
              <div className="h-2 w-full rounded-full bg-gray-200">
                <div
                  className="h-2 rounded-full bg-blue-600 transition-all duration-300 ease-out"
                  style={{
                    width: `${deleteProgress.total > 0 ? ((deleteProgress.success + deleteProgress.failed) / deleteProgress.total) * 100 : 0}%`,
                  }}
                ></div>
              </div>

              {/* Progress Stats */}
              <div className="grid grid-cols-3 gap-2 text-sm">
                <div className="rounded bg-green-50 p-2 text-center">
                  <div className="font-semibold text-green-600">{deleteProgress.success}</div>
                  <div className="text-xs text-green-500">Berhasil</div>
                </div>
                <div className="rounded bg-red-50 p-2 text-center">
                  <div className="font-semibold text-red-600">{deleteProgress.failed}</div>
                  <div className="text-xs text-red-500">Gagal</div>
                </div>
                <div className="rounded bg-slate-50 p-2 text-center">
                  <div className="font-semibold text-slate-600">{deleteProgress.total}</div>
                  <div className="text-xs text-slate-500">Total</div>
                </div>
              </div>
            </div>
          )}

          {deleteErrors.length > 0 && (
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDeleteErrors(!showDeleteErrors)}
                className="w-full"
              >
                {showDeleteErrors ? 'Sembunyikan' : 'Tampilkan'} Error Details (
                {deleteErrors.length})
              </Button>
              {showDeleteErrors && (
                <div className="mt-2 max-h-32 overflow-y-auto rounded border bg-red-50 p-2 text-xs">
                  {deleteErrors.map((error, index) => (
                    <div key={index} className="mb-1 rounded bg-white p-1 text-red-700">
                      <span className="font-medium">{error.name}:</span> {error.message}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          <AlertDialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowBulkDeleteDialog(false)
                setDeleteStatus('idle')
              }}
              disabled={isDeleting && deleteStatus !== 'completed'}
            >
              {deleteStatus === 'completed' ? 'Tutup' : 'Batal'}
            </Button>
            {deleteStatus !== 'completed' && (
              <AlertDialogAction
                onClick={handleBulkDeleteConfirm}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 disabled:opacity-50"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menghapus...
                  </>
                ) : (
                  'Hapus Semua'
                )}
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
