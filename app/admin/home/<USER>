'use client'

import { useState, useEffect } from 'react'
import { AttendanceType } from '@/lib/domain/entities/absence'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { Search, User, Calendar, AlertTriangle, RefreshCw } from 'lucide-react'
import { getAttendanceTypeLabel } from '@/lib/utils/attendance-validation'
// Sound management import
import { playSuccessSound, playAlertSound, playErrorSound } from '@/lib/utils/sound-manager'
import { TIMEZONE_CONFIG } from '@/lib/config'

interface Student {
  uniqueCode: string
  name: string
  className: string
  nis: string | null
}

interface ManualEntryProps {
  allowedAttendanceTypes: AttendanceType[]
}

export default function ManualEntry({ allowedAttendanceTypes }: ManualEntryProps) {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [attendanceType, setAttendanceType] = useState<AttendanceType | ''>('')
  const [reason, setReason] = useState('')
  const [recordedAt, setRecordedAt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [searchResults, setSearchResults] = useState<Student[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false)
  const [duplicateData, setDuplicateData] = useState<{
    student: Student
    attendanceType: AttendanceType
    recordedAt: string
    reason?: string
  } | null>(null)

  // Date range state for sick and excused absence
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // Check if current attendance type supports date range
  const supportsDateRange =
    attendanceType === AttendanceType.SICK || attendanceType === AttendanceType.EXCUSED_ABSENCE

  // Set default date/time to current
  useEffect(() => {
    const now = new Date()
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
      .toISOString()
      .slice(0, 16)
    setRecordedAt(localDateTime)

    // Set default start and end dates for date range
    const localDate = localDateTime.slice(0, 10)
    setStartDate(localDate)
    setEndDate(localDate)
  }, [])

  // Reset form when attendance type changes
  useEffect(() => {
    // Reset to current date when switching attendance types
    const now = new Date()
    const localDate = now.toISOString().slice(0, 10)
    setStartDate(localDate)
    setEndDate(localDate)
  }, [attendanceType])

  // Search students
  const searchStudents = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([])
      return
    }

    setIsSearching(true)
    try {
      const response = await fetch(`/api/students/search?q=${encodeURIComponent(query)}`)
      if (response.ok) {
        const students = await response.json()
        setSearchResults(students)
      } else {
        setSearchResults([])
      }
    } catch (error) {
      console.error('Error searching students:', error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      searchStudents(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  const handleStudentSelect = (student: Student) => {
    setSelectedStudent(student)
    setSearchQuery(student.name)
    setSearchResults([])
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields based on whether using date range or single date
    if (!selectedStudent || !attendanceType) {
      playErrorSound()
      toast({
        title: 'Error',
        description: 'Harap lengkapi semua field yang diperlukan.',
        variant: 'destructive',
      })
      return
    }

    // Validate date fields based on attendance type
    if (supportsDateRange) {
      // For sick and excused absence, use date range
      if (!startDate || !endDate) {
        playErrorSound()
        toast({
          title: 'Error',
          description: 'Harap pilih tanggal mulai dan tanggal akhir.',
          variant: 'destructive',
        })
        return
      }

      // Validate date range
      const start = new Date(startDate)
      const end = new Date(endDate)
      if (start > end) {
        playErrorSound()
        toast({
          title: 'Error',
          description: 'Tanggal mulai tidak boleh setelah tanggal akhir.',
          variant: 'destructive',
        })
        return
      }

      // No maximum range limit - allow full flexibility for administrative needs
    } else {
      // For other attendance types, use single date/time
      if (!recordedAt) {
        playErrorSound()
        toast({
          title: 'Error',
          description: 'Harap pilih tanggal dan waktu.',
          variant: 'destructive',
        })
        return
      }
    }

    // Check if reason is required for certain attendance types
    const requiresReason = [
      AttendanceType.TEMPORARY_LEAVE,
      AttendanceType.EXCUSED_ABSENCE,
      AttendanceType.SICK,
    ].includes(attendanceType as AttendanceType)
    if (requiresReason && !reason.trim()) {
      playErrorSound()
      toast({
        title: 'Error',
        description: 'Alasan diperlukan untuk jenis absensi ini.',
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)

    try {
      // Prepare request body based on attendance type
      const requestBody = supportsDateRange
        ? {
            // Use date range for sick and excused absence
            uniqueCode: selectedStudent.uniqueCode,
            type: attendanceType,
            // Use SSOT WITA utilities instead of hardcoded time
            startDate: (() => {
              const [year, month, day] = startDate.split('-').map(Number)
              const witaDate = new Date(year, month - 1, day, 8, 0, 0, 0) // 08:00 WITA
              return witaDate.toISOString()
            })(),
            endDate: (() => {
              const [year, month, day] = endDate.split('-').map(Number)
              const witaDate = new Date(year, month - 1, day, 8, 0, 0, 0) // 08:00 WITA
              return witaDate.toISOString()
            })(),
            reason: reason.trim() || undefined,
          }
        : {
            // Use single date/time for other attendance types
            uniqueCode: selectedStudent.uniqueCode,
            type: attendanceType,
            recordedAt: new Date(recordedAt).toISOString(),
            reason: reason.trim() || undefined,
          }

      const response = await fetch('/api/absence/manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (response.ok) {
        const data = await response.json()
        playSuccessSound()

        // Handle different response types (single vs bulk)
        if (supportsDateRange && data.result) {
          // Bulk response for date range
          const { totalDays, successCount, failureCount } = data.result

          if (failureCount > 0) {
            // Partial success
            toast({
              title: 'Sebagian Berhasil',
              description: `${successCount} dari ${totalDays} hari berhasil dicatat. ${failureCount} hari gagal.`,
              variant: 'default',
            })
          } else {
            // Full success
            toast({
              title: 'Berhasil',
              description: `Absensi ${getAttendanceTypeLabel(attendanceType as AttendanceType)} untuk ${selectedStudent.name} berhasil dicatat untuk ${totalDays} hari.`,
            })
          }
        } else {
          // Single response
          toast({
            title: 'Berhasil',
            description: `Absensi ${getAttendanceTypeLabel(attendanceType as AttendanceType)} untuk ${selectedStudent.name} berhasil dicatat.`,
          })
        }

        // Reset form
        setSelectedStudent(null)
        setSearchQuery('')
        setAttendanceType('')
        setReason('')

        const now = new Date()
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
          .toISOString()
          .slice(0, 16)
        setRecordedAt(localDateTime)

        const localDate = localDateTime.slice(0, 10)
        setStartDate(localDate)
        setEndDate(localDate)
      } else {
        const error = await response.json()

        // Handle duplicate attendance specifically
        if (
          response.status === 409 ||
          error.message?.includes('already recorded') ||
          error.message?.includes('Attendance already recorded') ||
          error.message?.includes('sudah tercatat')
        ) {
          // Play alert sound for duplicate entries
          playAlertSound()

          setDuplicateData({
            student: selectedStudent,
            attendanceType: attendanceType as AttendanceType,
            recordedAt,
            reason: reason.trim() || undefined,
          })
          setShowDuplicateDialog(true)
        } else {
          // Play error sound for other errors
          playErrorSound()

          toast({
            title: 'Error',
            description: error.message || 'Terjadi kesalahan saat mencatat absensi.',
            variant: 'destructive',
          })
        }
      }
    } catch (error) {
      console.error('Error recording manual attendance:', error)

      // Play error sound for network/unexpected errors
      playErrorSound()

      toast({
        title: 'Error',
        description: 'Terjadi kesalahan tak terduga.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateDuplicate = async () => {
    if (!duplicateData) return

    setIsLoading(true)
    setShowDuplicateDialog(false)

    try {
      const response = await fetch('/api/absence/manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uniqueCode: duplicateData.student.uniqueCode,
          type: duplicateData.attendanceType,
          recordedAt: new Date(duplicateData.recordedAt).toISOString(),
          reason: duplicateData.reason,
          force: true, // Force update existing record
        }),
      })

      if (response.ok) {
        // Play success sound for successful duplicate update
        playSuccessSound()

        toast({
          title: 'Berhasil',
          description: `Absensi ${getAttendanceTypeLabel(duplicateData.attendanceType)} untuk ${duplicateData.student.name} berhasil diperbarui.`,
        })

        // Reset form
        setSelectedStudent(null)
        setSearchQuery('')
        setAttendanceType('')
        setReason('')
        const now = new Date()
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
          .toISOString()
          .slice(0, 16)
        setRecordedAt(localDateTime)
        setDuplicateData(null)
      } else {
        const error = await response.json()

        // Play error sound for update failures
        playErrorSound()

        toast({
          title: 'Error',
          description: error.message || 'Terjadi kesalahan saat memperbarui absensi.',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error updating duplicate attendance:', error)

      // Play error sound for network/unexpected errors
      playErrorSound()

      toast({
        title: 'Error',
        description: 'Terjadi kesalahan tak terduga.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelDuplicate = () => {
    setShowDuplicateDialog(false)
    setDuplicateData(null)
  }

  const requiresReason =
    attendanceType &&
    [AttendanceType.TEMPORARY_LEAVE, AttendanceType.EXCUSED_ABSENCE, AttendanceType.SICK].includes(
      attendanceType as AttendanceType
    )

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Entry Manual Absensi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Student Search */}
            <div className="space-y-2">
              <Label htmlFor="student-search">Cari Siswa</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="student-search"
                  type="text"
                  placeholder="Masukkan nama atau NIS siswa..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
                {isSearching && (
                  <div className="absolute right-3 top-3">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                  </div>
                )}
              </div>

              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="max-h-40 overflow-y-auto rounded-md border bg-white shadow-sm">
                  {searchResults.map(student => (
                    <button
                      key={student.uniqueCode}
                      type="button"
                      onClick={() => handleStudentSelect(student)}
                      className="w-full border-b px-3 py-2 text-left last:border-b-0 hover:bg-gray-50"
                    >
                      <div className="font-medium">{student.name}</div>
                      <div className="text-sm text-gray-500">
                        {student.className} • NIS: {student.nis || 'N/A'}
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {/* Selected Student */}
              {selectedStudent && (
                <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
                  <div className="font-medium text-blue-900">{selectedStudent.name}</div>
                  <div className="text-sm text-blue-700">
                    {selectedStudent.className} • NIS: {selectedStudent.nis || 'N/A'}
                  </div>
                </div>
              )}
            </div>

            {/* Attendance Type */}
            <div className="space-y-2">
              <Label htmlFor="attendance-type">Jenis Absensi</Label>
              <Select
                value={attendanceType}
                onValueChange={value => setAttendanceType(value as AttendanceType | '')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih jenis absensi" />
                </SelectTrigger>
                <SelectContent>
                  {allowedAttendanceTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {getAttendanceTypeLabel(type)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Reason (required for certain types) */}
            {requiresReason && (
              <div className="space-y-2">
                <Label htmlFor="reason">
                  Alasan <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="reason"
                  placeholder="Masukkan alasan..."
                  value={reason}
                  onChange={e => setReason(e.target.value)}
                  rows={3}
                />
              </div>
            )}

            {/* Date/Time Selection */}
            {supportsDateRange ? (
              // Date Range Mode for Sick and Excused Absence
              <div className="space-y-4">
                <div className="rounded bg-blue-50 p-3 text-sm text-blue-700">
                  <p className="font-medium">Mode Rentang Tanggal</p>
                  <p>Pilih tanggal yang sama untuk 1 hari, atau rentang untuk beberapa hari</p>
                  <p className="mt-1 text-xs text-blue-600">
                    💡 Fleksibel: Tanggal masa depan tanpa batasan, weekend (Sabtu & Minggu)
                    otomatis dilewati
                  </p>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="start-date" className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Tanggal Mulai <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={startDate}
                      onChange={e => {
                        setStartDate(e.target.value)
                        // Auto-adjust end date if it's before start date
                        if (endDate && e.target.value > endDate) {
                          setEndDate(e.target.value)
                        }
                      }}
                      // Allow future dates for manual entries (no restriction for administrative planning)
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-date" className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Tanggal Akhir <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={endDate}
                      onChange={e => setEndDate(e.target.value)}
                      min={startDate} // Ensure end date is not before start date
                      // Allow future dates for manual entries (no restriction for administrative planning)
                    />
                  </div>
                </div>
                {startDate && endDate && (
                  <div className="rounded bg-green-50 p-2 text-sm text-green-700">
                    <span className="font-medium">
                      {(() => {
                        // Calculate working days (excluding weekends)
                        const start = new Date(startDate)
                        const end = new Date(endDate)
                        let workingDays = 0
                        const current = new Date(start)

                        while (current <= end) {
                          // Skip weekends (Saturday = 6, Sunday = 0)
                          const dayOfWeek = current.getDay()
                          if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                            workingDays++
                          }
                          current.setDate(current.getDate() + 1)
                        }

                        return workingDays
                      })()}{' '}
                      hari kerja
                    </span>{' '}
                    akan dicatat (tidak termasuk Sabtu & Minggu)
                  </div>
                )}
              </div>
            ) : (
              // Single Date Mode for other attendance types
              <div className="space-y-2">
                <Label htmlFor="recorded-at" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Tanggal & Waktu <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="recorded-at"
                  type="datetime-local"
                  value={recordedAt}
                  onChange={e => setRecordedAt(e.target.value)}
                />
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isLoading || !selectedStudent || !attendanceType}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Menyimpan...
                </>
              ) : supportsDateRange ? (
                `Catat Absensi (${
                  startDate && endDate
                    ? (() => {
                        // Calculate working days (excluding weekends) for button text
                        const start = new Date(startDate)
                        const end = new Date(endDate)
                        let workingDays = 0
                        const current = new Date(start)

                        while (current <= end) {
                          // Skip weekends (Saturday = 6, Sunday = 0)
                          const dayOfWeek = current.getDay()
                          if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                            workingDays++
                          }
                          current.setDate(current.getDate() + 1)
                        }

                        return workingDays
                      })()
                    : 0
                } hari kerja)`
              ) : (
                'Catat Absensi'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Duplicate Attendance Dialog */}
      <Dialog open={showDuplicateDialog} onOpenChange={setShowDuplicateDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Absensi Sudah Ada
            </DialogTitle>
            <DialogDescription>
              Siswa <strong>{duplicateData?.student.name}</strong> sudah memiliki catatan absensi
              untuk hari ini.
            </DialogDescription>
          </DialogHeader>

          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Apakah Anda ingin memperbarui catatan absensi yang sudah ada dengan data baru?
            </AlertDescription>
          </Alert>

          {duplicateData && (
            <div className="space-y-2 rounded-md bg-gray-50 p-3">
              <div className="text-sm">
                <strong>Data yang akan diperbarui:</strong>
              </div>
              <div className="text-sm">
                <span className="font-medium">Siswa:</span> {duplicateData.student.name} (
                {duplicateData.student.className})
              </div>
              <div className="text-sm">
                <span className="font-medium">Jenis:</span>{' '}
                {getAttendanceTypeLabel(duplicateData.attendanceType)}
              </div>
              <div className="text-sm">
                <span className="font-medium">Waktu:</span>{' '}
                {new Date(duplicateData.recordedAt).toLocaleString(TIMEZONE_CONFIG.LOCALE, {
                  timeZone: TIMEZONE_CONFIG.TIMEZONE,
                })}
              </div>
              {duplicateData.reason && (
                <div className="text-sm">
                  <span className="font-medium">Alasan:</span> {duplicateData.reason}
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDuplicate} disabled={isLoading}>
              Batal
            </Button>
            <Button onClick={handleUpdateDuplicate} disabled={isLoading}>
              {isLoading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Memperbarui...
                </>
              ) : (
                'Perbarui Absensi'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
