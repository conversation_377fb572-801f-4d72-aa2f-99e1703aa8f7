'use client'

import React from 'react'
import {
  BulkImportDialog,
  BulkImportConfig,
  ValidationError,
} from '@/components/shared/BulkImportDialog'
import { GraduationCap } from 'lucide-react'

interface BulkStudentImportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete: () => void
}

// Student validation function
const validateStudentRow = (row: any, rowIndex: number): ValidationError[] => {
  const errors: ValidationError[] = []

  // Validate name
  if (!row.name || row.name.trim().length === 0) {
    errors.push({
      row: rowIndex,
      field: 'name',
      message: '<PERSON>a wajib diisi',
      value: row.name,
    })
  } else if (row.name.length > 250) {
    errors.push({
      row: rowIndex,
      field: 'name',
      message: 'Nama maksimal 250 karakter',
      value: row.name,
    })
  }

  // Validate username
  if (!row.username || row.username.trim().length === 0) {
    errors.push({
      row: rowIndex,
      field: 'username',
      message: 'Username wajib diisi',
      value: row.username,
    })
  } else if (row.username.length < 3 || row.username.length > 50) {
    errors.push({
      row: rowIndex,
      field: 'username',
      message: 'Username harus 3-50 karakter',
      value: row.username,
    })
  } else if (!/^[a-zA-Z0-9_]+$/.test(row.username)) {
    errors.push({
      row: rowIndex,
      field: 'username',
      message: 'Username hanya boleh berisi huruf, angka, dan underscore',
      value: row.username,
    })
  }

  // Validate password
  if (!row.password || row.password.length < 6) {
    errors.push({
      row: rowIndex,
      field: 'password',
      message: 'Password minimal 6 karakter',
      value: row.password ? '***' : '',
    })
  }

  // Validate className
  if (!row.className || row.className.trim().length === 0) {
    errors.push({
      row: rowIndex,
      field: 'className',
      message: 'Nama kelas wajib diisi',
      value: row.className,
    })
  }

  // Validate gender
  if (!row.gender || row.gender.trim().length === 0) {
    errors.push({
      row: rowIndex,
      field: 'gender',
      message: 'Gender wajib diisi',
      value: row.gender,
    })
  } else {
    const normalizedGender = row.gender.toLowerCase()
    const validGenders = ['male', 'female', 'laki-laki', 'perempuan']
    if (!validGenders.includes(normalizedGender)) {
      errors.push({
        row: rowIndex,
        field: 'gender',
        message: 'Gender harus "male", "female", "Laki-laki", atau "Perempuan"',
        value: row.gender,
      })
    }
  }

  // Validate email if provided
  if (row.googleEmail && row.googleEmail.trim() !== '') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(row.googleEmail)) {
      errors.push({
        row: rowIndex,
        field: 'googleEmail',
        message: 'Format email tidak valid',
        value: row.googleEmail,
      })
    }
  }

  return errors
}

// Student import configuration
const studentImportConfig: BulkImportConfig = {
  title: 'Bulk Import Siswa',
  description: 'Import multiple siswa sekaligus menggunakan file CSV atau Excel',
  icon: <GraduationCap className="h-5 w-5" />,
  apiEndpoint: '/api/users/bulk-upload',
  fileFormats: ['CSV', 'Excel'],
  acceptedExtensions: '.csv,.xlsx,.xls',
  maxFileSize: 10, // 10MB
  maxRecords: 1000,
  sampleFileName: 'sample_student_import.csv',
  sampleData: [
    {
      name: 'Ahmad Rizki',
      username: 'test4',
      password: 'password123',
      className: 'XII RPL 1',
      nis: '12345678',
      googleEmail: '<EMAIL>',
      whatsapp: '081234567890',
      gender: 'male',
    },
    {
      name: 'Siti Nurhaliza',
      username: 'test5',
      password: 'securepass456',
      className: 'XII RPL 2',
      nis: '12345679',
      googleEmail: '<EMAIL>',
      whatsapp: '081234567891',
      gender: 'female',
    },
    {
      name: 'Budi Santoso',
      username: 'test6',
      password: 'password789',
      className: 'XI TKJ 1',
      nis: '12345680',
      googleEmail: '<EMAIL>',
      whatsapp: '081234567892',
      gender: 'Laki-laki',
    },
    {
      name: 'Dewi Sartika',
      username: 'test7',
      password: 'password321',
      className: 'XI MM 1',
      nis: '12345681',
      googleEmail: '<EMAIL>',
      whatsapp: '081234567893',
      gender: 'Perempuan',
    },
  ],
  requiredFields: [
    {
      field: 'name',
      label: 'Nama',
      description: 'Nama lengkap siswa (maksimal 250 karakter)',
    },
    {
      field: 'username',
      label: 'Username',
      description: 'Username unik (3-50 karakter, huruf, angka, underscore)',
    },
    {
      field: 'password',
      label: 'Password',
      description: 'Password (minimal 6 karakter)',
    },
    {
      field: 'className',
      label: 'Nama Kelas',
      description: 'Nama kelas yang sudah ada di sistem',
    },
    {
      field: 'nis',
      label: 'NIS',
      description: 'Nomor Induk Siswa (opsional)',
    },
    {
      field: 'googleEmail',
      label: 'Email Google',
      description: 'Email Google untuk login (opsional)',
    },
    {
      field: 'whatsapp',
      label: 'WhatsApp',
      description: 'Nomor WhatsApp (opsional)',
    },
    {
      field: 'gender',
      label: 'Gender',
      description: 'Jenis kelamin: male, female, Laki-laki, atau Perempuan',
    },
  ],
  validateRow: validateStudentRow,
}

export function BulkStudentImportDialog({
  open,
  onOpenChange,
  onImportComplete,
}: BulkStudentImportDialogProps) {
  return (
    <BulkImportDialog
      open={open}
      onOpenChange={onOpenChange}
      onImportComplete={onImportComplete}
      config={studentImportConfig}
    />
  )
}
