'use client'

import { useState, useEffect, useMemo } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminAuth } from '@/hooks/use-hybrid-auth'
import { RefreshCw, Users, UserCheck, Clock, AlertTriangle, Search, Calendar } from 'lucide-react'
import { SchoolReportExportButton } from '@/components/reports/ReportExportButton'
import {
  AttendanceTimeTooltip,
  shouldShowTimeTooltips,
} from '@/components/reports/AttendanceTimeTooltip'
import {
  <PERSON><PERSON><PERSON>,
  Pa<PERSON>ation<PERSON>ontent,
  Pa<PERSON>ationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'

// Import chart components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'
import { TIMEZONE_CONFIG } from '@/lib/config'
import { ChartDataProcessor } from '@/lib/utils/chart-data-processor'

// School Report Interface (Real Data Structure)
interface SchoolReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  entry: boolean
  entryTime: string | null
  lateEntry: boolean
  lateEntryTime: string | null
  excusedAbsence: boolean
  excusedAbsenceTime?: string | null
  temporaryLeave: boolean
  temporaryLeaveTime?: string | null
  returnFromLeave: boolean
  returnFromLeaveTime?: string | null
  sick: boolean
  sickTime?: string | null
}

// School Statistics Interface
interface SchoolStats {
  total: number
  entry: number
  lateEntry: number
  excusedAbsence: number
  temporaryLeave: number
  returnFromLeave: number
  sick: number
  entryPercentage: number
  lateEntryPercentage: number
  excusedAbsencePercentage: number
  temporaryLeavePercentage: number
  returnFromLeavePercentage: number
  sickPercentage: number
}

// Trend Data for Charts - Updated to show separate lines
interface SchoolTrendData {
  date: string
  entry: number
  lateEntry: number
  excusedAbsence: number
  sick: number
  temporaryLeave: number
  returnFromLeave: number
}

export default function SchoolReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user: admin, isLoading: sessionLoading } = useAdminAuth()

  // State management for real data
  const [reports, setReports] = useState<SchoolReport[]>([])
  const [stats, setStats] = useState<SchoolStats>({
    total: 0,
    entry: 0,
    lateEntry: 0,
    excusedAbsence: 0,
    temporaryLeave: 0,
    returnFromLeave: 0,
    sick: 0,
    entryPercentage: 0,
    lateEntryPercentage: 0,
    excusedAbsencePercentage: 0,
    temporaryLeavePercentage: 0,
    returnFromLeavePercentage: 0,
    sickPercentage: 0,
  })
  const [trendData, setTrendData] = useState<SchoolTrendData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [searchQuery, setSearchQuery] = useState('')
  const [availableClasses, setAvailableClasses] = useState<Array<{ value: string; label: string }>>(
    []
  )
  const [isLoadingClasses, setIsLoadingClasses] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1) // Current month (1-12)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()) // Current year

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50)

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'ascending' | 'descending'
  } | null>(null) // 50 students per page for 3000 students

  // Fetch available classes
  const fetchClasses = async () => {
    try {
      setIsLoadingClasses(true)
      const response = await fetch('/api/classes')
      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }
      const classes = await response.json()

      // Transform classes to dropdown format
      const classOptions = classes.map((cls: any) => ({
        value: cls.name.toLowerCase().replace(/\s+/g, '-'),
        label: cls.name,
      }))

      setAvailableClasses(classOptions)
    } catch (error) {
      toast({
        title: 'Gagal memuat daftar kelas',
        description: 'Menggunakan daftar kelas default',
        variant: 'destructive',
      })
    } finally {
      setIsLoadingClasses(false)
    }
  }

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      // Super Admin, Teacher, and Receptionist can access school reports
      if (
        admin.role !== 'super_admin' &&
        admin.role !== 'teacher' &&
        admin.role !== 'receptionist'
      ) {
        router.push('/admin/home')
        return
      }
      // Fetch classes when admin is authenticated
      fetchClasses()
    }
  }, [admin, sessionLoading, router])

  // Helper function to safely convert various types to boolean
  const toBooleanSafe = (value: any): boolean => {
    if (typeof value === 'boolean') return value
    if (typeof value === 'number') return value === 1
    if (typeof value === 'string') return value === 'true' || value === '1'
    return false
  }

  // Function to generate real trend data from actual attendance times using fixed time slots
  const generateRealHourlyTrendData = (reports: SchoolReport[]): SchoolTrendData[] => {
    // Fixed time slots for consistent chart display (school hours) - 06:00 WITA to 18:00 WITA
    const hours = [
      '06:00',
      '07:00',
      '08:00',
      '09:00',
      '10:00',
      '11:00',
      '12:00',
      '13:00',
      '14:00',
      '15:00',
      '16:00',
      '17:00',
      '18:00',
    ]

    // Initialize hourly counters - always return this structure for consistent chart display
    const hourlyData = hours.map(hour => ({
      date: hour,
      entry: 0,
      lateEntry: 0,
      excusedAbsence: 0,
      sick: 0,
      temporaryLeave: 0,
      returnFromLeave: 0,
    }))

    // If no reports, return initialized hourly data with zeros (chart will show flat line at 0)
    if (!reports || reports.length === 0) {
      return hourlyData
    }

    // Track real vs fallback data usage
    let realTimestampCount = 0
    let fallbackCount = 0

    // Helper function to extract hour from time string and map to nearest hour slot
    const extractAndMapHour = (timeStr: string | null | undefined): string | null => {
      if (!timeStr || timeStr === '-' || timeStr === 'null' || timeStr === null) {
        return null
      }

      // Skip count format (like "3x") - this indicates aggregated data, not real timestamps
      if (typeof timeStr === 'string' && timeStr.includes('x')) {
        return null
      }

      try {
        let hourMinute: string

        // Handle different time formats
        if (timeStr.includes('T')) {
          // ISO format: "2024-01-15T13:30:00.000Z"
          const date = new Date(timeStr)
          if (isNaN(date.getTime())) {
            return null
          }
          hourMinute = date.toLocaleTimeString(TIMEZONE_CONFIG.LOCALE, {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: TIMEZONE_CONFIG.TIMEZONE,
            hour12: false,
          })
          realTimestampCount++
        } else if (timeStr.match(/^\d{1,2}:\d{2}(:\d{2})?$/)) {
          // Already in HH:MM or HH:MM:SS format
          hourMinute = timeStr.substring(0, 5) // Get HH:MM part
          realTimestampCount++
        } else if (timeStr.match(/^\d{1,2}\.\d{2}\.\d{2}$/)) {
          // Dot format: "14.15.49" -> convert to "14:15"
          const parts = timeStr.split('.')
          hourMinute = `${parts[0].padStart(2, '0')}:${parts[1]}`
          realTimestampCount++
        } else {
          return null
        }

        // Map to the nearest hour slot in our predefined hours
        const [hour, minute] = hourMinute.split(':').map(Number)
        if (isNaN(hour) || isNaN(minute)) {
          return null
        }

        const timeInMinutes = hour * 60 + minute

        // Find the closest hour slot
        let closestHour = '06:00'
        let minDiff = Infinity

        hours.forEach(hourSlot => {
          const [slotHour] = hourSlot.split(':').map(Number)
          const slotTimeInMinutes = slotHour * 60
          const diff = Math.abs(timeInMinutes - slotTimeInMinutes)

          if (diff < minDiff) {
            minDiff = diff
            closestHour = hourSlot
          }
        })

        return closestHour
      } catch (error) {
        return null
      }
    }

    // Process each student's attendance records and map to hour slots
    reports.forEach(report => {
      // Extract hours from attendance times
      const entryHour = extractAndMapHour(report.entryTime)
      const lateEntryHour = extractAndMapHour(report.lateEntryTime)
      const excusedAbsenceHour = extractAndMapHour(report.excusedAbsenceTime)
      const sickHour = extractAndMapHour(report.sickTime)
      const temporaryLeaveHour = extractAndMapHour(report.temporaryLeaveTime)
      const returnFromLeaveHour = extractAndMapHour(report.returnFromLeaveTime)

      // Map to hour slots and increment counters - only use fallback when no real timestamp exists
      if (toBooleanSafe(report.entry)) {
        if (entryHour) {
          // Use real timestamp data
          const hourSlot = hourlyData.find(h => h.date === entryHour)
          if (hourSlot) {
            hourSlot.entry++
          }
        } else {
          // Only use fallback if absolutely no timestamp data exists
          fallbackCount++
          const targetHour = '07:00' // Default to 7 AM if no time data
          const hourSlot = hourlyData.find(h => h.date === targetHour)
          if (hourSlot) {
            hourSlot.entry++
          }
        }
      }

      if (toBooleanSafe(report.lateEntry)) {
        if (lateEntryHour) {
          const hourSlot = hourlyData.find(h => h.date === lateEntryHour)
          if (hourSlot) {
            hourSlot.lateEntry++
          }
        } else {
          fallbackCount++
          const targetHour = '08:00'
          const hourSlot = hourlyData.find(h => h.date === targetHour)
          if (hourSlot) {
            hourSlot.lateEntry++
          }
        }
      }

      if (toBooleanSafe(report.excusedAbsence)) {
        if (excusedAbsenceHour) {
          const hourSlot = hourlyData.find(h => h.date === excusedAbsenceHour)
          if (hourSlot) {
            hourSlot.excusedAbsence++
          }
        } else {
          fallbackCount++
          const targetHour = '09:00'
          const hourSlot = hourlyData.find(h => h.date === targetHour)
          if (hourSlot) {
            hourSlot.excusedAbsence++
          }
        }
      }

      if (toBooleanSafe(report.sick)) {
        if (sickHour) {
          const hourSlot = hourlyData.find(h => h.date === sickHour)
          if (hourSlot) {
            hourSlot.sick++
          }
        } else {
          fallbackCount++
          const targetHour = '09:00'
          const hourSlot = hourlyData.find(h => h.date === targetHour)
          if (hourSlot) {
            hourSlot.sick++
          }
        }
      }

      if (toBooleanSafe(report.temporaryLeave)) {
        if (temporaryLeaveHour) {
          const hourSlot = hourlyData.find(h => h.date === temporaryLeaveHour)
          if (hourSlot) {
            hourSlot.temporaryLeave++
          }
        } else {
          fallbackCount++
          const targetHour = '11:00'
          const hourSlot = hourlyData.find(h => h.date === targetHour)
          if (hourSlot) {
            hourSlot.temporaryLeave++
          }
        }
      }

      if (toBooleanSafe(report.returnFromLeave)) {
        if (returnFromLeaveHour) {
          const hourSlot = hourlyData.find(h => h.date === returnFromLeaveHour)
          if (hourSlot) {
            hourSlot.returnFromLeave++
          }
        } else {
          fallbackCount++
          const targetHour = '12:00'
          const hourSlot = hourlyData.find(h => h.date === targetHour)
          if (hourSlot) {
            hourSlot.returnFromLeave++
          }
        }
      }
    })

    return hourlyData
  }

  // Unused function removed for clean code

  // ✅ FALLBACK: Function to generate monthly trend data (for backward compatibility)
  const generateMonthlyTrendData = (reports: SchoolReport[]): SchoolTrendData[] => {
    // Generate weeks in the selected month
    const weeks = ['Minggu 1', 'Minggu 2', 'Minggu 3', 'Minggu 4', 'Minggu 5']

    // Initialize weekly counters
    const weeklyData = weeks.map(week => ({
      date: week,
      entry: 0,
      lateEntry: 0,
      excusedAbsence: 0,
      sick: 0,
      temporaryLeave: 0,
      returnFromLeave: 0,
    }))

    // If no reports, return initialized weekly data with zeros
    if (!reports || reports.length === 0) {
      return weeklyData
    }

    // Process aggregated data if available
    const isAggregated = reports.length > 0 && (reports[0] as any).aggregatedCounts

    if (isAggregated) {
      // For aggregated data, distribute across weeks
      const totalWeeks = weeks.length
      reports.forEach((report, index) => {
        const weekIndex = Math.floor((index / reports.length) * totalWeeks)
        const counts = (report as any).aggregatedCounts

        if (weekIndex < weeklyData.length) {
          weeklyData[weekIndex].entry += counts.entry || 0
          weeklyData[weekIndex].lateEntry += counts.lateEntry || 0
          weeklyData[weekIndex].excusedAbsence += counts.excusedAbsence || 0
          weeklyData[weekIndex].sick += counts.sick || 0
          weeklyData[weekIndex].temporaryLeave += counts.temporaryLeave || 0
          weeklyData[weekIndex].returnFromLeave += counts.returnFromLeave || 0
        }
      })
    } else {
      // For daily data, group by weeks
      const reportsPerWeek = Math.ceil(reports.length / weeks.length)
      weeks.forEach((_, weekIndex) => {
        const startIndex = weekIndex * reportsPerWeek
        const endIndex = Math.min(startIndex + reportsPerWeek, reports.length)
        const weekReports = reports.slice(startIndex, endIndex)

        weeklyData[weekIndex].entry = weekReports.filter(r => toBooleanSafe(r.entry)).length
        weeklyData[weekIndex].lateEntry = weekReports.filter(r => toBooleanSafe(r.lateEntry)).length
        weeklyData[weekIndex].excusedAbsence = weekReports.filter(r =>
          toBooleanSafe(r.excusedAbsence)
        ).length
        weeklyData[weekIndex].sick = weekReports.filter(r => toBooleanSafe(r.sick)).length
        weeklyData[weekIndex].temporaryLeave = weekReports.filter(r =>
          toBooleanSafe(r.temporaryLeave)
        ).length
        weeklyData[weekIndex].returnFromLeave = weekReports.filter(r =>
          toBooleanSafe(r.returnFromLeave)
        ).length
      })
    }

    return weeklyData
  }

  // ✅ FIXED: Function to generate yearly trend data using actual attendance dates
  const generateYearlyTrendDataFromRealDates = async (
    selectedYear: number,
    classFilter: string = 'all'
  ): Promise<SchoolTrendData[]> => {
    // Initialize month counters for the year
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Ags',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ]

    const monthlyData = months.map(month => ({
      date: month,
      entry: 0,
      lateEntry: 0,
      excusedAbsence: 0,
      sick: 0,
      temporaryLeave: 0,
      returnFromLeave: 0,
    }))

    try {
      // Get detailed attendance records for the entire year with actual timestamps
      const startDate = new Date(selectedYear, 0, 1) // January 1st
      const endDate = new Date(selectedYear, 11, 31) // December 31st

      console.log(`📅 School: Fetching attendance records for year ${selectedYear}:`, {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })

      // Fetch attendance records via API
      let apiUrl = `/api/absence/reports/range?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}&reportType=school&_t=${Date.now()}`

      // Add class filter if not 'all'
      if (classFilter !== 'all') {
        const originalClassName = availableClasses.find(cls => cls.value === classFilter)?.label
        if (originalClassName) {
          apiUrl += `&class=${encodeURIComponent(originalClassName)}`
        }
      }

      const response = await fetch(apiUrl)

      if (!response.ok) {
        console.error('Failed to fetch attendance records for school yearly chart')
        return monthlyData
      }

      const attendanceRecords = await response.json()
      console.log(
        `📊 School yearly chart: Processing ${attendanceRecords.length} attendance records`
      )

      // Process each attendance record and map to correct month
      attendanceRecords.forEach((record: any) => {
        // Get the date from summaryDate or updatedAt
        const recordDate = new Date(record.summaryDate || record.updatedAt)

        if (isNaN(recordDate.getTime())) {
          console.warn('Invalid date in school attendance record:', record)
          return
        }

        // Get the month index (0-11)
        const monthIndex = recordDate.getMonth()

        // Count attendance types for this record
        if (record.entry) monthlyData[monthIndex].entry++
        if (record.lateEntry) monthlyData[monthIndex].lateEntry++
        if (record.excusedAbsence) monthlyData[monthIndex].excusedAbsence++
        if (record.sick) monthlyData[monthIndex].sick++
        if (record.temporaryLeave) monthlyData[monthIndex].temporaryLeave++
        if (record.returnFromLeave) monthlyData[monthIndex].returnFromLeave++
      })

      console.log('📊 School yearly chart: Generated from real dates', monthlyData)
      return monthlyData
    } catch (error) {
      console.error('Error generating school yearly trend data from real dates:', error)
      return monthlyData
    }
  }

  // ✅ FALLBACK: Function to generate yearly trend data (for backward compatibility)
  const generateYearlyTrendData = (reports: SchoolReport[]): SchoolTrendData[] => {
    // Generate months in the selected year
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Agu',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ]

    // Initialize monthly counters
    const monthlyData = months.map(month => ({
      date: month,
      entry: 0,
      lateEntry: 0,
      excusedAbsence: 0,
      sick: 0,
      temporaryLeave: 0,
      returnFromLeave: 0,
    }))

    // If no reports, return initialized monthly data with zeros
    if (!reports || reports.length === 0) {
      return monthlyData
    }

    // Process aggregated data if available
    const isAggregated = reports.length > 0 && (reports[0] as any).aggregatedCounts

    if (isAggregated) {
      // For aggregated data, distribute across months
      const totalMonths = months.length
      reports.forEach((report, index) => {
        const monthIndex = Math.floor((index / reports.length) * totalMonths)
        const counts = (report as any).aggregatedCounts

        if (monthIndex < monthlyData.length) {
          monthlyData[monthIndex].entry += counts.entry || 0
          monthlyData[monthIndex].lateEntry += counts.lateEntry || 0
          monthlyData[monthIndex].excusedAbsence += counts.excusedAbsence || 0
          monthlyData[monthIndex].sick += counts.sick || 0
          monthlyData[monthIndex].temporaryLeave += counts.temporaryLeave || 0
          monthlyData[monthIndex].returnFromLeave += counts.returnFromLeave || 0
        }
      })
    } else {
      // For daily data, group by months
      const reportsPerMonth = Math.ceil(reports.length / months.length)
      months.forEach((_, monthIndex) => {
        const startIndex = monthIndex * reportsPerMonth
        const endIndex = Math.min(startIndex + reportsPerMonth, reports.length)
        const monthReports = reports.slice(startIndex, endIndex)

        monthlyData[monthIndex].entry = monthReports.filter(r => toBooleanSafe(r.entry)).length
        monthlyData[monthIndex].lateEntry = monthReports.filter(r =>
          toBooleanSafe(r.lateEntry)
        ).length
        monthlyData[monthIndex].excusedAbsence = monthReports.filter(r =>
          toBooleanSafe(r.excusedAbsence)
        ).length
        monthlyData[monthIndex].sick = monthReports.filter(r => toBooleanSafe(r.sick)).length
        monthlyData[monthIndex].temporaryLeave = monthReports.filter(r =>
          toBooleanSafe(r.temporaryLeave)
        ).length
        monthlyData[monthIndex].returnFromLeave = monthReports.filter(r =>
          toBooleanSafe(r.returnFromLeave)
        ).length
      })
    }

    return monthlyData
  }

  // Fetch school reports data with improved caching and real-time updates
  const fetchReports = async (forceRefresh = false) => {
    try {
      setIsLoading(true)

      // Reset stats to initial state to prevent showing stale data
      setStats({
        total: 0,
        entry: 0,
        lateEntry: 0,
        excusedAbsence: 0,
        temporaryLeave: 0,
        returnFromLeave: 0,
        sick: 0,
        entryPercentage: 0,
        lateEntryPercentage: 0,
        excusedAbsencePercentage: 0,
        temporaryLeavePercentage: 0,
        returnFromLeavePercentage: 0,
        sickPercentage: 0,
      })
      setReports([])

      const queryParams = new URLSearchParams()

      // ✅ UNIFIED CACHE STRATEGY: Handle different report types consistently
      if (date === 'monthly') {
        queryParams.append('date', 'monthly')
        queryParams.append('month', selectedMonth.toString())
        queryParams.append('year', selectedYear.toString())
      } else if (date === 'yearly') {
        queryParams.append('date', 'yearly')
        queryParams.append('year', selectedYear.toString())
      } else {
        queryParams.append('date', date)
      }

      queryParams.append('reportType', 'school') // Only school data
      if (classFilter !== 'all') {
        // ✅ CONSISTENCY: Use same class name handling as prayer reports
        const originalClassName = availableClasses.find(cls => cls.value === classFilter)?.label
        if (originalClassName) {
          queryParams.append('class', originalClassName)
        }
      }

      // ✅ SMART CACHE: Only force fresh data when explicitly requested (manual refresh)
      // Let write-through cache strategy handle real-time updates automatically
      const isRealTimeReport = ['today', 'yesterday'].includes(date)

      // Use the forceRefresh parameter to determine if this is a manual refresh
      const isManualRefresh = forceRefresh

      if (isManualRefresh) {
        // Manual refresh: Force fresh data and bypass cache
        queryParams.append('force_fresh', 'true')
        queryParams.append('_t', Date.now().toString())
      } else if (isRealTimeReport) {
        // Real-time reports: Use cache with 2-minute browser cache busting
        queryParams.append('_t', Math.floor(Date.now() / 120000).toString()) // 2-minute intervals
      } else {
        // Historical reports: Use cache with 10-minute browser cache busting
        queryParams.append('_t', Math.floor(Date.now() / 600000).toString()) // 10-minute intervals
      }

      // Production: Remove debug logging
      const apiUrl = `/api/absence/reports?${queryParams.toString()}`

      const response = await fetch(apiUrl, {
        headers: isManualRefresh
          ? {
              // Manual refresh: No browser cache
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
              Expires: '0',
            }
          : isRealTimeReport
            ? {
                // Real-time: Short browser cache, let server cache handle freshness
                'Cache-Control': 'max-age=60', // 1-minute browser cache
              }
            : {
                // Historical: Longer browser cache
                'Cache-Control': 'max-age=300', // 5-minute browser cache
              },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch school reports: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      // Validate data structure
      if (!Array.isArray(data)) {
        throw new Error('Invalid data structure received from API')
      }

      // ✅ FIXED: Implement fallback mechanism to show all students when no attendance data
      if (data.length === 0 && classFilter !== 'all') {
        try {
          console.log(`📊 No attendance data found for class filter: ${classFilter}`)
          console.log(`📊 Attempting to fetch all students for class to show empty records`)

          const originalClassName = availableClasses.find(cls => cls.value === classFilter)?.label
          if (originalClassName) {
            // Fetch all students in this class with cache busting for real-time data
            const studentsResponse = await fetch(
              `/api/users?role=student&class=${encodeURIComponent(originalClassName)}&_t=${Date.now()}`,
              {
                headers: {
                  // Use same cache strategy as reports for consistency
                  'Cache-Control': 'max-age=60', // 1-minute browser cache for real-time updates
                },
              }
            )

            if (studentsResponse.ok) {
              const studentsData = await studentsResponse.json()
              console.log(`📊 Found ${studentsData.length} students in class ${originalClassName}`)

              // Create empty attendance records for all students in the class
              const emptyReports = studentsData.map((student: any) => ({
                uniqueCode: student.uniqueCode,
                name: student.name,
                className: student.className || originalClassName,
                summaryDate: new Date().toISOString().split('T')[0],
                // School attendance fields - all false/null for empty data
                entry: false,
                entryTime: null,
                lateEntry: false,
                lateEntryTime: null,
                excusedAbsence: false,
                excusedAbsenceTime: null,
                excusedAbsenceReason: null,
                sick: false,
                sickTime: null,
                sickReason: null,
                temporaryLeave: false,
                temporaryLeaveTime: null,
                temporaryLeaveReason: null,
                returnFromLeave: false,
                returnFromLeaveTime: null,
                // For aggregated data (monthly/yearly)
                aggregatedCounts: ['monthly', 'yearly'].includes(date)
                  ? {
                      entry: 0,
                      lateEntry: 0,
                      excusedAbsence: 0,
                      sick: 0,
                      temporaryLeave: 0,
                      returnFromLeave: 0,
                    }
                  : undefined,
              }))

              setReports(emptyReports)
              console.log(
                `📊 Created ${emptyReports.length} empty attendance records for class ${originalClassName}`
              )

              // Show notification that we're showing students with no attendance data
              toast({
                title: 'Menampilkan siswa tanpa data kehadiran',
                description: `Menampilkan ${emptyReports.length} siswa dari kelas ${originalClassName} dengan data kosong`,
                variant: 'default',
              })

              return // Exit early, don't show the "no data" message
            } else {
              console.error('Failed to fetch students for class:', studentsResponse.statusText)
            }
          }
        } catch (error) {
          console.error('Error fetching students for empty attendance fallback:', error)
        }
      }

      console.log(`📥 SCHOOL REPORTS: Received ${data.length} reports for date filter: "${date}"`)
      console.log('📊 SCHOOL REPORTS: Sample data structure:', data.slice(0, 2))

      setReports(data)

      // Show notification for no data (only if fallback didn't work)
      if (data.length === 0) {
        toast({
          title: 'Tidak ada data',
          description: `Tidak ada data laporan sekolah untuk ${date === 'today' ? 'hari ini' : date}`,
          variant: 'default',
        })
      }

      // Calculate school statistics based on data type
      let total = data.length

      // ✅ FIXED: If no data but class filter is applied, get actual student count for that class
      if (total === 0 && classFilter !== 'all') {
        try {
          const originalClassName = availableClasses.find(cls => cls.value === classFilter)?.label
          if (originalClassName) {
            const countResponse = await fetch(
              `/api/students/count?class=${encodeURIComponent(originalClassName)}`
            )
            if (countResponse.ok) {
              const countData = await countResponse.json()
              total = countData.count || 0
              console.log(`📊 Got student count for class ${originalClassName}: ${total}`)
            }
          }
        } catch (error) {
          console.error('Failed to fetch student count for class:', error)
        }
      }
      let entryCount,
        lateEntryCount,
        excusedAbsenceCount,
        temporaryLeaveCount,
        returnFromLeaveCount,
        sickCount

      // Check if this is aggregated data (monthly, yearly)
      const isAggregatedData = ['monthly', 'yearly'].includes(date)

      // ✅ DEBUG: Log data structure for yearly reports
      if (date === 'yearly' && data.length > 0) {
        console.log(`📊 KPI Data for yearly (${data.length} students):`, {
          sampleStudent: data[0],
          hasAggregatedCounts: !!data[0].aggregatedCounts,
          totalEntryFromKPI: data.reduce(
            (sum: number, r: any) => sum + (r.aggregatedCounts?.entry || 0),
            0
          ),
        })
      }

      if (isAggregatedData && data.length > 0 && data[0].aggregatedCounts) {
        // For aggregated data, sum up the counts
        entryCount = data.reduce((sum: number, r: any) => sum + (r.aggregatedCounts?.entry || 0), 0)
        lateEntryCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.lateEntry || 0),
          0
        )
        excusedAbsenceCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.excusedAbsence || 0),
          0
        )
        temporaryLeaveCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.temporaryLeave || 0),
          0
        )
        returnFromLeaveCount = data.reduce(
          (sum: number, r: any) => sum + (r.aggregatedCounts?.returnFromLeave || 0),
          0
        )
        sickCount = data.reduce((sum: number, r: any) => sum + (r.aggregatedCounts?.sick || 0), 0)
      } else {
        // For daily data, count boolean values with more robust checking
        // Fix the counting logic for proper boolean evaluation

        entryCount = data.filter((r: SchoolReport) => {
          // Ensure proper boolean conversion - handle 1/0, true/false, "true"/"false"
          return toBooleanSafe(r.entry)
        }).length

        lateEntryCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.lateEntry)
        }).length

        excusedAbsenceCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.excusedAbsence)
        }).length

        temporaryLeaveCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.temporaryLeave)
        }).length

        returnFromLeaveCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.returnFromLeave)
        }).length

        sickCount = data.filter((r: SchoolReport) => {
          return toBooleanSafe(r.sick)
        }).length

        // Production: Remove debug logging for performance
      }

      // Calculate percentages with proper logic to cap at 100%
      let entryPercentage = 0
      let lateEntryPercentage = 0
      let excusedAbsencePercentage = 0
      let temporaryLeavePercentage = 0
      let returnFromLeavePercentage = 0
      let sickPercentage = 0

      if (isAggregatedData) {
        // For aggregated data, calculate based on average attendance
        // Use total unique students as base (data.length represents unique students)
        const avgAttendanceBase = Math.max(total, 1)
        const totalExpectedAttendance =
          avgAttendanceBase *
          (['yearly'].includes(date) ? 200 : ['monthly'].includes(date) ? 20 : 5) // Estimated school days

        entryPercentage = Math.min(
          100,
          Math.round((entryCount / Math.max(totalExpectedAttendance, entryCount)) * 100)
        )
        lateEntryPercentage = Math.min(
          100,
          Math.round((lateEntryCount / Math.max(totalExpectedAttendance, lateEntryCount)) * 100)
        )
        excusedAbsencePercentage = Math.min(
          100,
          Math.round(
            (excusedAbsenceCount / Math.max(totalExpectedAttendance, excusedAbsenceCount)) * 100
          )
        )
        temporaryLeavePercentage = Math.min(
          100,
          Math.round(
            (temporaryLeaveCount / Math.max(totalExpectedAttendance, temporaryLeaveCount)) * 100
          )
        )
        returnFromLeavePercentage = Math.min(
          100,
          Math.round(
            (returnFromLeaveCount / Math.max(totalExpectedAttendance, returnFromLeaveCount)) * 100
          )
        )
        sickPercentage = Math.min(
          100,
          Math.round((sickCount / Math.max(totalExpectedAttendance, sickCount)) * 100)
        )
      } else {
        // For daily data, calculate normally but cap at 100%
        entryPercentage = total > 0 ? Math.min(100, Math.round((entryCount / total) * 100)) : 0
        lateEntryPercentage =
          total > 0 ? Math.min(100, Math.round((lateEntryCount / total) * 100)) : 0
        excusedAbsencePercentage =
          total > 0 ? Math.min(100, Math.round((excusedAbsenceCount / total) * 100)) : 0
        temporaryLeavePercentage =
          total > 0 ? Math.min(100, Math.round((temporaryLeaveCount / total) * 100)) : 0
        returnFromLeavePercentage =
          total > 0 ? Math.min(100, Math.round((returnFromLeaveCount / total) * 100)) : 0
        sickPercentage = total > 0 ? Math.min(100, Math.round((sickCount / total) * 100)) : 0
      }

      setStats({
        total,
        entry: entryCount,
        lateEntry: lateEntryCount,
        excusedAbsence: excusedAbsenceCount,
        temporaryLeave: temporaryLeaveCount,
        returnFromLeave: returnFromLeaveCount,
        sick: sickCount,
        entryPercentage,
        lateEntryPercentage,
        excusedAbsencePercentage,
        temporaryLeavePercentage,
        returnFromLeavePercentage,
        sickPercentage,
      })

      // Production: Remove debug logging for performance

      // Generate real trend data based on current period with all 6 lines
      let realTrendData: SchoolTrendData[] = []

      // ✅ FIXED: Generate appropriate trend data based on selected filter using real dates
      switch (date) {
        case 'today':
        case 'yesterday':
          // Hourly data for daily reports
          realTrendData = generateRealHourlyTrendData(data)
          break

        case 'monthly':
          // ✅ UNIFIED CACHE STRATEGY: Use dedicated monthly reports API with clean architecture
          console.log(
            `🎯 SCHOOL REPORTS: Using unified monthly reports API: ${selectedMonth}/${selectedYear} for class: ${classFilter}`
          )
          try {
            // ✅ FIXED: Build API URL with class filter parameter
            let apiUrl = `/api/reports/monthly?month=${selectedMonth}&year=${selectedYear}&reportType=school&_t=${Date.now()}`

            // Add class filter if not 'all'
            if (classFilter !== 'all') {
              // ✅ FIXED: Find the original class name from availableClasses instead of transforming
              const originalClassName = availableClasses.find(
                cls => cls.value === classFilter
              )?.label
              if (originalClassName) {
                apiUrl += `&class=${encodeURIComponent(originalClassName)}`
                console.log(`📊 Adding class filter to monthly API: ${originalClassName}`)
              }
            }

            const response = await fetch(apiUrl)
            if (!response.ok) {
              throw new Error('Failed to fetch monthly school report')
            }
            const result = await response.json()

            // ✅ FIXED: Use ChartDataProcessor for consistent data processing
            const chartData = ChartDataProcessor.processMonthlyReportForChart(
              result.data,
              'school'
            ).map(item => ({
              date: item.date,
              entry: item.entry || 0,
              lateEntry: item.lateEntry || 0,
              excusedAbsence: item.excusedAbsence || 0,
              sick: item.sick || 0,
              temporaryLeave: item.temporaryLeave || 0,
              returnFromLeave: item.returnFromLeave || 0,
            }))

            realTrendData = chartData

            console.log(
              `📊 SCHOOL REPORTS: Generated monthly data from unified API:`,
              realTrendData.slice(0, 3)
            )
          } catch (error) {
            console.error('Failed to fetch monthly school report:', error)
            // ✅ FIXED: Return empty data instead of fallback to avoid artificial distribution
            console.log('⚠️ Returning empty monthly data due to API failure')
            const weeks = ['Minggu 1', 'Minggu 2', 'Minggu 3', 'Minggu 4', 'Minggu 5']
            realTrendData = weeks.map(week => ({
              date: week,
              entry: 0,
              lateEntry: 0,
              excusedAbsence: 0,
              sick: 0,
              temporaryLeave: 0,
              returnFromLeave: 0,
            }))
          }
          break
        case 'yearly':
          // ✅ UNIFIED CACHE STRATEGY: Use dedicated yearly reports API with clean architecture
          console.log(
            `🎯 SCHOOL REPORTS: Using unified yearly reports API: ${selectedYear} for class: ${classFilter}`
          )
          try {
            // ✅ FIXED: Build API URL with class filter parameter
            let apiUrl = `/api/reports/yearly?year=${selectedYear}&reportType=school&_t=${Date.now()}`

            // Add class filter if not 'all'
            if (classFilter !== 'all') {
              // ✅ FIXED: Find the original class name from availableClasses instead of transforming
              const originalClassName = availableClasses.find(
                cls => cls.value === classFilter
              )?.label
              if (originalClassName) {
                apiUrl += `&class=${encodeURIComponent(originalClassName)}`
                console.log(`📊 Adding class filter to yearly API: ${originalClassName}`)
              }
            }

            const response = await fetch(apiUrl)
            if (!response.ok) {
              throw new Error('Failed to fetch yearly school report')
            }
            const result = await response.json()

            // ✅ FIXED: Use ChartDataProcessor for consistent data processing
            console.log(`📊 School Yearly API Response:`, {
              monthsCount: result.data.months?.length,
              summaryCount: result.data.summary?.length,
              sampleMonth: result.data.months?.find((m: any) => m.monthName === 'Jul'),
            })

            const chartData = ChartDataProcessor.processYearlyReportForChart(
              result.data,
              'school'
            ).map((item: any) => ({
              date: item.date,
              entry: item.entry || 0,
              lateEntry: item.lateEntry || 0,
              excusedAbsence: item.excusedAbsence || 0,
              sick: item.sick || 0,
              temporaryLeave: item.temporaryLeave || 0,
              returnFromLeave: item.returnFromLeave || 0,
            }))

            console.log(
              `📊 School Chart Data Generated:`,
              chartData.find(item => item.date === 'Jul')
            )
            realTrendData = chartData

            console.log(
              `📊 SCHOOL REPORTS: Generated yearly data from unified API:`,
              realTrendData.slice(0, 3)
            )
          } catch (error) {
            console.error('Failed to fetch yearly school report:', error)
            // ✅ FIXED: Return empty data instead of fallback to avoid artificial distribution
            console.log('⚠️ Returning empty yearly data due to API failure')
            const monthNames = [
              'Jan',
              'Feb',
              'Mar',
              'Apr',
              'Mei',
              'Jun',
              'Jul',
              'Ags',
              'Sep',
              'Okt',
              'Nov',
              'Des',
            ]
            realTrendData = monthNames.map(month => ({
              date: month,
              entry: 0,
              lateEntry: 0,
              excusedAbsence: 0,
              sick: 0,
              temporaryLeave: 0,
              returnFromLeave: 0,
            }))
          }
          break
        default:
          // Default to hourly data
          realTrendData = generateRealHourlyTrendData(data)
      }

      console.log(`📈 SCHOOL REPORTS: Generated trend data:`, realTrendData)
      console.log(`📊 SCHOOL REPORTS: Trend data length: ${realTrendData.length}`)
      setTrendData(realTrendData)
    } catch (error) {
      toast({
        title: 'Gagal memuat laporan sekolah',
        description: 'Terjadi kesalahan saat mengambil data laporan',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Natural sorting function for better string/number sorting
  const naturalSort = (a: string, b: string): number => {
    const pattern = /(\d+|\D+)/g
    const partsA = String(a).match(pattern) || []
    const partsB = String(b).match(pattern) || []

    for (let i = 0; i < Math.min(partsA.length, partsB.length); i++) {
      const partA = partsA[i]
      const partB = partsB[i]
      const numA = !isNaN(Number(partA))
      const numB = !isNaN(Number(partB))

      if (numA && numB) {
        const diff = parseInt(partA) - parseInt(partB)
        if (diff !== 0) return diff
      } else {
        const diff = partA.localeCompare(partB)
        if (diff !== 0) return diff
      }
    }
    return partsA.length - partsB.length
  }

  // ✅ OPTIMIZED: Filter and sort reports with enhanced functionality
  const filteredReports = useMemo(() => {
    let result = [...reports]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        report =>
          report.name.toLowerCase().includes(query) ||
          report.uniqueCode.toLowerCase().includes(query) ||
          report.className.toLowerCase().includes(query)
      )
    }

    // Apply default ordering for today/yesterday filters (most recent first)
    if ((date === 'today' || date === 'yesterday') && !sortConfig) {
      result.sort((a, b) => {
        // Sort by most recent activity time (check all time fields)
        const getLatestTime = (report: SchoolReport) => {
          const times = [
            report.entryTime,
            report.lateEntryTime,
            report.excusedAbsenceTime,
            report.temporaryLeaveTime,
            report.returnFromLeaveTime,
            report.sickTime,
          ]
            .filter(Boolean)
            .map(time => new Date(time!).getTime())
          return times.length > 0 ? Math.max(...times) : 0
        }

        const timeA = getLatestTime(a)
        const timeB = getLatestTime(b)
        return timeB - timeA // Most recent first
      })
    }

    // Apply manual sorting if configured
    if (sortConfig !== null) {
      result.sort((a, b) => {
        let valueA: any
        let valueB: any

        // Handle special sorting keys for boolean fields
        if (sortConfig.key === 'entry') {
          valueA = a.entry ? '1' : '0'
          valueB = b.entry ? '1' : '0'
        } else if (sortConfig.key === 'lateEntry') {
          valueA = a.lateEntry ? '1' : '0'
          valueB = b.lateEntry ? '1' : '0'
        } else if (sortConfig.key === 'excusedAbsence') {
          valueA = a.excusedAbsence ? '1' : '0'
          valueB = b.excusedAbsence ? '1' : '0'
        } else if (sortConfig.key === 'temporaryLeave') {
          valueA = a.temporaryLeave ? '1' : '0'
          valueB = b.temporaryLeave ? '1' : '0'
        } else if (sortConfig.key === 'returnFromLeave') {
          valueA = a.returnFromLeave ? '1' : '0'
          valueB = b.returnFromLeave ? '1' : '0'
        } else if (sortConfig.key === 'sick') {
          valueA = a.sick ? '1' : '0'
          valueB = b.sick ? '1' : '0'
        } else {
          valueA = a[sortConfig.key as keyof SchoolReport]
          valueB = b[sortConfig.key as keyof SchoolReport]
        }

        if (valueA === null || valueA === undefined) valueA = ''
        if (valueB === null || valueB === undefined) valueB = ''
        valueA = String(valueA).toLowerCase()
        valueB = String(valueB).toLowerCase()

        const comparison = naturalSort(valueA, valueB)
        return sortConfig.direction === 'ascending' ? comparison : -comparison
      })
    }

    return result
  }, [reports, searchQuery, sortConfig, date])

  // Pagination logic
  const totalPages = Math.ceil(filteredReports.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedReports = filteredReports.slice(startIndex, endIndex)

  // Sorting functions
  const handleSort = (key: string) => {
    // If clicking on the same column, toggle direction
    if (sortConfig && sortConfig.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'ascending' ? 'descending' : 'ascending',
      })
    } else {
      // Default to ascending for first click
      setSortConfig({ key, direction: 'ascending' })
    }
  }

  const renderSortIndicator = (key: string) => {
    if (sortConfig?.key !== key) {
      return <span className="ml-1 text-gray-400">↕</span>
    }
    return <span className="ml-1">{sortConfig.direction === 'ascending' ? '↑' : '↓'}</span>
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [classFilter, date, searchQuery, selectedMonth, selectedYear, sortConfig])

  // Load data on component mount and when filters change
  useEffect(() => {
    if (
      admin &&
      (admin.role === 'super_admin' || admin.role === 'teacher' || admin.role === 'receptionist')
    ) {
      fetchReports()
    }
  }, [admin, date, classFilter, selectedMonth, selectedYear])

  // Modern Loading Component with Skeleton Screens
  const LoadingSkeleton = () => (
    <div className="space-y-6 duration-500 animate-in fade-in-50">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          <Skeleton className="h-4 w-96 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          <Skeleton className="h-10 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
        </div>
      </div>

      {/* Report Type Selector Skeleton */}
      <div className="mb-6 flex flex-wrap gap-2">
        {[...Array(3)].map((_, i) => (
          <Skeleton
            key={i}
            className="h-10 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
          />
        ))}
      </div>

      {/* Filter Controls Skeleton */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Skeleton className="h-10 w-full animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 md:w-48" />
        <Skeleton className="h-10 w-full animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 md:w-48" />
        <Skeleton className="h-10 flex-1 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                </div>
                <Skeleton className="h-12 w-12 animate-pulse rounded-full bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Chart Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-6 w-48 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
              <Skeleton className="h-4 w-64 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Chart Area */}
            <div className="h-80 w-full animate-pulse rounded-lg bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />

            {/* Chart Legend */}
            <div className="flex justify-center gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton className="h-3 w-3 animate-pulse rounded-full bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-4 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            <Skeleton className="h-4 w-24 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 border-b pb-2">
              {[...Array(8)].map((_, i) => (
                <Skeleton
                  key={i}
                  className="h-4 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
                />
              ))}
            </div>

            {/* Table Rows */}
            {[...Array(8)].map((_, i) => (
              <div key={i} className="grid grid-cols-8 gap-4 py-2">
                {[...Array(8)].map((_, j) => (
                  <Skeleton
                    key={j}
                    className="h-4 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
                  />
                ))}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Loading Indicator */}
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-3 text-gray-500">
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
          <span className="text-sm font-medium">Memuat data laporan sekolah...</span>
        </div>
      </div>
    </div>
  )

  // Show loading state
  if (sessionLoading || !admin) {
    return <LoadingSkeleton />
  }

  // Show loading skeleton when fetching data
  if (isLoading) {
    return <LoadingSkeleton />
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'teacher' && admin.role !== 'receptionist') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan sekolah.</p>
        </CardContent>
      </Card>
    )
  }

  // Production: Remove debug logging for performance

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Sekolah</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran sekolah siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => fetchReports(true)} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <SchoolReportExportButton
            rawData={reports}
            availableClasses={availableClasses.map(cls => ({
              id: cls.value,
              name: cls.label,
            }))}
            buttonText="Unduh Laporan Sekolah"
            buttonVariant="default"
            className="bg-green-600 hover:bg-green-700"
          />
        </div>
      </div>

      {/* Report Type Selector */}
      <div className="mb-6 flex flex-wrap gap-2">
        <Button
          variant={!['monthly', 'yearly'].includes(date) ? 'default' : 'outline'}
          onClick={() => setDate('today')}
          className="whitespace-nowrap"
        >
          📊 Laporan Harian
        </Button>
        <Button
          variant={date === 'monthly' ? 'default' : 'outline'}
          onClick={() => setDate('monthly')}
          className="whitespace-nowrap"
        >
          📅 Laporan Bulanan
        </Button>
        <Button
          variant={date === 'yearly' ? 'default' : 'outline'}
          onClick={() => setDate('yearly')}
          className="whitespace-nowrap"
        >
          📈 Laporan Tahunan
        </Button>
      </div>

      {/* Daily Report Filters - Only show for daily reports */}
      {!['monthly', 'yearly'].includes(date) && (
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <Select value={classFilter} onValueChange={setClassFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Pilih Kelas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kelas</SelectItem>
              {isLoadingClasses ? (
                <SelectItem value="loading" disabled>
                  Memuat kelas...
                </SelectItem>
              ) : (
                availableClasses.map(cls => (
                  <SelectItem key={cls.value} value={cls.value}>
                    {cls.label}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          <Select value={date} onValueChange={setDate}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Periode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hari Ini</SelectItem>
              <SelectItem value="yesterday">Kemarin</SelectItem>
            </SelectContent>
          </Select>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Cari siswa..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}

      {/* Monthly/Yearly Report Filters */}
      {['monthly', 'yearly'].includes(date) && (
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <Select value={classFilter} onValueChange={setClassFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Pilih Kelas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kelas</SelectItem>
              {isLoadingClasses ? (
                <SelectItem value="loading" disabled>
                  Memuat kelas...
                </SelectItem>
              ) : (
                availableClasses.map(cls => (
                  <SelectItem key={cls.value} value={cls.value}>
                    {cls.label}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>

          {date === 'monthly' && (
            <Select
              value={selectedMonth.toString()}
              onValueChange={value => setSelectedMonth(parseInt(value))}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Pilih Bulan" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Januari</SelectItem>
                <SelectItem value="2">Februari</SelectItem>
                <SelectItem value="3">Maret</SelectItem>
                <SelectItem value="4">April</SelectItem>
                <SelectItem value="5">Mei</SelectItem>
                <SelectItem value="6">Juni</SelectItem>
                <SelectItem value="7">Juli</SelectItem>
                <SelectItem value="8">Agustus</SelectItem>
                <SelectItem value="9">September</SelectItem>
                <SelectItem value="10">Oktober</SelectItem>
                <SelectItem value="11">November</SelectItem>
                <SelectItem value="12">Desember</SelectItem>
              </SelectContent>
            </Select>
          )}

          {date === 'yearly' && (
            <Select
              value={selectedYear.toString()}
              onValueChange={value => setSelectedYear(parseInt(value))}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Pilih Tahun" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2025">2025</SelectItem>
                <SelectItem value="2026">2026</SelectItem>
                <SelectItem value="2027">2027</SelectItem>
                <SelectItem value="2028">2028</SelectItem>
                <SelectItem value="2029">2029</SelectItem>
                <SelectItem value="2030">2030</SelectItem>
              </SelectContent>
            </Select>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Cari siswa..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}

      {/* KPI Cards - Split into 6 separate cards with improved accessible colors */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        <Card className="border-l-4 border-l-slate-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-slate-700">{stats.total}</div>
                    <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                    <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-slate-100">
                <Users className="h-6 w-6 text-slate-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-emerald-600">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-emerald-700">{stats.entry}</div>
                    <div className="text-sm font-medium text-gray-600">Masuk</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
                <UserCheck className="h-6 w-6 text-emerald-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-yellow-700">{stats.lateEntry}</div>
                    <div className="text-sm font-medium text-gray-600">Terlambat</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
                <Clock className="h-6 w-6 text-yellow-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-600">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-blue-700">{stats.excusedAbsence}</div>
                    <div className="text-sm font-medium text-gray-600">Ijin</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Calendar className="h-6 w-6 text-blue-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-rose-600">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-rose-700">{stats.sick}</div>
                    <div className="text-sm font-medium text-gray-600">Sakit</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-rose-100">
                <AlertTriangle className="h-6 w-6 text-rose-700" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-indigo-600">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                {isLoading ? (
                  <>
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="mt-1 h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </>
                ) : (
                  <>
                    <div className="text-3xl font-bold text-indigo-700">{stats.temporaryLeave}</div>
                    <div className="text-sm font-medium text-gray-600">Ijin Keluar</div>
                  </>
                )}
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100">
                <Users className="h-6 w-6 text-indigo-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* School Attendance Trend Chart - Show for all filters with appropriate data visualization */}
      {
        <div className="hidden md:block">
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">
                    Tren Kehadiran Sekolah
                  </CardTitle>
                  <p className="text-sm text-gray-500">
                    {date === 'today' || date === 'yesterday'
                      ? 'Grafik per jam berdasarkan timestamp aktual dari database siswa (06:00-18:00 WITA)'
                      : date === 'monthly'
                        ? 'Grafik mingguan untuk bulan yang dipilih berdasarkan data agregat'
                        : date === 'yearly'
                          ? 'Grafik bulanan untuk tahun yang dipilih berdasarkan data agregat'
                          : 'Grafik kehadiran berdasarkan periode yang dipilih'}
                  </p>
                </div>
                <div className="text-gray-400">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <circle cx="12" cy="6" r="1" />
                    <circle cx="12" cy="12" r="1" />
                    <circle cx="12" cy="18" r="1" />
                  </svg>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {isLoading ? (
                <div className="h-80 w-full animate-pulse rounded-lg bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
              ) : (
                <>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={trendData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                      >
                        <defs>
                          <linearGradient id="entryGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#10b981" stopOpacity={0.1} />
                            <stop offset="100%" stopColor="#10b981" stopOpacity={0} />
                          </linearGradient>
                          <linearGradient id="lateGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.1} />
                            <stop offset="100%" stopColor="#f59e0b" stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                        <XAxis
                          dataKey="date"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#94a3b8' }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: '#94a3b8' }}
                          tickFormatter={value => Math.floor(value).toString()}
                          domain={[0, (dataMax: number) => Math.max(10, Math.ceil(dataMax * 1.1))]}
                          allowDecimals={false}
                        />
                        <Tooltip
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="rounded-lg bg-gray-900 px-3 py-2 text-white shadow-lg">
                                  <p className="font-medium">{label}</p>
                                  {payload.map((entry, index) => (
                                    <p key={index} className="text-sm">
                                      <span
                                        className="mr-2 inline-block h-2 w-2 rounded-full"
                                        style={{ backgroundColor: entry.color }}
                                      ></span>
                                      {entry.name}: {Math.floor(Number(entry.value))}
                                    </p>
                                  ))}
                                </div>
                              )
                            }
                            return null
                          }}
                        />
                        {/* Entry/Kehadiran Line - Updated color to match KPI card */}
                        <Line
                          type="monotone"
                          dataKey="entry"
                          stroke="#059669"
                          strokeWidth={3}
                          dot={{ fill: '#059669', strokeWidth: 2, r: 4 }}
                          activeDot={{ r: 6, fill: '#059669', strokeWidth: 2, stroke: '#fff' }}
                          name="Kehadiran"
                        />
                        {/* Late Entry/Terlambat Line - Updated color to match KPI card */}
                        <Line
                          type="monotone"
                          dataKey="lateEntry"
                          stroke="#ca8a04"
                          strokeWidth={2}
                          dot={{ fill: '#ca8a04', strokeWidth: 2, r: 3 }}
                          activeDot={{ r: 5, fill: '#ca8a04', strokeWidth: 2, stroke: '#fff' }}
                          name="Terlambat"
                        />
                        {/* Excused Absence/Ijin Line - Updated color to match KPI card */}
                        <Line
                          type="monotone"
                          dataKey="excusedAbsence"
                          stroke="#2563eb"
                          strokeWidth={2}
                          dot={{ fill: '#2563eb', strokeWidth: 2, r: 3 }}
                          activeDot={{ r: 5, fill: '#2563eb', strokeWidth: 2, stroke: '#fff' }}
                          name="Ijin"
                        />
                        {/* Sick Line - Updated color to match KPI card */}
                        <Line
                          type="monotone"
                          dataKey="sick"
                          stroke="#e11d48"
                          strokeWidth={2}
                          dot={{ fill: '#e11d48', strokeWidth: 2, r: 3 }}
                          activeDot={{ r: 5, fill: '#e11d48', strokeWidth: 2, stroke: '#fff' }}
                          name="Sakit"
                        />
                        {/* Temporary Leave Line - Updated color to match KPI card */}
                        <Line
                          type="monotone"
                          dataKey="temporaryLeave"
                          stroke="#4f46e5"
                          strokeWidth={2}
                          dot={{ fill: '#4f46e5', strokeWidth: 2, r: 3 }}
                          activeDot={{ r: 5, fill: '#4f46e5', strokeWidth: 2, stroke: '#fff' }}
                          name="Ijin Keluar"
                        />
                        {/* Return from Leave Line - Updated color to match KPI card */}
                        <Line
                          type="monotone"
                          dataKey="returnFromLeave"
                          stroke="#0f766e"
                          strokeWidth={2}
                          dot={{ fill: '#0f766e', strokeWidth: 2, r: 3 }}
                          activeDot={{ r: 5, fill: '#0f766e', strokeWidth: 2, stroke: '#fff' }}
                          name="Kembali"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Chart Legend - Updated with matching accessible colors */}
                  <div className="mt-4 flex flex-wrap justify-center gap-4">
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-emerald-600"></div>
                      <span className="text-sm text-gray-600">Kehadiran</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-yellow-600"></div>
                      <span className="text-sm text-gray-600">Terlambat</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-blue-600"></div>
                      <span className="text-sm text-gray-600">Ijin</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-rose-600"></div>
                      <span className="text-sm text-gray-600">Sakit</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-indigo-600"></div>
                      <span className="text-sm text-gray-600">Ijin Keluar</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-teal-700"></div>
                      <span className="text-sm text-gray-600">Kembali</span>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      }

      {/* Student Data Matrix - Moved to Bottom */}
      <Card>
        <CardHeader>
          <CardTitle>Data Siswa Sekolah ({filteredReports.length} siswa)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No</TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('uniqueCode')}
                  >
                    Kode Siswa {renderSortIndicator('uniqueCode')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('name')}
                  >
                    Nama {renderSortIndicator('name')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('className')}
                  >
                    Kelas {renderSortIndicator('className')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('entry')}
                  >
                    Masuk {renderSortIndicator('entry')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('lateEntry')}
                  >
                    Terlambat {renderSortIndicator('lateEntry')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('excusedAbsence')}
                  >
                    Ijin {renderSortIndicator('excusedAbsence')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('sick')}
                  >
                    Sakit {renderSortIndicator('sick')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('temporaryLeave')}
                  >
                    Ijin Keluar {renderSortIndicator('temporaryLeave')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('returnFromLeave')}
                  >
                    Kembali {renderSortIndicator('returnFromLeave')}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : paginatedReports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={11} className="py-8 text-center text-gray-500">
                      Tidak ada data siswa ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedReports.map((report, index) => {
                    const globalIndex = startIndex + index + 1
                    const isAggregatedData = ['monthly', 'yearly'].includes(date)
                    const showTooltips = shouldShowTimeTooltips(date)

                    // Calculate attendance display values based on data type
                    let entryDisplay: string,
                      lateEntryDisplay: string,
                      excusedAbsenceDisplay: string,
                      sickDisplay: string,
                      temporaryLeaveDisplay: string,
                      returnFromLeaveDisplay: string

                    if (isAggregatedData && (report as any).aggregatedCounts) {
                      const counts = (report as any).aggregatedCounts

                      // Show total counts for aggregated data
                      entryDisplay = `${counts.entry} total`
                      lateEntryDisplay = counts.lateEntry > 0 ? `${counts.lateEntry} total` : '-'
                      excusedAbsenceDisplay =
                        counts.excusedAbsence > 0 ? `${counts.excusedAbsence} hari` : '-'
                      sickDisplay = counts.sick > 0 ? `${counts.sick} hari` : '-'
                      temporaryLeaveDisplay =
                        counts.temporaryLeave > 0 ? `${counts.temporaryLeave} total` : '-'
                      returnFromLeaveDisplay =
                        counts.returnFromLeave > 0 ? `${counts.returnFromLeave} total` : '-'
                    } else {
                      // Show checkmarks for today/yesterday
                      entryDisplay = report.entry ? '✓' : '✗'
                      lateEntryDisplay = report.lateEntry ? 'Terlambat' : '-'
                      excusedAbsenceDisplay = report.excusedAbsence ? 'Ijin' : '-'
                      sickDisplay = report.sick ? 'Sakit' : '-'
                      temporaryLeaveDisplay = report.temporaryLeave ? 'Ya' : '-'
                      returnFromLeaveDisplay = report.returnFromLeave ? 'Ya' : '-'
                    }

                    return (
                      <TableRow key={report.uniqueCode}>
                        <TableCell>{globalIndex}</TableCell>
                        <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                        <TableCell className="font-medium">{report.name}</TableCell>
                        <TableCell>{report.className}</TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={entryDisplay}
                            timeValue={report.entryTime}
                            variant={report.entry ? 'success' : 'danger'}
                            attendanceType="Masuk Sekolah"
                            showTooltip={showTooltips}
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={lateEntryDisplay}
                            timeValue={report.lateEntryTime}
                            variant={report.lateEntry ? 'warning' : 'secondary'}
                            attendanceType="Terlambat"
                            showTooltip={showTooltips}
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={excusedAbsenceDisplay}
                            timeValue={report.excusedAbsenceTime}
                            variant={report.excusedAbsence ? 'info' : 'secondary'}
                            attendanceType="Ijin"
                            showTooltip={showTooltips}
                            reason={
                              report.excusedAbsence && (report as any).reason
                                ? (report as any).reason
                                : undefined
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={sickDisplay}
                            timeValue={report.sickTime}
                            variant={report.sick ? 'danger' : 'secondary'}
                            attendanceType="Sakit"
                            showTooltip={showTooltips}
                            reason={
                              report.sick && (report as any).reason
                                ? (report as any).reason
                                : undefined
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={temporaryLeaveDisplay}
                            timeValue={report.temporaryLeaveTime}
                            variant={report.temporaryLeave ? 'neutral' : 'secondary'}
                            attendanceType="Ijin Sementara"
                            showTooltip={showTooltips}
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={returnFromLeaveDisplay}
                            timeValue={report.returnFromLeaveTime}
                            variant={report.returnFromLeave ? 'neutral' : 'secondary'}
                            attendanceType="Kembali dari Ijin"
                            showTooltip={showTooltips}
                          />
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Menampilkan {startIndex + 1}-{Math.min(endIndex, filteredReports.length)} dari{' '}
                {filteredReports.length} siswa
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      className={
                        currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      className={
                        currentPage === totalPages
                          ? 'pointer-events-none opacity-50'
                          : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
