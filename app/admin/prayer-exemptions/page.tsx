'use client'

/**
 * Prayer Exemption Management Page - Enhanced UI/UX
 * Only accessible by super_admin
 * Clean Architecture: Presentation Layer
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import { Calendar, Plus, Trash2, AlertCircle, Loader2, Search, Filter } from 'lucide-react'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { useAdminPermission } from '@/hooks/use-admin-permission'
import { useFormValidation, ValidationRules } from '@/hooks/use-form-validation'
import {
  PrayerExemption,
  PrayerExemptionType,
  PrayerType,
  RecurrencePattern,
  DayOfWeek,
  getPrayerExemptionTypeLabel,
  getPrayerTypeLabel,
  getRecurrencePatternLabel,
  getDayOfWeekLabel,
} from '@/lib/domain/entities/prayer-exemption'

// Removed Class interface since we only support global exemptions

// Form validation rules
const formValidationRules: ValidationRules = {
  exemptionType: { required: true },
  prayerType: { required: true },
  reason: { required: true, minLength: 10, maxLength: 500 },
  exemptionDate: {
    custom: (value, formData) => {
      if (formData?.recurrencePattern === RecurrencePattern.ONCE && !value) {
        return 'Tanggal harus diisi untuk pengecualian sekali'
      }
      return null
    },
  },
  startDate: {
    custom: (value, formData) => {
      if (formData?.recurrencePattern !== RecurrencePattern.ONCE && !value) {
        return 'Tanggal mulai harus diisi untuk pengecualian berulang'
      }
      return null
    },
  },
  // Removed targetId validation since we only support global exemptions
  daysOfWeek: {
    custom: (value, formData) => {
      if (
        formData?.recurrencePattern === RecurrencePattern.WEEKLY &&
        (!value || value.length === 0)
      ) {
        return 'Hari dalam seminggu harus dipilih untuk pengecualian mingguan'
      }
      return null
    },
  },
}

export default function PrayerExemptionsPage() {
  // Authentication and authorization
  const { permission, loading: permissionLoading } = useAdminPermission('super_admin')

  // State management
  const [exemptions, setExemptions] = useState<PrayerExemption[]>([])
  const [loading, setLoading] = useState(true)
  const [createLoading, setCreateLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [exemptionToDelete, setExemptionToDelete] = useState<PrayerExemption | null>(null)

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | PrayerExemptionType>('all')

  const { toast } = useToast()

  // Form validation with initial values
  const initialFormValues = {
    exemptionType: PrayerExemptionType.GLOBAL, // Always global since we only support global exemptions
    exemptionDate: '',
    recurrencePattern: RecurrencePattern.ONCE,
    startDate: '',
    endDate: '',
    daysOfWeek: [] as DayOfWeek[],
    prayerType: '' as PrayerType | '',
    reason: '',
  }

  const {
    values: formData,
    setValue: setFormValue,
    reset: resetForm,
  } = useFormValidation(initialFormValues, formValidationRules)

  // Load data on component mount
  useEffect(() => {
    if (permission?.isSuperAdmin) {
      loadExemptions()
    }
  }, [permission])

  // Load exemptions from API
  const loadExemptions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/prayer-exemptions')

      if (!response.ok) {
        throw new Error('Failed to load exemptions')
      }

      const data = await response.json()
      setExemptions(data.exemptions || [])
    } catch (error) {
      console.error('Error loading exemptions:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat data pengecualian shalat',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Removed loadClasses function since we only support global exemptions

  // Handle form submission for creating exemption - supports recurring
  const handleCreateExemption = async () => {
    try {
      setCreateLoading(true)

      // Validate common fields
      if (!formData.exemptionType || !formData.prayerType || !formData.reason.trim()) {
        toast({
          title: 'Error',
          description: 'Jenis pengecualian, jenis shalat, dan alasan harus diisi',
          variant: 'destructive',
        })
        return
      }

      // Validate dates based on recurrence pattern
      if (formData.recurrencePattern === RecurrencePattern.ONCE) {
        if (!formData.exemptionDate) {
          toast({
            title: 'Error',
            description: 'Tanggal harus diisi untuk pengecualian sekali',
            variant: 'destructive',
          })
          return
        }
      } else {
        if (!formData.startDate) {
          toast({
            title: 'Error',
            description: 'Tanggal mulai harus diisi untuk pengecualian berulang',
            variant: 'destructive',
          })
          return
        }

        if (
          formData.recurrencePattern === RecurrencePattern.WEEKLY &&
          formData.daysOfWeek.length === 0
        ) {
          toast({
            title: 'Error',
            description: 'Hari dalam seminggu harus dipilih untuk pengecualian mingguan',
            variant: 'destructive',
          })
          return
        }
      }

      // Removed class validation since we only support global exemptions

      if (formData.reason.trim().length < 10) {
        toast({
          title: 'Error',
          description: 'Alasan harus minimal 10 karakter',
          variant: 'destructive',
        })
        return
      }

      const response = await fetch('/api/prayer-exemptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          exemptionType: formData.exemptionType,
          recurrencePattern: formData.recurrencePattern,
          exemptionDate:
            formData.recurrencePattern === RecurrencePattern.ONCE ? formData.exemptionDate : null,
          startDate:
            formData.recurrencePattern !== RecurrencePattern.ONCE ? formData.startDate : null,
          endDate:
            formData.recurrencePattern !== RecurrencePattern.ONCE ? formData.endDate || null : null,
          daysOfWeek:
            formData.recurrencePattern === RecurrencePattern.WEEKLY ? formData.daysOfWeek : null,
          prayerType: formData.prayerType,
          reason: formData.reason.trim(),
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create exemption')
      }

      toast({
        title: 'Berhasil',
        description: 'Pengecualian shalat berhasil dibuat',
      })

      // Reset form and close dialog
      resetForm()
      setIsCreateDialogOpen(false)

      // Reload exemptions
      loadExemptions()
    } catch (error: any) {
      console.error('Error creating exemption:', error)
      toast({
        title: 'Error',
        description: error.message || 'Gagal membuat pengecualian shalat',
        variant: 'destructive',
      })
    } finally {
      setCreateLoading(false)
    }
  }

  // Handle exemption deletion - open confirmation dialog
  const handleDeleteClick = (exemption: PrayerExemption) => {
    setExemptionToDelete(exemption)
    setDeleteDialogOpen(true)
  }

  // Confirm and execute deletion
  const handleConfirmDelete = async () => {
    if (!exemptionToDelete) return

    try {
      setDeleteLoading(true)

      const response = await fetch(`/api/prayer-exemptions/${exemptionToDelete.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete exemption')
      }

      toast({
        title: 'Berhasil',
        description: 'Pengecualian shalat berhasil dihapus',
      })

      // Close dialog and reload exemptions
      setDeleteDialogOpen(false)
      setExemptionToDelete(null)
      loadExemptions()
    } catch (error) {
      console.error('Error deleting exemption:', error)
      toast({
        title: 'Error',
        description: 'Gagal menghapus pengecualian shalat',
        variant: 'destructive',
      })
    } finally {
      setDeleteLoading(false)
    }
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  // Removed getClassName function since we only support global exemptions

  // Filter and search exemptions
  const filteredExemptions = exemptions.filter(exemption => {
    // Filter by type
    if (filterType !== 'all' && exemption.exemptionType !== filterType) {
      return false
    }

    // Search in reason only (no class search since we only support global exemptions)
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      const reasonMatch = exemption.reason.toLowerCase().includes(query)

      if (!reasonMatch) {
        return false
      }
    }

    return true
  })

  // Show loading or permission check
  if (permissionLoading || loading) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="mt-2 text-slate-600 dark:text-slate-400">Loading...</p>
          </div>
        </div>
      </div>
    )
  }

  // Check permission
  if (!permission?.isSuperAdmin) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
            <h2 className="mb-2 text-xl font-semibold text-slate-800 dark:text-slate-100">
              Akses Ditolak
            </h2>
            <p className="text-slate-600 dark:text-slate-400">
              Hanya Super Admin yang dapat mengakses halaman ini.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="sticky top-0 z-10 border-b border-slate-200 bg-white/95 backdrop-blur-sm dark:border-slate-700 dark:bg-slate-900/95">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">
                Pengecualian Shalat
              </h1>
              <p className="mt-1 text-sm text-slate-600 dark:text-slate-400">
                Kelola pengecualian shalat untuk situasi khusus
              </p>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex w-full items-center gap-2 sm:w-auto">
                  <Plus className="h-4 w-4" />
                  <span className="hidden sm:inline">Tambah Pengecualian</span>
                  <span className="sm:hidden">Tambah</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Tambah Pengecualian Shalat</DialogTitle>
                  <DialogDescription>
                    Buat aturan pengecualian shalat untuk tanggal tertentu atau berulang
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  {/* Exemption type is always GLOBAL, so no need to show selection */}
                  <div className="rounded-md bg-blue-50 p-3 dark:bg-blue-950">
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      <strong>Jenis Pengecualian:</strong>{' '}
                      {getPrayerExemptionTypeLabel(PrayerExemptionType.GLOBAL)}
                    </p>
                    <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                      Pengecualian akan berlaku untuk semua siswa
                    </p>
                  </div>

                  {/* Recurrence Pattern Selection */}
                  <div className="grid gap-2">
                    <Label htmlFor="recurrencePattern">Pola Pengulangan</Label>
                    <Select
                      value={formData.recurrencePattern}
                      onValueChange={value => {
                        setFormValue('recurrencePattern', value as RecurrencePattern)
                        // Reset related fields when pattern changes
                        if (value === RecurrencePattern.ONCE) {
                          setFormValue('startDate', '')
                          setFormValue('endDate', '')
                          setFormValue('daysOfWeek', [])
                        } else {
                          setFormValue('exemptionDate', '')
                          if (value !== RecurrencePattern.WEEKLY) {
                            setFormValue('daysOfWeek', [])
                          }
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih pola pengulangan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={RecurrencePattern.ONCE}>
                          {getRecurrencePatternLabel(RecurrencePattern.ONCE)}
                        </SelectItem>
                        <SelectItem value={RecurrencePattern.WEEKLY}>
                          {getRecurrencePatternLabel(RecurrencePattern.WEEKLY)}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* One-time exemption date */}
                  {formData.recurrencePattern === RecurrencePattern.ONCE && (
                    <div className="grid gap-2">
                      <Label htmlFor="exemptionDate">Tanggal</Label>
                      <Input
                        id="exemptionDate"
                        type="date"
                        value={formData.exemptionDate}
                        onChange={e => setFormValue('exemptionDate', e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                  )}

                  {/* Recurring exemption dates */}
                  {formData.recurrencePattern !== RecurrencePattern.ONCE && (
                    <>
                      <div className="grid gap-2">
                        <Label htmlFor="startDate">Tanggal Mulai</Label>
                        <Input
                          id="startDate"
                          type="date"
                          value={formData.startDate}
                          onChange={e => setFormValue('startDate', e.target.value)}
                          min={new Date().toISOString().split('T')[0]}
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="endDate">Tanggal Berakhir (Opsional)</Label>
                        <Input
                          id="endDate"
                          type="date"
                          value={formData.endDate}
                          onChange={e => setFormValue('endDate', e.target.value)}
                          min={formData.startDate || new Date().toISOString().split('T')[0]}
                        />
                        <p className="text-xs text-slate-500">
                          Kosongkan untuk pengecualian tanpa batas waktu
                        </p>
                      </div>
                    </>
                  )}

                  {/* Weekly pattern - days selection */}
                  {formData.recurrencePattern === RecurrencePattern.WEEKLY && (
                    <div className="grid gap-2">
                      <Label>Hari dalam Seminggu</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {Object.values(DayOfWeek).map(day => (
                          <label key={day} className="flex cursor-pointer items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={formData.daysOfWeek.includes(day)}
                              onChange={e => {
                                const currentDays = formData.daysOfWeek || []
                                if (e.target.checked) {
                                  setFormValue('daysOfWeek', [...currentDays, day])
                                } else {
                                  setFormValue(
                                    'daysOfWeek',
                                    currentDays.filter(d => d !== day)
                                  )
                                }
                              }}
                              className="rounded border-gray-300"
                            />
                            <span className="text-sm">{getDayOfWeekLabel(day)}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="grid gap-2">
                    <Label htmlFor="prayerType">Jenis Shalat</Label>
                    <Select
                      value={formData.prayerType}
                      onValueChange={value => setFormValue('prayerType', value as PrayerType)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih jenis shalat" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={PrayerType.ZUHR}>
                          {getPrayerTypeLabel(PrayerType.ZUHR)}
                        </SelectItem>
                        <SelectItem value={PrayerType.ASR}>
                          {getPrayerTypeLabel(PrayerType.ASR)}
                        </SelectItem>
                        <SelectItem value={PrayerType.BOTH}>
                          {getPrayerTypeLabel(PrayerType.BOTH)}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="reason">Alasan</Label>
                    <Textarea
                      id="reason"
                      placeholder="Masukkan alasan pengecualian (minimal 10 karakter)"
                      value={formData.reason}
                      onChange={e => setFormValue('reason', e.target.value)}
                      rows={3}
                    />
                    <p className="text-xs text-slate-500">
                      {formData.reason.length}/500 karakter (minimal 10)
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                    disabled={createLoading}
                  >
                    Batal
                  </Button>
                  <Button type="button" onClick={handleCreateExemption} disabled={createLoading}>
                    {createLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Menyimpan...
                      </>
                    ) : (
                      'Simpan'
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        <div className="mb-6">
          {/* Stats Summary */}
          <div className="mb-6">
            <div className="w-full rounded-lg border border-slate-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-4 shadow-sm transition-all duration-200 hover:shadow-md dark:border-slate-700 dark:from-slate-800 dark:to-slate-700">
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 p-3 shadow-md">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400">
                    Total Pengecualian
                  </p>
                  <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                    {exemptions.length}
                  </p>
                  <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
                    Pengecualian shalat aktif
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Search and Filter Section */}
          <div className="mb-4 flex flex-col gap-3 sm:flex-row">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-slate-400" />
              <Input
                placeholder="Cari berdasarkan alasan..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={value => setFilterType(value as any)}>
                <SelectTrigger className="w-[140px]">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  <SelectItem value={PrayerExemptionType.GLOBAL}>
                    {getPrayerExemptionTypeLabel(PrayerExemptionType.GLOBAL)}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Exemptions List */}
        <div className="space-y-4">
          {filteredExemptions.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Calendar className="mx-auto mb-4 h-12 w-12 text-slate-400" />
                  <h3 className="mb-2 text-lg font-medium text-slate-800 dark:text-slate-100">
                    {exemptions.length === 0 ? 'Belum Ada Pengecualian' : 'Tidak Ada Hasil'}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-400">
                    {exemptions.length === 0
                      ? 'Klik tombol "Tambah Pengecualian" untuk membuat aturan pengecualian shalat.'
                      : 'Tidak ada pengecualian yang sesuai dengan pencarian atau filter Anda.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredExemptions.map(exemption => (
              <Card key={exemption.id} className="transition-shadow hover:shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex flex-col gap-3 sm:flex-row sm:items-start sm:justify-between">
                    <div className="min-w-0 flex-1">
                      <CardTitle className="text-base leading-tight sm:text-lg">
                        {getPrayerExemptionTypeLabel(exemption.exemptionType)}
                      </CardTitle>
                      <div className="mt-2 space-y-1">
                        <div className="text-sm text-slate-600 dark:text-slate-400">
                          {/* Display date info based on recurrence pattern */}
                          {exemption.recurrencePattern === RecurrencePattern.ONCE ? (
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(exemption.exemptionDate!)}
                            </span>
                          ) : (
                            <div className="space-y-1">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {getRecurrencePatternLabel(exemption.recurrencePattern)}
                              </span>
                              <span className="text-xs">
                                {exemption.startDate && formatDate(exemption.startDate)}
                                {exemption.endDate && ` - ${formatDate(exemption.endDate)}`}
                                {!exemption.endDate && ' - Tanpa batas'}
                              </span>
                              {exemption.recurrencePattern === RecurrencePattern.WEEKLY &&
                                exemption.daysOfWeek && (
                                  <span className="block text-xs">
                                    Hari:{' '}
                                    {exemption.daysOfWeek
                                      .map(day => getDayOfWeekLabel(day))
                                      .join(', ')}
                                  </span>
                                )}
                            </div>
                          )}
                        </div>
                        <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          {getPrayerTypeLabel(exemption.prayerType)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 sm:flex-shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(exemption)}
                        className="text-red-600 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-950"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="ml-1 sm:hidden">Hapus</span>
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <div className="rounded-md bg-slate-50 p-3 dark:bg-slate-800">
                      <p className="text-sm text-slate-700 dark:text-slate-300">
                        <strong className="text-slate-900 dark:text-slate-100">Alasan:</strong>
                      </p>
                      <p className="mt-1 text-sm text-slate-600 dark:text-slate-400">
                        {exemption.reason}
                      </p>
                    </div>
                    <p className="flex items-center gap-1 text-xs text-slate-500">
                      <span>
                        Dibuat pada {new Date(exemption.createdAt).toLocaleDateString('id-ID')}
                      </span>
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </main>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Pengecualian Shalat</AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div>
                <p className="text-sm text-muted-foreground">
                  Apakah Anda yakin ingin menghapus pengecualian shalat ini?
                </p>
                {exemptionToDelete && (
                  <div className="mt-3 rounded-md bg-slate-50 p-3 dark:bg-slate-800">
                    <div className="text-sm font-medium text-slate-900 dark:text-slate-100">
                      {getPrayerExemptionTypeLabel(exemptionToDelete.exemptionType)} -{' '}
                      {getPrayerTypeLabel(exemptionToDelete.prayerType)}
                    </div>
                    <div className="mt-1 text-xs text-slate-600 dark:text-slate-400">
                      {exemptionToDelete.reason}
                    </div>
                  </div>
                )}
                <div className="mt-3 text-sm text-red-600 dark:text-red-400">
                  Tindakan ini tidak dapat dibatalkan.
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={deleteLoading}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {deleteLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AdminBottomNav activeTab="profile" />
    </div>
  )
}
