'use client'

import { useState, use<PERSON>ffect, use<PERSON>em<PERSON>, useC<PERSON>back } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useToast } from '@/hooks/use-toast'
import { useAdminAuth } from '@/hooks/use-hybrid-auth'
import { RefreshCw, Users, UserCheck, Target, Search } from 'lucide-react'
import { PrayerReportExportButton } from '@/components/reports/ReportExportButton'
import {
  AttendanceTimeTooltip,
  shouldShowTimeTooltips,
} from '@/components/reports/AttendanceTimeTooltip'
import {
  Pagination,
  <PERSON><PERSON>ation<PERSON>ontent,
  Pa<PERSON>ationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'

// Import chart components
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts'
import { TIMEZONE_CONFIG } from '@/lib/config'
import { ChartDataProcessor } from '@/lib/utils/chart-data-processor'

// Prayer Report Interface (Real Data Structure)
interface PrayerReport {
  uniqueCode: string
  name: string
  className: string
  summaryDate: string
  zuhr: boolean
  zuhrTime: string | null
  asr: boolean
  asrTime: string | null
  dismissal: boolean
  dismissalTime: string | null
  ijin: boolean
  ijinTime?: string | null
}

// Prayer Statistics Interface
interface PrayerStats {
  total: number
  zuhr: number
  asr: number
  dismissal: number
  ijin: number
  zuhrPercentage: number
  asrPercentage: number
  dismissalPercentage: number
  ijinPercentage: number
}

// Trend Data for Charts
interface PrayerTrendData {
  date: string
  zuhr: number
  asr: number
  ijin: number
}

// ✅ OPTIMIZED: Extract stats calculation to pure function
const calculatePrayerStats = (data: PrayerReport[], date: string): PrayerStats => {
  const total = data.length
  const isAggregatedData = ['monthly', 'yearly'].includes(date)

  // ✅ OPTIMIZED: Single pass instead of multiple filter operations
  let zuhrCount = 0,
    asrCount = 0,
    dismissalCount = 0,
    ijinCount = 0

  if (['today', 'yesterday'].includes(date)) {
    // Single O(n) pass for daily data
    for (const report of data) {
      if (report.zuhr) zuhrCount++
      if (report.asr) asrCount++
      if (report.ijin) ijinCount++
    }

    const activeStudents = Math.max(total - ijinCount, 1)
    const zuhrPercentage = total > 0 ? Math.round((zuhrCount / activeStudents) * 100) : 0
    const asrPercentage = total > 0 ? Math.round((asrCount / activeStudents) * 100) : 0
    const ijinPercentage = total > 0 ? Math.round((ijinCount / total) * 100) : 0

    return {
      total,
      zuhr: zuhrCount,
      asr: asrCount,
      dismissal: dismissalCount,
      ijin: ijinCount,
      zuhrPercentage,
      asrPercentage,
      dismissalPercentage: 0,
      ijinPercentage,
    }
  } else if (isAggregatedData && data.length > 0 && (data[0] as any).aggregatedCounts) {
    // Optimized aggregated data processing for monthly, yearly
    let totalAvailableZuhr = 0,
      totalAvailableAsr = 0
    let totalPossibleZuhr = 0,
      totalPossibleAsr = 0,
      totalPossibleIjin = 0

    let totalDays = 1
    if (date === 'monthly') totalDays = 22
    else if (date === 'yearly') totalDays = 200

    // Single pass through aggregated data
    for (const report of data) {
      const counts = (report as any).aggregatedCounts
      const availableDays = Math.max(totalDays - (counts?.ijin || 0), 0)
      totalAvailableZuhr += availableDays
      totalAvailableAsr += availableDays
      totalPossibleZuhr += counts?.zuhr || 0
      totalPossibleAsr += counts?.asr || 0
      totalPossibleIjin += counts?.ijin || 0
    }

    zuhrCount = totalPossibleZuhr
    asrCount = totalPossibleAsr
    ijinCount = totalPossibleIjin

    const zuhrPercentage =
      totalAvailableZuhr > 0 ? Math.round((zuhrCount / totalAvailableZuhr) * 100) : 0
    const asrPercentage =
      totalAvailableAsr > 0 ? Math.round((asrCount / totalAvailableAsr) * 100) : 0
    const ijinPercentage = total > 0 ? Math.round((ijinCount / (total * totalDays)) * 100) : 0

    return {
      total,
      zuhr: zuhrCount,
      asr: asrCount,
      dismissal: dismissalCount,
      ijin: ijinCount,
      zuhrPercentage,
      asrPercentage,
      dismissalPercentage: 0,
      ijinPercentage,
    }
  }

  // Default return for edge cases
  return {
    total,
    zuhr: 0,
    asr: 0,
    dismissal: 0,
    ijin: 0,
    zuhrPercentage: 0,
    asrPercentage: 0,
    dismissalPercentage: 0,
    ijinPercentage: 0,
  }
}

// ✅ FIXED: Function to generate monthly trend data using actual attendance dates
const generateMonthlyTrendDataFromRealDates = async (
  selectedMonth: number,
  selectedYear: number,
  reportType: 'prayer' | 'school' = 'prayer'
): Promise<PrayerTrendData[]> => {
  // Initialize week counters for the month
  const weeks = ['Minggu 1', 'Minggu 2', 'Minggu 3', 'Minggu 4', 'Minggu 5']
  const weeklyData = weeks.map(week => ({
    date: week,
    zuhr: 0,
    asr: 0,
    ijin: 0,
  }))

  try {
    // Get detailed attendance records for the month with actual timestamps
    const startDate = new Date(selectedYear, selectedMonth - 1, 1)
    const endDate = new Date(selectedYear, selectedMonth, 0)

    console.log(`📅 Fetching attendance records for ${selectedMonth}/${selectedYear}:`, {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    })

    // Fetch attendance records via API
    const response = await fetch(
      `/api/absence/reports/range?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}&reportType=${reportType}&_t=${Date.now()}`
    )

    if (!response.ok) {
      console.error('Failed to fetch attendance records for monthly chart')
      return weeklyData
    }

    const attendanceRecords = await response.json()
    console.log(`📊 Monthly chart: Processing ${attendanceRecords.length} attendance records`)

    // Process each attendance record and map to correct week
    attendanceRecords.forEach((record: any) => {
      // Get the date from summaryDate or updatedAt
      const recordDate = new Date(record.summaryDate || record.updatedAt)

      if (isNaN(recordDate.getTime())) {
        console.warn('Invalid date in attendance record:', record)
        return
      }

      // Calculate which week of the month this date falls into
      const dayOfMonth = recordDate.getDate()
      const weekIndex = Math.min(Math.floor((dayOfMonth - 1) / 7), 4) // 0-4 for weeks 1-5

      // Count attendance types for this record
      if (record.zuhr) weeklyData[weekIndex].zuhr++
      if (record.asr) weeklyData[weekIndex].asr++
      if (record.ijin) weeklyData[weekIndex].ijin++
    })

    console.log('📊 Monthly chart: Generated from real dates', weeklyData)
    return weeklyData
  } catch (error) {
    console.error('Error generating monthly trend data from real dates:', error)
    return weeklyData
  }
}

// ✅ FALLBACK: Function to generate monthly trend data from aggregated data (for backward compatibility)
const generateMonthlyTrendDataFromAggregatedData = (reports: PrayerReport[]): PrayerTrendData[] => {
  // Initialize week counters for the month
  const weeks = ['Minggu 1', 'Minggu 2', 'Minggu 3', 'Minggu 4', 'Minggu 5']
  const weeklyData = weeks.map(week => ({
    date: week,
    zuhr: 0,
    asr: 0,
    ijin: 0,
  }))

  // If no reports, return initialized weekly data with zeros
  if (!reports || reports.length === 0) {
    console.log('Monthly chart: No reports data, returning zeros')
    return weeklyData
  }

  // Check if we have aggregated data
  const isAggregated = reports.length > 0 && (reports[0] as any).aggregatedCounts

  if (isAggregated) {
    // For monthly aggregated data, we need to distribute students with attendance
    // across weeks in a way that reflects realistic patterns
    let studentsWithAttendance = 0
    let totalZuhr = 0,
      totalAsr = 0,
      totalIjin = 0

    // First pass: count students with actual attendance and totals
    reports.forEach(report => {
      const counts = (report as any).aggregatedCounts
      if (counts && (counts.zuhr > 0 || counts.asr > 0 || counts.ijin > 0)) {
        studentsWithAttendance++
        totalZuhr += counts.zuhr || 0
        totalAsr += counts.asr || 0
        totalIjin += counts.ijin || 0
      }
    })

    // If we have students with attendance, distribute them across weeks
    if (studentsWithAttendance > 0) {
      // Simple distribution: put most attendance in weeks 1, 3, and 5
      // This reflects typical school patterns where some weeks have more activity
      const weekWeights = [0.3, 0.1, 0.3, 0.1, 0.2] // Weeks 1,3,5 have more activity

      weekWeights.forEach((weight, index) => {
        weeklyData[index].zuhr = Math.round(totalZuhr * weight)
        weeklyData[index].asr = Math.round(totalAsr * weight)
        weeklyData[index].ijin = Math.round(totalIjin * weight)
      })
    }
  }

  console.log('Monthly chart: Generated from aggregated data', weeklyData)
  return weeklyData
}

// ✅ REMOVED: Function generateYearlyTrendDataFromRealDates - replaced by ChartDataProcessor
// This function was redundant and potentially inconsistent with the new clean architecture

// ✅ REMOVED: Function generateYearlyTrendDataFromAggregatedData was using artificial distribution
// This caused yearly charts to show flat lines across all months instead of realistic data
// Now using ChartDataProcessor.processYearlyReportForChart() for accurate data distribution

// ✅ Function to generate real hourly trend data from actual prayer times (07:00 - 17:00 WITA)
const generateRealHourlyPrayerTrendData = (reports: PrayerReport[]): PrayerTrendData[] => {
  // Fixed time slots for consistent chart display (prayer hours) - 07:00 WITA to 17:00 WITA
  const hours = [
    '07:00',
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
  ]

  // Initialize hourly counters - always return this structure for consistent chart display
  const hourlyData = hours.map(hour => ({
    date: hour,
    zuhr: 0,
    asr: 0,
    ijin: 0,
  }))

  // If no reports, return initialized hourly data with zeros (chart will show flat line at 0)
  if (!reports || reports.length === 0) {
    return hourlyData
  }

  // Track real vs fallback data usage
  let realTimestampCount = 0

  // Helper function to extract hour from time string and map to nearest hour slot
  const extractAndMapHour = (timeStr: string | null | undefined): string | null => {
    if (!timeStr || timeStr === '-' || timeStr === 'null' || timeStr === null) {
      return null
    }

    // Skip count format (like "3x") - this indicates aggregated data, not real timestamps
    if (typeof timeStr === 'string' && timeStr.includes('x')) {
      return null
    }

    // Skip boolean-like values - we need actual timestamps
    if (timeStr === '✓' || timeStr === 'Ya' || timeStr === 'true' || timeStr === 'false') {
      return null
    }

    try {
      let hourMinute: string

      // Handle different time formats
      if (timeStr.includes('T')) {
        // ISO format: "2024-01-15T13:30:00.000Z"
        const date = new Date(timeStr)
        if (isNaN(date.getTime())) {
          return null
        }
        hourMinute = date.toLocaleTimeString(TIMEZONE_CONFIG.LOCALE, {
          hour: '2-digit',
          minute: '2-digit',
          timeZone: TIMEZONE_CONFIG.TIMEZONE,
          hour12: false,
        })
        realTimestampCount++
      } else if (timeStr.match(/^\d{1,2}:\d{2}(:\d{2})?$/)) {
        // Already in HH:MM or HH:MM:SS format
        hourMinute = timeStr.substring(0, 5) // Get HH:MM part
        realTimestampCount++
      } else if (timeStr.match(/^\d{1,2}\.\d{2}\.\d{2}$/)) {
        // Dot format: "14.15.49" -> convert to "14:15"
        const parts = timeStr.split('.')
        hourMinute = `${parts[0].padStart(2, '0')}:${parts[1]}`
        realTimestampCount++
      } else if (timeStr.includes(',') && timeStr.includes('WITA')) {
        // Format: "29 Apr 2025, 12:15 WITA"
        const timePart = timeStr.split(',')[1]?.trim()
        if (timePart) {
          const timeOnly = timePart.replace(' WITA', '').trim()
          if (timeOnly.match(/^\d{1,2}:\d{2}$/)) {
            hourMinute = timeOnly
            realTimestampCount++
          } else {
            return null
          }
        } else {
          return null
        }
      } else {
        // Try to parse as a date string
        const date = new Date(timeStr)
        if (!isNaN(date.getTime())) {
          hourMinute = date.toLocaleTimeString(TIMEZONE_CONFIG.LOCALE, {
            hour: '2-digit',
            minute: '2-digit',
            timeZone: TIMEZONE_CONFIG.TIMEZONE,
            hour12: false,
          })
          realTimestampCount++
        } else {
          return null
        }
      }

      // Map to the nearest hour slot in our predefined hours
      const [hour, minute] = hourMinute.split(':').map(Number)
      if (isNaN(hour) || isNaN(minute)) {
        return null
      }

      const timeInMinutes = hour * 60 + minute

      // Find the closest hour slot
      let closestHour = '07:00'
      let minDiff = Infinity

      hours.forEach(hourSlot => {
        const [slotHour] = hourSlot.split(':').map(Number)
        const slotTimeInMinutes = slotHour * 60
        const diff = Math.abs(timeInMinutes - slotTimeInMinutes)

        if (diff < minDiff) {
          minDiff = diff
          closestHour = hourSlot
        }
      })

      return closestHour
    } catch (error) {
      return null
    }
  }

  // Process each student's prayer records and map to hour slots
  reports.forEach(report => {
    // Extract hours from prayer times
    const zuhrHour = extractAndMapHour(report.zuhrTime)
    const asrHour = extractAndMapHour(report.asrTime)
    const ijinHour = extractAndMapHour(report.ijinTime)

    // Map to hour slots and increment counters - ONLY use real timestamp data, NO fallback
    if (report.zuhr && zuhrHour) {
      // Use real timestamp data only
      const hourSlot = hourlyData.find(h => h.date === zuhrHour)
      if (hourSlot) {
        hourSlot.zuhr++
      }
    }

    if (report.asr && asrHour) {
      // Use real timestamp data only
      const hourSlot = hourlyData.find(h => h.date === asrHour)
      if (hourSlot) {
        hourSlot.asr++
      }
    }

    if (report.ijin && ijinHour) {
      // Use real timestamp data only
      const hourSlot = hourlyData.find(h => h.date === ijinHour)
      if (hourSlot) {
        hourSlot.ijin++
      }
    }
  })

  // Always return real data structure - no fallback dummy data
  return hourlyData
}

// ✅ FIXED: Async function to generate trend data using real dates
const generateTrendDataFromRealDates = async (
  date: string,
  selectedMonth: number,
  selectedYear: number,
  classFilter: string = 'all',
  availableClasses: Array<{ value: string; label: string }> = [],
  reports: PrayerReport[] = []
): Promise<PrayerTrendData[]> => {
  console.log(
    `🎯 generateTrendDataFromRealDates called with date: "${date}", classFilter: "${classFilter}", reports count: ${reports.length}`
  )

  if (date === 'today' || date === 'yesterday') {
    // ONLY use real hourly data based on actual prayer times - NO SYNTHETIC DATA
    const realHourlyData = generateRealHourlyPrayerTrendData(reports)
    console.log(`📊 Generated hourly data for ${date}:`, realHourlyData.slice(0, 3))
    // Always return real data, even if empty - no fallback synthetic data
    return realHourlyData
  } else if (date === 'monthly') {
    // ✅ NEW: Use dedicated monthly reports API with clean architecture
    console.log(
      `🎯 Using new monthly reports API: ${selectedMonth}/${selectedYear} for class: ${classFilter}`
    )
    try {
      // ✅ FIXED: Build API URL with class filter parameter
      let apiUrl = `/api/reports/monthly?month=${selectedMonth}&year=${selectedYear}&reportType=prayer&_t=${Date.now()}`

      // Add class filter if not 'all'
      if (classFilter !== 'all') {
        // ✅ FIXED: Find the original class name from availableClasses instead of transforming
        const originalClassName = availableClasses.find(cls => cls.value === classFilter)?.label
        if (originalClassName) {
          apiUrl += `&class=${encodeURIComponent(originalClassName)}`
          console.log(`📊 Adding class filter to monthly API: ${originalClassName}`)
        }
      }

      const response = await fetch(apiUrl)
      if (!response.ok) {
        throw new Error('Failed to fetch monthly report')
      }
      const result = await response.json()

      // ✅ FIXED: Use ChartDataProcessor for consistent data processing
      const chartData = ChartDataProcessor.processMonthlyReportForChart(result.data, 'prayer').map(
        item => ({
          date: item.date,
          zuhr: item.zuhr || 0,
          asr: item.asr || 0,
          ijin: item.ijin || 0,
        })
      )

      console.log(`📊 Generated monthly data from new API:`, chartData.slice(0, 3))
      return chartData
    } catch (error) {
      console.error('Failed to fetch monthly report:', error)
      // Fallback to old method
      const monthlyData = await generateMonthlyTrendDataFromRealDates(
        selectedMonth,
        selectedYear,
        'prayer'
      )
      return monthlyData
    }
  } else if (date === 'yearly') {
    // ✅ NEW: Use dedicated yearly reports API with clean architecture
    console.log(`🎯 Using new yearly reports API: ${selectedYear} for class: ${classFilter}`)
    try {
      // ✅ FIXED: Build API URL with class filter parameter
      let apiUrl = `/api/reports/yearly?year=${selectedYear}&reportType=prayer&_t=${Date.now()}`

      // Add class filter if not 'all'
      if (classFilter !== 'all') {
        // ✅ FIXED: Find the original class name from availableClasses instead of transforming
        const originalClassName = availableClasses.find(cls => cls.value === classFilter)?.label
        if (originalClassName) {
          apiUrl += `&class=${encodeURIComponent(originalClassName)}`
          console.log(`📊 Adding class filter to yearly API: ${originalClassName}`)
        }
      }

      const response = await fetch(apiUrl)
      if (!response.ok) {
        throw new Error('Failed to fetch yearly report')
      }
      const result = await response.json()

      // ✅ FIXED: Use ChartDataProcessor for consistent data processing
      const chartData = ChartDataProcessor.processYearlyReportForChart(result.data, 'prayer').map(
        item => ({
          date: item.date,
          zuhr: item.zuhr || 0,
          asr: item.asr || 0,
          ijin: item.ijin || 0,
        })
      )

      console.log(`📊 Generated yearly data from new API:`, chartData.slice(0, 3))
      return chartData
    } catch (error) {
      console.error('Failed to fetch yearly report:', error)
      // ✅ FIXED: Return empty data instead of fallback to avoid artificial distribution
      console.log('⚠️ Returning empty yearly data due to API failure')
      const monthNames = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'Mei',
        'Jun',
        'Jul',
        'Ags',
        'Sep',
        'Okt',
        'Nov',
        'Des',
      ]
      return monthNames.map(month => ({
        date: month,
        zuhr: 0,
        asr: 0,
        ijin: 0,
      }))
    }
  }

  console.log(`❌ Unknown date filter: "${date}", returning empty array`)
  return []
}

// ✅ FALLBACK: Synchronous function for backward compatibility
const generateTrendData = (date: string, reports: PrayerReport[] = []): PrayerTrendData[] => {
  console.log(
    `🎯 generateTrendData (fallback) called with date: "${date}", reports count: ${reports.length}`
  )

  if (date === 'today' || date === 'yesterday') {
    // ONLY use real hourly data based on actual prayer times - NO SYNTHETIC DATA
    const realHourlyData = generateRealHourlyPrayerTrendData(reports)
    console.log(`📊 Generated hourly data for ${date}:`, realHourlyData.slice(0, 3))
    // Always return real data, even if empty - no fallback synthetic data
    return realHourlyData
  } else if (date === 'monthly') {
    // For monthly data, use aggregated data distribution - NO SYNTHETIC DATA
    const monthlyData = generateMonthlyTrendDataFromAggregatedData(reports)
    console.log(`📊 Generated monthly data:`, monthlyData.slice(0, 3))
    return monthlyData
  } else if (date === 'yearly') {
    // ✅ FIXED: Yearly data should use the new API, not fallback functions
    console.log(`⚠️ Yearly data requested in fallback function - this should not happen`)
    console.log(`🎯 Use the new yearly API instead: /api/reports/yearly`)
    // Return empty data to avoid artificial distribution
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Ags',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ]
    return monthNames.map(month => ({
      date: month,
      zuhr: 0,
      asr: 0,
      ijin: 0,
    }))
  }

  console.log(`❌ Unknown date filter: "${date}", returning empty array`)
  return []
}

export default function PrayerReportsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user: admin, isLoading: sessionLoading } = useAdminAuth()

  // State management for real data
  const [reports, setReports] = useState<PrayerReport[]>([])
  const [stats, setStats] = useState<PrayerStats>({
    total: 0,
    zuhr: 0,
    asr: 0,
    dismissal: 0,
    ijin: 0,
    zuhrPercentage: 0,
    asrPercentage: 0,
    dismissalPercentage: 0,
    ijinPercentage: 0,
  })
  const [trendData, setTrendData] = useState<PrayerTrendData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [classFilter, setClassFilter] = useState('all')
  const [date, setDate] = useState('today')
  const [searchQuery, setSearchQuery] = useState('')
  const [availableClasses, setAvailableClasses] = useState<Array<{ value: string; label: string }>>(
    []
  )
  const [isLoadingClasses, setIsLoadingClasses] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50)

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'ascending' | 'descending'
  } | null>(null)

  // Natural sorting function for better string/number sorting
  const naturalSort = (a: string, b: string): number => {
    const pattern = /(\d+|\D+)/g
    const partsA = String(a).match(pattern) || []
    const partsB = String(b).match(pattern) || []

    for (let i = 0; i < Math.min(partsA.length, partsB.length); i++) {
      const partA = partsA[i]
      const partB = partsB[i]
      const numA = !isNaN(Number(partA))
      const numB = !isNaN(Number(partB))

      if (numA && numB) {
        const diff = parseInt(partA) - parseInt(partB)
        if (diff !== 0) return diff
      } else {
        const diff = partA.localeCompare(partB)
        if (diff !== 0) return diff
      }
    }
    return partsA.length - partsB.length
  }

  // ✅ OPTIMIZED: Memoize filtered and sorted reports
  const filteredReports = useMemo(() => {
    let result = [...reports]

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        report =>
          report.name.toLowerCase().includes(query) ||
          report.uniqueCode.toLowerCase().includes(query) ||
          report.className.toLowerCase().includes(query)
      )
    }

    // Apply default ordering for today/yesterday filters (most recent first)
    if ((date === 'today' || date === 'yesterday') && !sortConfig) {
      result.sort((a, b) => {
        // Sort by most recent activity time (check all time fields)
        const getLatestTime = (report: PrayerReport) => {
          const times = [report.zuhrTime, report.asrTime, report.dismissalTime, report.ijinTime]
            .filter(Boolean)
            .map(time => new Date(time!).getTime())
          return times.length > 0 ? Math.max(...times) : 0
        }

        const timeA = getLatestTime(a)
        const timeB = getLatestTime(b)
        return timeB - timeA // Most recent first
      })
    }

    // Apply manual sorting if configured
    if (sortConfig !== null) {
      result.sort((a, b) => {
        let valueA: any
        let valueB: any

        // Handle special sorting keys
        if (sortConfig.key === 'zuhr') {
          valueA = a.zuhr ? '1' : '0'
          valueB = b.zuhr ? '1' : '0'
        } else if (sortConfig.key === 'asr') {
          valueA = a.asr ? '1' : '0'
          valueB = b.asr ? '1' : '0'
        } else if (sortConfig.key === 'dismissal') {
          valueA = a.dismissal ? '1' : '0'
          valueB = b.dismissal ? '1' : '0'
        } else if (sortConfig.key === 'ijin') {
          valueA = a.ijin ? '1' : '0'
          valueB = b.ijin ? '1' : '0'
        } else {
          valueA = a[sortConfig.key as keyof PrayerReport]
          valueB = b[sortConfig.key as keyof PrayerReport]
        }

        if (valueA === null || valueA === undefined) valueA = ''
        if (valueB === null || valueB === undefined) valueB = ''
        valueA = String(valueA).toLowerCase()
        valueB = String(valueB).toLowerCase()

        const comparison = naturalSort(valueA, valueB)
        return sortConfig.direction === 'ascending' ? comparison : -comparison
      })
    }

    return result
  }, [reports, searchQuery, sortConfig, date])

  // ✅ OPTIMIZED: Memoize pagination calculations
  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(filteredReports.length / itemsPerPage)
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedReports = filteredReports.slice(startIndex, endIndex)

    return {
      totalPages,
      startIndex,
      endIndex,
      paginatedReports,
    }
  }, [filteredReports, currentPage, itemsPerPage])

  // ✅ OPTIMIZED: Memoize classes fetching function
  const fetchClasses = useCallback(async () => {
    try {
      setIsLoadingClasses(true)
      const response = await fetch('/api/classes')
      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }
      const classes = await response.json()

      const classOptions = classes.map((cls: any) => ({
        value: cls.name.toLowerCase().replace(/\s+/g, '-'),
        label: cls.name,
      }))

      setAvailableClasses(classOptions)
    } catch (error) {
      toast({
        title: 'Gagal memuat daftar kelas',
        description: 'Menggunakan daftar kelas default',
        variant: 'destructive',
      })
    } finally {
      setIsLoadingClasses(false)
    }
  }, [toast])

  // ✅ OPTIMIZED: Memoize fetchReports function to prevent unnecessary re-renders
  const fetchReports = useCallback(
    async (forceRefresh = false) => {
      try {
        setIsLoading(true)

        // Reset stats to prevent showing stale data
        setStats({
          total: 0,
          zuhr: 0,
          asr: 0,
          dismissal: 0,
          ijin: 0,
          zuhrPercentage: 0,
          asrPercentage: 0,
          dismissalPercentage: 0,
          ijinPercentage: 0,
        })
        setReports([])

        const queryParams = new URLSearchParams()

        // Handle different report types
        if (date === 'monthly') {
          queryParams.append('date', 'monthly')
          queryParams.append('month', selectedMonth.toString())
          queryParams.append('year', selectedYear.toString())
        } else if (date === 'yearly') {
          queryParams.append('date', 'yearly')
          queryParams.append('year', selectedYear.toString())
        } else {
          queryParams.append('date', date)
        }

        queryParams.append('reportType', 'prayer')
        if (classFilter !== 'all') {
          // ✅ FIXED: Find the original class name from availableClasses instead of transforming
          const originalClassName = availableClasses.find(cls => cls.value === classFilter)?.label
          if (originalClassName) {
            queryParams.append('class', originalClassName)
          }
        }

        // ✅ SMART CACHE: Only force fresh data when explicitly requested (manual refresh)
        // Let write-through cache strategy handle real-time updates automatically
        const isRealTimeReport = ['today', 'yesterday'].includes(date)

        // Use the forceRefresh parameter to determine if this is a manual refresh
        const isManualRefresh = forceRefresh

        if (isManualRefresh) {
          // Manual refresh: Force fresh data and bypass cache
          queryParams.append('force_fresh', 'true')
          queryParams.append('_t', Date.now().toString())
        } else if (isRealTimeReport) {
          // Real-time reports: Use cache with 2-minute browser cache busting
          queryParams.append('_t', Math.floor(Date.now() / 120000).toString()) // 2-minute intervals
        } else {
          // Historical reports: Use cache with 10-minute browser cache busting
          queryParams.append('_t', Math.floor(Date.now() / 600000).toString()) // 10-minute intervals
        }

        const response = await fetch(`/api/absence/reports?${queryParams.toString()}`, {
          headers: isManualRefresh
            ? {
                // Manual refresh: No browser cache
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                Pragma: 'no-cache',
                Expires: '0',
              }
            : isRealTimeReport
              ? {
                  // Real-time: Short browser cache, let server cache handle freshness
                  'Cache-Control': 'max-age=60', // 1-minute browser cache
                }
              : {
                  // Historical: Longer browser cache
                  'Cache-Control': 'max-age=300', // 5-minute browser cache
                },
        })

        if (!response.ok) {
          throw new Error(
            `Failed to fetch prayer reports: ${response.status} ${response.statusText}`
          )
        }

        const data = await response.json()

        if (!Array.isArray(data)) {
          throw new Error('Invalid data structure received from API')
        }

        console.log(`📥 Received ${data.length} reports for date filter: "${date}"`)
        console.log('📊 Sample data structure:', data.slice(0, 2))

        setReports(data)

        // Calculate stats and trend data
        const calculatedStats = calculatePrayerStats(data, date)
        setStats(calculatedStats)

        try {
          console.log(
            `🎯 About to generate trend data for date: "${date}" with ${data.length} reports`
          )

          // ✅ CLEAN ARCHITECTURE: Use new reports system for monthly and yearly charts
          let calculatedTrendData: PrayerTrendData[]
          if (date === 'monthly' || date === 'yearly') {
            console.log(
              `🎯 Using new reports system for ${date} chart with class filter: ${classFilter}`
            )
            calculatedTrendData = await generateTrendDataFromRealDates(
              date,
              selectedMonth,
              selectedYear,
              classFilter,
              availableClasses,
              data
            )
          } else {
            // For today/yesterday, use synchronous function
            calculatedTrendData = generateTrendData(
              date,
              data // Pass the actual reports data for real analysis
            )
          }

          console.log(`📈 Generated trend data:`, calculatedTrendData)
          console.log(`📊 Trend data length: ${calculatedTrendData.length}`)
          setTrendData(calculatedTrendData)
        } catch (error) {
          console.error('❌ Error generating trend data:', error)
          setTrendData([]) // Set empty array as fallback
        }

        if (data.length === 0) {
          toast({
            title: 'Tidak ada data',
            description: `Tidak ada data laporan shalat untuk ${date === 'today' ? 'hari ini' : date}`,
            variant: 'default',
          })
        }
      } catch (error) {
        toast({
          title: 'Gagal memuat laporan shalat',
          description: 'Terjadi kesalahan saat mengambil data laporan',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    },
    [date, classFilter, selectedMonth, selectedYear, toast]
  )

  // Role-based access control
  useEffect(() => {
    if (!sessionLoading && admin) {
      if (
        admin.role !== 'super_admin' &&
        admin.role !== 'admin' &&
        admin.role !== 'teacher' &&
        admin.role !== 'receptionist'
      ) {
        router.push('/admin/home')
        return
      }
      fetchClasses()
    }
  }, [admin, sessionLoading, router, fetchClasses])

  // Sorting functions
  const handleSort = (key: string) => {
    // If clicking on the same column, toggle direction
    if (sortConfig && sortConfig.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'ascending' ? 'descending' : 'ascending',
      })
    } else {
      // Default to ascending for first click
      setSortConfig({ key, direction: 'ascending' })
    }
  }

  const renderSortIndicator = (key: string) => {
    if (sortConfig?.key !== key) {
      return <span className="ml-1 text-gray-400">↕</span>
    }
    return <span className="ml-1">{sortConfig.direction === 'ascending' ? '↑' : '↓'}</span>
  }

  // ✅ OPTIMIZED: Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [classFilter, date, searchQuery, selectedMonth, selectedYear, sortConfig])

  // ✅ OPTIMIZED: Load data when filters change
  useEffect(() => {
    if (
      admin &&
      (admin.role === 'super_admin' ||
        admin.role === 'admin' ||
        admin.role === 'teacher' ||
        admin.role === 'receptionist')
    ) {
      fetchReports()
    }
  }, [admin, fetchReports])

  // Modern Loading Component with Skeleton Screens
  const LoadingSkeleton = () => (
    <div className="space-y-6 duration-500 animate-in fade-in-50">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          <Skeleton className="h-4 w-96 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          <Skeleton className="h-10 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
        </div>
      </div>

      {/* Filter Controls Skeleton */}
      <div className="flex flex-col gap-4 md:flex-row">
        <Skeleton className="h-10 w-full animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 md:w-48" />
        <Skeleton className="h-10 w-full animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 md:w-48" />
        <Skeleton className="h-10 flex-1 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-3 w-12 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                </div>
                <Skeleton className="h-12 w-12 animate-pulse rounded-full bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Chart Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-6 w-48 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
              <Skeleton className="h-4 w-64 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Chart Area */}
            <div className="h-80 w-full animate-pulse rounded-lg bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />

            {/* Chart Legend */}
            <div className="flex justify-center gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton className="h-3 w-3 animate-pulse rounded-full bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  <Skeleton className="h-4 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            <Skeleton className="h-4 w-24 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-6 gap-4 border-b pb-2">
              {[...Array(6)].map((_, i) => (
                <Skeleton
                  key={i}
                  className="h-4 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
                />
              ))}
            </div>

            {/* Table Rows */}
            {[...Array(8)].map((_, i) => (
              <div key={i} className="grid grid-cols-6 gap-4 py-2">
                {[...Array(6)].map((_, j) => (
                  <Skeleton
                    key={j}
                    className="h-4 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200"
                  />
                ))}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Loading Indicator */}
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-3 text-gray-500">
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
          <span className="text-sm font-medium">Memuat data laporan shalat...</span>
        </div>
      </div>
    </div>
  )

  // Show loading state
  if (sessionLoading || !admin) {
    return <LoadingSkeleton />
  }

  // Access denied for non-authorized roles
  if (admin.role !== 'super_admin' && admin.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">Akses Ditolak</h2>
          <p className="text-gray-600">Anda tidak memiliki izin untuk mengakses laporan shalat.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analitik Shalat</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Dashboard analitik kehadiran shalat siswa
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => fetchReports(true)} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            Refresh
          </Button>
          <PrayerReportExportButton
            rawData={reports}
            availableClasses={availableClasses.map(cls => ({
              id: cls.value,
              name: cls.label,
            }))}
            buttonText="Unduh Laporan Shalat"
            buttonVariant="default"
            className="bg-green-600 hover:bg-green-700"
          />
        </div>
      </div>

      {/* Report Type Selector */}
      <div className="mb-6 flex flex-wrap gap-2">
        <Button
          variant={!['monthly', 'yearly'].includes(date) ? 'default' : 'outline'}
          onClick={() => setDate('today')}
          className="whitespace-nowrap"
        >
          📊 Laporan Harian
        </Button>
        <Button
          variant={date === 'monthly' ? 'default' : 'outline'}
          onClick={() => setDate('monthly')}
          className="whitespace-nowrap"
        >
          📅 Laporan Bulanan
        </Button>
        <Button
          variant={date === 'yearly' ? 'default' : 'outline'}
          onClick={() => setDate('yearly')}
          className="whitespace-nowrap"
        >
          📈 Laporan Tahunan
        </Button>
      </div>

      {/* Daily Report Filters - Only show for daily reports */}
      {!['monthly', 'yearly'].includes(date) && (
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <Select value={classFilter} onValueChange={setClassFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Pilih Kelas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kelas</SelectItem>
              {isLoadingClasses ? (
                <SelectItem value="loading" disabled>
                  Memuat kelas...
                </SelectItem>
              ) : (
                availableClasses.map(cls => (
                  <SelectItem key={cls.value} value={cls.value}>
                    {cls.label}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          <Select value={date} onValueChange={setDate}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Periode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hari Ini</SelectItem>
              <SelectItem value="yesterday">Kemarin</SelectItem>
            </SelectContent>
          </Select>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Cari siswa..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}

      {/* Monthly/Yearly Report Filters */}
      {['monthly', 'yearly'].includes(date) && (
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <Select value={classFilter} onValueChange={setClassFilter}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="Pilih Kelas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Kelas</SelectItem>
              {isLoadingClasses ? (
                <SelectItem value="loading" disabled>
                  Memuat kelas...
                </SelectItem>
              ) : (
                availableClasses.map(cls => (
                  <SelectItem key={cls.value} value={cls.value}>
                    {cls.label}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>

          {date === 'monthly' && (
            <Select
              value={selectedMonth.toString()}
              onValueChange={value => setSelectedMonth(parseInt(value))}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Pilih Bulan" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Januari</SelectItem>
                <SelectItem value="2">Februari</SelectItem>
                <SelectItem value="3">Maret</SelectItem>
                <SelectItem value="4">April</SelectItem>
                <SelectItem value="5">Mei</SelectItem>
                <SelectItem value="6">Juni</SelectItem>
                <SelectItem value="7">Juli</SelectItem>
                <SelectItem value="8">Agustus</SelectItem>
                <SelectItem value="9">September</SelectItem>
                <SelectItem value="10">Oktober</SelectItem>
                <SelectItem value="11">November</SelectItem>
                <SelectItem value="12">Desember</SelectItem>
              </SelectContent>
            </Select>
          )}

          {date === 'yearly' && (
            <Select
              value={selectedYear.toString()}
              onValueChange={value => setSelectedYear(parseInt(value))}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Pilih Tahun" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2025">2025</SelectItem>
                <SelectItem value="2026">2026</SelectItem>
                <SelectItem value="2027">2027</SelectItem>
                <SelectItem value="2028">2028</SelectItem>
                <SelectItem value="2029">2029</SelectItem>
                <SelectItem value="2030">2030</SelectItem>
              </SelectContent>
            </Select>
          )}

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Cari siswa..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      )}

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {isLoading ? (
          // Loading state for KPI cards
          [...Array(4)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-8 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="h-4 w-20 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                    <Skeleton className="h-3 w-16 animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                  </div>
                  <Skeleton className="h-12 w-12 animate-pulse rounded-full bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <>
            <Card className="border-l-4 border-l-blue-500 transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
                    <div className="text-sm font-medium text-gray-600">Total Siswa</div>
                    <div className="mt-1 text-xs text-gray-500">Siswa aktif</div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500 transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-green-600">{stats.zuhr}</div>
                    <div className="text-sm font-medium text-gray-600">Shalat Zuhur</div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                    <UserCheck className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-orange-500 transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-orange-600">{stats.asr}</div>
                    <div className="text-sm font-medium text-gray-600">Shalat Asr</div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
                    <Target className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-yellow-500 transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-3xl font-bold text-yellow-600">{stats.ijin}</div>
                    <div className="text-sm font-medium text-gray-600">Ijin</div>
                  </div>
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
                    <UserCheck className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Prayer Trend Chart - Hide on mobile */}
      <div className="hidden md:block">
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  Tren Kehadiran Shalat
                </CardTitle>
                <p className="text-sm text-gray-500">
                  {date === 'today' || date === 'yesterday'
                    ? 'Grafik per jam berdasarkan timestamp aktual dari database siswa (07:00-17:00 WITA)'
                    : date === 'monthly'
                      ? 'Grafik mingguan untuk bulan yang dipilih berdasarkan data shalat'
                      : 'Grafik bulanan untuk tahun yang dipilih berdasarkan data shalat'}
                </p>
              </div>
              <div className="text-gray-400">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <circle cx="12" cy="6" r="1" />
                  <circle cx="12" cy="12" r="1" />
                  <circle cx="12" cy="18" r="1" />
                </svg>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            {(() => {
              console.log(
                `🎨 Chart render check - isLoading: ${isLoading}, trendData.length: ${trendData.length}`
              )
              console.log(`📊 Current trendData:`, trendData)
              return null
            })()}
            {isLoading ? (
              <div className="h-80 w-full animate-pulse rounded-lg bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200" />
            ) : trendData.length === 0 ? (
              <div className="flex h-80 items-center justify-center text-gray-500">
                <div className="text-center">
                  <p>Tidak ada data tren untuk ditampilkan</p>
                  <p className="mt-2 text-sm">
                    Chart menampilkan distribusi waktu berdasarkan timestamp real dari database
                  </p>
                  <p className="mt-1 text-xs text-gray-400">
                    Hanya data dengan timestamp aktual yang akan ditampilkan
                  </p>
                  <p className="mt-2 text-xs text-red-500">
                    Debug: trendData.length = {trendData.length}, date = {date}
                  </p>
                </div>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  {/* Use LineChart for all periods including yearly (trend chart) */}
                  <LineChart data={trendData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                    <defs>
                      <linearGradient id="zuhrGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#22c55e" stopOpacity={0.1} />
                        <stop offset="100%" stopColor="#22c55e" stopOpacity={0} />
                      </linearGradient>
                      <linearGradient id="asrGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#f97316" stopOpacity={0.1} />
                        <stop offset="100%" stopColor="#f97316" stopOpacity={0} />
                      </linearGradient>
                      <linearGradient id="ijinGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#eab308" stopOpacity={0.1} />
                        <stop offset="100%" stopColor="#eab308" stopOpacity={0} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                    <XAxis
                      dataKey="date"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#94a3b8' }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#94a3b8' }}
                      allowDecimals={false}
                      domain={[0, (dataMax: number) => Math.max(5, Math.ceil(dataMax * 1.1))]}
                    />
                    <Tooltip
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          const zuhrValue = payload.find(p => p.dataKey === 'zuhr')?.value || 0
                          const asrValue = payload.find(p => p.dataKey === 'asr')?.value || 0
                          const ijinValue = payload.find(p => p.dataKey === 'ijin')?.value || 0
                          return (
                            <div className="rounded-lg bg-gray-900 px-3 py-2 text-white shadow-lg">
                              <p className="font-medium">{label} WITA</p>
                              <p className="mb-1 text-xs text-gray-300">Jumlah siswa yang absen:</p>
                              <p className="text-sm">
                                <span className="mr-2 inline-block h-2 w-2 rounded-full bg-green-500"></span>
                                Shalat Zuhur: {zuhrValue} siswa
                              </p>
                              <p className="text-sm">
                                <span className="mr-2 inline-block h-2 w-2 rounded-full bg-orange-500"></span>
                                Shalat Asr: {asrValue} siswa
                              </p>
                              <p className="text-sm">
                                <span className="mr-2 inline-block h-2 w-2 rounded-full bg-yellow-500"></span>
                                Ijin: {ijinValue} siswa
                              </p>
                            </div>
                          )
                        }
                        return null
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="zuhr"
                      stroke="#22c55e"
                      strokeWidth={2}
                      dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, fill: '#22c55e', strokeWidth: 2, stroke: '#fff' }}
                    />
                    <Line
                      type="monotone"
                      dataKey="asr"
                      stroke="#f97316"
                      strokeWidth={2}
                      dot={{ fill: '#f97316', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, fill: '#f97316', strokeWidth: 2, stroke: '#fff' }}
                    />
                    <Line
                      type="monotone"
                      dataKey="ijin"
                      stroke="#eab308"
                      strokeWidth={2}
                      dot={{ fill: '#eab308', strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, fill: '#eab308', strokeWidth: 2, stroke: '#fff' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Student Data Matrix - Moved to Bottom */}
      <Card>
        <CardHeader>
          <CardTitle>Data Siswa Shalat ({filteredReports.length} siswa)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No</TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('uniqueCode')}
                  >
                    Kode Siswa {renderSortIndicator('uniqueCode')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('name')}
                  >
                    Nama {renderSortIndicator('name')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('className')}
                  >
                    Kelas {renderSortIndicator('className')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('zuhr')}
                  >
                    Zuhur {renderSortIndicator('zuhr')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('asr')}
                  >
                    Asr {renderSortIndicator('asr')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('dismissal')}
                  >
                    Pulang {renderSortIndicator('dismissal')}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-800"
                    onClick={() => handleSort('ijin')}
                  >
                    Ijin {renderSortIndicator('ijin')}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-12" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : paginationData.paginatedReports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="py-8 text-center text-gray-500">
                      Tidak ada data siswa ditemukan
                    </TableCell>
                  </TableRow>
                ) : (
                  paginationData.paginatedReports.map((report, index) => {
                    const globalIndex = paginationData.startIndex + index + 1
                    const isAggregatedData = ['monthly', 'yearly'].includes(date)
                    const showTooltips = shouldShowTimeTooltips(date)

                    let zuhrDisplay: string,
                      asrDisplay: string,
                      dismissalDisplay: string,
                      ijinDisplay: string
                    if (['today', 'yesterday'].includes(date)) {
                      zuhrDisplay = report.zuhr ? '✓' : '✗'
                      asrDisplay = report.asr ? '✓' : '✗'
                      dismissalDisplay = report.dismissal ? '✓' : '✗'
                      ijinDisplay = report.ijin ? 'Ijin' : '-'
                    } else if (isAggregatedData && (report as any).aggregatedCounts) {
                      const counts = (report as any).aggregatedCounts
                      zuhrDisplay = counts.zuhr > 0 ? `${counts.zuhr} hari` : '-'
                      asrDisplay = counts.asr > 0 ? `${counts.asr} hari` : '-'
                      dismissalDisplay = counts.dismissal > 0 ? `${counts.dismissal} total` : '-'
                      ijinDisplay = counts.ijin > 0 ? `${counts.ijin} hari` : '-'
                    } else {
                      // Default values for edge cases
                      zuhrDisplay = '-'
                      asrDisplay = '-'
                      dismissalDisplay = '-'
                      ijinDisplay = '-'
                    }

                    return (
                      <TableRow key={report.uniqueCode}>
                        <TableCell>{globalIndex}</TableCell>
                        <TableCell className="font-mono text-sm">{report.uniqueCode}</TableCell>
                        <TableCell className="font-medium">{report.name}</TableCell>
                        <TableCell>{report.className}</TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={zuhrDisplay}
                            timeValue={report.zuhrTime}
                            variant={
                              zuhrDisplay === '✓'
                                ? 'success'
                                : zuhrDisplay === '✗'
                                  ? 'danger'
                                  : 'secondary'
                            }
                            attendanceType="Shalat Zuhur"
                            showTooltip={showTooltips}
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={asrDisplay}
                            timeValue={report.asrTime}
                            variant={
                              asrDisplay === '✓'
                                ? 'success'
                                : asrDisplay === '✗'
                                  ? 'danger'
                                  : 'secondary'
                            }
                            attendanceType="Shalat Asr"
                            showTooltip={showTooltips}
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={dismissalDisplay}
                            timeValue={report.dismissalTime}
                            variant={
                              dismissalDisplay === '✓'
                                ? 'success'
                                : dismissalDisplay === '✗'
                                  ? 'danger'
                                  : 'secondary'
                            }
                            attendanceType="Pulang"
                            showTooltip={showTooltips}
                          />
                        </TableCell>
                        <TableCell>
                          <AttendanceTimeTooltip
                            displayValue={ijinDisplay}
                            timeValue={report.ijinTime}
                            variant={ijinDisplay === 'Ijin' ? 'warning' : 'secondary'}
                            attendanceType="Ijin"
                            showTooltip={showTooltips}
                            reason={
                              report.ijin && (report as any).reason
                                ? (report as any).reason
                                : undefined
                            }
                          />
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {paginationData.totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Menampilkan {paginationData.startIndex + 1}-
                {Math.min(paginationData.endIndex, filteredReports.length)} dari{' '}
                {filteredReports.length} siswa
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      className={
                        currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, paginationData.totalPages) }, (_, i) => {
                    let pageNum
                    if (paginationData.totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= paginationData.totalPages - 2) {
                      pageNum = paginationData.totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => setCurrentPage(pageNum)}
                          isActive={currentPage === pageNum}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        setCurrentPage(Math.min(paginationData.totalPages, currentPage + 1))
                      }
                      className={
                        currentPage === paginationData.totalPages
                          ? 'pointer-events-none opacity-50'
                          : 'cursor-pointer'
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
