# 🎓 Panduan Data Realistis SMK 3 Banjarmasin

## 📋 Overview

Dokumentasi ini menjelaskan cara menggunakan data dummy realistis yang dibuat khusus untuk testing aplikasi absensi sholat SMK 3 Banjarmasin. Data ini mencakup **3000 siswa** dengan nama Indonesia yang realistis dan **6 bulan data absensi** yang menyerupai kondisi nyata sekolah.

## 🚀 Cara Load Data Realistis

### Metode Otomatis (Recommended)

```bash
# 1. Pastikan container PostgreSQL berjalan
docker compose -f docker-compose.dev.yml up -d

# 2. Berikan permission execute
chmod +x scripts/load-complete-realistic-data.sh

# 3. Jalankan script (akan memakan waktu 5-10 menit)
./scripts/load-complete-realistic-data.sh
```

### Metode Manual

```bash
# 1. Fix constraints terlebih dahulu
docker exec -i shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev < scripts/fix-constraints-for-dev.sql

# 2. Load data realistis
docker exec -i shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev < scripts/generate-realistic-dummy-data.sql
```

## 📊 Data yang Dibuat

### 👥 Students (3000 siswa)

- **Nama**: Kombinasi nama depan dan belakang Indonesia yang umum di Kalimantan Selatan
- **NIS**: Format tahun + nomor urut (contoh: 20220001, 20220002)
- **Username**: nama_depan + 4 digit terakhir NIS (contoh: muhammad0001)
- **Password**: `student123` (untuk semua siswa)
- **Gender**: Distribusi 55% laki-laki, 45% perempuan
- **WhatsApp**: Format Indonesia 628xxxxxxxxxx
- **Kelas**: Tersebar merata di 15 kelas (200 siswa per kelas)

### 🏫 Distribusi Kelas

| Tingkat   | Jurusan | Kelas        | Jumlah Siswa   |
| --------- | ------- | ------------ | -------------- |
| X         | IPA     | 1, 2, 3      | 600 siswa      |
| X         | IPS     | 1, 2         | 400 siswa      |
| XI        | IPA     | 1, 2, 3      | 600 siswa      |
| XI        | IPS     | 1, 2         | 400 siswa      |
| XII       | IPA     | 1, 2, 3      | 600 siswa      |
| XII       | IPS     | 1, 2         | 400 siswa      |
| **Total** |         | **15 kelas** | **3000 siswa** |

### 👨‍💼 Staff & Admin

- **Super Admin**: username `superadmin`
- **Kepala Sekolah**: username `kepsek`
- **Wakil Kepala Sekolah**: username `wakasek`
- **Staff TU**: username `stafftu`
- **Guru PAI**: username `guru_pai`
- **Guru BK**: username `guru_bk`
- **Wali Kelas**: username `wali_12ipa1`
- **Resepsionis**: username `resepsionis`

Password untuk semua staff: `student123`

### 📈 Data Absensi (6 Bulan)

#### Pola Kehadiran Realistis

- **Base attendance**: 85% siswa hadir setiap hari
- **Monday effect**: -10% kehadiran (efek post-weekend)
- **Friday effect**: -5% kehadiran (efek pre-weekend)
- **Exam periods**: -15% kehadiran (bulan 6 & 12)
- **Weekend**: Tidak ada data (sekolah libur)
- **Holidays**: Tanggal 1 setiap bulan (libur nasional)

#### Jenis Absensi

| Tipe Absensi   | Waktu       | Persentase Kehadiran | Keterangan                |
| -------------- | ----------- | -------------------- | ------------------------- |
| **Entry**      | 07:00-07:30 | 80%                  | Masuk sekolah tepat waktu |
| **Late Entry** | 07:30-08:00 | 20%                  | Terlambat masuk           |
| **Zuhr**       | 11:30-12:30 | 90%                  | Sholat Dzuhur             |
| **Asr**        | 14:30-15:30 | 85%                  | Sholat Ashar              |
| **Pulang**     | 15:00-16:00 | 95%                  | Pulang sekolah            |
| **Ijin**       | 08:00       | 18% dari absent      | Izin dengan keterangan    |
| **Sick**       | 08:00       | 12% dari absent      | Sakit                     |

#### Pattern Realistic

- Senin: Kehadiran lebih rendah (post-weekend syndrome)
- Jumat: Kehadiran agak rendah (pre-weekend effect)
- Periode ujian: Kehadiran menurun drastis
- Siswa yang absen: 30% memberikan keterangan (izin/sakit)

## 🧪 Testing Scenarios

### 1. Login Testing

```bash
# Login sebagai siswa
Username: muhammad0001
Password: student123

# Login sebagai admin
Username: superadmin
Password: student123

# Login sebagai guru
Username: guru_pai
Password: student123
```

### 2. Attendance Queries untuk Testing

```sql
-- Cek siswa per kelas
SELECT c.name, COUNT(u.id) as siswa
FROM classes c
LEFT JOIN users u ON c.id = u.class_id AND u.role = 'student'
GROUP BY c.name
ORDER BY c.name;

-- Absensi hari ini
SELECT type, COUNT(*) as total
FROM absences
WHERE DATE(recorded_at) = CURRENT_DATE
GROUP BY type;

-- Tingkat kehadiran per kelas (last 7 days)
SELECT
    c.name as kelas,
    COUNT(DISTINCT u.unique_code) as total_siswa,
    COUNT(DISTINCT a.unique_code) as siswa_hadir,
    ROUND(COUNT(DISTINCT a.unique_code) * 100.0 / COUNT(DISTINCT u.unique_code), 2) as persentase_kehadiran
FROM classes c
JOIN users u ON c.id = u.class_id AND u.role = 'student'
LEFT JOIN absences a ON u.unique_code = a.unique_code
    AND a.recorded_at >= CURRENT_DATE - INTERVAL '7 days'
    AND a.type IN ('Entry', 'Late Entry')
GROUP BY c.id, c.name
ORDER BY persentase_kehadiran DESC;

-- Siswa paling rajin sholat (Zuhr + Asr)
SELECT
    u.name,
    c.name as kelas,
    COUNT(a.id) as total_sholat
FROM users u
JOIN classes c ON u.class_id = c.id
JOIN absences a ON u.unique_code = a.unique_code
WHERE a.type IN ('Zuhr', 'Asr')
    AND a.recorded_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY u.unique_code, u.name, c.name
ORDER BY total_sholat DESC
LIMIT 20;

-- Pattern absensi per hari dalam seminggu
SELECT
    EXTRACT(DOW FROM recorded_at) as hari_numeric,
    CASE EXTRACT(DOW FROM recorded_at)
        WHEN 0 THEN 'Minggu'
        WHEN 1 THEN 'Senin'
        WHEN 2 THEN 'Selasa'
        WHEN 3 THEN 'Rabu'
        WHEN 4 THEN 'Kamis'
        WHEN 5 THEN 'Jumat'
        WHEN 6 THEN 'Sabtu'
    END as hari,
    COUNT(*) as total_absensi,
    COUNT(DISTINCT unique_code) as siswa_unik
FROM absences
WHERE type = 'Entry'
    AND recorded_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY EXTRACT(DOW FROM recorded_at)
ORDER BY hari_numeric;
```

### 3. Sample Students untuk Testing

| Nama             | Username     | NIS      | Kelas   | Gender |
| ---------------- | ------------ | -------- | ------- | ------ |
| Muhammad Pratama | muhammad0001 | 20220001 | X IPA 1 | Male   |
| Siti Sari        | siti0002     | 20220002 | X IPA 1 | Female |
| Ahmad Wijaya     | ahmad0003    | 20220003 | X IPA 1 | Male   |
| Dewi Kusuma      | dewi0004     | 20220004 | X IPA 1 | Female |
| Rizki Perdana    | rizki0005    | 20220005 | X IPA 1 | Male   |

_Gunakan username + password `student123` untuk login_

## 📊 Analytics & Reporting Testing

### Dashboard Metrics

Data ini memungkinkan testing untuk berbagai metrics:

1. **Attendance Rate**: Per kelas, per hari, per minggu
2. **Prayer Participation**: Zuhr vs Asr attendance
3. **Punctuality**: On-time vs Late Entry ratio
4. **Absence Patterns**: Sick vs Permission trends
5. **Class Comparison**: Performance between classes
6. **Weekly Trends**: Monday effect, Friday effect
7. **Gender Analysis**: Male vs Female attendance patterns

### Report Examples

```sql
-- Monthly attendance report
SELECT
    DATE_TRUNC('month', recorded_at) as bulan,
    type,
    COUNT(*) as total,
    COUNT(DISTINCT unique_code) as siswa_unik
FROM absences
GROUP BY DATE_TRUNC('month', recorded_at), type
ORDER BY bulan DESC, total DESC;

-- Class ranking by prayer attendance
SELECT
    c.name as kelas,
    COUNT(CASE WHEN a.type = 'Zuhr' THEN 1 END) as zuhr_count,
    COUNT(CASE WHEN a.type = 'Asr' THEN 1 END) as asr_count,
    COUNT(CASE WHEN a.type IN ('Zuhr', 'Asr') THEN 1 END) as total_prayer,
    COUNT(DISTINCT u.unique_code) as total_siswa,
    ROUND(COUNT(CASE WHEN a.type IN ('Zuhr', 'Asr') THEN 1 END) * 100.0 /
          (COUNT(DISTINCT u.unique_code) * 2), 2) as prayer_participation_rate
FROM classes c
JOIN users u ON c.id = u.class_id AND u.role = 'student'
LEFT JOIN absences a ON u.unique_code = a.unique_code
    AND a.type IN ('Zuhr', 'Asr')
    AND a.recorded_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY c.id, c.name
ORDER BY prayer_participation_rate DESC;
```

## 🔄 Reset & Regenerate

### Reset Data

```bash
# Reset semua data
docker exec -it shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev -c "
TRUNCATE TABLE absences CASCADE;
TRUNCATE TABLE users CASCADE;
"

# Re-run migration
npm run db:migrate

# Reload realistic data
./scripts/load-complete-realistic-data.sh
```

### Regenerate dengan Parameter Berbeda

Edit file `scripts/generate-realistic-dummy-data.sql` untuk mengubah:

- Jumlah siswa (ubah loop `1..3000`)
- Periode data (ubah `INTERVAL '6 months'`)
- Attendance probability (ubah `0.85`)
- Prayer participation rate (ubah `0.90` untuk Zuhr, `0.85` untuk Asr)

## 🎯 Use Cases untuk Testing

### 1. Performance Testing

- **Large dataset**: 3000+ users, 100K+ records
- **Complex queries**: Join multiple tables
- **Date range queries**: 6 months of data
- **Aggregation**: COUNT, GROUP BY, time-based analytics

### 2. UI/UX Testing

- **Pagination**: Large student lists
- **Search**: Find students by name/NIS
- **Filtering**: By class, date, attendance type
- **Charts**: Attendance trends, prayer participation

### 3. Business Logic Testing

- **Attendance rules**: Late entry detection
- **Prayer time validation**: Within time windows
- **Class management**: Student distribution
- **Role permissions**: Admin vs student access

### 4. API Testing

- **Authentication**: Multiple user roles
- **CRUD operations**: Create, read, update attendance
- **Bulk operations**: Class-wise operations
- **Reporting endpoints**: Analytics APIs

## 🔍 Troubleshooting

### Error: Constraint violation

```bash
# Fix constraints
docker exec -i shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev < scripts/fix-constraints-for-dev.sql
```

### Error: Out of memory

```bash
# Reduce student count in generate script
# Edit scripts/generate-realistic-dummy-data.sql
# Change: FOR i IN 1..1500 LOOP  # Reduce to 1500 students
```

### Error: Generation too slow

```bash
# Monitor progress
docker logs -f shalatYuk-postgres-dev

# Check database activity
docker exec shalatYuk-postgres-dev top
```

---

## 🎉 Ready for Real Testing!

Dengan data realistis ini, Anda dapat:

✅ **Testing performa** dengan data volume besar
✅ **Testing UI** dengan nama-nama Indonesia yang real
✅ **Testing business logic** dengan pola absensi yang realistis
✅ **Testing reporting** dengan data 6 bulan
✅ **Demo ke stakeholder** dengan data yang meyakinkan

**Selamat testing! Data SMK 3 Banjarmasin sudah siap! 🚀**
