# 🎓 Complete Attendance Types - SMK 3 Banjarmasin

## 📋 Overview

Panduan lengkap untuk data realistis SMK 3 Banjarmasin yang mencakup **SEMUA 10 jenis absensi** sesuai dengan fitur aplikasi yang sebenarnya. Data ini dirancang untuk testing komprehensif dengan pola kehadiran yang realistis.

## 🚀 Quick Start

```bash
# Load complete realistic data dengan semua attendance types
chmod +x scripts/load-complete-realistic-data.sh
./scripts/load-complete-realistic-data.sh
```

## 📊 10 Attendance Types Lengkap

### 🏫 School Attendance (Kehadiran Sekolah)

| Type           | Indonesian      | Role Access  | Time Window | Description               |
| -------------- | --------------- | ------------ | ----------- | ------------------------- |
| **Entry**      | Masuk           | Teacher      | 07:00-07:30 | Masuk sekolah tepat waktu |
| **Late Entry** | <PERSON>su<PERSON> Terl<PERSON> | Receptionist | 07:30-08:00 | Masuk sekolah terlambat   |

### 🕌 Prayer Attendance (Kehadiran Sholat)

| Type     | Indonesian        | Role Access | Time Window  | Description             |
| -------- | ----------------- | ----------- | ------------ | ----------------------- |
| **Zuhr** | Shalat Zuhur      | Admin       | 11:30-12:30  | Sholat Dzuhur berjamaah |
| **Asr**  | Shalat Ashar      | Admin       | 14:30-15:30  | Sholat Ashar berjamaah  |
| **Ijin** | Izin Tidak Shalat | Admin       | Prayer times | Izin tidak ikut sholat  |

### 🏠 School Departure (Pulang Sekolah)

| Type       | Indonesian | Role Access | Time Window | Description    |
| ---------- | ---------- | ----------- | ----------- | -------------- |
| **Pulang** | Pulang     | Admin       | 15:00-16:00 | Pulang sekolah |

### 🏥 Absence Management (Manajemen Ketidakhadiran)

| Type                  | Indonesian        | Role Access  | Time Window | Description               |
| --------------------- | ----------------- | ------------ | ----------- | ------------------------- |
| **Excused Absence**   | Izin              | Receptionist | 08:00       | Izin dengan keterangan    |
| **Sick**              | Sakit             | Receptionist | 08:00       | Sakit dengan surat dokter |
| **Temporary Leave**   | Izin Sementara    | Receptionist | 08:00       | Izin sementara            |
| **Return from Leave** | Kembali dari Izin | Receptionist | 08:00       | Kembali dari izin         |

## 👥 Role-Based Access Control

### Super Admin

- **Access**: ALL attendance types
- **Login**: superadmin/student123
- **Permissions**: Full system access

### Admin (Prayer Management)

- **Access**: Zuhr, Asr, Pulang, Ijin
- **Login**: kepsek/student123, wakasek/student123, stafftu/student123
- **Focus**: Prayer attendance and school dismissal

### Teacher (School Entry)

- **Access**: Entry
- **Login**: guru_pai/student123, guru_bk/student123, wali_12ipa1/student123
- **Focus**: Morning school entry

### Receptionist (Absence Management)

- **Access**: Late Entry, Excused Absence, Temporary Leave, Return from Leave, Sick
- **Login**: resepsionis1/student123, resepsionis2/student123
- **Focus**: Late arrivals and absence documentation

### Student

- **Access**: QR Code display only
- **Login**: fajar0001/student123, surya0002/student123, etc.
- **Function**: Display QR for scanning

## 📈 Realistic Data Patterns

### Daily School Flow

```
07:00-07:30  Entry (85% students) - Teachers scan
07:30-08:00  Late Entry (15% students) - Receptionists scan
11:30-12:30  Zuhr Prayer (88% participation) - Admins scan
             Ijin (6% of attending students) - Admins scan
14:30-15:30  Asr Prayer (82% participation) - Admins scan
             Ijin (7% of attending students) - Admins scan
15:00-16:00  Pulang (96% students) - Admins scan
```

### Absence Documentation

```
Student Absent (15% daily):
├── 35% have formal documentation:
│   ├── 40% → Sick (Receptionist records)
│   ├── 25% → Excused Absence (Receptionist records)
│   ├── 20% → Temporary Leave (Receptionist records)
│   └── 15% → Return from Leave (Receptionist records)
└── 65% undocumented absence (no record)
```

### Weekly Patterns

- **Monday**: Lower attendance (70% vs 85%) - post-weekend effect
- **Tuesday-Thursday**: Normal attendance (85%)
- **Friday**: Slightly lower (80%) - pre-weekend effect
- **Weekend**: No school data

## 🧪 Comprehensive Testing Scenarios

### 1. Role Permission Testing

```bash
# Test admin can scan prayers
Login: kepsek/student123
Scanner: Set to "Zuhr" → Should work
Scanner: Set to "Entry" → Should be restricted

# Test teacher can scan entry
Login: guru_pai/student123
Scanner: Set to "Entry" → Should work
Scanner: Set to "Zuhr" → Should be restricted

# Test receptionist can manage absences
Login: resepsionis1/student123
Manual Entry: "Sick" → Should work
Scanner: Set to "Late Entry" → Should work
Scanner: Set to "Zuhr" → Should be restricted
```

### 2. QR Scanner Testing

```bash
# Different roles see different attendance type options
Admin: Zuhr, Asr, Pulang, Ijin
Teacher: Entry only
Receptionist: Late Entry, Excused Absence, Temporary Leave, Return from Leave, Sick
Student: QR display only
```

### 3. Manual Entry Testing

```bash
# Test manual entry for non-scan types
Receptionist can manually enter:
- Excused Absence (no QR scan needed)
- Sick leave (no QR scan needed)
- Temporary Leave
- Return from Leave
```

### 4. Reports & Analytics Testing

```sql
-- Test attendance type distribution
SELECT type, COUNT(*) as total,
       ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 1) as percentage
FROM absences
GROUP BY type
ORDER BY total DESC;

-- Test role-based data access
SELECT u.role, COUNT(a.id) as records_created
FROM users u
LEFT JOIN absences a ON u.unique_code = a.unique_code
WHERE u.role != 'student'
GROUP BY u.role;

-- Test daily flow patterns
SELECT
    EXTRACT(HOUR FROM recorded_at) as hour,
    type,
    COUNT(*) as count
FROM absences
WHERE DATE(recorded_at) = CURRENT_DATE - INTERVAL '1 day'
GROUP BY EXTRACT(HOUR FROM recorded_at), type
ORDER BY hour, count DESC;
```

## 📊 Expected Data Volume

Dengan 3000 siswa dan 4 bulan data:

```
Estimated Records:
├── Entry: ~204,000 records (85% × 3000 × 80 days)
├── Late Entry: ~36,000 records (15% × 3000 × 80 days)
├── Zuhr: ~211,000 records (88% × 3000 × 80 days)
├── Asr: ~197,000 records (82% × 3000 × 80 days)
├── Pulang: ~230,000 records (96% × 3000 × 80 days)
├── Ijin: ~35,000 records (prayer excuses)
├── Sick: ~8,000 records (absence documentation)
├── Excused Absence: ~5,000 records
├── Temporary Leave: ~4,000 records
└── Return from Leave: ~3,000 records

Total: ~933,000+ attendance records
```

## 🔧 Advanced Testing Features

### Performance Testing

- Large dataset (900K+ records)
- Complex role-based queries
- Time-based filtering
- Multi-type aggregations

### UI/UX Testing

- Role-specific interface elements
- Attendance type dropdowns
- Permission-based button visibility
- Real-time scanner feedback

### Business Logic Testing

- Time window validations
- Role permission enforcement
- Duplicate prevention
- Data consistency checks

### API Testing

- Role-based endpoint access
- Attendance type validation
- Bulk operations
- Real-time updates

## 🚨 Troubleshooting

### Error: "Attendance type not allowed"

```bash
# Check user role permissions
SELECT role FROM users WHERE username = 'your_username';

# Verify attendance type is available for role
# Refer to role access table above
```

### Error: "Enum value does not exist"

```bash
# Run enum fixes
docker exec -i shalatYuk-postgres-dev psql -U shalatdev -d shalat_yuk_dev < scripts/add-more-enum-values.sql
```

### Performance Issues

```bash
# Check record count
SELECT COUNT(*) FROM absences;

# If too many records, reduce student count in script
# Edit scripts/complete-realistic-data.sql
# Change: FOR i IN 1..1500 LOOP  -- Reduce to 1500 students
```

## 🎯 Real-World Testing Benefits

### Authentic School Environment

- **Complete workflow**: Entry → Prayer → Departure → Absence management
- **Role separation**: Different staff handle different attendance types
- **Time-based logic**: Realistic time windows for each activity
- **Permission system**: Proper access control testing

### Comprehensive Data Coverage

- **All attendance scenarios**: Present, late, absent, excused, sick
- **Prayer participation**: Realistic Islamic school patterns
- **Administrative workflows**: Complete absence documentation
- **Seasonal patterns**: Monday effects, weekend gaps, holiday handling

---

## 🎉 Ready for Production-Level Testing!

Dengan data lengkap ini, Anda dapat:

✅ **Test semua fitur aplikasi** dengan data yang realistis  
✅ **Validate role-based permissions** sesuai struktur sekolah nyata  
✅ **Performance testing** dengan volume data besar  
✅ **Demo ke stakeholder** dengan alur kerja yang meyakinkan  
✅ **Integration testing** untuk semua attendance types

**Selamat testing dengan data SMK 3 Banjarmasin yang lengkap! 🚀**
