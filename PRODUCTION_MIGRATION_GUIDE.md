# 🚀 Production Migration Guide - Add Reason Column

## 📋 Overview
This guide will help you migrate the production database to add the `reason` column to the `absences` table for storing attendance reasons.

## ⚠️ Pre-Migration Checklist

### 1. **Backup Production Database**
```bash
# Create a backup before migration
pg_dump $PRODUCTION_DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. **Verify Current Schema**
```bash
# Connect to production database and verify current absences table structure
psql $PRODUCTION_DATABASE_URL -c "\d+ absences"
```

Expected current structure (without reason column):
```
Column      | Type                     | Nullable | Default
------------|--------------------------|----------|--------
id          | integer                  | not null | nextval(...)
unique_code | character varying(36)    | not null |
type        | attendance_type          | not null |
recorded_at | timestamp without time zone | not null |
created_at  | timestamp without time zone | not null | now()
```

## 🔧 Migration Steps

### Step 1: Set Environment Variables
```bash
# Set your production database URL
export DATABASE_URL="your_production_database_url_here"
```

### Step 2: Run Drizzle Migration
```bash
# Navigate to project directory
cd /path/to/ShalatYuk

# Run the migration
npx drizzle-kit migrate
```

### Step 3: Verify Migration
```bash
# Check if reason column was added successfully
psql $DATABASE_URL -c "\d+ absences"
```

Expected structure after migration:
```
Column      | Type                     | Nullable | Default
------------|--------------------------|----------|--------
id          | integer                  | not null | nextval(...)
unique_code | character varying(36)    | not null |
type        | attendance_type          | not null |
recorded_at | timestamp without time zone | not null |
created_at  | timestamp without time zone | not null | now()
reason      | character varying(500)   | yes      |
```

### Step 4: Test Migration
```bash
# Test inserting a record with reason
psql $DATABASE_URL -c "
INSERT INTO absences (unique_code, type, recorded_at, reason) 
VALUES ('test-code-123', 'Sick', NOW(), 'Test reason for migration');
"

# Verify the record
psql $DATABASE_URL -c "
SELECT id, type, reason FROM absences WHERE unique_code = 'test-code-123';
"

# Clean up test record
psql $DATABASE_URL -c "
DELETE FROM absences WHERE unique_code = 'test-code-123';
"
```

## 📁 Migration Files

### Main Migration File
- **File**: `drizzle/migrations/0007_add_reason_column.sql`
- **Content**: `ALTER TABLE "absences" ADD COLUMN "reason" varchar(500);`

### Metadata Files
- **Journal**: `drizzle/migrations/meta/_journal.json`
- **Snapshot**: `drizzle/migrations/meta/0007_snapshot.json`

## 🔄 Rollback Plan (If Needed)

If something goes wrong, you can rollback:

```bash
# Rollback the migration (remove reason column)
psql $DATABASE_URL -c "ALTER TABLE absences DROP COLUMN IF EXISTS reason;"

# Restore from backup if needed
psql $DATABASE_URL < backup_YYYYMMDD_HHMMSS.sql
```

## ✅ Post-Migration Verification

### 1. Application Testing
- Deploy the updated application code
- Test manual entry with attendance types requiring reason:
  - "Izin Sementara" (Temporary Leave)
  - "Izin" (Excused Absence)  
  - "Sakit" (Sick)

### 2. Data Validation
```bash
# Check that existing records are not affected
psql $DATABASE_URL -c "
SELECT COUNT(*) as total_records, 
       COUNT(reason) as records_with_reason 
FROM absences;
"
```

### 3. Functionality Testing
- Test that reason is required for specific attendance types
- Test that reason is optional for other attendance types
- Verify reason is saved and retrieved correctly

## 🚨 Troubleshooting

### Issue: Migration Fails
```bash
# Check current migration status
npx drizzle-kit status

# If migration is partially applied, check database state
psql $DATABASE_URL -c "SELECT * FROM __drizzle_migrations ORDER BY id;"
```

### Issue: Column Already Exists
If the reason column already exists:
```bash
# Check if column exists
psql $DATABASE_URL -c "
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'absences' AND column_name = 'reason';
"

# If exists, mark migration as applied manually
psql $DATABASE_URL -c "
INSERT INTO __drizzle_migrations (hash, created_at) 
VALUES ('your_migration_hash', NOW());
"
```

## 📞 Support

If you encounter issues:
1. Check the backup was created successfully
2. Verify database connection
3. Check application logs after deployment
4. Test with a small subset of data first

## 🎯 Expected Impact

- **Zero Downtime**: This is an additive migration
- **Backward Compatible**: Existing functionality continues to work
- **Data Safe**: No existing data is modified
- **Performance**: Minimal impact (adding nullable column)

---

**Migration prepared by**: AI Assistant  
**Date**: 2025-06-27  
**Version**: Production Ready
