#!/bin/bash

# Fix Missing Asr Data Script
# This script clears cache and forces fresh data to resolve the missing Asr issue

echo "🔧 Fixing Missing Asr Data Issue..."
echo ""

# Configuration
BASE_URL="http://localhost:3000"
ADMIN_TOKEN="${ADMIN_TOKEN:-your_actual_token_here}"

if [ "$ADMIN_TOKEN" = "your_actual_token_here" ]; then
    echo "❌ Please set your admin token:"
    echo "   export ADMIN_TOKEN='your_actual_admin_token'"
    echo "   Then run this script again"
    exit 1
fi

echo "🧹 Step 1: Clearing prayer report cache..."

# Clear prayer cache with materialized view refresh
curl -s -X POST "$BASE_URL/api/admin/cache/clear" \
  -H "Content-Type: application/json" \
  -H "Cookie: admin_auth_token=$ADMIN_TOKEN" \
  -d '{"type": "prayer"}' | jq -r '.message // .error'

echo ""
echo "⏳ Step 2: Waiting 3 seconds for cache to clear..."
sleep 3

echo ""
echo "📊 Step 3: Requesting fresh prayer data..."

# Request fresh data
RESPONSE=$(curl -s "$BASE_URL/api/absence/reports?date=today&reportType=prayer&force_fresh=true&_t=$(date +%s)" \
  -H "Cookie: admin_auth_token=$ADMIN_TOKEN")

# Count total records
TOTAL_COUNT=$(echo "$RESPONSE" | jq '. | length')
echo "✅ Retrieved $TOTAL_COUNT student records"

# Count Asr records
ASR_COUNT=$(echo "$RESPONSE" | jq '[.[] | select(.attendanceRecords[]?.type == "Asr")] | length')
echo "🔍 Found $ASR_COUNT students with Asr attendance"

if [ "$ASR_COUNT" -gt 0 ]; then
    echo ""
    echo "🎉 SUCCESS: Asr data is now visible!"
    echo ""
    echo "📋 Students with Asr:"
    echo "$RESPONSE" | jq -r '.[] | select(.attendanceRecords[]?.type == "Asr") | "   - \(.name) (\(.className // "No class"))"'
else
    echo ""
    echo "❌ ISSUE PERSISTS: No Asr records found"
    echo ""
    echo "🔍 Sample of what we found:"
    echo "$RESPONSE" | jq -r '.[:5][] | "   - \(.name): \([.attendanceRecords[]?.type] | join(", "))"'
fi

echo ""
echo "🎯 Checking specific students from database:"

# Check specific students
STUDENT_1="7205df03-15ff-4f8b-a7aa-ec9aa2d90e78"
STUDENT_2="1bd574a7-fc36-4ec9-b9e0-bac11eb65b44"

for STUDENT in "$STUDENT_1" "$STUDENT_2"; do
    STUDENT_DATA=$(echo "$RESPONSE" | jq -r ".[] | select(.uniqueCode == \"$STUDENT\")")
    if [ -n "$STUDENT_DATA" ]; then
        STUDENT_NAME=$(echo "$STUDENT_DATA" | jq -r '.name')
        ASR_RECORD=$(echo "$STUDENT_DATA" | jq -r '.attendanceRecords[]? | select(.type == "Asr") | .recordedAt')
        echo "✅ $STUDENT: $STUDENT_NAME"
        if [ -n "$ASR_RECORD" ] && [ "$ASR_RECORD" != "null" ]; then
            echo "   Asr: $ASR_RECORD"
        else
            echo "   No Asr record found"
        fi
    else
        echo "❌ $STUDENT: Not found in results"
    fi
done

echo ""
echo "📝 Next Steps:"
if [ "$ASR_COUNT" -gt 0 ]; then
    echo "✅ Cache issue resolved! Asr data is now visible."
    echo "✅ The write-through cache should prevent this issue in the future."
else
    echo "❌ Issue persists. Possible causes:"
    echo "   1. Database timezone issue"
    echo "   2. Date range query problem"
    echo "   3. Attendance records not in expected format"
    echo "   4. Check server logs for errors"
fi

echo ""
echo "🔍 To debug further, check server logs for:"
echo "   - 'Fetching data from database'"
echo "   - 'WRITE-THROUGH: Cache updated'"
echo "   - Any error messages"
