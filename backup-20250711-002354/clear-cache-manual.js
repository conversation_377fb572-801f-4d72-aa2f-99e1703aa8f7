#!/usr/bin/env node

/**
 * Manual Cache Clear Script
 * Quick script to clear prayer report cache and verify data
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'

// Get admin token from environment or prompt user to set it
const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'your_actual_token_here'

async function clearCacheAndTest() {
  console.log('🧹 Clearing Prayer Report Cache...\n')

  // Step 1: Clear prayer cache
  try {
    const response = await fetch(`${BASE_URL}/api/admin/cache/clear`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      },
      body: JSON.stringify({
        type: 'prayer'
      })
    })

    if (response.ok) {
      const result = await response.json()
      console.log('✅ Cache cleared successfully:', result.message)
    } else {
      console.log('❌ Failed to clear cache:', response.status, response.statusText)
      if (response.status === 401) {
        console.log('🔐 Please update the admin token in the script')
      }
      return
    }
  } catch (error) {
    console.log('❌ Cache clear error:', error.message)
    return
  }

  // Step 2: Wait a moment
  console.log('\n⏳ Waiting 2 seconds...')
  await new Promise(resolve => setTimeout(resolve, 2000))

  // Step 3: Request fresh data
  console.log('\n📊 Requesting fresh prayer report data...')
  try {
    const response = await fetch(`${BASE_URL}/api/absence/reports?date=today&reportType=prayer&force_fresh=true&_t=${Date.now()}`, {
      headers: {
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      console.log(`✅ Retrieved ${data.length} student records`)

      // Check for Asr attendance
      const studentsWithAsr = data.filter(student =>
        student.attendanceRecords &&
        student.attendanceRecords.some(record => record.type === 'Asr')
      )

      console.log(`🔍 Found ${studentsWithAsr.length} students with Asr attendance`)

      if (studentsWithAsr.length > 0) {
        console.log('\n📋 Students with Asr:')
        studentsWithAsr.forEach((student, index) => {
          const asrRecord = student.attendanceRecords.find(record => record.type === 'Asr')
          console.log(`${index + 1}. ${student.name} (${student.className || 'No class'})`)
          console.log(`   Time: ${asrRecord.recordedAt}`)
          console.log(`   Code: ${student.uniqueCode}`)
        })
        console.log('\n🎉 SUCCESS: Asr data is now visible!')
      } else {
        console.log('\n❌ No Asr records found')
        console.log('\n🔍 Sample of what we found:')
        data.slice(0, 5).forEach((student, index) => {
          const types = student.attendanceRecords?.map(r => r.type).join(', ') || 'No attendance'
          console.log(`${index + 1}. ${student.name}: ${types}`)
        })
      }

      // Check specific students from your database
      const targetStudents = [
        '7205df03-15ff-4f8b-a7aa-ec9aa2d90e78',
        '1bd574a7-fc36-4ec9-b9e0-bac11eb65b44'
      ]

      console.log('\n🎯 Checking specific students from database:')
      targetStudents.forEach(uniqueCode => {
        const student = data.find(s => s.uniqueCode === uniqueCode)
        if (student) {
          const asrRecord = student.attendanceRecords?.find(r => r.type === 'Asr')
          console.log(`✅ ${uniqueCode}: ${student.name}`)
          if (asrRecord) {
            console.log(`   Asr: ${asrRecord.recordedAt}`)
          } else {
            console.log(`   No Asr record found`)
          }
        } else {
          console.log(`❌ ${uniqueCode}: Not found in results`)
        }
      })

    } else {
      console.log('❌ Failed to get fresh data:', response.status, response.statusText)
    }
  } catch (error) {
    console.log('❌ Data request error:', error.message)
  }

  console.log('\n📝 Instructions:')
  console.log('1. Update the admin token in this script with your actual token')
  console.log('2. If Asr data appears, the cache was the issue')
  console.log('3. If Asr data still missing, check database timezone or query logic')
  console.log('4. Check server logs for any error messages')
}

// Run the script
clearCacheAndTest().catch(console.error)
