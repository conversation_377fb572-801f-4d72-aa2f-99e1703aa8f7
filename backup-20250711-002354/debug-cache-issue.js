#!/usr/bin/env node

/**
 * Debug Cache Issue - Manual Cache Clear and Data Verification
 * This script helps debug why new Asr attendance data is not showing up
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'

async function debugCacheIssue() {
  console.log('🔍 Debugging Cache Issue for Missing Asr Data...\n')

  // Step 1: Clear all prayer report cache manually
  console.log('1️⃣ Clearing Prayer Report Cache...')
  try {
    const clearResponse = await fetch(`${BASE_URL}/api/admin/cache/clear`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'admin_auth_token=your_token_here', // Replace with actual token
      },
      body: JSON.stringify({
        pattern: 'absence:reports:prayer:*'
      })
    })

    if (clearResponse.ok) {
      console.log('✅ Prayer cache cleared successfully')
    } else {
      console.log(`❌ Failed to clear cache: ${clearResponse.status}`)
    }
  } catch (error) {
    console.log(`❌ Cache clear error: ${error.message}`)
  }

  // Step 2: Force fresh data request
  console.log('\n2️⃣ Requesting Fresh Data (force_fresh=true)...')
  try {
    const freshResponse = await fetch(`${BASE_URL}/api/absence/reports?date=today&reportType=prayer&force_fresh=true&_t=${Date.now()}`, {
      headers: {
        'Cookie': 'admin_auth_token=your_token_here', // Replace with actual token
      }
    })

    if (freshResponse.ok) {
      const data = await freshResponse.json()
      console.log(`✅ Fresh data retrieved: ${data.length} records`)
      
      // Check for Asr records
      const asrRecords = data.filter(record => 
        record.attendanceRecords && 
        record.attendanceRecords.some(att => att.type === 'Asr')
      )
      
      console.log(`🔍 Found ${asrRecords.length} students with Asr records`)
      
      if (asrRecords.length > 0) {
        console.log('📋 Students with Asr:')
        asrRecords.forEach(student => {
          const asrRecord = student.attendanceRecords.find(att => att.type === 'Asr')
          console.log(`   - ${student.name} (${student.uniqueCode}): ${asrRecord.recordedAt}`)
        })
      } else {
        console.log('❌ No Asr records found in fresh data!')
        console.log('🔍 Sample records:')
        data.slice(0, 3).forEach(student => {
          console.log(`   - ${student.name}: ${student.attendanceRecords?.map(att => att.type).join(', ') || 'No attendance'}`)
        })
      }
    } else {
      console.log(`❌ Failed to get fresh data: ${freshResponse.status}`)
    }
  } catch (error) {
    console.log(`❌ Fresh data error: ${error.message}`)
  }

  // Step 3: Check specific students from database
  console.log('\n3️⃣ Checking Specific Students...')
  const studentsToCheck = [
    '7205df03-15ff-4f8b-a7aa-ec9aa2d90e78',
    '1bd574a7-fc36-4ec9-b9e0-bac11eb65b44'
  ]

  for (const uniqueCode of studentsToCheck) {
    try {
      const studentResponse = await fetch(`${BASE_URL}/api/absence/check?uniqueCode=${uniqueCode}&date=today`, {
        headers: {
          'Cookie': 'admin_auth_token=your_token_here', // Replace with actual token
        }
      })

      if (studentResponse.ok) {
        const studentData = await studentResponse.json()
        console.log(`✅ Student ${uniqueCode}:`)
        console.log(`   Records: ${studentData.attendanceRecords?.map(att => `${att.type} (${att.recordedAt})`).join(', ') || 'None'}`)
      } else {
        console.log(`❌ Failed to check student ${uniqueCode}: ${studentResponse.status}`)
      }
    } catch (error) {
      console.log(`❌ Student check error for ${uniqueCode}: ${error.message}`)
    }
  }

  // Step 4: Test write-through cache
  console.log('\n4️⃣ Testing Write-Through Cache...')
  try {
    const writeThoughResponse = await fetch(`${BASE_URL}/api/admin/cache/write-through`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'admin_auth_token=your_token_here', // Replace with actual token
      },
      body: JSON.stringify({
        action: 'health'
      })
    })

    if (writeThoughResponse.ok) {
      const healthData = await writeThoughResponse.json()
      console.log('✅ Write-through cache health:', healthData.healthCheck?.status || 'Unknown')
    } else {
      console.log(`❌ Write-through health check failed: ${writeThoughResponse.status}`)
    }
  } catch (error) {
    console.log(`❌ Write-through test error: ${error.message}`)
  }

  // Step 5: Final verification
  console.log('\n5️⃣ Final Verification (normal request)...')
  try {
    const normalResponse = await fetch(`${BASE_URL}/api/absence/reports?date=today&reportType=prayer&_t=${Math.floor(Date.now() / 120000)}`, {
      headers: {
        'Cookie': 'admin_auth_token=your_token_here', // Replace with actual token
      }
    })

    if (normalResponse.ok) {
      const data = await normalResponse.json()
      const asrCount = data.filter(record => 
        record.attendanceRecords && 
        record.attendanceRecords.some(att => att.type === 'Asr')
      ).length
      
      console.log(`✅ Normal request: ${data.length} total records, ${asrCount} with Asr`)
      
      if (asrCount > 0) {
        console.log('🎉 SUCCESS: Asr data is now visible!')
      } else {
        console.log('❌ ISSUE PERSISTS: Asr data still not visible')
      }
    } else {
      console.log(`❌ Normal request failed: ${normalResponse.status}`)
    }
  } catch (error) {
    console.log(`❌ Final verification error: ${error.message}`)
  }

  console.log('\n📋 Summary:')
  console.log('1. If Asr data appears in fresh request but not normal request: Cache issue')
  console.log('2. If Asr data missing in both: Database query or timezone issue')
  console.log('3. Check server logs for write-through cache messages')
  console.log('4. Update the Cookie header with your actual admin token')
}

// Run the debug
debugCacheIssue().catch(console.error)
