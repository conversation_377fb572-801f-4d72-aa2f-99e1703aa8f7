#!/bin/bash

# Fix Monthly and Yearly Reports Script
# This script clears cache and tests monthly/yearly reports after timezone fix

echo "🗓️ Fixing Monthly and Yearly Reports..."
echo ""

# Configuration
BASE_URL="http://localhost:3000"
ADMIN_TOKEN="${ADMIN_TOKEN:-your_actual_token_here}"

if [ "$ADMIN_TOKEN" = "your_actual_token_here" ]; then
    echo "❌ Please set your admin token:"
    echo "   export ADMIN_TOKEN='your_actual_admin_token'"
    echo "   Then run this script again"
    exit 1
fi

echo "🧹 Step 1: Clearing all report cache..."

# Clear all cache
curl -s -X POST "$BASE_URL/api/admin/cache/clear" \
  -H "Content-Type: application/json" \
  -H "Cookie: admin_auth_token=$ADMIN_TOKEN" \
  -d '{"type": "all"}' | jq -r '.message // .error'

echo ""
echo "⏳ Step 2: Waiting 3 seconds for cache to clear..."
sleep 3

echo ""
echo "📊 Step 3: Testing Current Month Report..."

# Test current month
RESPONSE=$(curl -s "$BASE_URL/api/absence/reports?date=current-month&reportType=prayer&force_fresh=true&_t=$(date +%s)" \
  -H "Cookie: admin_auth_token=$ADMIN_TOKEN")

TOTAL_COUNT=$(echo "$RESPONSE" | jq '. | length')
ASR_COUNT=$(echo "$RESPONSE" | jq '[.[] | select(.attendanceRecords[]?.type == "Asr" or .aggregatedCounts.asr > 0 or .asr == true)] | length')

echo "✅ Current Month: $TOTAL_COUNT total records, $ASR_COUNT with Asr"

echo ""
echo "📊 Step 4: Testing Monthly Report (July 2025)..."

# Test specific month
RESPONSE=$(curl -s "$BASE_URL/api/absence/reports?date=monthly&month=7&year=2025&reportType=prayer&force_fresh=true&_t=$(date +%s)" \
  -H "Cookie: admin_auth_token=$ADMIN_TOKEN")

TOTAL_COUNT=$(echo "$RESPONSE" | jq '. | length')
ASR_COUNT=$(echo "$RESPONSE" | jq '[.[] | select(.attendanceRecords[]?.type == "Asr" or .aggregatedCounts.asr > 0 or .asr == true)] | length')

echo "✅ July 2025: $TOTAL_COUNT total records, $ASR_COUNT with Asr"

echo ""
echo "📊 Step 5: Testing Yearly Report (2025)..."

# Test yearly
RESPONSE=$(curl -s "$BASE_URL/api/absence/reports?date=yearly&year=2025&reportType=prayer&force_fresh=true&_t=$(date +%s)" \
  -H "Cookie: admin_auth_token=$ADMIN_TOKEN")

TOTAL_COUNT=$(echo "$RESPONSE" | jq '. | length')
ASR_COUNT=$(echo "$RESPONSE" | jq '[.[] | select(.attendanceRecords[]?.type == "Asr" or .aggregatedCounts.asr > 0 or .asr == true)] | length')

echo "✅ Year 2025: $TOTAL_COUNT total records, $ASR_COUNT with Asr"

echo ""
echo "🎯 Step 6: Checking specific students..."

# Check specific students in current month
STUDENT_1="7205df03-15ff-4f8b-a7aa-ec9aa2d90e78"
STUDENT_2="1bd574a7-fc36-4ec9-b9e0-bac11eb65b44"

RESPONSE=$(curl -s "$BASE_URL/api/absence/reports?date=current-month&reportType=prayer&force_fresh=true&_t=$(date +%s)" \
  -H "Cookie: admin_auth_token=$ADMIN_TOKEN")

for STUDENT in "$STUDENT_1" "$STUDENT_2"; do
    STUDENT_DATA=$(echo "$RESPONSE" | jq -r ".[] | select(.uniqueCode == \"$STUDENT\")")
    if [ -n "$STUDENT_DATA" ]; then
        STUDENT_NAME=$(echo "$STUDENT_DATA" | jq -r '.name')
        
        # Check for Asr in different formats
        ASR_RECORD=$(echo "$STUDENT_DATA" | jq -r '.attendanceRecords[]? | select(.type == "Asr") | .recordedAt')
        ASR_COUNT=$(echo "$STUDENT_DATA" | jq -r '.aggregatedCounts.asr // 0')
        ASR_BOOL=$(echo "$STUDENT_DATA" | jq -r '.asr // false')
        
        echo "✅ $STUDENT: $STUDENT_NAME"
        
        if [ -n "$ASR_RECORD" ] && [ "$ASR_RECORD" != "null" ]; then
            echo "   Asr Record: $ASR_RECORD"
        elif [ "$ASR_COUNT" != "0" ] && [ "$ASR_COUNT" != "null" ]; then
            echo "   Asr Count: $ASR_COUNT"
        elif [ "$ASR_BOOL" = "true" ]; then
            echo "   Asr: Found"
        else
            echo "   Asr: Not found"
        fi
    else
        echo "❌ $STUDENT: Not found in current month report"
    fi
done

echo ""
echo "📝 Results Summary:"
if [ "$ASR_COUNT" -gt 0 ]; then
    echo "✅ SUCCESS: Monthly and yearly reports now show Asr data!"
    echo "✅ Timezone fix for monthly/yearly reports is working"
else
    echo "❌ Issue persists in monthly/yearly reports"
    echo "🔍 Check server logs for timezone conversion messages"
fi

echo ""
echo "🔍 To verify the fix, check server logs for:"
echo "   - '📅 MONTHLY FILTER' messages"
echo "   - '📅 YEARLY FILTER' messages"
echo "   - UTC start/end times should be correct for WITA"
echo ""
echo "📅 Expected UTC ranges for WITA:"
echo "   - July 2025: 2025-06-30T16:00:00.000Z to 2025-07-31T15:59:59.999Z"
echo "   - Year 2025: 2024-12-31T16:00:00.000Z to 2025-12-31T15:59:59.999Z"
