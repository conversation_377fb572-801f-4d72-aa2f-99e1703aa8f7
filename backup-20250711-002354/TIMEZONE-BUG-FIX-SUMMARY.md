# 🐛 Timezone Bug Fix - Missing Asr Data Issue

## 🔍 **Root Cause Analysis**

Anda benar bahwa data Asr seharusnya ada tapi tidak muncul di reports. Set<PERSON><PERSON> analisis mendalam, saya menemukan **bug serius dalam timezone handling** yang menyebabkan database query menggunakan date range yang salah.

### **Data dari Database**
```
Record ID 5: 7205df03-15ff-4f8b-a7aa-ec9aa2d90e78 Asr 2025-07-10 10:11:40 UTC
Record ID 7: 1bd574a7-fc36-4ec9-b9e0-bac11eb65b44 Asr 2025-07-10 10:15:08 UTC
```

### **Log Error yang Menunjukkan Bug**
```
Daily filter - Date range (WITA): { start: '2025-07-09T08:00:00.000Z', end: '2025-07-10T07:59:59.999Z' }
Daily filter - Original WITA range: {
  witaStart: '2025-07-09T16:00:00.000Z',
  witaEnd: '2025-07-10T15:59:59.999Z'
}
```

**Masalah**: Ada **dua date range berbeda**! <PERSON> pertama salah (08:00-07:59) dan yang kedua benar (16:00-15:59).

## 🐛 **Bug yang Ditemukan**

**File**: `lib/data/repositories/absence.ts` - Line 321

**Kode Bermasalah**:
```typescript
// ❌ BUG: Membuat date dalam server timezone, bukan WITA
const cleanDate = new Date(year, month, day)

// ❌ BUG: setHours() menggunakan server timezone
const witaStartOfDay = new Date(cleanDate)
witaStartOfDay.setHours(0, 0, 0, 0)

// ❌ BUG: Konversi salah karena base date sudah salah
const utcStartOfDay = new Date(witaStartOfDay.getTime() - 8 * 60 * 60 * 1000)
```

**Mengapa Bug Ini Terjadi**:
1. `new Date(year, month, day)` membuat date dalam **server timezone** (bisa UTC, WIB, dll)
2. `setHours(0, 0, 0, 0)` set jam 00:00 dalam **server timezone**, bukan WITA
3. Konversi `- 8 * 60 * 60 * 1000` salah karena base date sudah dalam timezone yang salah
4. Hasil: Date range tidak mencakup waktu yang tepat untuk WITA

**Dampak**:
- Record Asr pada `2025-07-10 10:11:40 UTC` tidak masuk dalam date range yang salah
- Data yang seharusnya muncul di "hari ini" tidak ter-query dari database

## ✅ **Fix yang Diterapkan**

**File**: `lib/data/repositories/absence.ts` - Line 320-331

**Kode Baru**:
```typescript
// ✅ FIX: Create date range correctly for WITA timezone
// WITA is UTC+8, so we need to create UTC dates that represent WITA day boundaries

// Create start of day in WITA (00:00:00 WITA = 16:00:00 previous day UTC)
const utcStartOfDay = new Date(Date.UTC(year, month, day - 1, 16, 0, 0, 0))

// Create end of day in WITA (23:59:59 WITA = 15:59:59 current day UTC)
const utcEndOfDay = new Date(Date.UTC(year, month, day, 15, 59, 59, 999))

// For logging: show what WITA times these UTC times represent
const witaStartOfDay = new Date(utcStartOfDay.getTime() + 8 * 60 * 60 * 1000)
const witaEndOfDay = new Date(utcEndOfDay.getTime() + 8 * 60 * 60 * 1000)
```

**Penjelasan Fix**:
1. **Langsung buat UTC dates** yang merepresentasikan WITA day boundaries
2. **WITA 00:00:00** = **UTC 16:00:00 hari sebelumnya** (karena WITA = UTC+8)
3. **WITA 23:59:59** = **UTC 15:59:59 hari yang sama**
4. **Date range sekarang benar**: `2025-07-09T16:00:00.000Z` sampai `2025-07-10T15:59:59.999Z`

## 🎯 **Verifikasi Fix**

**Record Asr yang hilang**:
- `2025-07-10 10:11:40 UTC` ✅ **Sekarang masuk dalam range** (antara 16:00 hari sebelumnya dan 15:59 hari ini)
- `2025-07-10 10:15:08 UTC` ✅ **Sekarang masuk dalam range**

**Date Range Baru (Benar)**:
- Start: `2025-07-09T16:00:00.000Z` (= 2025-07-10 00:00:00 WITA)
- End: `2025-07-10T15:59:59.999Z` (= 2025-07-10 23:59:59 WITA)

## 🧪 **Testing**

### **Manual Test**:
1. **Clear cache**: `POST /api/admin/cache/clear` dengan `{"type": "prayer"}`
2. **Request fresh data**: `GET /api/absence/reports?date=today&reportType=prayer&force_fresh=true`
3. **Verify Asr data**: Cek apakah record Asr sekarang muncul

### **Script Test**:
```bash
# Set your admin token
export ADMIN_TOKEN="your_actual_admin_token"

# Run the test
node test-timezone-fix.js
```

## 📊 **Expected Results**

**Sebelum Fix**:
- ❌ Record Asr tidak muncul di reports
- ❌ Date range salah: `08:00:00.000Z` sampai `07:59:59.999Z`
- ❌ Database query tidak mencakup waktu yang tepat

**Setelah Fix**:
- ✅ Record Asr muncul di reports
- ✅ Date range benar: `16:00:00.000Z` sampai `15:59:59.999Z`
- ✅ Database query mencakup semua record WITA hari ini

## 🔧 **Files Modified**

1. **`lib/data/repositories/absence.ts`** - Fixed timezone conversion logic
2. **`app/api/admin/cache/clear/route.ts`** - Added cache clearing endpoint
3. **`test-timezone-fix.js`** - Test script untuk verify fix

## 🚀 **Next Steps**

1. **Test the fix** dengan script atau manual
2. **Verify Asr data** muncul di prayer reports
3. **Monitor logs** untuk memastikan date range benar
4. **Clear cache** jika masih ada data lama

## 📝 **Prevention**

Untuk mencegah bug serupa di masa depan:
1. **Always use UTC dates** untuk database queries
2. **Test timezone conversion** dengan data real
3. **Add logging** untuk debug date ranges
4. **Use timezone libraries** untuk konversi yang kompleks

**Bug ini sudah diperbaiki dan data Asr Anda seharusnya sekarang muncul di reports!** 🎉
