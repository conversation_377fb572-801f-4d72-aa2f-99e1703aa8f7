#!/usr/bin/env node

/**
 * Test Timezone Fix
 * Verify that the timezone bug fix resolves the missing Asr data issue
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'your_actual_token_here'

async function testTimezoneFix() {
  console.log('🔧 Testing Timezone Fix for Missing Asr Data...\n')

  if (ADMIN_TOKEN === 'your_actual_token_here') {
    console.log('❌ Please set your admin token:')
    console.log('   export ADMIN_TOKEN="your_actual_admin_token"')
    console.log('   Then run this script again')
    process.exit(1)
  }

  // Step 1: Clear cache to force fresh database query
  console.log('1️⃣ Clearing cache to force fresh database query...')
  try {
    const clearResponse = await fetch(`${BASE_URL}/api/admin/cache/clear`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      },
      body: JSON.stringify({ type: 'prayer' })
    })

    if (clearResponse.ok) {
      const result = await clearResponse.json()
      console.log('✅ Cache cleared:', result.message)
    } else {
      console.log('❌ Failed to clear cache:', clearResponse.status)
      if (clearResponse.status === 401) {
        console.log('🔐 Please check your admin token')
        process.exit(1)
      }
    }
  } catch (error) {
    console.log('❌ Cache clear error:', error.message)
  }

  // Step 2: Wait for cache to clear
  console.log('\n⏳ Waiting 3 seconds for cache to clear...')
  await new Promise(resolve => setTimeout(resolve, 3000))

  // Step 3: Request fresh data with force_fresh to see database query
  console.log('\n2️⃣ Requesting fresh data (should trigger database query)...')
  try {
    const response = await fetch(`${BASE_URL}/api/absence/reports?date=today&reportType=prayer&force_fresh=true&_t=${Date.now()}`, {
      headers: {
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      console.log(`✅ Retrieved ${data.length} student records`)

      // Check for Asr records
      const studentsWithAsr = data.filter(student => 
        student.attendanceRecords && 
        student.attendanceRecords.some(record => record.type === 'Asr')
      )

      console.log(`🔍 Found ${studentsWithAsr.length} students with Asr attendance`)

      if (studentsWithAsr.length > 0) {
        console.log('\n🎉 SUCCESS: Timezone fix worked! Asr data is now visible!')
        console.log('\n📋 Students with Asr:')
        studentsWithAsr.forEach((student, index) => {
          const asrRecord = student.attendanceRecords.find(record => record.type === 'Asr')
          console.log(`${index + 1}. ${student.name} (${student.className || 'No class'})`)
          console.log(`   Asr Time: ${asrRecord.recordedAt}`)
          console.log(`   Unique Code: ${student.uniqueCode}`)
        })
      } else {
        console.log('\n❌ Issue persists: No Asr records found')
        
        // Show sample data for debugging
        console.log('\n🔍 Sample of what we found:')
        data.slice(0, 5).forEach((student, index) => {
          const types = student.attendanceRecords?.map(r => r.type).join(', ') || 'No attendance'
          console.log(`${index + 1}. ${student.name}: ${types}`)
        })
      }

      // Check specific students from database
      const targetStudents = [
        '7205df03-15ff-4f8b-a7aa-ec9aa2d90e78',
        '1bd574a7-fc36-4ec9-b9e0-bac11eb65b44'
      ]

      console.log('\n🎯 Checking specific students from your database:')
      targetStudents.forEach(uniqueCode => {
        const student = data.find(s => s.uniqueCode === uniqueCode)
        if (student) {
          const asrRecord = student.attendanceRecords?.find(r => r.type === 'Asr')
          console.log(`✅ ${uniqueCode}: ${student.name}`)
          if (asrRecord) {
            console.log(`   ✅ Asr found: ${asrRecord.recordedAt}`)
          } else {
            console.log(`   ❌ No Asr record`)
            const allTypes = student.attendanceRecords?.map(r => r.type).join(', ') || 'None'
            console.log(`   Available: ${allTypes}`)
          }
        } else {
          console.log(`❌ ${uniqueCode}: Not found in results`)
        }
      })

    } else {
      console.log(`❌ Failed to get data: ${response.status} ${response.statusText}`)
    }
  } catch (error) {
    console.log(`❌ Data request error: ${error.message}`)
  }

  // Step 4: Test normal request (should use cache now)
  console.log('\n3️⃣ Testing normal request (should use updated cache)...')
  try {
    const response = await fetch(`${BASE_URL}/api/absence/reports?date=today&reportType=prayer&_t=${Math.floor(Date.now() / 120000)}`, {
      headers: {
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      const asrCount = data.filter(student => 
        student.attendanceRecords && 
        student.attendanceRecords.some(record => record.type === 'Asr')
      ).length

      console.log(`✅ Normal request: ${data.length} total records, ${asrCount} with Asr`)
      
      if (asrCount > 0) {
        console.log('🎉 PERFECT: Cache is now updated with correct data!')
      } else {
        console.log('⚠️  Cache might still have old data')
      }
    } else {
      console.log(`❌ Normal request failed: ${response.status}`)
    }
  } catch (error) {
    console.log(`❌ Normal request error: ${error.message}`)
  }

  console.log('\n📋 Summary:')
  console.log('✅ Timezone bug fixed: UTC date range now correctly represents WITA day boundaries')
  console.log('✅ WITA 00:00-23:59 now maps to UTC 16:00 (prev day) - 15:59 (current day)')
  console.log('✅ Database records at 10:11 UTC should now be included in today\'s WITA range')
  console.log('')
  console.log('🔍 Check server logs for:')
  console.log('   - "✅ FIXED Daily filter - UTC range for database query"')
  console.log('   - "📅 WITA day boundaries (for reference)"')
}

// Run the test
testTimezoneFix().catch(console.error)
