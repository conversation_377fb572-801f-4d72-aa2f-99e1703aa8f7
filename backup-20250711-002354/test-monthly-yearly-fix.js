#!/usr/bin/env node

/**
 * Test Monthly and Yearly Reports Timezone Fix
 * Verify that monthly and yearly reports now show updated data
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'your_actual_token_here'

async function testMonthlyYearlyFix() {
  console.log('🗓️ Testing Monthly and Yearly Reports Timezone Fix...\n')

  if (ADMIN_TOKEN === 'your_actual_token_here') {
    console.log('❌ Please set your admin token:')
    console.log('   export ADMIN_TOKEN="your_actual_admin_token"')
    console.log('   Then run this script again')
    process.exit(1)
  }

  // Step 1: Clear cache for all report types
  console.log('1️⃣ Clearing all report cache...')
  try {
    const clearResponse = await fetch(`${BASE_URL}/api/admin/cache/clear`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      },
      body: JSON.stringify({ type: 'all' })
    })

    if (clearResponse.ok) {
      const result = await clearResponse.json()
      console.log('✅ Cache cleared:', result.message)
    } else {
      console.log('❌ Failed to clear cache:', clearResponse.status)
      if (clearResponse.status === 401) {
        console.log('🔐 Please check your admin token')
        process.exit(1)
      }
    }
  } catch (error) {
    console.log('❌ Cache clear error:', error.message)
  }

  // Step 2: Wait for cache to clear
  console.log('\n⏳ Waiting 3 seconds for cache to clear...')
  await new Promise(resolve => setTimeout(resolve, 3000))

  // Step 3: Test current month report
  console.log('\n2️⃣ Testing Current Month Report...')
  await testReport('current-month', 'Current Month')

  // Step 4: Test specific monthly report (July 2025)
  console.log('\n3️⃣ Testing Specific Monthly Report (July 2025)...')
  await testReport('monthly', 'July 2025', { month: 7, year: 2025 })

  // Step 5: Test yearly report (2025)
  console.log('\n4️⃣ Testing Yearly Report (2025)...')
  await testReport('yearly', '2025', { year: 2025 })

  console.log('\n📋 Summary:')
  console.log('✅ Monthly and yearly timezone bugs fixed')
  console.log('✅ Date ranges now correctly represent WITA boundaries')
  console.log('✅ Your Asr data should now appear in all report types')
  console.log('')
  console.log('🔍 Check server logs for:')
  console.log('   - "📅 MONTHLY FILTER" messages')
  console.log('   - "📅 YEARLY FILTER" messages')
  console.log('   - UTC start/end times should be correct for WITA')
}

async function testReport(dateParam, reportName, params = {}) {
  try {
    // Build URL with parameters
    let url = `${BASE_URL}/api/absence/reports?date=${dateParam}&reportType=prayer&force_fresh=true&_t=${Date.now()}`
    
    if (params.month) url += `&month=${params.month}`
    if (params.year) url += `&year=${params.year}`

    const response = await fetch(url, {
      headers: {
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      console.log(`✅ ${reportName}: Retrieved ${data.length} student records`)

      // Check for Asr records
      const studentsWithAsr = data.filter(student => 
        student.attendanceRecords && 
        student.attendanceRecords.some(record => record.type === 'Asr') ||
        student.aggregatedCounts && student.aggregatedCounts.asr > 0 ||
        student.asr === true
      )

      console.log(`🔍 Found ${studentsWithAsr.length} students with Asr attendance`)

      if (studentsWithAsr.length > 0) {
        console.log(`🎉 SUCCESS: ${reportName} now shows Asr data!`)
        
        // Show sample of students with Asr
        const sampleSize = Math.min(3, studentsWithAsr.length)
        console.log(`📋 Sample students with Asr (showing ${sampleSize}):`)
        studentsWithAsr.slice(0, sampleSize).forEach((student, index) => {
          let asrInfo = 'Found'
          
          // Try to get more specific Asr info
          if (student.attendanceRecords) {
            const asrRecord = student.attendanceRecords.find(record => record.type === 'Asr')
            if (asrRecord) asrInfo = asrRecord.recordedAt
          } else if (student.aggregatedCounts && student.aggregatedCounts.asr > 0) {
            asrInfo = `${student.aggregatedCounts.asr}x`
          } else if (student.asrTime) {
            asrInfo = student.asrTime
          }
          
          console.log(`   ${index + 1}. ${student.name} (${student.className || 'No class'}): ${asrInfo}`)
        })
      } else {
        console.log(`❌ ${reportName}: No Asr records found`)
        
        // Show sample data for debugging
        console.log('🔍 Sample of what we found:')
        data.slice(0, 3).forEach((student, index) => {
          let attendanceInfo = 'No attendance'
          
          if (student.attendanceRecords && student.attendanceRecords.length > 0) {
            attendanceInfo = student.attendanceRecords.map(r => r.type).join(', ')
          } else if (student.aggregatedCounts) {
            const counts = []
            if (student.aggregatedCounts.zuhr > 0) counts.push(`Zuhr: ${student.aggregatedCounts.zuhr}`)
            if (student.aggregatedCounts.asr > 0) counts.push(`Asr: ${student.aggregatedCounts.asr}`)
            if (student.aggregatedCounts.dismissal > 0) counts.push(`Pulang: ${student.aggregatedCounts.dismissal}`)
            attendanceInfo = counts.length > 0 ? counts.join(', ') : 'No attendance'
          }
          
          console.log(`   ${index + 1}. ${student.name}: ${attendanceInfo}`)
        })
      }

      // Check specific students from database
      const targetStudents = [
        '7205df03-15ff-4f8b-a7aa-ec9aa2d90e78',
        '1bd574a7-fc36-4ec9-b9e0-bac11eb65b44'
      ]

      console.log(`🎯 Checking specific students in ${reportName}:`)
      targetStudents.forEach(uniqueCode => {
        const student = data.find(s => s.uniqueCode === uniqueCode)
        if (student) {
          let hasAsr = false
          let asrInfo = 'Not found'
          
          if (student.attendanceRecords) {
            const asrRecord = student.attendanceRecords.find(r => r.type === 'Asr')
            if (asrRecord) {
              hasAsr = true
              asrInfo = asrRecord.recordedAt
            }
          } else if (student.aggregatedCounts && student.aggregatedCounts.asr > 0) {
            hasAsr = true
            asrInfo = `${student.aggregatedCounts.asr}x`
          } else if (student.asr === true) {
            hasAsr = true
            asrInfo = student.asrTime || 'Found'
          }
          
          console.log(`${hasAsr ? '✅' : '❌'} ${uniqueCode}: ${student.name}`)
          console.log(`   Asr: ${asrInfo}`)
        } else {
          console.log(`❌ ${uniqueCode}: Not found in ${reportName}`)
        }
      })

    } else {
      console.log(`❌ ${reportName} request failed: ${response.status} ${response.statusText}`)
    }
  } catch (error) {
    console.log(`❌ ${reportName} request error: ${error.message}`)
  }
}

// Run the test
testMonthlyYearlyFix().catch(console.error)
