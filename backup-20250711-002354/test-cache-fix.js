#!/usr/bin/env node

/**
 * Test Cache Strategy Fix
 * Verifies that the cache strategy is working correctly
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'

async function testCacheStrategy() {
  console.log('🧪 Testing Cache Strategy Fix...\n')

  const tests = [
    {
      name: '1. Normal Request (Should use cache)',
      url: `${BASE_URL}/api/absence/reports?date=today&reportType=prayer`,
      expectCache: true,
      description: 'Normal page load should use cached data'
    },
    {
      name: '2. Manual Refresh (Should bypass cache)',
      url: `${BASE_URL}/api/absence/reports?date=today&reportType=prayer&force_fresh=true`,
      expectCache: false,
      description: 'Manual refresh should fetch fresh data from database'
    },
    {
      name: '3. Historical Data (Should use cache)',
      url: `${BASE_URL}/api/absence/reports?date=yesterday&reportType=prayer`,
      expectCache: true,
      description: 'Historical data should be cached longer'
    },
    {
      name: '4. School Reports (Should use cache)',
      url: `${BASE_URL}/api/absence/reports?date=today&reportType=school`,
      expectCache: true,
      description: 'School reports should also use cache'
    }
  ]

  for (const test of tests) {
    console.log(`\n${test.name}`)
    console.log(`📝 ${test.description}`)
    console.log(`🔗 ${test.url}`)
    
    try {
      const startTime = Date.now()
      const response = await fetch(test.url, {
        headers: {
          'Cookie': 'admin_auth_token=your_token_here', // Replace with actual token
        }
      })
      const endTime = Date.now()
      const responseTime = endTime - startTime

      if (response.ok) {
        const data = await response.json()
        console.log(`✅ Status: ${response.status}`)
        console.log(`⏱️  Response Time: ${responseTime}ms`)
        console.log(`📊 Data Count: ${Array.isArray(data) ? data.length : 'N/A'}`)
        
        // Analyze response time to guess cache usage
        if (test.expectCache && responseTime < 50) {
          console.log(`🚀 LIKELY CACHE HIT (fast response)`)
        } else if (!test.expectCache && responseTime > 20) {
          console.log(`🗄️  LIKELY DATABASE QUERY (slower response)`)
        } else {
          console.log(`⚠️  Response time: ${responseTime}ms (check logs for cache behavior)`)
        }
      } else {
        console.log(`❌ Error: ${response.status} ${response.statusText}`)
        if (response.status === 401) {
          console.log(`🔐 Authentication required - update the Cookie header with valid token`)
        }
      }
    } catch (error) {
      console.log(`❌ Request failed: ${error.message}`)
    }
  }

  console.log('\n📋 How to Verify the Fix:')
  console.log('1. Check server logs for Redis cache operations')
  console.log('2. Look for "Redis GET" vs "Fetching data from database" messages')
  console.log('3. Normal requests should show cache hits')
  console.log('4. force_fresh=true requests should show database queries')
  console.log('\n🔍 Server Log Patterns to Look For:')
  console.log('✅ Cache Hit: "Redis GET: absence:reports:prayer:2025-07-10:all:day"')
  console.log('🗄️  Database Query: "Fetching data from database"')
  console.log('🚀 Write-Through: "WRITE-THROUGH: Cache updated for [student] - [type]"')
}

// Run the test
testCacheStrategy().catch(console.error)
