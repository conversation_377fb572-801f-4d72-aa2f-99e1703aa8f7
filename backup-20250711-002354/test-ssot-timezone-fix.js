#!/usr/bin/env node

/**
 * Test SSOT Timezone Fix
 * Verify that all features now use Single Source of Truth for WITA timezone handling
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
const ADMIN_TOKEN = process.env.ADMIN_TOKEN || 'your_actual_token_here'

async function testSSOTTimezoneFix() {
  console.log('🔧 Testing SSOT Timezone Fix Across All Features...\n')

  if (ADMIN_TOKEN === 'your_actual_token_here') {
    console.log('❌ Please set your admin token:')
    console.log('   export ADMIN_TOKEN="your_actual_admin_token"')
    console.log('   Then run this script again')
    process.exit(1)
  }

  // Step 1: Clear all cache
  console.log('1️⃣ Clearing all cache to force fresh database queries...')
  try {
    const clearResponse = await fetch(`${BASE_URL}/api/admin/cache/clear`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      },
      body: JSON.stringify({ type: 'all' })
    })

    if (clearResponse.ok) {
      const result = await clearResponse.json()
      console.log('✅ Cache cleared:', result.message)
    } else {
      console.log('❌ Failed to clear cache:', clearResponse.status)
      if (clearResponse.status === 401) {
        console.log('🔐 Please check your admin token')
        process.exit(1)
      }
    }
  } catch (error) {
    console.log('❌ Cache clear error:', error.message)
  }

  // Step 2: Wait for cache to clear
  console.log('\n⏳ Waiting 3 seconds for cache to clear...')
  await new Promise(resolve => setTimeout(resolve, 3000))

  // Step 3: Test all date range endpoints
  const testCases = [
    {
      name: 'Daily Report (Today)',
      url: `${BASE_URL}/api/absence/reports?date=today&reportType=prayer&force_fresh=true`,
      expectLog: '✅ FIXED Daily filter - UTC range for database query'
    },
    {
      name: 'Specific Date (2025-07-10)',
      url: `${BASE_URL}/api/absence/reports?date=2025-07-10&reportType=prayer&force_fresh=true`,
      expectLog: '✅ SSOT SPECIFIC DATE'
    },
    {
      name: 'Current Month',
      url: `${BASE_URL}/api/absence/reports?date=current-month&reportType=prayer&force_fresh=true`,
      expectLog: '📅 SSOT CURRENT MONTH FILTER'
    },
    {
      name: 'Last 3 Months',
      url: `${BASE_URL}/api/absence/reports?date=last-3months&reportType=prayer&force_fresh=true`,
      expectLog: '📅 SSOT LAST 3 MONTHS FILTER'
    },
    {
      name: 'Monthly Report (July 2025)',
      url: `${BASE_URL}/api/absence/reports?date=monthly&month=7&year=2025&reportType=prayer&force_fresh=true`,
      expectLog: '📅 SSOT MONTHLY FILTER'
    },
    {
      name: 'Yearly Report (2025)',
      url: `${BASE_URL}/api/absence/reports?date=yearly&year=2025&reportType=prayer&force_fresh=true`,
      expectLog: '📅 SSOT YEARLY FILTER'
    },
    {
      name: 'Date Range (July 1-10, 2025)',
      url: `${BASE_URL}/api/absence/reports/range?startDate=2025-07-01&endDate=2025-07-10&reportType=prayer&force_fresh=true`,
      expectLog: '✅ SSOT RANGE'
    }
  ]

  console.log('\n2️⃣ Testing all SSOT timezone implementations...\n')

  for (const testCase of testCases) {
    console.log(`🧪 Testing: ${testCase.name}`)
    
    try {
      const response = await fetch(testCase.url, {
        headers: {
          'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        const recordCount = Array.isArray(data) ? data.length : (data.data ? data.data.length : 0)
        
        console.log(`   ✅ Status: ${response.status}`)
        console.log(`   📊 Records: ${recordCount}`)
        
        // Check for Asr data specifically
        let asrCount = 0
        const records = Array.isArray(data) ? data : (data.data || [])
        
        if (Array.isArray(records)) {
          asrCount = records.filter(record => 
            record.attendanceRecords && 
            record.attendanceRecords.some(att => att.type === 'Asr') ||
            record.aggregatedCounts && record.aggregatedCounts.asr > 0 ||
            record.asr === true
          ).length
        }
        
        console.log(`   🔍 Asr Records: ${asrCount}`)
        
        if (asrCount > 0) {
          console.log(`   🎉 SUCCESS: SSOT fix working - Asr data visible!`)
        } else {
          console.log(`   ⚠️  No Asr data found (check server logs for SSOT messages)`)
        }
        
      } else {
        console.log(`   ❌ Failed: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`)
    }
    
    console.log('') // Empty line for readability
  }

  // Step 4: Test specific students
  console.log('3️⃣ Testing specific students with known Asr data...')
  
  const targetStudents = [
    '7205df03-15ff-4f8b-a7aa-ec9aa2d90e78',
    '1bd574a7-fc36-4ec9-b9e0-bac11eb65b44'
  ]

  try {
    const response = await fetch(`${BASE_URL}/api/absence/reports?date=today&reportType=prayer&force_fresh=true`, {
      headers: {
        'Cookie': `admin_auth_token=${ADMIN_TOKEN}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      
      console.log('🎯 Checking specific students:')
      targetStudents.forEach(uniqueCode => {
        const student = data.find(s => s.uniqueCode === uniqueCode)
        if (student) {
          const asrRecord = student.attendanceRecords?.find(r => r.type === 'Asr')
          console.log(`✅ ${uniqueCode}: ${student.name}`)
          if (asrRecord) {
            console.log(`   ✅ Asr found: ${asrRecord.recordedAt}`)
          } else {
            console.log(`   ❌ No Asr record`)
            const allTypes = student.attendanceRecords?.map(r => r.type).join(', ') || 'None'
            console.log(`   Available: ${allTypes}`)
          }
        } else {
          console.log(`❌ ${uniqueCode}: Not found in results`)
        }
      })
    } else {
      console.log(`❌ Failed to get student data: ${response.status}`)
    }
  } catch (error) {
    console.log(`❌ Student check error: ${error.message}`)
  }

  // Step 5: Summary
  console.log('\n📋 SSOT Timezone Fix Summary:')
  console.log('✅ All date range creation now uses centralized SSOT functions:')
  console.log('   - createWITADateRange() for daily dates')
  console.log('   - createWITAMonthRange() for monthly ranges')
  console.log('   - createWITAYearRange() for yearly ranges')
  console.log('   - createWITACustomRange() for custom date ranges')
  console.log('')
  console.log('✅ Fixed endpoints:')
  console.log('   - /api/absence/reports (all date filters)')
  console.log('   - /api/absence/reports/range (custom ranges)')
  console.log('   - /api/debug/absence (debug endpoint)')
  console.log('')
  console.log('✅ WITA timezone conversion is now consistent across all features')
  console.log('✅ Database queries use correct UTC ranges for WITA boundaries')
  console.log('✅ Your Asr data should now appear in all report types')
  console.log('')
  console.log('🔍 Check server logs for SSOT messages:')
  console.log('   - "✅ SSOT SPECIFIC DATE"')
  console.log('   - "📅 SSOT MONTHLY FILTER"')
  console.log('   - "📅 SSOT YEARLY FILTER"')
  console.log('   - "✅ SSOT RANGE"')
  console.log('')
  console.log('📅 All date ranges now correctly map WITA to UTC:')
  console.log('   - WITA 00:00:00 = UTC 16:00:00 (previous day)')
  console.log('   - WITA 23:59:59 = UTC 15:59:59 (same day)')
}

// Run the test
testSSOTTimezoneFix().catch(console.error)
