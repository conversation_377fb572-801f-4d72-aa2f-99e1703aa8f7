# 🗓️ Monthly & Yearly Reports Timezone Fix

## 🔍 **Problem Analysis**

Setelah fix timezone untuk laporan harian ber<PERSON>il, tern<PERSON>ta **laporan bulanan dan tahunan masih menggunakan timezone bug yang sama**. <PERSON>reka tidak ter-update karena menggunakan logic date range yang salah.

### **Root Cause**
File: `app/api/absence/reports/route.ts`

**Bug yang sama** seperti laporan harian:
1. `new Date(year, month - 1, 1)` - membuat date dalam server timezone
2. `setHours(0, 0, 0, 0)` - set jam dalam server timezone, bukan WITA
3. Tidak ada konversi UTC yang benar untuk WITA

## 🐛 **Bugs yang Diperbaiki**

### **1. Monthly Filter (Line 114-124)**

**Before (Buggy)**:
```typescript
// ❌ BUG: Server timezone, bukan WITA
startDate = new Date(year, month - 1, 1) // First day of month
endDate = new Date(year, month, 0) // Last day of month
startDate.setHours(0, 0, 0, 0)
endDate.setHours(23, 59, 59, 999)
```

**After (Fixed)**:
```typescript
// ✅ FIX: Create month range correctly for WITA timezone
const firstDay = new Date(year, month - 1, 1)
const lastDay = new Date(year, month, 0)

startDate = new Date(Date.UTC(firstDay.getFullYear(), firstDay.getMonth(), firstDay.getDate() - 1, 16, 0, 0, 0))
endDate = new Date(Date.UTC(lastDay.getFullYear(), lastDay.getMonth(), lastDay.getDate(), 15, 59, 59, 999))
```

### **2. Yearly Filter (Line 138-147)**

**Before (Buggy)**:
```typescript
// ❌ BUG: Server timezone, bukan WITA
startDate = new Date(year, 0, 1) // January 1st
endDate = new Date(year, 11, 31) // December 31st
startDate.setHours(0, 0, 0, 0)
endDate.setHours(23, 59, 59, 999)
```

**After (Fixed)**:
```typescript
// ✅ FIX: Create year range correctly for WITA timezone
startDate = new Date(Date.UTC(year - 1, 11, 31, 16, 0, 0, 0)) // Dec 31 prev year 16:00 UTC
endDate = new Date(Date.UTC(year, 11, 31, 15, 59, 59, 999)) // Dec 31 current year 15:59 UTC
```

### **3. Current Month Filter (Line 94-103)**

**Before (Buggy)**:
```typescript
// ❌ BUG: Server timezone
startDate = new Date(now.getFullYear(), now.getMonth(), 1)
startDate.setHours(0, 0, 0, 0)
endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0)
endDate.setHours(23, 59, 59, 999)
```

**After (Fixed)**:
```typescript
// ✅ FIX: Create current month range correctly for WITA timezone
const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)

startDate = new Date(Date.UTC(firstDay.getFullYear(), firstDay.getMonth(), firstDay.getDate() - 1, 16, 0, 0, 0))
endDate = new Date(Date.UTC(lastDay.getFullYear(), lastDay.getMonth(), lastDay.getDate(), 15, 59, 59, 999))
```

### **4. Last 3 Months Filter (Line 118-127)**

**Before (Buggy)**:
```typescript
// ❌ BUG: Server timezone
endDate = createDateForDay()
startDate = new Date(endDate)
startDate.setMonth(startDate.getMonth() - 3)
startDate.setHours(0, 0, 0, 0)
endDate.setHours(23, 59, 59, 999)
```

**After (Fixed)**:
```typescript
// ✅ FIX: Create 3-month range correctly for WITA timezone
const endDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
const startDay = new Date(endDay)
startDay.setMonth(startDay.getMonth() - 3)

startDate = new Date(Date.UTC(startDay.getFullYear(), startDay.getMonth(), startDay.getDate() - 1, 16, 0, 0, 0))
endDate = new Date(Date.UTC(endDay.getFullYear(), endDay.getMonth(), endDay.getDate(), 15, 59, 59, 999))
```

## 📅 **WITA to UTC Conversion Logic**

**Konsep**: WITA = UTC+8, jadi:
- **WITA 00:00:00** = **UTC 16:00:00 hari sebelumnya**
- **WITA 23:59:59** = **UTC 15:59:59 hari yang sama**

**Contoh untuk Juli 2025**:
- **WITA Range**: 2025-07-01 00:00:00 sampai 2025-07-31 23:59:59
- **UTC Range**: 2025-06-30 16:00:00 sampai 2025-07-31 15:59:59

**Data Asr Anda**:
- `2025-07-10 10:11:40 UTC` ✅ **Masuk dalam range Juli 2025**
- `2025-07-10 10:15:08 UTC` ✅ **Masuk dalam range Juli 2025**

## 🧪 **Testing**

### **Manual Test**:
```bash
# Set admin token
export ADMIN_TOKEN="your_actual_admin_token"

# Test the fix
./fix-monthly-yearly.sh
```

### **API Test**:
```bash
# Clear cache
curl -X POST http://localhost:3000/api/admin/cache/clear \
  -H "Content-Type: application/json" \
  -H "Cookie: admin_auth_token=YOUR_TOKEN" \
  -d '{"type": "all"}'

# Test monthly report
curl "http://localhost:3000/api/absence/reports?date=monthly&month=7&year=2025&reportType=prayer&force_fresh=true"

# Test yearly report  
curl "http://localhost:3000/api/absence/reports?date=yearly&year=2025&reportType=prayer&force_fresh=true"
```

## 📊 **Expected Results**

**Sebelum Fix**:
- ❌ Data Asr tidak muncul di laporan bulanan/tahunan
- ❌ Date range salah untuk WITA timezone
- ❌ Record `2025-07-10 10:11:40 UTC` tidak ter-query

**Setelah Fix**:
- ✅ Data Asr muncul di semua laporan (harian, bulanan, tahunan)
- ✅ Date range benar untuk WITA timezone
- ✅ Record `2025-07-10 10:11:40 UTC` ter-query dengan benar

## 🔍 **Verification**

**Check Server Logs untuk**:
```
📅 MONTHLY FILTER (2025-07): {
  witaStart: '2025-07-01 00:00:00 WITA',
  witaEnd: '2025-07-31 23:59:59 WITA',
  utcStart: '2025-06-30T16:00:00.000Z',
  utcEnd: '2025-07-31T15:59:59.999Z'
}

📅 YEARLY FILTER (2025): {
  witaStart: '2025-01-01 00:00:00 WITA',
  witaEnd: '2025-12-31 23:59:59 WITA',
  utcStart: '2024-12-31T16:00:00.000Z',
  utcEnd: '2025-12-31T15:59:59.999Z'
}
```

## 🎯 **Impact**

**Files Modified**:
- `app/api/absence/reports/route.ts` - Fixed all date range filters

**Report Types Fixed**:
- ✅ Current Month (`current-month`)
- ✅ Last 3 Months (`last-3months`) 
- ✅ Specific Monthly (`monthly`)
- ✅ Yearly (`yearly`)

**Data Visibility**:
- ✅ Data Asr sekarang muncul di semua jenis laporan
- ✅ Timezone handling konsisten di seluruh sistem
- ✅ Database queries menggunakan UTC range yang benar

## 📝 **Summary**

**Masalah**: Laporan bulanan dan tahunan menggunakan timezone bug yang sama seperti laporan harian.

**Solusi**: Terapkan fix timezone yang sama untuk semua filter date range.

**Hasil**: Semua laporan (harian, bulanan, tahunan) sekarang menampilkan data Asr dengan benar.

**Data Asr Anda sekarang akan muncul di semua jenis laporan!** 🎉
